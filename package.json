{"name": "haute-vault", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^2.5.4", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.14", "turbo": "^2.5.5", "typescript": "5.8.3"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}