import { useState, useEffect, useCallback } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@repo/backend/convex/_generated/api';
import { useAuth } from './useBetterAuth';

const LOCAL_STORAGE_KEY = 'recentSearches';
const MAX_LOCAL_SEARCHES = 10;
const MAX_SERVER_SEARCHES = 20;

interface SearchHistoryItem {
  query: string;
  timestamp: number;
  resultCount?: number;
}

export const useRecentSearches = () => {
  const { user } = useAuth();
  const [localSearches, setLocalSearches] = useState<string[]>([]);
  const [mergedSearches, setMergedSearches] = useState<string[]>([]);
  const [isCleared, setIsCleared] = useState(false);

  // Convex mutations and queries
  const saveSearchHistory = useMutation(api.productQueries.saveSearchHistory);
  const serverSearches = useQuery(
    api.productQueries.getUserRecentSearches,
    user ? { limit: MAX_SERVER_SEARCHES } : "skip"
  );

  // Load searches from localStorage
  const getLocalRecentSearches = useCallback((): string[] => {
    try {
      const stored = localStorage.getItem(LOCAL_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return Array.isArray(parsed) ? parsed.slice(0, MAX_LOCAL_SEARCHES) : [];
      }
    } catch (error) {
      console.warn('Failed to parse recent searches from localStorage:', error);
    }
    return [];
  }, []);

  // Save searches to localStorage
  const saveLocalRecentSearches = useCallback((searches: string[]) => {
    try {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(searches));
    } catch (error) {
      console.warn('Failed to save recent searches to localStorage:', error);
    }
  }, []);

  // Merge local and server searches, removing duplicates
  const mergeSearches = useCallback((local: string[], server: string[]): string[] => {
    const merged = [...local];
    
    // Add server searches that aren't already in local
    server.forEach(query => {
      if (!merged.includes(query)) {
        merged.push(query);
      }
    });
    
    // Return unique searches, prioritizing local ones
    return merged.slice(0, MAX_LOCAL_SEARCHES);
  }, []);

  // Initialize searches on mount
  useEffect(() => {
    const local = getLocalRecentSearches();
    setLocalSearches(local);
    setMergedSearches(local);
  }, [getLocalRecentSearches]);

  // Update merged searches when server data changes
  useEffect(() => {
    if (serverSearches && user && !isCleared) {
      const merged = mergeSearches(localSearches, serverSearches);
      setMergedSearches(merged);
    }
  }, [serverSearches, localSearches, mergeSearches, user, isCleared]);

  // Add a new search query
  const addSearch = useCallback(async (
    query: string, 
    resultCount?: number, 
    category?: string, 
    brand?: string
  ) => {
    if (!query.trim()) return;

    const trimmedQuery = query.trim();
    
    // Update local state immediately
    const updatedLocal = [
      trimmedQuery,
      ...localSearches.filter(s => s !== trimmedQuery)
    ].slice(0, MAX_LOCAL_SEARCHES);
    
    setLocalSearches(updatedLocal);
    setIsCleared(false); // Reset cleared flag when adding new search
    saveLocalRecentSearches(updatedLocal);
    
    // Update merged searches
    const merged = mergeSearches(updatedLocal, serverSearches || []);
    setMergedSearches(merged);
    
    // Save to server if user is logged in
    if (user) {
      try {
        await saveSearchHistory({
          searchQuery: trimmedQuery,
          resultCount,
          category,
          brand,
        });
      } catch (error) {
        console.warn('Failed to save search to server:', error);
      }
    }
  }, [localSearches, serverSearches, user, saveSearchHistory, mergeSearches, saveLocalRecentSearches]);

  // Clear all searches
  const clearSearches = useCallback(() => {
    setLocalSearches([]);
    setMergedSearches([]);
    setIsCleared(true);
    saveLocalRecentSearches([]);
    
    // Note: We don't clear server searches as they might be useful for analytics
    // But we need to prevent them from being merged back in immediately
  }, [saveLocalRecentSearches]);

  // Remove a specific search
  const removeSearch = useCallback((queryToRemove: string) => {
    const updatedLocal = localSearches.filter(s => s !== queryToRemove);
    setLocalSearches(updatedLocal);
    saveLocalRecentSearches(updatedLocal);
    
    // Also remove from merged searches to ensure immediate UI update
    const updatedMerged = mergedSearches.filter(s => s !== queryToRemove);
    setMergedSearches(updatedMerged);
  }, [localSearches, mergedSearches, saveLocalRecentSearches]);

  return {
    searches: mergedSearches,
    addSearch,
    clearSearches,
    removeSearch,
    isLoading: serverSearches === undefined && !!user,
  };
};
