"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { useState } from "react";
import { toast } from "sonner";

export interface UseFavoritesReturn {
  isFavorited: boolean;
  isLoading: boolean;
  toggleFavorite: () => Promise<void>;
  favoritesCount: number;
}

/**
 * Hook for managing a single product's favorite status
 */
export function useFavorite(productId: Id<"products">): UseFavoritesReturn {
  const [isToggling, setIsToggling] = useState(false);
  
  const isFavorited = useQuery(api.favorites.isFavorited, { productId }) ?? false;
  const favoritesCount = useQuery(api.favorites.getFavoritesCount, { productId }) ?? 0;
  const toggleFavoriteMutation = useMutation(api.favorites.toggleFavorite);

  const toggleFavorite = async () => {
    try {
      setIsToggling(true);
      const result = await toggleFavoriteMutation({ productId });
      
      if (result.isFavorited) {
        toast.success("Added to favorites!");
      } else {
        toast.success("Removed from favorites");
      }
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to add favorites");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to update favorites");
      }
    } finally {
      setIsToggling(false);
    }
  };

  return {
    isFavorited,
    isLoading: isToggling,
    toggleFavorite,
    favoritesCount,
  };
}

/**
 * Hook for managing multiple products' favorite statuses (useful for product grids)
 */
export function useFavoriteStatuses(productIds: Id<"products">[]) {
  const favoriteStatuses = useQuery(
    api.favorites.getFavoriteStatuses, 
    { productIds }
  ) ?? {};
  
  const toggleFavoriteMutation = useMutation(api.favorites.toggleFavorite);

  const toggleFavorite = async (productId: Id<"products">) => {
    try {
      const result = await toggleFavoriteMutation({ productId });
      
      if (result.isFavorited) {
        toast.success("Added to favorites!");
      } else {
        toast.success("Removed from favorites");
      }
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to add favorites");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to update favorites");
      }
    }
  };

  const isFavorited = (productId: Id<"products">) => {
    return favoriteStatuses[productId] ?? false;
  };

  return {
    favoriteStatuses,
    toggleFavorite,
    isFavorited,
  };
}

/**
 * Hook for fetching user's favorite products
 */
export function useUserFavorites(options?: { 
  limit?: number; 
  offset?: number;
  includeSold?: boolean;
}) {
  const { limit = 20, offset = 0, includeSold = true } = options ?? {};
  
  const favoritesData = useQuery(api.favorites.getUserFavorites, {
    limit,
    offset,
    includeSold,
  });

  return {
    favorites: favoritesData?.products ?? [],
    total: favoritesData?.total ?? 0,
    hasMore: favoritesData?.hasMore ?? false,
    isLoading: favoritesData === undefined,
    summary: favoritesData?.summary ?? {
      total: 0,
      available: 0,
      sold: 0,
      reserved: 0,
    },
  };
}

/**
 * Hook for fetching user's favorites organized by status
 */
export function useFavoritesWithStatus(options?: { 
  limit?: number; 
  offset?: number;
}) {
  const { limit = 20, offset = 0 } = options ?? {};
  
  const favoritesData = useQuery(api.favorites.getFavoritesWithStatus, {
    limit,
    offset,
  });

  return {
    available: favoritesData?.available ?? { products: [], total: 0, hasMore: false },
    sold: favoritesData?.sold ?? { products: [], total: 0 },
    reserved: favoritesData?.reserved ?? { products: [], total: 0 },
    summary: favoritesData?.summary ?? {
      total: 0,
      available: 0,
      sold: 0,
      reserved: 0,
      other: 0,
    },
    pagination: favoritesData?.pagination ?? {
      limit,
      offset,
      totalAvailable: 0,
    },
    isLoading: favoritesData === undefined,
  };
}

/**
 * Hook for managing individual favorite actions
 */
export function useFavoriteActions() {
  const addToFavoritesMutation = useMutation(api.favorites.addToFavorites);
  const removeFromFavoritesMutation = useMutation(api.favorites.removeFromFavorites);
  const toggleFavoriteMutation = useMutation(api.favorites.toggleFavorite);

  const addToFavorites = async (productId: Id<"products">) => {
    try {
      await addToFavoritesMutation({ productId });
      toast.success("Added to favorites!");
    } catch (error) {
      console.error("Failed to add to favorites:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to add favorites");
        } else if (error.message.includes("already in favorites")) {
          toast.info("Already in favorites");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to add to favorites");
      }
    }
  };

  const removeFromFavorites = async (productId: Id<"products">) => {
    try {
      await removeFromFavoritesMutation({ productId });
      toast.success("Removed from favorites");
    } catch (error) {
      console.error("Failed to remove from favorites:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to manage favorites");
        } else if (error.message.includes("not in favorites")) {
          toast.info("Not in favorites");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to remove from favorites");
      }
    }
  };

  const toggleFavorite = async (productId: Id<"products">) => {
    try {
      const result = await toggleFavoriteMutation({ productId });
      
      if (result.isFavorited) {
        toast.success("Added to favorites!");
      } else {
        toast.success("Removed from favorites");
      }
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to add favorites");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to update favorites");
      }
    }
  };

  return {
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
  };
}
