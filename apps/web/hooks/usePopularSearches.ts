import { useState, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@repo/backend/convex/_generated/api';

const FALLBACK_POPULAR_SEARCHES = [
  'Rolex Submariner',
  'Omega Speedmaster', 
  '<PERSON><PERSON> Philippe',
  'Tudor Black Bay',
  'Cartier Santos',
  'Audemars Piguet',
  'Vintage',
  'GMT',
  'Chronograph',
  'Luxury'
];

export const usePopularSearches = (options?: {
  limit?: number;
  category?: string;
  timeRange?: '7d' | '30d' | '90d';
  enableFallback?: boolean;
}) => {
  const {
    limit = 10,
    category,
    timeRange = '30d',
    enableFallback = true
  } = options || {};

  const [popularSearches, setPopularSearches] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch popular searches from Convex
  const convexPopularSearches = useQuery(
    api.productQueries.getPopularSearches,
    { limit, category, timeRange }
  );

  // Fetch category-based popular searches
  const categoryPopularSearches = useQuery(
    api.productQueries.getPopularSearchesByCategory,
    { limit: Math.ceil(limit / 2) }
  );

  useEffect(() => {
    if (convexPopularSearches) {
      // Extract queries from the popular searches
      const queries = convexPopularSearches.map(search => search.query);
      
      if (queries.length > 0) {
        setPopularSearches(queries);
        setIsLoading(false);
      } else if (enableFallback) {
        // Use fallback if no popular searches found
        setPopularSearches(FALLBACK_POPULAR_SEARCHES.slice(0, limit));
        setIsLoading(false);
      }
    } else if (convexPopularSearches === null && enableFallback) {
      // Use fallback if query returns null (no data)
      setPopularSearches(FALLBACK_POPULAR_SEARCHES.slice(0, limit));
      setIsLoading(false);
    }
  }, [convexPopularSearches, limit, enableFallback]);

  // Get popular searches by category
  const getPopularByCategory = (categoryType: 'brand' | 'category' | 'general') => {
    if (!categoryPopularSearches) return [];
    
    switch (categoryType) {
      case 'brand':
        return categoryPopularSearches.brands?.map((item: any) => item.query) || [];
      case 'category':
        return categoryPopularSearches.categories?.map((item: any) => item.query) || [];
      case 'general':
        return categoryPopularSearches.general?.map((item: any) => item.query) || [];
      default:
        return [];
    }
  };

  // Get trending searches (most recent popular searches)
  const getTrendingSearches = () => {
    if (!convexPopularSearches) return [];
    
    return convexPopularSearches
      .filter(search => search.recentSearches > 0)
      .sort((a, b) => b.recentSearches - a.recentSearches)
      .slice(0, 5)
      .map(search => search.query);
  };

  // Get search statistics for a specific query
  const getSearchStats = (query: string) => {
    if (!convexPopularSearches) return null;
    
    return convexPopularSearches.find(search => 
      search.query.toLowerCase() === query.toLowerCase()
    );
  };

  return {
    popularSearches,
    isLoading,
    getPopularByCategory,
    getTrendingSearches,
    getSearchStats,
    // Raw data for advanced usage
    rawData: convexPopularSearches,
    categoryData: categoryPopularSearches,
  };
};
