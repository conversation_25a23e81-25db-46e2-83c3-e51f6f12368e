"use client";

import { useQuery, useMutation } from "convex/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { api } from "@repo/backend/convex/_generated/api";
import { authClient } from "@repo/backend/better-auth/client";

export interface UseAuthReturn {
  user: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  hasActiveSubscription: boolean;
  sellerStatus: {
    hasProfile: boolean;
    verificationStatus: "pending" | "approved" | "rejected" | "under_review";
    canSell: boolean;
  } | null;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithApple: () => Promise<void>;
  updateProfile: (data: any) => Promise<void>;
  refreshSession: () => Promise<void>;
}

export function useAuth(): UseAuthReturn {
  const router = useRouter();
  const userResult = useQuery(api.auth.getCurrentUser);
  const hasActiveSubscription = useQuery(api.auth.hasActiveSubscription);
  const sellerStatus = useQuery(api.auth.getSellerStatus);
  const createMissingUser = useMutation(api.auth.createMissingConvexUser);


  const isLoading = userResult === undefined || (userResult && userResult.needsCreation);
  const user = (userResult && !userResult.authMismatch && !userResult.shouldClearAuth && !userResult.needsCreation) ? userResult : null;
  const isAuthenticated = !!user;

  // Debug logging can be enabled here if needed for troubleshooting
  // console.log("useAuth - Debug Info:", { isLoading, isAuthenticated, userType: user?.userType });

  // Handle missing user creation
  useEffect(() => {
    const handleMissingUser = async () => {
      if (userResult && userResult.needsCreation && userResult.betterAuthData) {
        try {
          console.log("Creating missing Convex user for Better Auth user");
          await createMissingUser({
            email: userResult.betterAuthData.email,
            name: userResult.betterAuthData.name || "User",
            betterAuthUserId: userResult.betterAuthData.userId,
            emailVerified: userResult.betterAuthData.emailVerified || false,
          });
          console.log("Successfully created missing user");
          // The user query will automatically refresh and get the new user
        } catch (err) {
          console.error("Failed to create missing user:", err);
        }
      }
    };

    handleMissingUser();
  }, [userResult, createMissingUser]);

  // Handle auth state mismatch by clearing authentication (fallback only)
  useEffect(() => {
    if (userResult && userResult.shouldClearAuth) {
      console.warn("Auth mismatch detected - user sync failed. Clearing auth state.");
      
      // Clear auth state and reload
      authClient.signOut().then(() => {
        localStorage.clear();
        sessionStorage.clear();
        window.location.reload();
      }).catch(() => {
        // Force reload anyway
        localStorage.clear();
        sessionStorage.clear();
        window.location.reload();
      });
    }
  }, [userResult]);

  const signIn = async (email: string, password: string) => {
    await authClient.signIn.email({
      email,
      password,
    });
    
    // Navigate to dashboard on successful login
    setTimeout(() => {
      router.push("/dashboard");
    }, 100);
  };

  const signUp = async (email: string, password: string, name: string) => {
    try {
      await authClient.signUp.email({
        email,
        password,
        name,
      });
      
      // Navigate to dashboard on successful signup
      setTimeout(() => {
        router.push("/dashboard");
      }, 100);
    } catch (error: any) {
      console.log("Signup error:", error);
      
      // Check if this is a "User already exists" error
      if (error?.message?.includes("User already exists") || 
          error?.body?.message?.includes("User already exists") ||
          error?.error?.includes("User already exists")) {
        
        console.log("User already exists - attempting signin instead");
        
        try {
          // User exists in Better Auth, try signing them in instead
          // The getCurrentUser query will automatically create the missing Convex user
          await authClient.signIn.email({
            email,
            password,
          });
          
          console.log("Successfully signed in existing Better Auth user");
          
          // Navigate to dashboard on successful signin
          setTimeout(() => {
            router.push("/dashboard");
          }, 100);
          
          return; // Exit early, signup successful via signin
          
        } catch (signinError) {
          console.error("Failed to signin existing Better Auth user:", signinError);
          throw new Error("User already exists but signin failed. Please try signing in directly.");
        }
      }
      
      // Re-throw the original error if it's not a "User already exists" error
      throw error;
    }
  };

  const signOut = async () => {
    await authClient.signOut();
    router.push("/");
  };

  const signInWithGoogle = async () => {
    await authClient.signIn.social({
      provider: "google",
    });
  };

  const signInWithApple = async () => {
    await authClient.signIn.social({
      provider: "apple",
    });
  };

  const updateProfile = async (data: any) => {
    // This would call a mutation to update the user profile
    // Implementation depends on your specific needs
  };

  const refreshSession = async () => {
    try {
      await authClient.updateSession();
      console.log("Session updated successfully");
      
      // Hard refresh to ensure all components get the updated user data
      setTimeout(() => {
        window.location.reload();
      }, 500);
    } catch (error) {
      console.error("Failed to refresh session:", error);
      // Fallback to hard refresh anyway
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }
  };

  return {
    user,
    isLoading,
    isAuthenticated,
    hasActiveSubscription: hasActiveSubscription ?? false,
    sellerStatus: sellerStatus ?? null,
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithApple,
    updateProfile,
    refreshSession,
  };
}

export function useRequireAuth() {
  const { user, isLoading } = useAuth();

  if (isLoading) return { isLoading: true, user: null };
  if (!user) throw new Error("Authentication required");

  return { isLoading: false, user };
}

export function useRequireSubscription() {
  const { user, hasActiveSubscription, isLoading } = useAuth();

  if (isLoading) return { isLoading: true, user: null, hasSubscription: false };
  if (!user) throw new Error("Authentication required");
  if (!hasActiveSubscription) throw new Error("Active subscription required");

  return { isLoading: false, user, hasSubscription: true };
}

export function useRequireSellerStatus() {
  const { user, sellerStatus, isLoading } = useAuth();

  if (isLoading) return { isLoading: true, user: null, canSell: false };
  if (!user) throw new Error("Authentication required");
  if (user.userType !== "seller") throw new Error("Seller account required");
  if (!sellerStatus?.canSell) throw new Error("Seller verification required");

  return { isLoading: false, user, sellerStatus, canSell: true };
}

export function useRequireAdmin() {
  const { user, isLoading } = useAuth();

  if (isLoading) return { isLoading: true, user: null };
  if (!user) throw new Error("Authentication required");
  if (user.userType !== "admin") throw new Error("Admin access required");
  return { isLoading: false, user };
}


