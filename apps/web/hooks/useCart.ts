"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { useState } from "react";
import { toast } from "sonner";

export interface CartItem {
  _id: Id<"cart">;
  quantity: number;
  _creationTime: number;
  product: {
    _id: Id<"products">;
    title: string;
    brand: string;
    price: number;
    images: string[];
    condition: string;
    category: string;
    status: string;
    seller?: {
      _id: string;
      name: string;
      businessName?: string;
      rating: number;
      reviewCount: number;
      verificationStatus?: string;
    };
  };
}

export interface CartTotal {
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  itemCount: number;
}

/**
 * Hook for managing cart functionality
 */
export function useCart() {
  const [isLoading, setIsLoading] = useState(false);
  
  const cartItems = useQuery(api.cart.getCart) ?? [];
  const cartCount = useQuery(api.cart.getCartCount) ?? 0;
  const cartTotal = useQuery(api.cart.getCartTotal);
  
  const addToCartMutation = useMutation(api.cart.addToCart);
  const removeFromCartMutation = useMutation(api.cart.removeFromCart);
  const updateQuantityMutation = useMutation(api.cart.updateCartQuantity);
  const clearCartMutation = useMutation(api.cart.clearCart);

  const addToCart = async (productId: Id<"products">, quantity: number = 1) => {
    try {
      setIsLoading(true);
      await addToCartMutation({ productId, quantity });
      toast.success("Added to cart!");
    } catch (error) {
      console.error("Failed to add to cart:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to add items to cart");
        } else if (error.message.includes("Cannot add your own product")) {
          toast.error("You cannot add your own products to cart");
        } else if (error.message.includes("not available")) {
          toast.error("This product is no longer available");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to add to cart");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const removeFromCart = async (productId: Id<"products">) => {
    try {
      setIsLoading(true);
      await removeFromCartMutation({ productId });
      toast.success("Removed from cart");
    } catch (error) {
      console.error("Failed to remove from cart:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to manage cart");
        } else if (error.message.includes("not in cart")) {
          toast.info("Item not in cart");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to remove from cart");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const updateQuantity = async (productId: Id<"products">, quantity: number) => {
    try {
      setIsLoading(true);
      await updateQuantityMutation({ productId, quantity });
      toast.success("Cart updated");
    } catch (error) {
      console.error("Failed to update cart:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to manage cart");
        } else if (error.message.includes("not in cart")) {
          toast.info("Item not in cart");
        } else if (error.message.includes("Maximum quantity")) {
          toast.error("Maximum 10 items per product");
        } else if (error.message.includes("at least 1")) {
          toast.error("Quantity must be at least 1");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to update cart");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const clearCart = async () => {
    try {
      setIsLoading(true);
      await clearCartMutation({});
      toast.success("Cart cleared");
    } catch (error) {
      console.error("Failed to clear cart:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to manage cart");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to clear cart");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return {
    cartItems,
    cartCount,
    cartTotal,
    isLoading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
  };
}

/**
 * Hook for checking if a product is in cart
 */
export function useIsInCart(productId: Id<"products">) {
  const isInCart = useQuery(api.cart.isInCart, { productId }) ?? false;
  return isInCart;
}

/**
 * Hook for individual cart actions (lighter weight)
 */
export function useCartActions() {
  const [isLoading, setIsLoading] = useState(false);
  
  const addToCartMutation = useMutation(api.cart.addToCart);
  const removeFromCartMutation = useMutation(api.cart.removeFromCart);

  const addToCart = async (productId: Id<"products">, quantity: number = 1) => {
    try {
      setIsLoading(true);
      await addToCartMutation({ productId, quantity });
      toast.success("Added to cart!");
      return true;
    } catch (error) {
      console.error("Failed to add to cart:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to add items to cart");
        } else if (error.message.includes("Cannot add your own product")) {
          toast.error("You cannot add your own products to cart");
        } else if (error.message.includes("not available")) {
          toast.error("This product is no longer available");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to add to cart");
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const removeFromCart = async (productId: Id<"products">) => {
    try {
      setIsLoading(true);
      await removeFromCartMutation({ productId });
      toast.success("Removed from cart");
      return true;
    } catch (error) {
      console.error("Failed to remove from cart:", error);
      if (error instanceof Error) {
        if (error.message.includes("Authentication required")) {
          toast.error("Please sign in to manage cart");
        } else if (error.message.includes("not in cart")) {
          toast.info("Item not in cart");
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to remove from cart");
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    addToCart,
    removeFromCart,
  };
}
