"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";

export function useMessages() {
  const conversations = useQuery(api.messages.getConversations);
  const unreadCount = useQuery(api.messages.getUnreadCount);
  
  const sendMessage = useMutation(api.messages.sendMessage);
  const markAsRead = useMutation(api.messages.markMessagesAsRead);

  return {
    conversations,
    unreadCount,
    sendMessage,
    markAsRead,
  };
}

export function useConversation(otherUserId?: string, productId?: string) {
  const messages = otherUserId 
    ? useQuery(api.messages.getMessages, {
        recipientId: otherUserId as Id<"users">,
        productId: productId as Id<"products"> | undefined,
      })
    : undefined;

  const conversation = otherUserId
    ? useQuery(api.messages.getConversation, {
        otherUserId: otherUserId as Id<"users">,
        productId: productId as Id<"products"> | undefined,
      })
    : undefined;

  return {
    messages,
    conversation,
  };
}

export function useSearchMessages(searchQuery: string) {
  const searchResults = useQuery(api.messages.searchMessages, {
    searchQuery,
  });

  return {
    searchResults,
  };
}
