"use client";

import { <PERSON><PERSON> } from "@repo/ui/src/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/src/components/card";
import { Shield, Home, LogIn } from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useBetterAuth";

export default function UnauthorizedPage() {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <Shield className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Access Denied
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400">
            You don't have permission to access this area
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            {user ? (
              <>
                You are logged in as <strong>{user.email}</strong> but your account
                doesn't have the required permissions to access this page.
              </>
            ) : (
              "Please log in with an authorized account to access this area."
            )}
          </div>
          
          <div className="space-y-2">
            <Button
              onClick={() => router.push("/")}
              className="w-full"
              variant="outline"
            >
              <Home className="mr-2 h-4 w-4" />
              Go to Homepage
            </Button>
            
            {!user && (
              <Button
                onClick={() => router.push("/login")}
                className="w-full"
              >
                <LogIn className="mr-2 h-4 w-4" />
                Log In
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
