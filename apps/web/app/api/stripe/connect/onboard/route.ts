import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@repo/backend/convex/_generated/api';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, email, country = 'US' } = body;

    if (!userId || !email) {
      return NextResponse.json(
        { error: 'User ID and email are required' },
        { status: 400 }
      );
    }

    // Create Stripe Connect account
    const result = await convex.action(api.stripeConnect.createConnectAccount, {
      userId,
      email,
      country,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating Connect account:', error);
    return NextResponse.json(
      { error: 'Failed to create Connect account' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Generate onboarding link
    const result = await convex.action(api.stripeConnect.generateOnboardingLink, {
      userId,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error generating onboarding link:', error);
    return NextResponse.json(
      { error: 'Failed to generate onboarding link' },
      { status: 500 }
    );
  }
}
