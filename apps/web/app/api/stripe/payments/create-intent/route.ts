import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@repo/backend/convex/_generated/api';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { productId, amount, currency = 'usd', metadata = {} } = body;

    if (!productId || !amount) {
      return NextResponse.json(
        { error: 'Product ID and amount are required' },
        { status: 400 }
      );
    }

    // Get product and seller information from Convex
    const product = await convex.query(api.productQueries.getProductById, {
      productId,
    });

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Get seller's Stripe Connect account
    const sellerProfile = await convex.query(api.stripeConnect.getConnectStatus, {
      userId: product.sellerId,
    });

    if (!sellerProfile?.hasStripeAccount || !sellerProfile.chargesEnabled) {
      return NextResponse.json(
        { error: 'Seller payment account not configured' },
        { status: 400 }
      );
    }

    // Create payment intent with transfer to seller
    const result = await convex.action(api.stripePayments.createPaymentIntent, {
      amount,
      currency,
      connectedAccountId: sellerProfile.accountId!,
      metadata: {
        productId,
        sellerId: product.sellerId,
        ...metadata,
      },
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
}
