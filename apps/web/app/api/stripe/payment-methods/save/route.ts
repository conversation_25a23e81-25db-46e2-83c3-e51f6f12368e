import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@repo/backend/convex/_generated/api';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
});

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { paymentMethodId } = body;

    if (!paymentMethodId) {
      return NextResponse.json(
        { error: 'Payment method ID is required' },
        { status: 400 }
      );
    }

    // Get or create Stripe customer
    const customerResult = await convex.action(api.stripeCustomers.getOrCreateCustomer);
    
    if (!customerResult.success || !customerResult.customerId) {
      return NextResponse.json(
        { error: 'Failed to get customer information' },
        { status: 400 }
      );
    }

    // Attach payment method to customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerResult.customerId,
    });

    // If this is the first payment method, make it default
    const existingMethods = await stripe.paymentMethods.list({
      customer: customerResult.customerId,
      type: 'card',
    });

    if (existingMethods.data.length === 1) {
      await stripe.customers.update(customerResult.customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Payment method saved successfully',
    });
  } catch (error) {
    console.error('Error saving payment method:', error);
    return NextResponse.json(
      { error: 'Failed to save payment method' },
      { status: 500 }
    );
  }
}
