"use client";

import { Badge } from "@repo/ui/components/badge";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Separator } from "@repo/ui/components/separator";
import { useMutation, useQuery, useAction } from "convex/react";
import { Download, Mail, Eye, DollarSign, ArrowLeft, Calendar, MapPin, Phone, Mail as MailIcon, Building2, Loader2 } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import Link from "next/link";

export default function InvoiceDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const [isSending, setIsSending] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [invoiceId, setInvoiceId] = useState<Id<"invoices"> | null>(null);
  
  React.useEffect(() => {
    params.then(({ id }) => {
      setInvoiceId(id as Id<"invoices">);
    });
  }, [params]);
  
  const invoice = useQuery(api.invoices.getInvoice, invoiceId ? { invoiceId } : "skip");
  const sendInvoiceEmail = useAction(api.invoices.sendInvoiceEmail);
  const generatePDF = useAction(api.invoices.generateInvoicePDF);
  const updateStatus = useMutation(api.invoices.updateInvoiceStatus);

  const handleSendEmail = async () => {
    if (!invoiceId) return;
    setIsSending(true);
    try {
      await sendInvoiceEmail({ invoiceId });
      toast.success("Invoice sent successfully!");
    } catch (error) {
      toast.error("Failed to send invoice");
      console.error(error);
    } finally {
      setIsSending(false);
    }
  };

  const handleGeneratePDF = async () => {
    if (!invoiceId) return;
    setIsGeneratingPDF(true);
    try {
      const storageId = await generatePDF({ invoiceId });
      // Download the PDF
      const url = `/api/storage/${storageId}`;
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoice-${invoice?.invoiceNumber}.pdf`;
      link.click();
      toast.success("PDF generated and downloaded!");
    } catch (error) {
      toast.error("Failed to generate PDF");
      console.error(error);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleMarkAsPaid = async () => {
    if (!invoiceId) return;
    try {
      await updateStatus({ invoiceId, status: "paid" });
      toast.success("Invoice marked as paid!");
    } catch (error) {
      toast.error("Failed to update invoice status");
      console.error(error);
    }
  };

  if (!invoice) {
    return (
      <div className="min-h-screen bg-background">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading invoice...</p>
          </div>
        </div>
      </div>
    );
  }

  const isOverdue = invoice.status !== "paid" && invoice.dueDate && new Date(invoice.dueDate) < new Date();
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-background">
      {/* Header Bar - Full Width */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/seller/invoices">
                <Button variant="ghost" className="rounded-xl font-light">
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-light text-primary tracking-wide">
                  INVOICE #{invoice.invoiceNumber}
                </h1>
                <p className="text-sm text-muted-foreground font-light">
                  Created {formatDate(invoice.updatedAt)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Badge
                variant={
                  invoice.status === "paid" ? "default" :
                  isOverdue ? "destructive" :
                  invoice.status === "sent" ? "secondary" : "outline"
                }
                className="px-3 py-1 text-sm font-light rounded-xl"
              >
                {isOverdue ? "Overdue" : invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
              </Badge>
              
              <Button
                variant="outline"
                onClick={handleGeneratePDF}
                disabled={isGeneratingPDF}
                className="rounded-xl font-light"
              >
                {isGeneratingPDF ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Download className="w-4 h-4 mr-2" />
                )}
                {isGeneratingPDF ? "Generating..." : "Download PDF"}
              </Button>
              
              {invoice.status !== "paid" && (
                <>
                  <Button
                    variant="outline"
                    onClick={handleSendEmail}
                    disabled={isSending}
                    className="rounded-xl font-light"
                  >
                    {isSending ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Mail className="w-4 h-4 mr-2" />
                    )}
                    {isSending ? "Sending..." : "Send Email"}
                  </Button>
                  
                  <Button
                    onClick={handleMarkAsPaid}
                    className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light px-6"
                  >
                    <DollarSign className="w-4 h-4 mr-2" />
                    Mark as Paid
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Invoice Card */}
        <Card className="rounded-xl border-border shadow-lg">
          <CardContent className="p-8">
            {/* Invoice Header */}
            <div className="flex justify-between items-start mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                  <Building2 className="w-6 h-6 text-primary-foreground" />
                </div>
                <div>
                  <h2 className="text-2xl font-light text-primary tracking-wide">HauteVault</h2>
                  <p className="text-sm text-muted-foreground font-light">Luxury Marketplace</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="inline-flex items-center px-4 py-2 bg-accent text-accent-foreground rounded-full text-sm font-light">
                  INVOICE
                </div>
                <h3 className="text-3xl font-light text-primary mt-2">#{invoice.invoiceNumber}</h3>
                <p className="text-muted-foreground text-sm mt-1 font-light">Invoice Date: {formatDate(invoice.updatedAt)}</p>
              </div>
            </div>

            <Separator className="my-8" />

            {/* Billing Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-8">
              {/* From */}
              <div>
                <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">From</h4>
                <div className="space-y-2">
                  <p className="font-light text-primary">HauteVault Seller</p>
                  <div className="flex items-center text-muted-foreground text-sm">
                    <MailIcon className="w-4 h-4 mr-2" />
                    <span className="font-light"><EMAIL></span>
                  </div>
                  <div className="flex items-center text-muted-foreground text-sm">
                    <Phone className="w-4 h-4 mr-2" />
                    <span className="font-light">+****************</span>
                  </div>
                  <div className="flex items-start text-muted-foreground text-sm">
                    <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                    <div>
                      <p className="font-light">123 Luxury Lane</p>
                      <p className="font-light">Beverly Hills, CA 90210</p>
                      <p className="font-light">United States</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bill To */}
              <div>
                <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">Bill To</h4>
                <div className="space-y-2">
                  <p className="font-light text-primary">{invoice.clientName}</p>
                  <div className="flex items-center text-muted-foreground text-sm">
                    <MailIcon className="w-4 h-4 mr-2" />
                    <span className="font-light">{invoice.clientEmail}</span>
                  </div>
                  {invoice.clientPhone && (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Phone className="w-4 h-4 mr-2" />
                      <span className="font-light">{invoice.clientPhone}</span>
                    </div>
                  )}
                  <div className="flex items-start text-muted-foreground text-sm">
                    <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                    <div>
                      <p className="font-light">{invoice.clientAddress.street}</p>
                      <p className="font-light">{invoice.clientAddress.city}, {invoice.clientAddress.state} {invoice.clientAddress.zipCode}</p>
                      <p className="font-light">{invoice.clientAddress.country}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Invoice Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 p-4 bg-primary/5 rounded-xl">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Invoice Date</p>
                  <p className="font-light text-primary">{formatDate(invoice.updatedAt)}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Due Date</p>
                  <p className={`font-light ${isOverdue ? 'text-destructive' : 'text-primary'}`}>
                    {invoice.dueDate ? formatDate(invoice.dueDate) : 'Not set'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Payment Terms</p>
                  <p className="font-light text-primary">{invoice.paymentTerms || 'Net 30'}</p>
                </div>
              </div>
            </div>

            {/* Items Table */}
            <div className="mb-8">
              <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">Item Details</h4>
              <div className="border border-border rounded-xl overflow-hidden">
                <div className="bg-primary/5 px-6 py-4 grid grid-cols-12 gap-4 text-sm font-light text-muted-foreground">
                  <div className="col-span-6">Description</div>
                  <div className="col-span-2 text-center">Quantity</div>
                  <div className="col-span-2 text-center">Unit Price</div>
                  <div className="col-span-2 text-right">Amount</div>
                </div>
                
                <div className="px-6 py-4 grid grid-cols-12 gap-4 text-sm border-t border-border">
                  <div className="col-span-6">
                    <p className="font-light text-primary">{invoice.itemDescription}</p>
                    <p className="text-muted-foreground text-sm mt-1 font-light">Product/Service</p>
                  </div>
                  <div className="col-span-2 text-center text-primary font-light">1</div>
                  <div className="col-span-2 text-center text-primary font-light">{formatCurrency(invoice.salePrice)}</div>
                  <div className="col-span-2 text-right font-light text-primary">{formatCurrency(invoice.salePrice)}</div>
                </div>
              </div>
            </div>

            {/* Totals */}
            <div className="flex justify-end">
              <div className="w-80 space-y-3">
                <div className="flex justify-between text-sm text-muted-foreground font-light">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(invoice.salePrice)}</span>
                </div>
                
                {invoice.tax && invoice.tax > 0 && (
                  <div className="flex justify-between text-sm text-muted-foreground font-light">
                    <span>Tax:</span>
                    <span>{formatCurrency(invoice.tax)}</span>
                  </div>
                )}
                
                <Separator />
                
                <div className="flex justify-between text-lg font-light text-primary">
                  <span>Total:</span>
                  <span>{formatCurrency(invoice.totalAmount)}</span>
                </div>
                
                {invoice.status === "paid" && (
                  <div className="flex justify-between text-sm text-green-600 font-light">
                    <span>Paid on:</span>
                    <span>{invoice.paidDate ? formatDate(invoice.paidDate) : 'N/A'}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            {invoice.notes && (
              <>
                <Separator className="my-8" />
                <div>
                  <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-3">Notes</h4>
                  <div className="p-4 bg-primary/5 rounded-xl">
                    <p className="text-muted-foreground font-light">{invoice.notes}</p>
                  </div>
                </div>
              </>
            )}

            {/* Footer */}
            <div className="mt-12 pt-8 border-t border-border">
              <div className="text-center text-muted-foreground text-sm font-light">
                <p>Thank you for your business!</p>
                <p className="mt-1">Please make payment by the due date to avoid any late fees.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}