"use client";

import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { SupplierForm } from "@/components/seller/suppliers/SupplierForm";
import { SupplierFormData } from "@/components/seller/suppliers/types";

export default function NewSupplierFormClient() {
  const router = useRouter();

  const handleSave = useCallback(async (data: SupplierFormData) => {
    console.log("Saving supplier:", data);
    // TODO: Save supplier to database
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log("Supplier saved successfully");
  }, []);

  const handleCancel = useCallback(() => {
    router.back();
  }, [router]);

  return (
    <SupplierForm
      onSave={handleSave}
      onCancel={handleCancel}
      isEditing={false}
    />
  );
}
