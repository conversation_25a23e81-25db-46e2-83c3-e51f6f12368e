import { Metadata } from "next";
import NewSupplierFormClient from "./NewSupplierFormClient";

export const metadata: Metadata = {
  title: "Add New Supplier | HauteVault",
  description: "Add a new supplier to your network on HauteVault's premium marketplace. Manage relationships and track financial transactions.",
  openGraph: {
    title: "Add New Supplier | HauteVault",
    description: "Add a new supplier to your network on HauteVault's premium marketplace. Manage relationships and track financial transactions.",
    type: "website",
  },
};

export default function AddSupplierPage() {
  return <NewSupplierFormClient />;
}
