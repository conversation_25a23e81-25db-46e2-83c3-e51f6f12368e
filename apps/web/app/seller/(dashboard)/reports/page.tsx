"use client";

import { useState } from "react";
import { Suspense } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@repo/ui/components/tabs";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Package, 
  Users, 
  Download,
  Calendar,
  Filter
} from "lucide-react";

// Import components we'll create
import { SalesOverview } from "@/components/seller/reports/SalesOverview";
import { TimeBasedReports } from "@/components/seller/reports/TimeBasedReports";
import { TopPerformers } from "@/components/seller/reports/TopPerformers";
import { KeyMetrics } from "@/components/seller/reports/KeyMetrics";
import { ExportTools } from "@/components/seller/reports/ExportTools";

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    to: new Date(),
  });

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100">
            Sales Reports
          </h1>
          <p className="text-neutral-600 dark:text-neutral-400 mt-2">
            Comprehensive analytics and insights for your sales performance
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Date Range
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Quick Stats Cards */}
      <Suspense fallback={<div>Loading metrics...</div>}>
        <KeyMetrics />
      </Suspense>

      {/* Main Reports Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="time-based" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Time Analysis
          </TabsTrigger>
          <TabsTrigger value="performers" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Top Performers
          </TabsTrigger>
          <TabsTrigger value="export" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export Data
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Suspense fallback={<div>Loading overview...</div>}>
            <SalesOverview dateRange={dateRange} />
          </Suspense>
        </TabsContent>

        <TabsContent value="time-based" className="space-y-6">
          <Suspense fallback={<div>Loading time-based reports...</div>}>
            <TimeBasedReports dateRange={dateRange} />
          </Suspense>
        </TabsContent>

        <TabsContent value="performers" className="space-y-6">
          <Suspense fallback={<div>Loading top performers...</div>}>
            <TopPerformers dateRange={dateRange} />
          </Suspense>
        </TabsContent>

        <TabsContent value="export" className="space-y-6">
          <Suspense fallback={<div>Loading export tools...</div>}>
            <ExportTools dateRange={dateRange} />
          </Suspense>
        </TabsContent>
      </Tabs>

      {/* Additional Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Report Period</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dateRange.from && dateRange.to ? 
                `${Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))} days` :
                "Custom Range"
              }
            </div>
            <p className="text-xs text-muted-foreground">
              {dateRange.from?.toLocaleDateString()} - {dateRange.to?.toLocaleDateString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Freshness</CardTitle>
            <Badge variant="secondary" className="text-xs">
              Real-time
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Live</div>
            <p className="text-xs text-muted-foreground">
              Last updated: {new Date().toLocaleTimeString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Export Options</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">CSV, PDF</div>
            <p className="text-xs text-muted-foreground">
              Download detailed reports
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
