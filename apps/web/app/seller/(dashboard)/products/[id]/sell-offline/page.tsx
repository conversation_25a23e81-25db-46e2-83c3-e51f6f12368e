"use client";

import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Textarea } from "@repo/ui/components/textarea";
import { useMutation, useQuery } from "convex/react";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";

const offlineSaleSchema = z.object({
  clientName: z.string().min(1, "Client name is required"),
  clientEmail: z.string().email("Valid email is required"),
  clientPhone: z.string().optional(),
  clientAddress: z.object({
    street: z.string().min(1, "Street address is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    zipCode: z.string().min(1, "ZIP code is required"),
    country: z.string().min(1, "Country is required"),
  }),
  salePrice: z.number().min(0.01, "Sale price must be greater than 0"),
  paymentMethod: z.enum(["cash", "check", "bank_transfer", "credit_card", "paypal", "other"]),
  notes: z.string().optional(),
});

type OfflineSaleForm = z.infer<typeof offlineSaleSchema>;

export default function SellOfflinePage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [productId, setProductId] = useState<Id<"products"> | null>(null);
  
  React.useEffect(() => {
    params.then(({ id }) => {
      setProductId(id as Id<"products">);
    });
  }, [params]);
  const product = useQuery(api.products.getProductById, productId ? { productId } : "skip");
  const createOfflineSale = useMutation(api.offlineSales.createOfflineSale);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<OfflineSaleForm>({
    resolver: zodResolver(offlineSaleSchema),
    defaultValues: {
      salePrice: product?.price || 0,
      clientAddress: {
        country: "United States",
      },
    },
  });

  const paymentMethod = watch("paymentMethod");

  if (!productId || !product) {
    return <div>Loading...</div>;
  }

  const onSubmit = async (data: OfflineSaleForm) => {
    if (!productId) {
      toast.error("Product ID not available");
      return;
    }
    setIsSubmitting(true);
    try {
      const result = await createOfflineSale({
        productId,
        ...data,
      });

      toast.success("Invoice created successfully! Product is now on hold.");
      router.push(`/seller/sales/${result.offlineSaleId}`);
    } catch (error) {
      toast.error("Failed to create invoice");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!product) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Create Offline Invoice</h1>
          <p className="text-muted-foreground">
            Generate an invoice for offline sale and put this item on hold in the marketplace
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Product Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {product.primaryImageId && (
                  <img
                    src={`/api/storage/${product.primaryImageId}`}
                    alt={product.title}
                    className="w-full h-48 object-cover rounded-lg"
                  />
                )}
                <div>
                  <h3 className="font-semibold text-lg">{product.title}</h3>
                  <p className="text-sm text-muted-foreground">{product.brand}</p>
                  <p className="text-lg font-bold text-green-600">
                    ${product.price.toFixed(2)}
                  </p>
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-sm text-yellow-800">
                    <strong>Note:</strong> This action will put the product on hold in the marketplace.
                    It will only be removed once payment is received and marked as paid.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sale Form */}
          <Card>
            <CardHeader>
              <CardTitle>Client & Invoice Information</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Client Information */}
                <div className="space-y-4">
                  <h4 className="font-semibold">Client Information</h4>
                  
                  <div>
                    <Label htmlFor="clientName">Client Name *</Label>
                    <Input
                      id="clientName"
                      {...register("clientName")}
                      placeholder="John Doe"
                    />
                    {errors.clientName && (
                      <p className="text-sm text-red-600">{errors.clientName.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="clientEmail">Client Email *</Label>
                    <Input
                      id="clientEmail"
                      type="email"
                      {...register("clientEmail")}
                      placeholder="<EMAIL>"
                    />
                    {errors.clientEmail && (
                      <p className="text-sm text-red-600">{errors.clientEmail.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="clientPhone">Client Phone</Label>
                    <Input
                      id="clientPhone"
                      {...register("clientPhone")}
                      placeholder="+****************"
                    />
                  </div>
                </div>

                {/* Client Address */}
                <div className="space-y-4">
                  <h4 className="font-semibold">Client Address</h4>
                  
                  <div>
                    <Label htmlFor="street">Street Address *</Label>
                    <Input
                      id="street"
                      {...register("clientAddress.street")}
                      placeholder="123 Main St"
                    />
                    {errors.clientAddress?.street && (
                      <p className="text-sm text-red-600">{errors.clientAddress.street.message}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="city">City *</Label>
                      <Input
                        id="city"
                        {...register("clientAddress.city")}
                        placeholder="New York"
                      />
                      {errors.clientAddress?.city && (
                        <p className="text-sm text-red-600">{errors.clientAddress.city.message}</p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="state">State *</Label>
                      <Input
                        id="state"
                        {...register("clientAddress.state")}
                        placeholder="NY"
                      />
                      {errors.clientAddress?.state && (
                        <p className="text-sm text-red-600">{errors.clientAddress.state.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="zipCode">ZIP Code *</Label>
                      <Input
                        id="zipCode"
                        {...register("clientAddress.zipCode")}
                        placeholder="10001"
                      />
                      {errors.clientAddress?.zipCode && (
                        <p className="text-sm text-red-600">{errors.clientAddress.zipCode.message}</p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="country">Country *</Label>
                      <Input
                        id="country"
                        {...register("clientAddress.country")}
                        placeholder="United States"
                      />
                      {errors.clientAddress?.country && (
                        <p className="text-sm text-red-600">{errors.clientAddress.country.message}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Sale Details */}
                <div className="space-y-4">
                  <h4 className="font-semibold">Invoice Details</h4>
                  
                  <div>
                    <Label htmlFor="salePrice">Invoice Amount *</Label>
                    <Input
                      id="salePrice"
                      type="number"
                      step="0.01"
                      {...register("salePrice", { valueAsNumber: true })}
                      placeholder="0.00"
                    />
                    {errors.salePrice && (
                      <p className="text-sm text-red-600">{errors.salePrice.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="paymentMethod">Payment Method *</Label>
                    <Select onValueChange={(value) => setValue("paymentMethod", value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="check">Check</SelectItem>
                        <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                        <SelectItem value="credit_card">Credit Card</SelectItem>
                        <SelectItem value="paypal">PayPal</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.paymentMethod && (
                      <p className="text-sm text-red-600">{errors.paymentMethod.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      {...register("notes")}
                      placeholder="Additional notes for the invoice..."
                      rows={3}
                    />
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.back()}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1"
                  >
                    {isSubmitting ? "Creating Invoice..." : "Generate Invoice & Put on Hold"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}