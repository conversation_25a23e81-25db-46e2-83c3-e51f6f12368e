"use client";

import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { ProductForm, ProductFormData } from "@/components/seller/product/ProductForm";

export default function NewProductFormClient() {
  const router = useRouter();

  const handleSave = useCallback(async (data: ProductFormData) => {
    console.log("Saving product:", data);
    // TODO: Save product to database
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log("Product saved successfully");
  }, []);

  const handleCancel = useCallback(() => {
    router.back();
  }, [router]);

  return (
    <ProductForm
      onSave={handleSave}
      onCancel={handleCancel}
      isEditing={false}
    />
  );
}




