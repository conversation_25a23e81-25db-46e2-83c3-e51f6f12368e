"use client";

import { Badge } from "@repo/ui/components/badge";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Input } from "@repo/ui/components/input";
import { Tabs, TabsContent, <PERSON>bsList, TabsTrigger } from "@repo/ui/components/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { useQuery, useMutation } from "convex/react";
import { toast } from "sonner";
import { 
  Search, 
  Plus, 
  FileText, 
  DollarSign, 
  Package, 
  TrendingUp, 
  Users, 
  Clock, 
  Target,
  Download,
  Mail,
  BarChart3,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Filter,
  Eye,
  MapPin,
  CreditCard,
  Percent,
  Star,
  Info
} from "lucide-react";
import Link from "next/link";
import { useState, useMemo } from "react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { SalesChart } from "@/components/seller/dashboard/SalesChart";
import { SalesExportPopover } from "@/components/seller/sales/SalesExportPopover";
import React from "react";
import { useRouter } from "next/navigation";

export default function SalesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("overview");
  const [timeRange, setTimeRange] = useState("30d");
  const [statusFilter, setStatusFilter] = useState("all");
  const [markingAsPaid, setMarkingAsPaid] = useState<string | null>(null);

  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // Backend queries
  const sellerMetrics = useQuery(api.sellerQueries.getSellerMetrics, { 
    timeRange: timeRange as "7d" | "30d" | "90d" | "1y"
  });
  const sellerOrders = useQuery(api.orderQueries.getSellerOrders, {
    status: statusFilter !== "all" ? statusFilter as any : undefined,
    limit: 20,
    includeRevenue: true
  });
  const offlineSales = useQuery(api.offlineSales.getOfflineSales);
  const markOfflineSaleAsPaid = useMutation(api.offlineSales.markOfflineSaleAsPaid);

  // Set default tab to offline sales if there are pending payments
  React.useEffect(() => {
    if (offlineSales && offlineSales.some(sale => sale.status === "pending_payment")) {
      setActiveTab("offline");
    }
  }, [offlineSales]);

  // Handle marking offline sale as paid
  const handleMarkAsPaid = async (saleId: string) => {
    if (markingAsPaid) return; // Prevent multiple clicks
    
    setMarkingAsPaid(saleId);
    try {
      await markOfflineSaleAsPaid({ saleId: saleId as any });
      toast.success("Sale marked as paid! Product has been removed from marketplace.");
    } catch (error) {
      toast.error("Failed to mark sale as paid");
      console.error(error);
    } finally {
      setMarkingAsPaid(null);
    }
  };

  // Handle downloading invoice PDF
  const handleDownloadInvoice = async (saleId: string) => {
    try {
      // Redirect to the invoices page where they can find and download the invoice
      router.push('/seller/invoices');
      toast.info("Redirecting to invoices page to download your invoice");
    } catch (error) {
      toast.error("Failed to download invoice");
      console.error(error);
    }
  };

  // Handle downloading invoice PDF directly
  const handleDownloadInvoiceDirect = async (invoiceId: string) => {
    try {
      // For now, we'll redirect to the invoice detail page where PDF download is available
      window.open(`/seller/invoices/${invoiceId}`, '_blank');
    } catch (error) {
      toast.error("Failed to download invoice");
      console.error(error);
    }
  };

  // Data processing
  const filteredSales = offlineSales?.filter(sale =>
    sale.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    sale.product?.title.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const filteredOrders = sellerOrders?.orders?.filter(order =>
    searchTerm === "" || 
    order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.product?.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.buyer?.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Calculate metrics
  const totalSalesValue = offlineSales?.reduce((sum, sale) => sum + sale.salePrice, 0) || 0;
  const totalOnlineSales = sellerOrders?.summary?.revenue?.totalRevenue || 0;
  const totalOrders = sellerOrders?.summary?.total || 0;
  const pendingOrders = filteredOrders.filter(o => ["pending", "confirmed"].includes(o.orderStatus)).length;
  const shippedOrders = filteredOrders.filter(o => o.orderStatus === "shipped").length;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case "delivered": return "default";
      case "shipped": return "secondary";
      case "confirmed": return "outline";
      case "pending": return "destructive";
      case "cancelled": return "destructive";
      default: return "outline";
    }
  };

  const topSellingProducts = useMemo(() => {
    if (!sellerOrders?.orders) return [];
    
    const productSales = new Map();
    sellerOrders.orders
      .filter(order => order.orderStatus === "delivered")
      .forEach(order => {
        const key = order.product?.title || "Unknown Product";
        const existing = productSales.get(key) || { title: key, sales: 0, revenue: 0 };
        productSales.set(key, {
          title: key,
          sales: existing.sales + 1,
          revenue: existing.revenue + (order.sellerEarnings || 0)
        });
      });
    
    return Array.from(productSales.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
  }, [sellerOrders]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Sales Dashboard</h1>
          <p className="text-muted-foreground font-light">
            Track revenue, manage orders, and analyze your sales performance
          </p>
        </div>
        
        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2">
          <SalesExportPopover 
            data={{
              orders: filteredOrders,
              offlineSales: filteredSales,
              invoices: [],
              metrics: {
                totalRevenue: totalOnlineSales + totalSalesValue,
                totalOrders: totalOrders + (offlineSales?.length || 0),
                conversionRate: sellerMetrics?.performance?.conversionRate || 0,
                averageOrderValue: sellerMetrics?.performance?.averageOrderValue || 0
              }
            }}
            timeRange={timeRange}
          >
            <Button size="sm" variant="outline" className="font-light">
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
          </SalesExportPopover>
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40 rounded-xl">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32 rounded-xl">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="confirmed">Confirmed</SelectItem>
              <SelectItem value="shipped">Shipped</SelectItem>
              <SelectItem value="delivered">Delivered</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search orders, products, customers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rounded-xl"
          />
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-primary/5 rounded-xl border border-border">
          <TabsTrigger value="overview" className="rounded-xl font-light">Overview</TabsTrigger>
          <TabsTrigger value="orders" className="rounded-xl font-light">Orders</TabsTrigger>
          <TabsTrigger value="offline" className="rounded-xl font-light">Offline Sales</TabsTrigger>
          <TabsTrigger value="analytics" className="rounded-xl font-light">Analytics</TabsTrigger>
        </TabsList>

        {/* Sales Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Revenue */}
            <Card className="bg-primary text-primary-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-light text-primary-foreground/80 uppercase tracking-wide">
                    <DollarSign className="w-4 h-4 mr-2 inline" />
                    Total Revenue
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-primary-foreground">
                  {formatCurrency(totalOnlineSales + totalSalesValue)}
                </div>
                <p className="text-xs text-primary-foreground/80 font-light">
                  Online: {formatCurrency(totalOnlineSales)} • Offline: {formatCurrency(totalSalesValue)}
                </p>
              </CardContent>
            </Card>

            {/* Total Orders */}
            <Card className="bg-accent text-accent-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-light text-accent-foreground/80 uppercase tracking-wide">
                  <Package className="w-4 h-4 mr-2 inline" />
                  Total Orders
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-accent-foreground">
                  {totalOrders + (offlineSales?.length || 0)}
                </div>
                <p className="text-xs text-accent-foreground/80 font-light">
                  Online: {totalOrders} • Offline: {offlineSales?.length || 0}
                </p>
              </CardContent>
            </Card>

            {/* Conversion Rate */}
            <Card className="bg-secondary text-secondary-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-light text-secondary-foreground/80 uppercase tracking-wide">
                  <Percent className="w-4 h-4 mr-2 inline" />
                  Conversion Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-secondary-foreground">
                  {sellerMetrics?.performance?.conversionRate?.toFixed(1) || "0.0"}%
                </div>
                <p className="text-xs text-secondary-foreground/80 font-light">
                  Views to purchases
                </p>
              </CardContent>
            </Card>

            {/* Average Order Value */}
            <Card className="bg-muted text-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-light text-foreground/70 uppercase tracking-wide">
                  <Target className="w-4 h-4 mr-2 inline" />
                  Avg Order Value
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-foreground">
                  {formatCurrency(sellerMetrics?.performance?.averageOrderValue || 0)}
                </div>
                <p className="text-xs text-foreground/70 font-light">
                  Per transaction
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Sales Chart */}
          <SalesChart />

          {/* Key Metrics Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Performing Products */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <Star className="w-5 h-5" />
                  Top Performing Products
                </CardTitle>
              </CardHeader>
              <CardContent>
                {topSellingProducts.length === 0 ? (
                  <div className="text-center py-8">
                    <Package className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                    <p className="text-muted-foreground font-light">No sales data yet</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {topSellingProducts.map((product, index) => (
                      <div key={product.title} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <span className="text-sm font-light text-primary">#{index + 1}</span>
                          </div>
                          <div>
                            <p className="font-light text-foreground">{product.title}</p>
                            <p className="text-xs text-muted-foreground font-light">{product.sales} sales</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-light text-foreground">{formatCurrency(product.revenue)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredOrders.slice(0, 5).map((order) => (
                    <div key={order._id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${
                          order.orderStatus === "delivered" ? "bg-green-500" :
                          order.orderStatus === "shipped" ? "bg-blue-500" :
                          order.orderStatus === "confirmed" ? "bg-yellow-500" :
                          "bg-gray-500"
                        }`} />
                        <div>
                          <p className="font-light text-foreground">{order.orderNumber}</p>
                          <p className="text-xs text-muted-foreground font-light">
                            {order.buyer?.name} • {formatCurrency(order.sellerEarnings || 0)}
                          </p>
                        </div>
                      </div>
                      <Badge variant={getOrderStatusColor(order.orderStatus)} className="font-light text-xs">
                        {order.orderStatus}
                      </Badge>
                    </div>
                  ))}
                  {filteredOrders.length === 0 && (
                    <div className="text-center py-8">
                      <Clock className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                      <p className="text-muted-foreground font-light">No recent orders</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Orders Management Tab */}
        <TabsContent value="orders" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">{pendingOrders}</div>
                <p className="text-sm text-muted-foreground font-light">Pending Orders</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">{shippedOrders}</div>
                <p className="text-sm text-muted-foreground font-light">Shipped Orders</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">
                  {filteredOrders.filter(o => o.orderStatus === "delivered").length}
                </div>
                <p className="text-sm text-muted-foreground font-light">Completed</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">
                  {formatCurrency(filteredOrders.reduce((sum, o) => sum + (o.sellerEarnings || 0), 0))}
                </div>
                <p className="text-sm text-muted-foreground font-light">Total Earnings</p>
              </CardContent>
            </Card>
          </div>

          {/* Orders List */}
          <Card className="rounded-xl border border-border bg-card shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground">Recent Orders</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredOrders.length === 0 ? (
                <div className="text-center py-12">
                  <Package className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                  <h3 className="text-lg font-light mb-2">No orders found</h3>
                  <p className="text-muted-foreground font-light">
                    Orders will appear here when customers make purchases
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredOrders.map((order) => (
                    <Card key={order._id} className="border border-border rounded-xl">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-4 mb-4">
                              <div>
                                <h3 className="font-light text-lg text-foreground">
                                  Order #{order.orderNumber}
                                </h3>
                                <p className="text-muted-foreground font-light">
                                  {order.product?.title} • {order.buyer?.name}
                                </p>
                              </div>
                              <Badge variant={getOrderStatusColor(order.orderStatus)} className="font-light">
                                {order.orderStatus}
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-muted-foreground font-light">Total Amount</p>
                                <p className="font-light text-foreground">{formatCurrency(order.totalAmount)}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Your Earnings</p>
                                <p className="font-light text-foreground">{formatCurrency(order.sellerEarnings || 0)}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Order Date</p>
                                <p className="font-light text-foreground">
                                  {new Date(order.orderDate).toLocaleDateString()}
                                </p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Payment Method</p>
                                <p className="font-light text-foreground">{order.paymentMethod}</p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" asChild className="font-light">
                              <Link href={`/seller/orders/${order._id}`}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </Link>
                            </Button>
                            {order.orderStatus === "confirmed" && (
                              <Button size="sm" className="font-light">
                                Mark Shipped
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Offline Sales Management Tab */}
        <TabsContent value="offline" className="space-y-6">
          {/* Header with Create Button */}
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-light text-foreground">Offline Sales Management</h2>
              <p className="text-muted-foreground font-light">Create invoices and track offline sales</p>
            </div>
            <Button asChild className="font-light">
              <Link href="/seller/products">
                <Plus className="h-4 w-4 mr-2" />
                Create Offline Sale
              </Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">
                  {filteredSales.filter(s => s.status === "pending_payment").length}
                </div>
                <p className="text-sm text-muted-foreground font-light">Pending Payment</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">
                  {filteredSales.filter(s => s.status === "paid").length}
                </div>
                <p className="text-sm text-muted-foreground font-light">Completed</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">
                  {filteredSales.filter(s => s.status === "cancelled").length}
                </div>
                <p className="text-sm text-muted-foreground font-light">Cancelled</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">
                  {formatCurrency(filteredSales.reduce((sum, s) => sum + s.salePrice, 0))}
                </div>
                <p className="text-sm text-muted-foreground font-light">Total Value</p>
              </CardContent>
            </Card>
          </div>

          {/* Workflow Information */}
          <Card className="border border-blue-200 bg-blue-50 rounded-xl">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Info className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-800 mb-2">How Offline Sales Work</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Create an invoice → Product is put on hold in marketplace</li>
                    <li>• Customer receives invoice and makes payment</li>
                    <li>• Mark as paid → Product is removed from marketplace</li>
                    <li>• Download PDF invoices anytime for record keeping</li>
                  </ul>
                  <p className="text-sm text-blue-600 mt-2">
                    💡 <strong>Tip:</strong> View detailed invoice information in the <Button variant="link" className="p-0 h-auto text-blue-600 underline" onClick={() => router.push('/seller/invoices')}>Invoices page</Button>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Offline Sales List */}
          <Card className="rounded-xl border border-border bg-card shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground">Offline Sales & Invoices</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredSales.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                  <h3 className="text-lg font-light mb-2">No offline sales found</h3>
                  <p className="text-muted-foreground font-light mb-4">
                    Offline sales will appear here when you create invoices
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={() => router.push('/seller/invoices')}
                    className="font-light"
                  >
                    View All Invoices
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredSales.map((sale) => (
                    <Card key={sale._id} className="border border-border rounded-xl">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-4 mb-4">
                              <div>
                                <h3 className="font-light text-lg text-foreground">
                                  {sale.clientName}
                                </h3>
                                <p className="text-muted-foreground font-light">
                                  {sale.product?.title} • {sale.product?.brand}
                                </p>
                              </div>
                              <Badge 
                                variant={
                                  sale.status === "paid" ? "default" : 
                                  sale.status === "pending_payment" ? "secondary" : 
                                  "destructive"
                                } 
                                className="font-light"
                              >
                                {sale.status === "pending_payment" ? "Pending Payment" : 
                                 sale.status === "paid" ? "Paid" : "Cancelled"}
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-muted-foreground font-light">Sale Amount</p>
                                <p className="font-light text-foreground">{formatCurrency(sale.salePrice)}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Payment Method</p>
                                <p className="font-light text-foreground">{sale.paymentMethod}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Sale Date</p>
                                <p className="font-light text-foreground">
                                  {new Date(sale.saleDate).toLocaleDateString()}
                                </p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Client Email</p>
                                <p className="font-light text-foreground">{sale.clientEmail}</p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex gap-2">
                            {sale.status === "pending_payment" && (
                              <Button 
                                variant="default" 
                                size="sm" 
                                className="font-light"
                                onClick={() => handleMarkAsPaid(sale._id)}
                                disabled={markingAsPaid === sale._id}
                              >
                                {markingAsPaid === sale._id ? (
                                  <>
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    Processing...
                                  </>
                                ) : (
                                  <>
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Mark as Paid
                                  </>
                                )}
                              </Button>
                            )}
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="font-light"
                              onClick={() => handleDownloadInvoice(sale._id)}
                            >
                              <Download className="h-4 w-4 mr-2" />
                              Download Invoice
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          {/* Analytics Header */}
          <div>
            <h2 className="text-xl font-light text-foreground">Sales Analytics</h2>
            <p className="text-muted-foreground font-light">Performance insights and trends</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Sales by Channel */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Sales by Channel
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-light text-foreground">Online Marketplace</span>
                    <span className="font-light text-foreground">{formatCurrency(totalOnlineSales)}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${totalOnlineSales / (totalOnlineSales + totalSalesValue) * 100}%` }}
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="font-light text-foreground">Offline Sales</span>
                    <span className="font-light text-foreground">{formatCurrency(totalSalesValue)}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-accent h-2 rounded-full" 
                      style={{ width: `${totalSalesValue / (totalOnlineSales + totalSalesValue) * 100}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customer Segments */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Customer Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-light text-foreground">New Customers</span>
                      <span className="font-light text-foreground">
                        {Math.round((totalOrders * 0.7))} ({Math.round(70)}%)
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="bg-primary h-2 rounded-full" style={{ width: "70%" }} />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-light text-foreground">Returning Customers</span>
                      <span className="font-light text-foreground">
                        {Math.round((totalOrders * 0.3))} ({Math.round(30)}%)
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="bg-accent h-2 rounded-full" style={{ width: "30%" }} />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Financial Tracking Tab */}
        <TabsContent value="financial" className="space-y-6">
          {/* Financial Header */}
          <div>
            <h2 className="text-xl font-light text-foreground">Financial Overview</h2>
            <p className="text-muted-foreground font-light">Payment status and invoice management</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Payment Status */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Payment Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="font-light text-foreground">Completed</span>
                    </div>
                    <span className="font-light text-foreground">
                      {filteredOrders.filter(o => o.orderStatus === "delivered").length}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-yellow-500" />
                      <span className="font-light text-foreground">Pending</span>
                    </div>
                    <span className="font-light text-foreground">{pendingOrders}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <XCircle className="w-4 h-4 text-red-500" />
                      <span className="font-light text-foreground">Failed</span>
                    </div>
                    <span className="font-light text-foreground">
                      {filteredOrders.filter(o => o.orderStatus === "cancelled").length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Sales Forecast */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Forecast
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-muted-foreground font-light">Next 30 Days</p>
                    <p className="text-2xl font-light text-foreground">
                      {formatCurrency((totalOnlineSales + totalSalesValue) * 0.15)}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 text-green-600">
                    <TrendingUp className="w-4 h-4" />
                    <span className="text-sm font-light">+15% projected growth</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Sales Tools Tab */}
        {/* <TabsContent value="tools" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Percent className="w-12 h-12 text-primary mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Create Sale/Discount</h3>
                <p className="text-muted-foreground font-light mb-4">
                  Set up promotions and discounts for your products
                </p>
                <Button className="w-full font-light">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Promotion
                </Button>
              </CardContent>
            </Card>

            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Package className="w-12 h-12 text-accent mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Bulk Order Processing</h3>
                <p className="text-muted-foreground font-light mb-4">
                  Process multiple orders at once
                </p>
                <Button variant="outline" className="w-full font-light">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Bulk Actions
                </Button>
              </CardContent>
            </Card>

            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Mail className="w-12 h-12 text-secondary mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Customer Outreach</h3>
                <p className="text-muted-foreground font-light mb-4">
                  Send emails and follow-ups to customers
                </p>
                <Button variant="outline" className="w-full font-light">
                  <Users className="w-4 h-4 mr-2" />
                  Contact Customers
                </Button>
              </CardContent>
            </Card>

            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <MapPin className="w-12 h-12 text-primary mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Geographic Analysis</h3>
                <p className="text-muted-foreground font-light mb-4">
                  View sales by location and region
                </p>
                <Button variant="outline" className="w-full font-light">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  View Map
                </Button>
              </CardContent>
            </Card>

            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Target className="w-12 h-12 text-accent mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Goal Tracking</h3>
                <p className="text-muted-foreground font-light mb-4">
                  Set and track sales targets
                </p>
                <Button variant="outline" className="w-full font-light">
                  <Calendar className="w-4 h-4 mr-2" />
                  Set Goals
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card className="rounded-xl border border-border bg-card shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Smart Alerts & Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-xl border border-yellow-200">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div>
                    <p className="font-light text-foreground">Low inventory alert</p>
                    <p className="text-sm text-muted-foreground font-light">3 products have less than 5 items in stock</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 bg-green-50 rounded-xl border border-green-200">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-light text-foreground">Sales milestone reached</p>
                    <p className="text-sm text-muted-foreground font-light">Congratulations! You've reached $10,000 in sales this month</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-xl border border-blue-200">
                  <TrendingUp className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="font-light text-foreground">Peak selling time</p>
                    <p className="text-sm text-muted-foreground font-light">Most of your sales happen between 2-4 PM on weekdays</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent> */}
      </Tabs>
    </div>
  );
}