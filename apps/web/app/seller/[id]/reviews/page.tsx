import { SellerReviewsPage } from "@/components/seller/SellerReviewsPage";
import { Metadata } from "next";

interface SellerReviewsPageProps {
  params: Promise<{ 
    id: string 
  }>;
}

export async function generateMetadata({ params }: SellerReviewsPageProps): Promise<Metadata> {
  const { id } = await params;
  return {
    title: `Seller Reviews - Haute Vault`,
    description: `View and manage reviews for this seller on Haute Vault marketplace`,
  };
}

export default async function SellerReviewsPageRoute({ params }: SellerReviewsPageProps) {
  const { id } = await params;
  return <SellerReviewsPage sellerId={id} />;
}
