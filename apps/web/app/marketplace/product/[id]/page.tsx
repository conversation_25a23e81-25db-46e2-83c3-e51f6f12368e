import { Suspense } from "react";
import { notFound } from "next/navigation";
import { getToken } from "@convex-dev/better-auth/nextjs";
import { auth } from "@repo/backend/better-auth/server";
import { api } from "@repo/backend/convex/_generated/api";
import { fetchQuery, fetchMutation } from "convex/nextjs";
import { ProductDetailPage } from "@/components/product/ProductDetailPage";
import { ProductDetailSkeleton } from "@/components/marketplace/ProductDetailSkeleton";
import { RequireSubscription } from "@/components/auth/protected-route";

interface ProductPageProps {
  params: Promise<{ id: string }>;
}

// Helper function to validate Convex ID format
function isValidConvexId(id: string): boolean {
  // Convex IDs are base64-like strings with variable length
  // They typically range from 20-40 characters
  if (!id || typeof id !== 'string') {
    return false;
  }
  
  // Convex IDs are usually between 20-40 characters long
  if (id.length < 20 || id.length > 40) {
    return false;
  }
  
  // Convex IDs contain only alphanumeric characters, hyphens, and underscores
  // They don't contain special characters that would cause URL encoding issues
  return /^[a-zA-Z0-9_-]+$/.test(id);
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { id } = await params;
  const token = await getToken(auth);

  // Decode the ID in case it's URL encoded
  const decodedId = decodeURIComponent(id);

  // Validate the ID format before making the query
  if (!isValidConvexId(decodedId)) {
    notFound();
  }

  try {
    const product = await fetchQuery(
      api.products.getProductById,
      { productId: decodedId as any },
      { token }
    );

    if (!product) {
      notFound();
    }

    // Increment view count (server-side, runs only once per page load)
    let updatedProduct = product;
    try {
      await fetchMutation(
        api.products.incrementProductViews,
        { productId: decodedId as any },
        { token }
      );
      
      // Fetch the updated product data to get the correct view count
      const refetchedProduct = await fetchQuery(
        api.products.getProductById,
        { productId: decodedId as any },
        { token }
      );
      
      if (refetchedProduct) {
        updatedProduct = refetchedProduct;
      }
    } catch (error) {
      console.error("Failed to increment view count:", error);
      // Don't fail the page load if view increment fails
    }

    // Use the updated product data (with correct view count)
    const productWithImages = updatedProduct;

    // Use the seller info that's already included in the product response
    const seller = updatedProduct.seller || {
      _id: updatedProduct.sellerId || "unknown",
      name: "Unknown Seller",
      userType: "seller",
      rating: 0,
      reviewCount: 0,
      totalSales: 0,
      memberSince: Date.now(),
      isVerified: false,
      responseTime: "Unknown",
      location: "Location not specified",
    };

    return (
      <RequireSubscription>
        <Suspense fallback={<ProductDetailSkeleton />}>
          <ProductDetailPage product={productWithImages as any} seller={seller as any} />
        </Suspense>
      </RequireSubscription>
    );
  } catch (error) {
    // Handle any errors from the Convex query (including invalid ID errors)
    console.error("Error fetching product:", error);
    notFound();
  }
}