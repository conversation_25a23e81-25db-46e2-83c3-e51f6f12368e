"use client";

import { MarketplaceContent } from "@/components/marketplace/MarketplaceContent";
import { ConditionalFloatingButton } from "@/components/marketplace/ConditionalFloatingButton";
import { Authenticated, Unauthenticated, AuthLoading } from "convex/react";
import { useRouter } from "next/navigation";
import { Button } from "@repo/ui/components/button";
import { Lock, Shield, Star } from "lucide-react";

export default function MarketplacePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen">
      {/* Loading State */}
      <AuthLoading>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
            <p className="mt-2 text-gray-400">Loading marketplace...</p>
          </div>
        </div>
      </AuthLoading>

      {/* Authenticated Users - Show Full Marketplace */}
      <Authenticated>
        <div className="min-h-screen">
          <MarketplaceContent />
          <ConditionalFloatingButton />
        </div>
      </Authenticated>

      {/* Unauthenticated Users - Show Access Required */}
      <Unauthenticated>
        <div className="min-h-screen flex items-center justify-center">
          <div className="max-w-md mx-auto text-center p-8">
            <div className="w-16 h-16 bg-gradient-to-br from-zinc-200 to-zinc-400 rounded-full flex items-center justify-center mx-auto mb-6">
              <Lock className="w-8 h-8 text-black" />
            </div>

            <h1 className="text-3xl font-bold mb-4">Premium Marketplace</h1>
            <p className="text-zinc-400 mb-8 leading-relaxed">
              Access to our curated luxury marketplace requires a premium subscription.
              Discover authenticated designer goods from verified sellers.
            </p>

            <div className="space-y-4 mb-8">
              <div className="flex items-center text-left">
                <Shield className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-sm text-zinc-300">100% Authenticated luxury goods</span>
              </div>
              <div className="flex items-center text-left">
                <Star className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-sm text-zinc-300">Verified seller network</span>
              </div>
              <div className="flex items-center text-left">
                <Lock className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-sm text-zinc-300">Buyer protection guarantee</span>
              </div>
            </div>

            <div className="space-y-3">
              <Button
                className="w-full bg-white text-black hover:bg-zinc-200 font-medium"
                onClick={() => router.push("/")}
              >
                Start Your Subscription
              </Button>
              <Button
                variant="outline"
                className="w-full border-zinc-700 text-white hover:bg-zinc-800"
                onClick={() => router.push("/")}
              >
                Learn More
              </Button>
            </div>

            <p className="text-xs text-zinc-500 mt-6">
              Already have an account? <button
                onClick={() => router.push("/")}
                className="text-white hover:underline"
              >
                Sign in here
              </button>
            </p>
          </div>
        </div>
      </Unauthenticated>
    </div>
  );
}

