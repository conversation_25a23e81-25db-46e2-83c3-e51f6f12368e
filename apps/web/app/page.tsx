"use client";

import { LandingPage } from "@/components/landing/LandingPage";
import { Authenticated, Unauthenticated, AuthLoading } from "convex/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function HomePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Loading State */}
      <AuthLoading>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
            <p className="mt-2 text-gray-400">Loading...</p>
          </div>
        </div>
      </AuthLoading>

      {/* Authenticated Users - Redirect to Marketplace */}
      <Authenticated>
        <AuthenticatedRedirect />
      </Authenticated>

      {/* Unauthenticated Users - Show Landing Page */}
      <Unauthenticated>
        <LandingPage />
      </Unauthenticated>
    </div>
  );
}

// Component to handle redirect for authenticated users
function AuthenticatedRedirect() {
  const router = useRouter();
  
  useEffect(() => {
    router.replace("/marketplace");
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
        <p className="mt-2 text-gray-400">Loading marketplace...</p>
      </div>
    </div>
  );
}
