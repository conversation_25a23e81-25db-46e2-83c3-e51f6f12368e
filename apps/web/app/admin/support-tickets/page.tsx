"use client";

import { useState, useMemo } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { Badge } from "@repo/ui/components/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { MessageCircle } from "lucide-react";

import {
  SearchAndFilters,
  ItemList,
  ChatInterface,
  SupportTicketItem,
} from "@/components/common";

export default function SupportTicketsPage() {
  const [selectedTicket, setSelectedTicket] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState({
    status: "all",
    priority: "all",
    category: "all",
  });

  // Fetch support tickets and admin users
  const tickets = useQuery(api.supportTickets.getAllSupportTickets, {
    status: filters.status === "all" ? undefined : (filters.status as any),
    priority: filters.priority === "all" ? undefined : (filters.priority as any),
    category: filters.category === "all" ? undefined : (filters.category as any),
  });

  const adminUsers = useQuery(api.supportTickets.getAdminUsers);
  const assignTicketToAdmin = useMutation(api.supportTickets.assignTicketToAdmin);
  const updateStatus = useMutation(api.supportTickets.updateTicketStatus);
  const addMessage = useMutation(api.supportTickets.addSupportMessage);

  // Get messages for selected ticket
  const ticketMessages = useQuery(
    api.supportTickets.getSupportTicket,
    selectedTicket ? { ticketId: selectedTicket as any } : "skip"
  );

  // Filter tickets based on search query
  const filteredTickets = useMemo(() => {
    if (!tickets) return [];
    
    return tickets.filter(ticket => {
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase();
        return (
          ticket.subject.toLowerCase().includes(searchLower) ||
          ticket.description.toLowerCase().includes(searchLower) ||
          ticket.ticketNumber.toLowerCase().includes(searchLower) ||
          (ticket.user?.name?.toLowerCase() || '').includes(searchLower) ||
          (ticket.user?.email?.toLowerCase() || '').includes(searchLower)
        );
      }
      return true;
    });
  }, [tickets, searchQuery]);

  const clearFilters = () => {
    setFilters({
      status: "all",
      priority: "all",
      category: "all",
    });
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleSendMessage = async (message: string) => {
    if (!selectedTicket) return;

    setIsLoading(true);
    try {
      await addMessage({
        ticketId: selectedTicket as any,
        content: message,
        messageType: "text",
      });
      toast.success("Message sent successfully");
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssignTicket = async (ticketId: string, adminId: string) => {
    try {
      await assignTicketToAdmin({ ticketId: ticketId as any, adminId: adminId as any });
      toast.success("Ticket assigned successfully");
    } catch (error) {
      console.error("Failed to assign ticket:", error);
      toast.error("Failed to assign ticket");
    }
  };

  const handleUpdateStatus = async (ticketId: string, status: string) => {
    try {
      await updateStatus({ 
        ticketId: ticketId as any, 
        status: status as any 
      });
      toast.success("Status updated successfully");
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Failed to update status");
    }
  };

  const handleSelectTicket = (ticketId: string) => {
    setSelectedTicket(ticketId);
  };

  // Get selected ticket details
  const selectedTicketDetails = useMemo(() => {
    if (!tickets || !selectedTicket) return null;
    return tickets.find(ticket => ticket._id === selectedTicket);
  }, [tickets, selectedTicket]);

  // Filter options for the SearchAndFilters component
  const filterOptions = {
    status: [
      { value: "all", label: "All Status" },
      { value: "open", label: "Open" },
      { value: "in_progress", label: "In Progress" },
      { value: "waiting_for_customer", label: "Waiting" },
      { value: "resolved", label: "Resolved" },
      { value: "closed", label: "Closed" }
    ],
    priority: [
      { value: "all", label: "All Priorities" },
      { value: "urgent", label: "Urgent" },
      { value: "high", label: "High" },
      { value: "medium", label: "Medium" },
      { value: "low", label: "Low" }
    ],
    category: [
      { value: "all", label: "All Categories" },
      { value: "general", label: "General" },
      { value: "technical", label: "Technical" },
      { value: "billing", label: "Billing" },
      { value: "order_issue", label: "Order Issue" },
      { value: "product_issue", label: "Product Issue" },
      { value: "seller_issue", label: "Seller Issue" },
      { value: "refund", label: "Refund" },
      { value: "other", label: "Other" }
    ]
  };

  // Stats for the SearchAndFilters component
  const stats = (
    <>
      <Badge variant="outline">
        {tickets?.length || 0} Total
      </Badge>
      <Badge variant="outline">
        {tickets?.filter(t => t.status === "open").length || 0} Open
      </Badge>
    </>
  );

  const headerActions = selectedTicketDetails && (
    <>
      <Select
        value={selectedTicketDetails.assignedAdminId || "unassigned"}
        onValueChange={(value) => handleAssignTicket(selectedTicketDetails._id, value === "unassigned" ? "" : value)}
      >
        <SelectTrigger className="w-40">
          <SelectValue placeholder="Assign to..." />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="unassigned">Unassigned</SelectItem>
          {adminUsers?.map((admin) => (
            <SelectItem key={admin.user?._id} value={admin.user?._id || "unknown"}>
              {admin.user?.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        value={selectedTicketDetails.status}
        onValueChange={(value) => handleUpdateStatus(selectedTicketDetails._id, value)}
      >
        <SelectTrigger className="w-32">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="open">Open</SelectItem>
          <SelectItem value="in_progress">In Progress</SelectItem>
          <SelectItem value="waiting_for_customer">Waiting</SelectItem>
          <SelectItem value="resolved">Resolved</SelectItem>
          <SelectItem value="closed">Closed</SelectItem>
        </SelectContent>
      </Select>
    </>
  );

  return (
    <>
      {/* Search and Filters Header */}
      <SearchAndFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        filters={filters}
        onFilterChange={handleFilterChange}
        filterOptions={filterOptions}
        onClearFilters={clearFilters}
        stats={stats}
        searchPlaceholder="Search tickets..."
      />

      <div className="flex h-full">
        {/* Tickets List */}
        <ItemList
          title="Support Tickets"
          items={filteredTickets}
          searchQuery={searchQuery}
          onItemSelect={handleSelectTicket}
          selectedItemId={selectedTicket}
          renderItem={(ticket) => <SupportTicketItem ticket={ticket} />}
          emptyStateMessage="No tickets yet"
          emptyStateSubMessage="Try a different search term"
          className="w-96"
        />

        {/* Chat Area */}
        {selectedTicketDetails ? (
          <ChatInterface
            title={selectedTicketDetails.subject}
            subtitle={`#${selectedTicketDetails.ticketNumber} • ${selectedTicketDetails.user?.name}`}
            avatarFallback={selectedTicketDetails.user?.name || 'User'}
            messages={ticketMessages?.messages || []}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            headerActions={headerActions}
            emptyStateMessage="No messages yet"
            emptyStateDescription="Start the conversation by sending a message"
          />
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageCircle className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-light text-foreground mb-2">
                Select a ticket
              </h3>
              <p className="text-muted-foreground font-light">
                Choose a ticket from the left to view details and respond
              </p>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
