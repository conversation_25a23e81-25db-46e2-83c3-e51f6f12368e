"use client";

import { useState, useMemo } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { formatDistanceToNow } from "date-fns";
import { toast } from "sonner";
import { Badge } from "@repo/ui/components/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { FileText, Mail, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { RequireAdmin } from "@/components/auth/protected-route";

import {
  SearchAndFilters,
  ItemList,
  StatusBadge
} from "@/components/common";

export default function AdminSellerApplicationsPage() {
  const [selectedApplication, setSelectedApplication] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState({
    status: "all",
    businessType: "all",
    monthlyVolume: "all",
  });

  // Fetch seller applications
  const applications = useQuery(api.sellerApplicationsSimple.getAllApplications, {});
  const updateApplicationStatus = useMutation(api.sellerApplicationsSimple.updateApplicationStatus);

  // Filter applications based on search query
  const filteredApplications = useMemo(() => {
    if (!applications) return [];
    
    return applications.filter(application => {
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase();
        return (
          application.firstName?.toLowerCase().includes(searchLower) ||
          application.lastName?.toLowerCase().includes(searchLower) ||
          application.email?.toLowerCase().includes(searchLower) ||
          application.businessName?.toLowerCase().includes(searchLower) ||
          application.businessType?.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  }, [applications, searchQuery]);

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  const clearFilters = () => {
    setFilters({
      status: "all",
      businessType: "all",
      monthlyVolume: "all",
    });
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleQuickAction = async (applicationId: string, status: "approved" | "rejected" | "under_review") => {
    try {
      await updateApplicationStatus({
        applicationId: applicationId as any,
        status,
      });
      toast.success(`Application ${status} successfully`);
    } catch (error) {
      console.error("Error updating application:", error);
      toast.error(`Failed to ${status} application`);
    }
  };

  const handleSelectApplication = (applicationId: string) => {
    setSelectedApplication(applicationId);
  };

  // Get selected application details
  const selectedApplicationDetails = useMemo(() => {
    if (!applications || !selectedApplication) return null;
    return applications.find(app => app._id === selectedApplication);
  }, [applications, selectedApplication]);

  // Filter options for the SearchAndFilters component
  const filterOptions = {
    status: [
      { value: "all", label: "All Status" },
      { value: "pending", label: "Pending" },
      { value: "under_review", label: "Under Review" },
      { value: "approved", label: "Approved" },
      { value: "rejected", label: "Rejected" },
      { value: "requires_info", label: "Requires Info" }
    ],
    businessType: [
      { value: "all", label: "All Types" },
      { value: "individual", label: "Individual" },
      { value: "business", label: "Business" },
      { value: "corporation", label: "Corporation" }
    ],
    monthlyVolume: [
      { value: "all", label: "All Volumes" },
      { value: "1-10", label: "$1-10K" },
      { value: "10-50", label: "$10-50K" },
      { value: "50-100", label: "$50-100K" },
      { value: "100+", label: "$100K+" }
    ]
  };

  // Stats for the SearchAndFilters component
  const stats = (
    <>
      <Badge variant="outline">
        {applications?.length || 0} Total
      </Badge>
      <Badge variant="outline" className="bg-blue-50 text-blue-700">
        {applications?.filter(a => a.status === "pending").length || 0} Pending
      </Badge>
      <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
        {applications?.filter(a => a.status === "under_review").length || 0} Under Review
      </Badge>
    </>
  );

  // Render function for application items
  const renderApplicationItem = (application: any) => (
    <div className="flex items-start space-x-3">
      <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center text-sm font-medium">
        {getInitials(application.firstName || '', application.lastName || '')}
      </div>

      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-foreground truncate">
            {application.firstName} {application.lastName}
          </h4>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {application.businessType}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {application.submittedAt ? 
                formatDistanceToNow(new Date(application.submittedAt), { addSuffix: true }) : 
                'Recently'
              }
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2 mb-2">
          <StatusBadge status={application.status} type="status" />
        </div>

        <p className="text-sm text-muted-foreground truncate font-light">
          {application.businessName}
        </p>

        <div className="flex items-center space-x-2 mt-2">
          <div className="flex items-center space-x-1">
            <Mail className="w-3 h-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground truncate font-light">
              {application.email}
            </span>
          </div>
          {application.monthlyVolume && (
            <>
              <span className="text-xs text-muted-foreground">•</span>
              <span className="text-xs text-muted-foreground truncate font-light">
                ${application.monthlyVolume}/month
              </span>
            </>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <RequireAdmin>
      <>
        {/* Search and Filters Header */}
        <SearchAndFilters
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          filters={filters}
          onFilterChange={handleFilterChange}
          filterOptions={filterOptions}
          onClearFilters={clearFilters}
          stats={stats}
          searchPlaceholder="Search applications..."
        />

        <div className="flex h-full">
          {/* Applications List */}
          <ItemList
            title="Applications"
            items={filteredApplications}
            searchQuery={searchQuery}
            onItemSelect={handleSelectApplication}
            selectedItemId={selectedApplication}
            renderItem={renderApplicationItem}
            emptyStateMessage="No applications yet"
            emptyStateSubMessage="Try a different search term"
            className="w-96"
          />

          {/* Application Details and Review */}
          <div className="flex-1 flex flex-col bg-background min-h-0">
            {selectedApplicationDetails ? (
              <>
                {/* Application Header */}
                <div className="border-b border-border bg-card p-4 shrink-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center text-sm font-medium">
                        {getInitials(selectedApplicationDetails.firstName || '', selectedApplicationDetails.lastName || '')}
                      </div>
                      <div>
                        <h3 className="font-medium text-foreground">
                          {selectedApplicationDetails.firstName} {selectedApplicationDetails.lastName}
                        </h3>
                        <p className="text-sm text-muted-foreground font-light">
                          {selectedApplicationDetails.businessName} • {selectedApplicationDetails.businessType}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Select
                        value={selectedApplicationDetails.status}
                        onValueChange={(value) => handleQuickAction(selectedApplicationDetails._id, value as any)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="under_review">Under Review</SelectItem>
                          <SelectItem value="approved">Approved</SelectItem>
                          <SelectItem value="rejected">Rejected</SelectItem>
                          <SelectItem value="requires_info">Requires Info</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Application Details */}
                <div className="flex-1 overflow-y-auto p-4 space-y-6 bg-gradient-to-b from-background to-muted/10 min-h-0">
                  {/* Personal Information */}
                  <div className="rounded-xl border border-border bg-card shadow-sm p-6">
                    <h3 className="text-lg font-light text-foreground mb-4">Personal Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-light text-muted-foreground">First Name</label>
                        <p className="font-medium">{selectedApplicationDetails.firstName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-light text-muted-foreground">Last Name</label>
                        <p className="font-medium">{selectedApplicationDetails.lastName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-light text-muted-foreground">Email</label>
                        <p className="font-medium">{selectedApplicationDetails.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-light text-muted-foreground">Phone</label>
                        <p className="font-medium">{selectedApplicationDetails.phone || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>

                  {/* Business Information */}
                  <div className="rounded-xl border border-border bg-card shadow-sm p-6">
                    <h3 className="text-lg font-light text-foreground mb-4">Business Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-light text-muted-foreground">Business Name</label>
                        <p className="font-medium">{selectedApplicationDetails.businessName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-light text-muted-foreground">Business Type</label>
                        <p className="font-medium capitalize">{selectedApplicationDetails.businessType}</p>
                      </div>
                      <div>
                        <label className="text-sm font-light text-muted-foreground">Website</label>
                        <p className="font-medium">Not provided</p>
                      </div>
                      <div>
                        <label className="text-sm font-light text-muted-foreground">Monthly Volume</label>
                        <p className="font-medium">
                          {selectedApplicationDetails.monthlyVolume ? `$${selectedApplicationDetails.monthlyVolume}` : 'Not specified'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Experience & Products */}
                  <div className="rounded-xl border border-border bg-card shadow-sm p-6">
                    <h3 className="text-lg font-light text-foreground mb-4">Experience & Products</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-light text-muted-foreground">Experience Description</label>
                        <p className="font-medium mt-1">Not provided</p>
                      </div>
                      <div>
                        <label className="text-sm font-light text-muted-foreground">Product Categories</label>
                        <p className="font-medium mt-1">Not specified</p>
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="rounded-xl border border-border bg-card shadow-sm p-6">
                    <h3 className="text-lg font-light text-foreground mb-4">Quick Actions</h3>
                    <div className="flex gap-2">
                      {selectedApplicationDetails.status === "pending" && (
                        <>
                          <button
                            className="px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted/50 transition-colors font-light"
                            onClick={() => handleQuickAction(selectedApplicationDetails._id, "under_review")}
                          >
                            <Clock className="w-4 h-4 mr-2 inline" />
                            Under Review
                          </button>
                          <button
                            className="px-3 py-2 text-sm bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-light"
                            onClick={() => handleQuickAction(selectedApplicationDetails._id, "approved")}
                          >
                            <CheckCircle className="w-4 h-4 mr-2 inline" />
                            Approve
                          </button>
                          <button
                            className="px-3 py-2 text-sm bg-destructive text-destructive-foreground rounded-lg hover:bg-destructive/90 transition-colors font-light"
                            onClick={() => handleQuickAction(selectedApplicationDetails._id, "rejected")}
                          >
                            <XCircle className="w-4 h-4 mr-2 inline" />
                            Reject
                          </button>
                        </>
                      )}
                      {selectedApplicationDetails.status === "under_review" && (
                        <>
                          <button
                            className="px-3 py-2 text-sm bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-light"
                            onClick={() => handleQuickAction(selectedApplicationDetails._id, "approved")}
                          >
                            <CheckCircle className="w-4 h-4 mr-2 inline" />
                            Approve
                          </button>
                          <button
                            className="px-3 py-2 text-sm bg-destructive text-destructive-foreground rounded-lg hover:bg-destructive/90 transition-colors font-light"
                            onClick={() => handleQuickAction(selectedApplicationDetails._id, "rejected")}
                          >
                            <XCircle className="w-4 h-4 mr-2 inline" />
                            Reject
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <FileText className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-light text-foreground mb-2">
                    Select an application
                  </h3>
                  <p className="text-muted-foreground font-light">
                    Choose an application from the left to review details
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    </RequireAdmin>
  );
}