import { Metadata } from "next";
import { AdminUserEdit } from "@/components/admin/AdminUserEdit";

export const metadata: Metadata = {
  title: "Edit User | HauteVault Admin",
  description: "Edit user information and settings on HauteVault's premium marketplace.",
  openGraph: {
    title: "Edit User | HauteVault Admin",
    description: "Edit user information and settings on HauteVault's premium marketplace.",
    type: "website",
  },
};

export default async function AdminUserEditPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  return <AdminUserEdit userId={id} />;
}
