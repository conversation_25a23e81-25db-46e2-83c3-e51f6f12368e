"use client";

import React, { createContext, useContext, useReducer, useEffect } from "react";

export interface SearchFilters {
  categories: string[];
  brands: string[];
  priceRange: {
    min: number;
    max: number;
  };
  condition: string[];
  size: string[];
  color: string[];
  material: string[];
  sortBy: "relevance" | "price_low" | "price_high" | "newest" | "oldest" | "popularity";
  availability: "all" | "available" | "sold";
}

export interface SearchQuery {
  query: string;
  filters: SearchFilters;
  timestamp: number;
}

export interface Product {
  id: string;
  title: string;
  brand: string;
  price: number;
  originalPrice?: number;
  category: string;
  condition: string;
  size?: string;
  color?: string;
  material?: string;
  images: string[];
  seller: {
    id: string;
    name: string;
    rating: number;
  };
  isAvailable: boolean;
  _creationTime: number;
  views: number;
  likes: number;
  tags: string[];
}

export interface SearchSuggestion {
  id: string;
  text: string;
  type: "query" | "brand" | "category" | "product";
  count?: number;
  image?: string;
}

export interface RecommendationContext {
  userId?: string;
  viewedProducts: string[];
  searchHistory: SearchQuery[];
  preferences: {
    categories: string[];
    brands: string[];
    priceRange: { min: number; max: number };
  };
}

interface SearchState {
  currentQuery: string;
  filters: SearchFilters;
  searchHistory: SearchQuery[];
  suggestions: SearchSuggestion[];
  trendingSearches: string[];
  recentlyViewed: Product[];
  searchResults: Product[];
  isLoading: boolean;
  totalResults: number;
  currentPage: number;
  itemsPerPage: number;
  recommendationContext: RecommendationContext;
}

type SearchAction =
  | { type: "SET_QUERY"; payload: string }
  | { type: "SET_FILTERS"; payload: Partial<SearchFilters> }
  | { type: "ADD_TO_HISTORY"; payload: SearchQuery }
  | { type: "SET_SUGGESTIONS"; payload: SearchSuggestion[] }
  | { type: "SET_TRENDING"; payload: string[] }
  | { type: "ADD_RECENTLY_VIEWED"; payload: Product }
  | { type: "SET_SEARCH_RESULTS"; payload: { results: Product[]; total: number } }
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_PAGE"; payload: number }
  | { type: "UPDATE_RECOMMENDATION_CONTEXT"; payload: Partial<RecommendationContext> }
  | { type: "CLEAR_SEARCH" };

const initialFilters: SearchFilters = {
  categories: [],
  brands: [],
  priceRange: { min: 0, max: 50000 },
  condition: [],
  size: [],
  color: [],
  material: [],
  sortBy: "relevance",
  availability: "all",
};

const initialState: SearchState = {
  currentQuery: "",
  filters: initialFilters,
  searchHistory: [],
  suggestions: [],
  trendingSearches: [],
  recentlyViewed: [],
  searchResults: [],
  isLoading: false,
  totalResults: 0,
  currentPage: 1,
  itemsPerPage: 24,
  recommendationContext: {
    viewedProducts: [],
    searchHistory: [],
    preferences: {
      categories: [],
      brands: [],
      priceRange: { min: 0, max: 50000 },
    },
  },
};

function searchReducer(state: SearchState, action: SearchAction): SearchState {
  switch (action.type) {
    case "SET_QUERY":
      return { ...state, currentQuery: action.payload };
    
    case "SET_FILTERS":
      return { 
        ...state, 
        filters: { ...state.filters, ...action.payload },
        currentPage: 1 // Reset to first page when filters change
      };
    
    case "ADD_TO_HISTORY":
      const newHistory = [action.payload, ...state.searchHistory.slice(0, 19)]; // Keep last 20 searches
      return { 
        ...state, 
        searchHistory: newHistory,
        recommendationContext: {
          ...state.recommendationContext,
          searchHistory: newHistory,
        }
      };
    
    case "SET_SUGGESTIONS":
      return { ...state, suggestions: action.payload };
    
    case "SET_TRENDING":
      return { ...state, trendingSearches: action.payload };
    
    case "ADD_RECENTLY_VIEWED":
      const updatedViewed = [
        action.payload,
        ...state.recentlyViewed.filter(p => p.id !== action.payload.id).slice(0, 19)
      ];
      return { 
        ...state, 
        recentlyViewed: updatedViewed,
        recommendationContext: {
          ...state.recommendationContext,
          viewedProducts: updatedViewed.map(p => p.id),
        }
      };
    
    case "SET_SEARCH_RESULTS":
      return { 
        ...state, 
        searchResults: action.payload.results,
        totalResults: action.payload.total,
        isLoading: false
      };
    
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };
    
    case "SET_PAGE":
      return { ...state, currentPage: action.payload };
    
    case "UPDATE_RECOMMENDATION_CONTEXT":
      return {
        ...state,
        recommendationContext: {
          ...state.recommendationContext,
          ...action.payload,
        }
      };
    
    case "CLEAR_SEARCH":
      return {
        ...state,
        currentQuery: "",
        filters: initialFilters,
        searchResults: [],
        totalResults: 0,
        currentPage: 1,
        isLoading: false,
      };
    
    default:
      return state;
  }
}

interface SearchContextType {
  state: SearchState;
  setQuery: (query: string) => void;
  setFilters: (filters: Partial<SearchFilters>) => void;
  addToHistory: (query: SearchQuery) => void;
  setSuggestions: (suggestions: SearchSuggestion[]) => void;
  setTrendingSearches: (searches: string[]) => void;
  addRecentlyViewed: (product: Product) => void;
  setSearchResults: (results: Product[], total: number) => void;
  setLoading: (loading: boolean) => void;
  setPage: (page: number) => void;
  updateRecommendationContext: (context: Partial<RecommendationContext>) => void;
  clearSearch: () => void;
  performSearch: (query?: string, filters?: Partial<SearchFilters>) => Promise<void>;
  getSuggestions: (query: string) => Promise<SearchSuggestion[]>;
  getRecommendations: (productId?: string, type?: "similar" | "personalized" | "trending") => Promise<Product[]>;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

export function SearchProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(searchReducer, initialState);

  // Load search history and preferences from localStorage on mount
  useEffect(() => {
    const savedHistory = localStorage.getItem("hautevault_search_history");
    const savedViewed = localStorage.getItem("hautevault_recently_viewed");
    const savedPreferences = localStorage.getItem("hautevault_search_preferences");

    if (savedHistory) {
      const history = JSON.parse(savedHistory);
      history.forEach((query: SearchQuery) => {
        dispatch({ type: "ADD_TO_HISTORY", payload: query });
      });
    }

    if (savedViewed) {
      const viewed = JSON.parse(savedViewed);
      viewed.forEach((product: Product) => {
        dispatch({ type: "ADD_RECENTLY_VIEWED", payload: product });
      });
    }

    if (savedPreferences) {
      const preferences = JSON.parse(savedPreferences);
      dispatch({ 
        type: "UPDATE_RECOMMENDATION_CONTEXT", 
        payload: { preferences } 
      });
    }
  }, []);

  // Save to localStorage when state changes
  useEffect(() => {
    localStorage.setItem("hautevault_search_history", JSON.stringify(state.searchHistory));
  }, [state.searchHistory]);

  useEffect(() => {
    localStorage.setItem("hautevault_recently_viewed", JSON.stringify(state.recentlyViewed));
  }, [state.recentlyViewed]);

  useEffect(() => {
    localStorage.setItem("hautevault_search_preferences", JSON.stringify(state.recommendationContext.preferences));
  }, [state.recommendationContext.preferences]);

  const setQuery = (query: string) => {
    dispatch({ type: "SET_QUERY", payload: query });
  };

  const setFilters = (filters: Partial<SearchFilters>) => {
    dispatch({ type: "SET_FILTERS", payload: filters });
  };

  const addToHistory = (query: SearchQuery) => {
    dispatch({ type: "ADD_TO_HISTORY", payload: query });
  };

  const setSuggestions = (suggestions: SearchSuggestion[]) => {
    dispatch({ type: "SET_SUGGESTIONS", payload: suggestions });
  };

  const setTrendingSearches = (searches: string[]) => {
    dispatch({ type: "SET_TRENDING", payload: searches });
  };

  const addRecentlyViewed = (product: Product) => {
    dispatch({ type: "ADD_RECENTLY_VIEWED", payload: product });
  };

  const setSearchResults = (results: Product[], total: number) => {
    dispatch({ type: "SET_SEARCH_RESULTS", payload: { results, total } });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: "SET_LOADING", payload: loading });
  };

  const setPage = (page: number) => {
    dispatch({ type: "SET_PAGE", payload: page });
  };

  const updateRecommendationContext = (context: Partial<RecommendationContext>) => {
    dispatch({ type: "UPDATE_RECOMMENDATION_CONTEXT", payload: context });
  };

  const clearSearch = () => {
    dispatch({ type: "CLEAR_SEARCH" });
  };

  const performSearch = async (query?: string, filters?: Partial<SearchFilters>) => {
    const searchQuery = query || state.currentQuery;
    const searchFilters = filters ? { ...state.filters, ...filters } : state.filters;

    if (!searchQuery.trim()) return;

    dispatch({ type: "SET_LOADING", payload: true });

    try {
      // Add to search history
      const searchEntry: SearchQuery = {
        query: searchQuery,
        filters: searchFilters,
        timestamp: Date.now(),
      };
      addToHistory(searchEntry);

      // Simulate API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock search results - replace with actual API call
      const mockResults: Product[] = []; // Would be populated from API
      const mockTotal = 0;

      setSearchResults(mockResults, mockTotal);
    } catch (error) {
      console.error("Search failed:", error);
      setSearchResults([], 0);
    }
  };

  const getSuggestions = async (query: string): Promise<SearchSuggestion[]> => {
    if (!query.trim()) return [];

    try {
      // Simulate API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Mock suggestions - replace with actual API call
      const mockSuggestions: SearchSuggestion[] = [];
      return mockSuggestions;
    } catch (error) {
      console.error("Failed to get suggestions:", error);
      return [];
    }
  };

  const getRecommendations = async (
    productId?: string, 
    type: "similar" | "personalized" | "trending" = "personalized"
  ): Promise<Product[]> => {
    try {
      // Simulate API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Mock recommendations - replace with actual API call
      const mockRecommendations: Product[] = [];
      return mockRecommendations;
    } catch (error) {
      console.error("Failed to get recommendations:", error);
      return [];
    }
  };

  const value: SearchContextType = {
    state,
    setQuery,
    setFilters,
    addToHistory,
    setSuggestions,
    setTrendingSearches,
    addRecentlyViewed,
    setSearchResults,
    setLoading,
    setPage,
    updateRecommendationContext,
    clearSearch,
    performSearch,
    getSuggestions,
    getRecommendations,
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
}

export function useSearch() {
  const context = useContext(SearchContext);
  if (context === undefined) {
    throw new Error("useSearch must be used within a SearchProvider");
  }
  return context;
}
