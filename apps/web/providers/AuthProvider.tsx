"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { ConvexProvider, ConvexReactClient } from "convex/react";
import { useAuth } from "@/hooks/useBetterAuth";
import { 
  AuthUser, 
  AuthContextValue, 
  SellerStatus,
  AuthPermissions 
} from "../../../packages/backend/better-auth/types";

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Create auth context
const AuthContext = createContext<AuthContextValue | undefined>(undefined);

/**
 * Auth context provider that wraps the app
 */
export function AuthProvider({ children }: { children: React.ReactNode }) {
  return (
    <ConvexProvider client={convex}>
      <AuthContextProvider>
        {children}
      </AuthContextProvider>
    </ConvexProvider>
  );
}

/**
 * Internal auth context provider
 */
function AuthContextProvider({ children }: { children: React.ReactNode }) {
  const auth = useAuth();
  const [sellerStatus, setSellerStatus] = useState<SellerStatus | null>(null);
  const [permissions, setPermissions] = useState<AuthPermissions>({
    canBuy: false,
    canSell: false,
    canManageUsers: false,
    canAccessAnalytics: false,
    canManageSubscriptions: false,
    requiresSubscription: true,
  });

  // Update seller status when user changes
  useEffect(() => {
    if (auth.user?.userType === "seller") {
      // This would typically be fetched from the server
      // For now, we'll set a default status
      setSellerStatus({
        hasProfile: true,
        verificationStatus: "pending",
        canSell: false,
      });
    } else {
      setSellerStatus(null);
    }
  }, [auth.user]);

  // Update permissions when user changes
  useEffect(() => {
    if (auth.user) {
      const isSubscriptionActive = auth.hasActiveSubscription;
      
      setPermissions({
        canBuy: auth.user.userType === "consumer" || auth.user.userType === "seller",
        canSell: auth.user.userType === "seller" && 
                 isSubscriptionActive && 
                 sellerStatus?.verificationStatus === "approved",
        canManageUsers: auth.user.userType === "admin",
        canAccessAnalytics: auth.user.userType === "admin" || auth.user.userType === "seller",
        canManageSubscriptions: auth.user.userType === "admin",
        requiresSubscription: auth.user.userType === "seller" && !isSubscriptionActive,
      });
    } else {
      setPermissions({
        canBuy: false,
        canSell: false,
        canManageUsers: false,
        canAccessAnalytics: false,
        canManageSubscriptions: false,
        requiresSubscription: true,
      });
    }
  }, [auth.user, auth.hasActiveSubscription, sellerStatus]);

  const contextValue: AuthContextValue = {
    user: auth.user,
    isLoading: auth.isLoading,
    isAuthenticated: auth.isAuthenticated,
    hasActiveSubscription: auth.hasActiveSubscription,
    sellerStatus,
    permissions,
    signOut: auth.signOut,
    updateProfile: auth.updateProfile,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Hook to use auth context
 */
export function useAuthContext(): AuthContextValue {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
}

/**
 * Hook for checking permissions
 */
export function usePermissions() {
  const { permissions } = useAuthContext();
  return permissions;
}

/**
 * Hook for seller-specific functionality
 */
export function useSellerContext() {
  const { user, sellerStatus, permissions } = useAuthContext();
  
  return {
    isSeller: user?.userType === "seller",
    sellerStatus,
    canSell: permissions.canSell,
    requiresVerification: sellerStatus?.verificationStatus !== "approved",
    requiresSubscription: permissions.requiresSubscription,
  };
}

/**
 * Hook for admin-specific functionality
 */
export function useAdminContext() {
  const { user, permissions } = useAuthContext();
  
  return {
    isAdmin: user?.userType === "admin",
    canManageUsers: permissions.canManageUsers,
    canAccessAnalytics: permissions.canAccessAnalytics,
    canManageSubscriptions: permissions.canManageSubscriptions,
  };
}

/**
 * Component that only renders for authenticated users
 */
export function AuthenticatedOnly({ 
  children,
  fallback 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { isAuthenticated, isLoading } = useAuthContext();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black dark:border-white"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <>{fallback}</> || null;
  }

  return <>{children}</>;
}

/**
 * Component that only renders for unauthenticated users
 */
export function UnauthenticatedOnly({ 
  children 
}: { 
  children: React.ReactNode;
}) {
  const { isAuthenticated, isLoading } = useAuthContext();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black dark:border-white"></div>
      </div>
    );
  }

  if (isAuthenticated) {
    return null;
  }

  return <>{children}</>;
}

/**
 * Component that renders different content based on user role
 */
export function RoleBasedContent({
  consumer,
  seller,
  admin,
  fallback
}: {
  consumer?: React.ReactNode;
  seller?: React.ReactNode;
  admin?: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { user, isLoading } = useAuthContext();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black dark:border-white"></div>
      </div>
    );
  }

  if (!user) {
    return <>{fallback}</> || null;
  }

  switch (user.userType) {
    case "consumer":
      return <>{consumer}</> || <>{fallback}</> || null;
    case "seller":
      return <>{seller}</> || <>{fallback}</> || null;
    case "admin":
      return <>{admin}</> || <>{fallback}</> || null;
    default:
      return <>{fallback}</> || null;
  }
}

/**
 * Component that renders content based on subscription status
 */
export function SubscriptionBasedContent({
  active,
  inactive,
  trial,
  fallback
}: {
  active?: React.ReactNode;
  inactive?: React.ReactNode;
  trial?: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { user, isLoading } = useAuthContext();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black dark:border-white"></div>
      </div>
    );
  }

  if (!user) {
    return <>{fallback}</> || null;
  }

  switch (user.subscriptionStatus) {
    case "active":
      return <>{active}</> || <>{fallback}</> || null;
    case "inactive":
      return <>{inactive}</> || <>{fallback}</> || null;
    case "trial":
      return <>{trial}</> || <>{fallback}</> || null;
    default:
      return <>{fallback}</> || null;
  }
}
