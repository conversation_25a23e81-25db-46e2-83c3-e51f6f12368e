"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useBetterAuth";
import { 
  UserType, 
  SubscriptionPlan, 
  ProtectedRouteConfig 
} from "@repo/backend/better-auth/types";

/**
 * Higher-order component for route protection
 */
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  config: ProtectedRouteConfig
) {
  return function AuthenticatedComponent(props: P) {
    const { 
      user, 
      isLoading, 
      isAuthenticated, 
      hasActiveSubscription
    } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading) {
        // Check authentication
        if (config.requireAuth && !isAuthenticated) {
          router.push(config.redirectUnauthenticated || "/auth/signin");
          return;
        }

        // Check role requirements
        if (config.allowedRoles && user) {
          const hasRequiredRole = config.allowedRoles.includes(user.userType);
          if (!hasRequiredRole) {
            router.push(config.redirectUnauthorized || "/unauthorized");
            return;
          }
        }

        // Check subscription requirements
        if (config.requireSubscription && user) {
          if (!hasActiveSubscription) {
            router.push("/subscription");
            return;
          }
        }

        // Check seller verification (if applicable)
        if (config.requireSellerVerification && user?.userType === "seller") {
          // This would need to be checked via a query to the seller profile
          // For now, we'll assume it's handled by the component itself
        }
      }
    }, [isLoading, isAuthenticated, user, router]);

    // Show loading state
    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-black dark:border-white"></div>
        </div>
      );
    }

    // Show unauthorized state
    if (config.requireAuth && !isAuthenticated) {
      return null; // Will redirect
    }

    if (config.allowedRoles && user && !config.allowedRoles.includes(user.userType)) {
      return null; // Will redirect
    }

    return <WrappedComponent {...props} />;
  };
}

/**
 * Hook for protecting routes with authentication
 */
export function useRequireAuth(requiredRole?: UserType) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push("/auth/signin");
        return;
      }

      if (requiredRole && user?.userType !== requiredRole) {
        router.push("/unauthorized");
        return;
      }
    }
  }, [isLoading, isAuthenticated, user, requiredRole, router]);

  return { user, isLoading, isAuthenticated };
}

/**
 * Hook for protecting routes with subscription requirements
 */
export function useRequireSubscription(requiredPlan?: SubscriptionPlan) {
  const { user, isLoading, hasActiveSubscription } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && user) {
      if (!hasActiveSubscription) {
        router.push("/subscription");
        return;
      }
    }
  }, [isLoading, user, hasActiveSubscription, router]);

  return { user, isLoading, hasSubscription: hasActiveSubscription };
}

/**
 * Component for protecting admin routes
 */
export function AdminRoute({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useRequireAuth("admin");

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-black dark:border-white"></div>
      </div>
    );
  }

  if (!user || user.userType !== "admin") {
    return null;
  }

  return <>{children}</>;
}

/**
 * Component for protecting seller routes
 */
export function SellerRoute({ 
  children, 
  requireVerification = false 
}: { 
  children: React.ReactNode;
  requireVerification?: boolean;
}) {
  const { user, isLoading } = useRequireAuth("seller");
  const { hasActiveSubscription } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-black dark:border-white"></div>
      </div>
    );
  }

  if (!user || user.userType !== "seller") {
    return null;
  }

  if (!hasActiveSubscription) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Subscription Required</h2>
          <p className="text-neutral-600 mb-6">
            You need an active subscription to access seller features.
          </p>
          <button
            onClick={() => window.location.href = "/subscription"}
            className="bg-black text-white px-6 py-3 rounded-lg hover:bg-neutral-800"
          >
            Upgrade Subscription
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

/**
 * Component for protecting premium features
 */
export function PremiumRoute({ 
  children,
  requiredPlan = "premium"
}: { 
  children: React.ReactNode;
  requiredPlan?: SubscriptionPlan;
}) {
  const { user, isLoading, hasActiveSubscription } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-black dark:border-white"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  if (!hasActiveSubscription) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Premium Feature</h2>
          <p className="text-neutral-600 mb-6">
            This feature requires a {requiredPlan} subscription or higher.
          </p>
          <button
            onClick={() => window.location.href = "/subscription"}
            className="bg-black text-white px-6 py-3 rounded-lg hover:bg-neutral-800"
          >
            Upgrade to {requiredPlan}
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

/**
 * Utility function to check if user can access a route
 */
export function canAccessRoute(
  user: any,
  config: ProtectedRouteConfig
): { canAccess: boolean; reason?: string } {
  if (config.requireAuth && !user) {
    return { canAccess: false, reason: "Authentication required" };
  }

  if (config.allowedRoles && user && !config.allowedRoles.includes(user.userType)) {
    return { canAccess: false, reason: "Insufficient role permissions" };
  }

  if (config.requireSubscription && user) {
    const isActive = user.subscriptionStatus === "active" && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!isActive) {
      return { canAccess: false, reason: "Active subscription required" };
    }

    if (config.allowedPlans && user.subscriptionPlan) {
      if (!config.allowedPlans.includes(user.subscriptionPlan)) {
        return { canAccess: false, reason: "Higher subscription plan required" };
      }
    }
  }

  return { canAccess: true };
}
