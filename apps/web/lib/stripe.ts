import { loadStripe, Stripe } from '@stripe/stripe-js';

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
let stripePromise: Promise<Stripe | null>;

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
  }
  return stripePromise;
};

// Stripe configuration constants
export const STRIPE_CONFIG = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
  appUrl: process.env.NEXT_PUBLIC_APP_URL!,
};

// Helper function to format currency
export const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
  }).format(amount);
};

// Helper function to calculate display fees
export const calculateDisplayFees = (amount: number, platformFeePercentage: number = 5.0) => {
  const platformFee = amount * (platformFeePercentage / 100);
  const stripeFee = amount * 0.029 + 0.30; // 2.9% + $0.30
  const sellerAmount = amount - platformFee - stripeFee;
  
  return {
    subtotal: amount,
    platformFee,
    stripeFee,
    sellerAmount,
    total: amount,
  };
};

// Stripe Connect URLs
export const getConnectUrls = () => {
  const baseUrl = STRIPE_CONFIG.appUrl;
  
  return {
    refreshUrl: `${baseUrl}/seller/onboarding/refresh`,
    returnUrl: `${baseUrl}/seller/onboarding/complete`,
    reauthorizeUrl: `${baseUrl}/seller/onboarding/reauthorize`,
  };
};

export default getStripe;
