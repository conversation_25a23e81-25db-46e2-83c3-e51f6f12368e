"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Switch } from "@repo/ui/components/switch";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Alert, AlertDescription } from "@repo/ui/components/alert";
import { 
  Shield, 
  Key, 
  Smartphone, 
  Eye, 
  EyeOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  MapPin,
  Monitor
} from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { toast } from "sonner";

interface LoginSession {
  id: string;
  device: string;
  location: string;
  lastActive: string;
  isCurrent: boolean;
  browser: string;
  ip: string;
}

const mockSessions: LoginSession[] = [
  {
    id: '1',
    device: 'MacBook Pro',
    location: 'New York, NY',
    lastActive: '2024-01-15T10:30:00Z',
    isCurrent: true,
    browser: 'Chrome 120',
    ip: '*************'
  },
  {
    id: '2',
    device: 'iPhone 15',
    location: 'New York, NY',
    lastActive: '2024-01-14T18:45:00Z',
    isCurrent: false,
    browser: 'Safari Mobile',
    ip: '*********'
  },
  {
    id: '3',
    device: 'iPad Pro',
    location: 'Los Angeles, CA',
    lastActive: '2024-01-10T14:20:00Z',
    isCurrent: false,
    browser: 'Safari',
    ip: '*************'
  }
];

export function SecuritySettings() {
  const [sessions, setSessions] = useState<LoginSession[]>(mockSessions);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [securitySettings, setSecuritySettings] = useState({
    emailNotifications: true,
    loginAlerts: true,
    deviceTracking: true,
    sessionTimeout: '30',
    passwordExpiry: false
  });

  const handlePasswordChange = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords don't match");
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement password change
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Password updated successfully!");
      setIsChangingPassword(false);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      toast.error("Failed to update password");
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnable2FA = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement 2FA setup
      await new Promise(resolve => setTimeout(resolve, 1000));
      setIs2FAEnabled(!is2FAEnabled);
      toast.success(is2FAEnabled ? "2FA disabled" : "2FA enabled successfully!");
    } catch (error) {
      toast.error("Failed to update 2FA settings");
    } finally {
      setIsLoading(false);
    }
  };

  const handleTerminateSession = async (sessionId: string) => {
    try {
      setSessions(prev => prev.filter(session => session.id !== sessionId));
      toast.success("Session terminated");
    } catch (error) {
      toast.error("Failed to terminate session");
    }
  };

  const handleTerminateAllSessions = async () => {
    try {
      setSessions(prev => prev.filter(session => session.isCurrent));
      toast.success("All other sessions terminated");
    } catch (error) {
      toast.error("Failed to terminate sessions");
    }
  };

  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    return {
      score: strength,
      label: strength < 2 ? 'Weak' : strength < 4 ? 'Medium' : 'Strong',
      color: strength < 2 ? 'text-red-600' : strength < 4 ? 'text-yellow-600' : 'text-green-600'
    };
  };

  const formatLastActive = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Active now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const passwordStrength = getPasswordStrength(passwordData.newPassword);

  return (
    <div className="space-y-8">
      {/* Password Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Password & Authentication
          </h3>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="w-5 h-5" />
              Change Password
            </CardTitle>
            <CardDescription>
              Update your password to keep your account secure
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Dialog open={isChangingPassword} onOpenChange={setIsChangingPassword}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  Change Password
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Change Password</DialogTitle>
                  <DialogDescription>
                    Enter your current password and choose a new one
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-6 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <div className="relative">
                      <Input
                        id="currentPassword"
                        type={showCurrentPassword ? "text" : "password"}
                        value={passwordData.currentPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                        placeholder="Enter current password"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                      >
                        {showCurrentPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <div className="relative">
                      <Input
                        id="newPassword"
                        type={showNewPassword ? "text" : "password"}
                        value={passwordData.newPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                        placeholder="Enter new password"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                      >
                        {showNewPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    
                    {passwordData.newPassword && (
                      <div className="flex items-center space-x-2 text-sm">
                        <span>Strength:</span>
                        <span className={passwordStrength.color}>
                          {passwordStrength.label}
                        </span>
                        <div className="flex-1 bg-neutral-200 dark:bg-neutral-700 rounded-full h-2 ml-2">
                          <div 
                            className={cn(
                              "h-2 rounded-full transition-all",
                              passwordStrength.score < 2 ? "bg-red-500" : 
                              passwordStrength.score < 4 ? "bg-yellow-500" : "bg-green-500"
                            )}
                            style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        value={passwordData.confirmPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        placeholder="Confirm new password"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    
                    {passwordData.confirmPassword && passwordData.newPassword !== passwordData.confirmPassword && (
                      <p className="text-sm text-red-600 dark:text-red-400">
                        Passwords don't match
                      </p>
                    )}
                  </div>
                  
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Choose a strong password with at least 8 characters, including uppercase, lowercase, numbers, and special characters.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="flex justify-end space-x-2 pt-4">
                    <Button variant="outline" onClick={() => setIsChangingPassword(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handlePasswordChange} disabled={isLoading}>
                      {isLoading ? "Updating..." : "Update Password"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>

        {/* Two-Factor Authentication */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="w-5 h-5" />
              Two-Factor Authentication
            </CardTitle>
            <CardDescription>
              Add an extra layer of security to your account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-medium text-black dark:text-white">
                  Authenticator App
                </p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Use an authenticator app to generate verification codes
                </p>
              </div>
              <div className="flex items-center space-x-2">
                {is2FAEnabled && (
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Enabled
                  </Badge>
                )}
                <Switch
                  checked={is2FAEnabled}
                  onCheckedChange={handleEnable2FA}
                  disabled={isLoading}
                />
              </div>
            </div>
            
            {!is2FAEnabled && (
              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  Enable 2FA to significantly improve your account security. We recommend using apps like Google Authenticator or Authy.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Login Sessions */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Active Sessions
          </h3>
          <Button 
            variant="outline" 
            onClick={handleTerminateAllSessions}
            disabled={sessions.length <= 1}
          >
            Terminate All Other Sessions
          </Button>
        </div>
        
        <div className="space-y-4">
          {sessions.map((session) => (
            <Card key={session.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Monitor className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
                      <p className="font-medium text-black dark:text-white">
                        {session.device}
                      </p>
                      {session.isCurrent && (
                        <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                          Current Session
                        </Badge>
                      )}
                    </div>
                    
                    <div className="space-y-1 text-sm text-neutral-600 dark:text-neutral-400">
                      <div className="flex items-center space-x-2">
                        <MapPin className="w-3 h-3" />
                        <span>{session.location}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="w-3 h-3" />
                        <span>{formatLastActive(session.lastActive)}</span>
                      </div>
                      <p>
                        {session.browser} • {session.ip}
                      </p>
                    </div>
                  </div>
                  
                  {!session.isCurrent && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTerminateSession(session.id)}
                      className="text-red-600 border-red-200 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Terminate
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* Security Preferences */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-black dark:text-white">
          Security Preferences
        </h3>
        
        <Card>
          <CardContent className="p-6 space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-medium text-black dark:text-white">
                  Email Security Alerts
                </p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Get notified about unusual account activity
                </p>
              </div>
              <Switch
                checked={securitySettings.emailNotifications}
                onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, emailNotifications: checked }))}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-medium text-black dark:text-white">
                  Login Notifications
                </p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Get notified when someone signs into your account
                </p>
              </div>
              <Switch
                checked={securitySettings.loginAlerts}
                onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, loginAlerts: checked }))}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-medium text-black dark:text-white">
                  Device Tracking
                </p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Track devices that access your account
                </p>
              </div>
              <Switch
                checked={securitySettings.deviceTracking}
                onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, deviceTracking: checked }))}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
