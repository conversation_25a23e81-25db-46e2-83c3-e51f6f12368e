"use client";

import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Checkbox } from "@repo/ui/components/checkbox";
import { 
  Plus, 
  Trash2, 
  Edit, 
  Home,
  Building,
  MapPin,
  Truck
} from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { toast } from "sonner";

import { Id } from "@repo/backend/convex/_generated/dataModel";

interface ShippingAddress {
  _id: Id<"shippingAddresses">;
  type: 'home' | 'work' | 'other';
  firstName: string;
  lastName: string;
  company?: string;
  street: string;
  street2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone?: string;
  isDefault: boolean;
  deliveryInstructions?: string;
}



export function ShippingSettings() {
  const addresses = useQuery(api.shippingAddresses.getUserShippingAddresses) || [];
  const addAddress = useMutation(api.shippingAddresses.addShippingAddress);
  const updateAddress = useMutation(api.shippingAddresses.updateShippingAddress);
  const deleteAddress = useMutation(api.shippingAddresses.deleteShippingAddress);
  const setDefaultAddress = useMutation(api.shippingAddresses.setDefaultShippingAddress);
  const [isAddingAddress, setIsAddingAddress] = useState(false);
  const [editingAddress, setEditingAddress] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [newAddress, setNewAddress] = useState<Omit<ShippingAddress, '_id'>>({
    type: 'home',
    firstName: '',
    lastName: '',
    company: '',
    street: '',
    street2: '',
    city: '',
    state: '',
    zip: '',
    country: 'US',
    phone: '',
    isDefault: false,
    deliveryInstructions: ''
  });

  const handleAddAddress = async () => {
    setIsLoading(true);
    try {
      await addAddress({
        type: newAddress.type,
        firstName: newAddress.firstName,
        lastName: newAddress.lastName,
        company: newAddress.company,
        street: newAddress.street,
        street2: newAddress.street2,
        city: newAddress.city,
        state: newAddress.state,
        zip: newAddress.zip,
        country: newAddress.country,
        phone: newAddress.phone,
        isDefault: newAddress.isDefault,
        deliveryInstructions: newAddress.deliveryInstructions,
      });
      
      toast.success("Address added successfully!");
      setIsAddingAddress(false);
      resetForm();
    } catch (error) {
      console.error("Failed to add address:", error);
      toast.error("Failed to add address");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateAddress = async () => {
    if (!editingAddress) return;
    
    setIsLoading(true);
    try {
      await updateAddress({
        addressId: editingAddress as Id<"shippingAddresses">,
        type: newAddress.type,
        firstName: newAddress.firstName,
        lastName: newAddress.lastName,
        company: newAddress.company,
        street: newAddress.street,
        street2: newAddress.street2,
        city: newAddress.city,
        state: newAddress.state,
        zip: newAddress.zip,
        country: newAddress.country,
        phone: newAddress.phone,
        isDefault: newAddress.isDefault,
        deliveryInstructions: newAddress.deliveryInstructions,
      });
      
      toast.success("Address updated successfully!");
      setEditingAddress(null);
      resetForm();
    } catch (error) {
      console.error("Failed to update address:", error);
      toast.error("Failed to update address");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveAddress = async (id: string) => {
    try {
      await deleteAddress({
        addressId: id as Id<"shippingAddresses">,
      });
      
      toast.success("Address removed");
    } catch (error) {
      console.error("Failed to remove address:", error);
      toast.error("Failed to remove address");
    }
  };

  const handleSetDefault = async (id: string) => {
    try {
      await setDefaultAddress({
        addressId: id as Id<"shippingAddresses">,
      });
      
      toast.success("Default address updated");
    } catch (error) {
      console.error("Failed to update default address:", error);
      toast.error("Failed to update default address");
    }
  };

  const handleEditAddress = (address: ShippingAddress) => {
    setNewAddress(address);
    setEditingAddress(address._id);
  };

  const resetForm = () => {
    setNewAddress({
      type: 'home',
      firstName: '',
      lastName: '',
      company: '',
      street: '',
      street2: '',
      city: '',
      state: '',
      zip: '',
      country: 'US',
      phone: '',
      isDefault: false,
      deliveryInstructions: ''
    });
  };

  const getAddressIcon = (type: string) => {
    switch (type) {
      case 'home':
        return <Home className="w-5 h-5" />;
      case 'work':
        return <Building className="w-5 h-5" />;
      default:
        return <MapPin className="w-5 h-5" />;
    }
  };

  const formatAddress = (address: ShippingAddress) => {
    const parts = [
      address.street,
      address.street2,
      `${address.city}, ${address.state} ${address.zip}`,
      address.country === 'US' ? 'United States' : address.country
    ].filter(Boolean);
    
    return parts.join('\n');
  };

  return (
    <div className="space-y-8">
      {/* Shipping Addresses */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Shipping Addresses
          </h3>
          <Dialog 
            open={isAddingAddress || !!editingAddress} 
            onOpenChange={(open) => {
              if (!open) {
                setIsAddingAddress(false);
                setEditingAddress(null);
                resetForm();
              } else {
                setIsAddingAddress(true);
              }
            }}
          >
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Address
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingAddress ? 'Edit Address' : 'Add New Address'}
                </DialogTitle>
                <DialogDescription>
                  {editingAddress 
                    ? 'Update your shipping address details'
                    : 'Add a new shipping address to your account'
                  }
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6 py-4">
                <div className="space-y-2">
                  <Label>Address Type</Label>
                  <Select value={newAddress.type} onValueChange={(value: any) => setNewAddress(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="home">🏠 Home</SelectItem>
                      <SelectItem value="work">🏢 Work</SelectItem>
                      <SelectItem value="other">📍 Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={newAddress.firstName}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, firstName: e.target.value }))}
                      placeholder="John"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={newAddress.lastName}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, lastName: e.target.value }))}
                      placeholder="Doe"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="company">Company (Optional)</Label>
                  <Input
                    id="company"
                    value={newAddress.company}
                    onChange={(e) => setNewAddress(prev => ({ ...prev, company: e.target.value }))}
                    placeholder="Company Name"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="street">Street Address</Label>
                  <Input
                    id="street"
                    value={newAddress.street}
                    onChange={(e) => setNewAddress(prev => ({ ...prev, street: e.target.value }))}
                    placeholder="123 Main Street"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="street2">Apartment, Suite, etc. (Optional)</Label>
                  <Input
                    id="street2"
                    value={newAddress.street2}
                    onChange={(e) => setNewAddress(prev => ({ ...prev, street2: e.target.value }))}
                    placeholder="Apt 4B, Suite 100"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={newAddress.city}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, city: e.target.value }))}
                      placeholder="New York"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="state">State/Province</Label>
                    <Input
                      id="state"
                      value={newAddress.state}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, state: e.target.value }))}
                      placeholder="NY"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="zip">ZIP/Postal Code</Label>
                    <Input
                      id="zip"
                      value={newAddress.zip}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, zip: e.target.value }))}
                      placeholder="10001"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Country</Label>
                    <Select value={newAddress.country} onValueChange={(value) => setNewAddress(prev => ({ ...prev, country: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="US">United States</SelectItem>
                        <SelectItem value="CA">Canada</SelectItem>
                        <SelectItem value="GB">United Kingdom</SelectItem>
                        <SelectItem value="FR">France</SelectItem>
                        <SelectItem value="DE">Germany</SelectItem>
                        <SelectItem value="IT">Italy</SelectItem>
                        <SelectItem value="ES">Spain</SelectItem>
                        <SelectItem value="AU">Australia</SelectItem>
                        <SelectItem value="JP">Japan</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number (Optional)</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={newAddress.phone}
                    onChange={(e) => setNewAddress(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="+****************"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="instructions">Delivery Instructions (Optional)</Label>
                  <Input
                    id="instructions"
                    value={newAddress.deliveryInstructions}
                    onChange={(e) => setNewAddress(prev => ({ ...prev, deliveryInstructions: e.target.value }))}
                    placeholder="Leave with doorman, Ring doorbell twice"
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isDefault"
                    checked={newAddress.isDefault}
                    onCheckedChange={(checked) => setNewAddress(prev => ({ ...prev, isDefault: !!checked }))}
                  />
                  <Label htmlFor="isDefault">Set as default shipping address</Label>
                </div>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setIsAddingAddress(false);
                      setEditingAddress(null);
                      resetForm();
                    }}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={editingAddress ? handleUpdateAddress : handleAddAddress} 
                    disabled={isLoading}
                  >
                    {isLoading 
                      ? (editingAddress ? "Updating..." : "Adding...") 
                      : (editingAddress ? "Update Address" : "Add Address")
                    }
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        
        <div className="grid gap-4">
          {addresses.map((address) => (
            <Card key={address._id} className={cn(
              "relative",
              address.isDefault && "ring-2 ring-blue-500 dark:ring-blue-400"
            )}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="text-neutral-600 dark:text-neutral-400 mt-1">
                      {getAddressIcon(address.type)}
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-black dark:text-white">
                          {address.firstName} {address.lastName}
                        </p>
                        {address.company && (
                          <Badge variant="outline" className="text-xs">
                            {address.company}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="text-sm text-neutral-600 dark:text-neutral-400 whitespace-pre-line">
                        {formatAddress(address)}
                      </div>
                      
                      {address.phone && (
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          📞 {address.phone}
                        </p>
                      )}
                      
                      {address.deliveryInstructions && (
                        <p className="text-sm text-blue-600 dark:text-blue-400">
                          📝 {address.deliveryInstructions}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {address.isDefault && (
                      <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                        Default
                      </Badge>
                    )}
                    
                    {!address.isDefault && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetDefault(address._id)}
                      >
                        Set Default
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditAddress(address)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveAddress(address._id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {addresses.length === 0 && (
            <Card className="border-dashed">
              <CardContent className="p-12 text-center">
                <Truck className="w-12 h-12 text-neutral-300 dark:text-neutral-700 mx-auto mb-4" />
                <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                  No shipping addresses added yet
                </p>
                <Button onClick={() => setIsAddingAddress(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Address
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <Separator />

      {/* Shipping Preferences */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-black dark:text-white">
          Shipping Preferences
        </h3>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Delivery Options</CardTitle>
            <CardDescription>
              Set your preferences for package delivery
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox id="signature" />
              <Label htmlFor="signature">Require signature for delivery</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox id="insurance" defaultChecked />
              <Label htmlFor="insurance">Include shipping insurance for high-value items</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox id="notifications" defaultChecked />
              <Label htmlFor="notifications">Send SMS notifications for delivery updates</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox id="weekend" />
              <Label htmlFor="weekend">Allow weekend delivery</Label>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
