"use client";

import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/hooks/useBetterAuth";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Separator } from "@repo/ui/components/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Calendar } from "@repo/ui/components/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { CalendarIcon, Camera, Save, User, Upload } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@repo/ui/lib/utils";
import { toast } from "sonner";
import { ProfileImage } from "@/components/common";

export function ProfileSettings() {
  const { user, refreshSession } = useAuth();
  const updateProfile = useMutation(api.userManagement.updateUserProfile);
  const userProfileData = useQuery(api.userManagement.getCurrentUserProfile);
  const generateUploadUrl = useMutation(api.userManagement.generateProfileImageUploadUrl);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingPhoto, setIsUploadingPhoto] = useState(false);
  const [tempImageUrl, setTempImageUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: undefined as Date | undefined,
    gender: '',
    bio: '',
    location: '',
    website: '',
    instagram: '',
    twitter: '',
    linkedin: '',
    facebook: '',
    occupation: '',
    company: '',
  });

  // Initialize form data when user and profile are loaded
  useEffect(() => {
    if (userProfileData) {
      setProfileData(prev => ({
        ...prev,
        firstName: userProfileData.name?.split(' ')[0] || '',
        lastName: userProfileData.name?.split(' ').slice(1).join(' ') || '',
        email: userProfileData.email || '',
        phone: user?.phone || '', // phone is in the user object from useAuth
      }));
    } else if (user) {
      // Fallback to auth user data
      setProfileData(prev => ({
        ...prev,
        firstName: user.name?.split(' ')[0] || '',
        lastName: user.name?.split(' ').slice(1).join(' ') || '',
        email: user.email || '',
        phone: user.phone || '',
      }));
    }
  }, [userProfileData, user]);

  // Load extended profile data when available
  useEffect(() => {
    if (userProfileData) {
      setProfileData(prev => ({
        ...prev,
        dateOfBirth: userProfileData.profile?.dateOfBirth ? new Date(userProfileData.profile.dateOfBirth) : undefined,
        gender: userProfileData.profile?.gender || '',
        bio: userProfileData.profile?.bio || '',
        location: userProfileData.profile?.location || '',
        website: userProfileData.profile?.website || '',
        instagram: userProfileData.profile?.instagram || '',
        twitter: userProfileData.profile?.twitter || '',
        linkedin: userProfileData.profile?.linkedin || '',
        facebook: userProfileData.profile?.facebook || '',
        occupation: userProfileData.profile?.occupation || '',
        company: userProfileData.profile?.company || '',
      }));
    }
  }, [userProfileData]);

  const handleInputChange = (field: string, value: string) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
  };

  // Helper function to convert empty strings to undefined for optional fields
  const getValueOrUndefined = (value: string | undefined) => {
    return value && value.trim() !== '' ? value.trim() : undefined;
  };

  const handlePhotoUpload = async (file: File) => {
    if (!file) return;
    
    console.log('Starting photo upload:', file.name, file.size, file.type);
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }
    
    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size must be less than 5MB');
      return;
    }

    setIsUploadingPhoto(true);
    try {
      // Generate a pre-signed URL from Convex
      const uploadUrl = await generateUploadUrl();
      
      // Upload the file directly to Convex storage as raw data
      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: file, // Send file directly, not as FormData
        headers: {
          'Content-Type': file.type, // Set the correct content type
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get the storage ID from the response
      const result = await response.json();
      const storageId = result.storageId;
      
      console.log('File uploaded successfully! Storage ID:', storageId);
      
      // Update profile with the storage ID
      await updateProfile({
        profileImage: storageId,
      });
      
      // Immediately show the uploaded image by creating a temporary URL
      const tempImageUrl = URL.createObjectURL(file);
      setTempImageUrl(tempImageUrl);
      
      toast.success('Profile photo updated successfully!');
      
      // The profile data will be automatically updated on the next query refresh
      // No need to manually refresh the page
    } catch (error) {
      console.error('Failed to upload photo:', error);
      toast.error('Failed to upload photo');
    } finally {
      setIsUploadingPhoto(false);
    }
  };

  const handlePhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handlePhotoUpload(file);
    }
    // Reset the input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Combine first and last name
      const fullName = `${profileData.firstName.trim()} ${profileData.lastName.trim()}`.trim();
      
      const result = await updateProfile({
        name: fullName,
        phone: getValueOrUndefined(profileData.phone),
        // Extended profile fields - only send if they have values
        bio: getValueOrUndefined(profileData.bio),
        location: getValueOrUndefined(profileData.location),
        dateOfBirth: profileData.dateOfBirth ? profileData.dateOfBirth.getTime() : undefined,
        gender: profileData.gender && profileData.gender !== '' ? profileData.gender as "male" | "female" | "non-binary" | "prefer-not-to-say" : undefined,
        website: getValueOrUndefined(profileData.website),
        instagram: getValueOrUndefined(profileData.instagram),
        twitter: getValueOrUndefined(profileData.twitter),
        linkedin: getValueOrUndefined(profileData.linkedin),
        facebook: getValueOrUndefined(profileData.facebook),
        occupation: getValueOrUndefined(profileData.occupation),
        company: getValueOrUndefined(profileData.company),
      });
      
      // Show success toast instead of refreshing session
      toast.success("Profile updated successfully!");
      
      // Note: The profile data will be automatically updated on the next query refresh
      // No need to manually refresh the session or page
    } catch (error) {
      console.error("Failed to update profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Profile Photo Section */}
      <div className="flex items-center space-x-8">
        <ProfileImage
          storageId={user?.profileImage}
          betterAuthImage={user?.image}
          name={userProfileData?.name || user?.name}
          size="xl"
          className="ring-4 ring-neutral-100 dark:ring-neutral-800 shadow-lg"
          tempImageUrl={tempImageUrl || undefined}
        />
        <div className="space-y-3">
          <h3 className="text-xl font-medium text-neutral-900 dark:text-neutral-100">
            Profile Photo
          </h3>
          <p className="text-neutral-600 dark:text-neutral-400 font-light leading-relaxed">
            Upload a professional photo to personalize your account and build trust with other members
          </p>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="rounded-xl border-neutral-300 hover:border-neutral-900 dark:border-neutral-600 dark:hover:border-neutral-100 transition-colors"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploadingPhoto}
            >
              {isUploadingPhoto ? (
                <>
                  <Upload className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Camera className="w-4 h-4 mr-2" />
                  Change Photo
                </>
              )}
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handlePhotoChange}
              className="hidden"
            />
          </div>
        </div>
      </div>

      <Separator />

      {/* Personal Information */}
      <div className="space-y-8">
        <h3 className="text-xl font-medium text-neutral-900 dark:text-neutral-100 tracking-tight">
          Personal Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={profileData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              placeholder="Enter your first name"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={profileData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              placeholder="Enter your last name"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              value={profileData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Enter your email"
              disabled // Usually email is not editable
              className="bg-neutral-50 dark:bg-neutral-800"
            />
            <p className="text-xs text-neutral-500">
              Contact support to change your email address
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              value={profileData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="+****************"
            />
          </div>
          
          <div className="space-y-2">
            <Label>Date of Birth</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !profileData.dateOfBirth && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {profileData.dateOfBirth ? (
                    format(profileData.dateOfBirth, "PPP")
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={profileData.dateOfBirth}
                  onSelect={(date) => setProfileData(prev => ({ ...prev, dateOfBirth: date }))}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="gender">Gender</Label>
            <Select value={profileData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Male</SelectItem>
                <SelectItem value="female">Female</SelectItem>
                <SelectItem value="non-binary">Non-binary</SelectItem>
                <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              value={profileData.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              placeholder="City, State/Country"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              type="url"
              value={profileData.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              placeholder="https://yourwebsite.com"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="occupation">Occupation</Label>
            <Input
              id="occupation"
              value={profileData.occupation}
              onChange={(e) => handleInputChange('occupation', e.target.value)}
              placeholder="Your job title or role"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="company">Company</Label>
            <Input
              id="company"
              value={profileData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              placeholder="Your company or organization"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="bio">Bio</Label>
          <Textarea
            id="bio"
            value={profileData.bio}
            onChange={(e) => handleInputChange('bio', e.target.value)}
            placeholder="Tell us a bit about yourself..."
            rows={4}
            maxLength={500}
          />
          <p className="text-xs text-neutral-500 text-right">
            {profileData.bio.length}/500 characters
          </p>
        </div>
      </div>

      <Separator />

      {/* Social Media */}
      <div className="space-y-8">
        <h3 className="text-xl font-medium text-neutral-900 dark:text-neutral-100 tracking-tight">
          Social Media
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="instagram">Instagram</Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-neutral-500 text-sm">
                @
              </span>
              <Input
                id="instagram"
                value={profileData.instagram}
                onChange={(e) => handleInputChange('instagram', e.target.value)}
                placeholder="username"
                className="pl-8"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="twitter">Twitter</Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-neutral-500 text-sm">
                @
              </span>
              <Input
                id="twitter"
                value={profileData.twitter}
                onChange={(e) => handleInputChange('twitter', e.target.value)}
                placeholder="username"
                className="pl-8"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="linkedin">LinkedIn</Label>
            <Input
              id="linkedin"
              value={profileData.linkedin}
              onChange={(e) => handleInputChange('linkedin', e.target.value)}
              placeholder="linkedin.com/in/username"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="facebook">Facebook</Label>
            <Input
              id="facebook"
              value={profileData.facebook}
              onChange={(e) => handleInputChange('facebook', e.target.value)}
              placeholder="facebook.com/username"
            />
          </div>
        </div>
      </div>

      <Separator />

      {/* Save Button */}
      <div className="flex justify-end pt-4">
        <Button 
          onClick={handleSave} 
          disabled={isLoading} 
          className="px-8 py-2.5 bg-neutral-900 hover:bg-neutral-800 dark:bg-neutral-100 dark:hover:bg-neutral-200 dark:text-neutral-900 rounded-xl font-medium transition-colors"
        >
          <Save className="w-4 h-4 mr-2" />
          {isLoading ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </div>
  );
}
