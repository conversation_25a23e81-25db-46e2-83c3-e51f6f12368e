"use client";

import { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { 
  CreditCard, 
  Plus, 
  Trash2, 
  Edit, 
  Shield,
  Calendar,
  DollarSign,
  FileText,
  Download
} from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { toast } from "sonner";

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'bank';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  name: string;
}

const mockPaymentMethods: PaymentMethod[] = [
  {
    id: '1',
    type: 'card',
    last4: '4242',
    brand: 'visa',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: true,
    name: 'Visa ending in 4242'
  },
  {
    id: '2',
    type: 'card',
    last4: '1234',
    brand: 'mastercard',
    expiryMonth: 8,
    expiryYear: 2024,
    isDefault: false,
    name: 'Mastercard ending in 1234'
  }
];

const mockInvoices = [
  {
    id: 'INV-001',
    date: '2024-01-15',
    amount: 2450.00,
    status: 'paid',
    description: 'Louis Vuitton Neverfull MM'
  },
  {
    id: 'INV-002',
    date: '2024-01-10',
    amount: 890.00,
    status: 'paid',
    description: 'Gucci Ace Sneakers'
  },
  {
    id: 'INV-003',
    date: '2023-12-28',
    amount: 3200.00,
    status: 'paid',
    description: 'Rolex Datejust'
  }
];

export function BillingSettings() {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>(mockPaymentMethods);
  const [isAddingCard, setIsAddingCard] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [newCard, setNewCard] = useState({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvc: '',
    name: '',
    billingAddress: {
      street: '',
      city: '',
      state: '',
      zip: '',
      country: 'US'
    }
  });

  const handleAddCard = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement add payment method
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Payment method added successfully!");
      setIsAddingCard(false);
      setNewCard({
        cardNumber: '',
        expiryMonth: '',
        expiryYear: '',
        cvc: '',
        name: '',
        billingAddress: {
          street: '',
          city: '',
          state: '',
          zip: '',
          country: 'US'
        }
      });
    } catch (error) {
      toast.error("Failed to add payment method");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveCard = async (id: string) => {
    try {
      setPaymentMethods(prev => prev.filter(pm => pm.id !== id));
      toast.success("Payment method removed");
    } catch (error) {
      toast.error("Failed to remove payment method");
    }
  };

  const handleSetDefault = async (id: string) => {
    try {
      setPaymentMethods(prev => 
        prev.map(pm => ({ ...pm, isDefault: pm.id === id }))
      );
      toast.success("Default payment method updated");
    } catch (error) {
      toast.error("Failed to update default payment method");
    }
  };

  const getCardIcon = (brand: string) => {
    switch (brand?.toLowerCase()) {
      case 'visa':
        return '💳';
      case 'mastercard':
        return '💳';
      case 'amex':
        return '💳';
      default:
        return '💳';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="space-y-8">
      {/* Payment Methods */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Payment Methods
          </h3>
          <Dialog open={isAddingCard} onOpenChange={setIsAddingCard}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Payment Method
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Add New Payment Method</DialogTitle>
                <DialogDescription>
                  Add a new credit or debit card to your account
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6 py-4">
                <div className="space-y-2">
                  <Label htmlFor="cardNumber">Card Number</Label>
                  <Input
                    id="cardNumber"
                    placeholder="1234 5678 9012 3456"
                    value={newCard.cardNumber}
                    onChange={(e) => setNewCard(prev => ({ ...prev, cardNumber: e.target.value }))}
                  />
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Expiry Month</Label>
                    <Select value={newCard.expiryMonth} onValueChange={(value) => setNewCard(prev => ({ ...prev, expiryMonth: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="MM" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 12 }, (_, i) => (
                          <SelectItem key={i + 1} value={String(i + 1).padStart(2, '0')}>
                            {String(i + 1).padStart(2, '0')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Expiry Year</Label>
                    <Select value={newCard.expiryYear} onValueChange={(value) => setNewCard(prev => ({ ...prev, expiryYear: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="YYYY" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 10 }, (_, i) => (
                          <SelectItem key={i} value={String(new Date().getFullYear() + i)}>
                            {new Date().getFullYear() + i}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="cvc">CVC</Label>
                    <Input
                      id="cvc"
                      placeholder="123"
                      value={newCard.cvc}
                      onChange={(e) => setNewCard(prev => ({ ...prev, cvc: e.target.value }))}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="cardName">Cardholder Name</Label>
                  <Input
                    id="cardName"
                    placeholder="John Doe"
                    value={newCard.name}
                    onChange={(e) => setNewCard(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h4 className="font-medium">Billing Address</h4>
                  
                  <div className="space-y-2">
                    <Label htmlFor="street">Street Address</Label>
                    <Input
                      id="street"
                      placeholder="123 Main St"
                      value={newCard.billingAddress.street}
                      onChange={(e) => setNewCard(prev => ({ 
                        ...prev, 
                        billingAddress: { ...prev.billingAddress, street: e.target.value }
                      }))}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        placeholder="New York"
                        value={newCard.billingAddress.city}
                        onChange={(e) => setNewCard(prev => ({ 
                          ...prev, 
                          billingAddress: { ...prev.billingAddress, city: e.target.value }
                        }))}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        placeholder="NY"
                        value={newCard.billingAddress.state}
                        onChange={(e) => setNewCard(prev => ({ 
                          ...prev, 
                          billingAddress: { ...prev.billingAddress, state: e.target.value }
                        }))}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="zip">ZIP Code</Label>
                      <Input
                        id="zip"
                        placeholder="10001"
                        value={newCard.billingAddress.zip}
                        onChange={(e) => setNewCard(prev => ({ 
                          ...prev, 
                          billingAddress: { ...prev.billingAddress, zip: e.target.value }
                        }))}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Country</Label>
                      <Select value={newCard.billingAddress.country} onValueChange={(value) => setNewCard(prev => ({ 
                        ...prev, 
                        billingAddress: { ...prev.billingAddress, country: value }
                      }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="US">United States</SelectItem>
                          <SelectItem value="CA">Canada</SelectItem>
                          <SelectItem value="GB">United Kingdom</SelectItem>
                          <SelectItem value="FR">France</SelectItem>
                          <SelectItem value="DE">Germany</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <Button variant="outline" onClick={() => setIsAddingCard(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddCard} disabled={isLoading}>
                    {isLoading ? "Adding..." : "Add Card"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        
        <div className="grid gap-4">
          {paymentMethods.map((method) => (
            <Card key={method.id} className={cn(
              "relative",
              method.isDefault && "ring-2 ring-blue-500 dark:ring-blue-400"
            )}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">
                      {getCardIcon(method.brand || '')}
                    </div>
                    <div>
                      <p className="font-medium text-black dark:text-white">
                        {method.name}
                      </p>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        Expires {method.expiryMonth}/{method.expiryYear}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {method.isDefault && (
                      <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                        Default
                      </Badge>
                    )}
                    
                    {!method.isDefault && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetDefault(method.id)}
                      >
                        Set Default
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveCard(method.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* Billing History */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Billing History
          </h3>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export All
          </Button>
        </div>
        
        <div className="space-y-4">
          {mockInvoices.map((invoice) => (
            <Card key={invoice.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="font-medium text-black dark:text-white">
                      {invoice.description}
                    </p>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      {invoice.id} • {new Date(invoice.date).toLocaleDateString()}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="font-semibold text-black dark:text-white">
                        {formatCurrency(invoice.amount)}
                      </p>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                        {invoice.status}
                      </Badge>
                    </div>
                    
                    <Button variant="outline" size="sm">
                      <FileText className="w-4 h-4 mr-2" />
                      View
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* Security Notice */}
      <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">
                Secure Payment Processing
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-400">
                All payment information is encrypted and processed securely through our certified payment partners. 
                We never store your complete card details on our servers.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
