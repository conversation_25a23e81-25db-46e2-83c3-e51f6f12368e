"use client";

import { ReactNode } from "react";
import { MarketplaceHeader } from "@/components/marketplace/MarketplaceHeader";
import { useRouter } from "next/navigation";

interface ProductLayoutProps {
  children: ReactNode;
  className?: string;
  showSearchBar?: boolean;
}

export function ProductLayout({ 
  children, 
  className = "",
  showSearchBar = true 
}: ProductLayoutProps) {
  const router = useRouter();

  const handleSearch = (query: string) => {
    // Navigate to marketplace with search query
    const searchParams = new URLSearchParams();
    if (query.trim()) {
      searchParams.set('search', query.trim());
    }
    
    const searchString = searchParams.toString();
    const url = searchString ? `/marketplace?${searchString}` : '/marketplace';
    router.push(url);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900">
      {/* Header */}
      <MarketplaceHeader onSearch={showSearchBar ? handleSearch : undefined} />
      
      {/* Main Content */}
      <main className={`${className}`}>
        {children}
      </main>
    </div>
  );
}
