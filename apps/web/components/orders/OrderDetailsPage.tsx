"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Separator } from "@repo/ui/components/separator";
import { FloatingSupportButton } from "@/components/marketplace/FloatingSupportButton";
import { 
  Package, 
  Truck, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Calendar, 
  DollarSign, 
  MapPin, 
  Clock,
  ArrowLeft,
  User,
  Star
} from "lucide-react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import { MarketplaceHeader } from "../marketplace/MarketplaceHeader";

interface OrderDetailsPageProps {
  orderId: string;
}

export function OrderDetailsPage({ orderId }: OrderDetailsPageProps) {
  const { user } = useAuth();
  
  // Query order details
  const orderData = useQuery(api.orderQueries.getOrderDetails, {
    orderId: orderId as any,
    includePaymentInfo: true,
  });

  const isLoading = orderData === undefined;

  // Status badge styling
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300", icon: Clock },
      confirmed: { color: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300", icon: CheckCircle },
      shipped: { color: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300", icon: Truck },
      delivered: { color: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300", icon: Package },
      cancelled: { color: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300", icon: XCircle },
      disputed: { color: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300", icon: AlertTriangle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1 px-3 py-1`}>
        <Icon className="w-4 h-4" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="p-8 text-center">
          <CardContent>
            <p className="text-neutral-600 dark:text-neutral-400">Please sign in to view order details.</p>
            <Button asChild className="mt-4">
              <Link href="/login">Sign In</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="w-8 h-8" />
          <Skeleton className="h-8 w-64" />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <Skeleton className="w-24 h-24" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                    <Skeleton className="h-3 w-1/4" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-24" />
              </CardHeader>
              <CardContent className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!orderData) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card className="p-8 text-center">
          <CardContent>
            <Package className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
              Order not found
            </h3>
            <p className="text-neutral-500 dark:text-neutral-400 mb-6">
              The order you're looking for doesn't exist or you don't have permission to view it.
            </p>
            <Button asChild>
              <Link href="/orders">Back to Orders</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const order = orderData;

  return (
    <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
      <MarketplaceHeader />
      <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/orders" className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            Back to Orders
          </Link>
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-black dark:text-white">
            Order #{order.orderNumber}
          </h1>
          <p className="text-neutral-600 dark:text-neutral-400">
            Placed on {formatDate(order.orderDate)}
          </p>
        </div>
        {getStatusBadge(order.orderStatus)}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Product Information */}
          <Card>
            <CardHeader>
              <CardTitle>Product Details</CardTitle>
            </CardHeader>
            <CardContent>
              {order.product ? (
                <div className="flex gap-4">
                  <div className="w-24 h-24 bg-neutral-100 dark:bg-neutral-800 rounded-lg overflow-hidden flex-shrink-0">
                    {order.product.images?.[0] ? (
                      <img
                        src={order.product.images[0]}
                        alt={order.product.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Package className="w-8 h-8 text-neutral-400" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg text-black dark:text-white">
                      {order.product.title}
                    </h3>
                    <p className="text-neutral-600 dark:text-neutral-400">
                      {order.product.brand}
                    </p>
                    <p className="text-sm text-neutral-500 dark:text-neutral-400">
                      Condition: {order.product.condition}
                    </p>
                    {order.product.size && (
                      <p className="text-sm text-neutral-500 dark:text-neutral-400">
                        Size: {order.product.size}
                      </p>
                    )}
                    {order.product.color && (
                      <p className="text-sm text-neutral-500 dark:text-neutral-400">
                        Color: {order.product.color}
                      </p>
                    )}
                    <Button asChild variant="outline" size="sm" className="mt-2">
                      <Link href={`/marketplace/product/${order.product._id}`}>
                        View Product
                      </Link>
                    </Button>
                  </div>
                </div>
              ) : (
                <p className="text-neutral-500 dark:text-neutral-400">Product information unavailable</p>
              )}
            </CardContent>
          </Card>

          {/* Seller Information */}
          {order.seller && (
            <Card>
              <CardHeader>
                <CardTitle>Seller Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-neutral-200 dark:bg-neutral-700 rounded-full flex items-center justify-center">
                    <User className="w-6 h-6 text-neutral-600 dark:text-neutral-400" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-black dark:text-white">
                      {order.seller.businessName || order.seller.name}
                    </h4>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm font-medium">{order.seller.rating.toFixed(1)}</span>
                      </div>
                      <span className="text-sm text-neutral-500">
                        ({order.seller.reviewCount} reviews)
                      </span>
                      <Badge
                        variant={order.seller.verificationStatus === "approved" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {order.seller.verificationStatus === "approved" ? "Verified" : "Unverified"}
                      </Badge>
                    </div>
                    <Button asChild variant="outline" size="sm" className="mt-2">
                      <Link href={`/seller/${order.seller._id}`}>
                        View Seller Profile
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Shipping Information */}
          {order.shippingAddress && (
            <Card>
              <CardHeader>
                <CardTitle>Shipping Address</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-3">
                  <MapPin className="w-5 h-5 text-neutral-500 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-black dark:text-white">{order.shippingAddress.name}</p>
                    <p className="text-neutral-600 dark:text-neutral-400">{order.shippingAddress.street}</p>
                    <p className="text-neutral-600 dark:text-neutral-400">
                      {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
                    </p>
                    <p className="text-neutral-600 dark:text-neutral-400">{order.shippingAddress.country}</p>
                    {order.shippingAddress.phone && (
                      <p className="text-neutral-600 dark:text-neutral-400">{order.shippingAddress.phone}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Tracking Information */}
          {order.trackingNumber && (
            <Card>
              <CardHeader>
                <CardTitle>Tracking Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Truck className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
                    <div>
                      <p className="font-medium text-black dark:text-white">
                        Tracking Number: <span className="font-mono">{order.trackingNumber}</span>
                      </p>
                      {order.carrier && (
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          Carrier: {order.carrier}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {order.estimatedDelivery && (
                    <div className="flex items-center gap-3">
                      <Calendar className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        Estimated Delivery: {formatDate(order.estimatedDelivery)}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-neutral-600 dark:text-neutral-400">Product Price</span>
                <span className="font-medium">{formatCurrency(order.totalAmount || 0)}</span>
              </div>
              
              {order.shippingCost && order.shippingCost > 0 && (
                <div className="flex justify-between">
                  <span className="text-neutral-600 dark:text-neutral-400">Shipping</span>
                  <span className="font-medium">{formatCurrency(order.shippingCost)}</span>
                </div>
              )}
              
              {order.tax && order.tax > 0 && (
                <div className="flex justify-between">
                  <span className="text-neutral-600 dark:text-neutral-400">Tax</span>
                  <span className="font-medium">{formatCurrency(order.tax)}</span>
                </div>
              )}
              
              <Separator />
              
              <div className="flex justify-between text-lg font-bold">
                <span>Total</span>
                <span>{formatCurrency(order.totalAmount)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Order Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Order Timeline</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <div>
                    <p className="text-sm font-medium">Order Placed</p>
                    <p className="text-xs text-neutral-500">{formatDate(order.orderDate)}</p>
                  </div>
                </div>

                {order.confirmedDate && (
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <div>
                      <p className="text-sm font-medium">Order Confirmed</p>
                      <p className="text-xs text-neutral-500">{formatDate(order.confirmedDate)}</p>
                    </div>
                  </div>
                )}

                {order.shippedDate && (
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <div>
                      <p className="text-sm font-medium">Order Shipped</p>
                      <p className="text-xs text-neutral-500">{formatDate(order.shippedDate)}</p>
                    </div>
                  </div>
                )}

                {order.deliveredDate && (
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <div>
                      <p className="text-sm font-medium">Order Delivered</p>
                      <p className="text-xs text-neutral-500">{formatDate(order.deliveredDate)}</p>
                    </div>
                  </div>
                )}

                {order.cancelledDate && (
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full" />
                    <div>
                      <p className="text-sm font-medium">Order Cancelled</p>
                      <p className="text-xs text-neutral-500">{formatDate(order.cancelledDate)}</p>
                      {order.cancellationReason && (
                        <p className="text-xs text-neutral-500 mt-1">
                          Reason: {order.cancellationReason}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {order.userPermissions?.canCancel && (
                <Button variant="destructive" size="sm" className="w-full">
                  Cancel Order
                </Button>
              )}
              
              {order.userPermissions?.canConfirmDelivery && (
                <Button variant="default" size="sm" className="w-full">
                  Confirm Delivery
                </Button>
              )}
              
              {order.userPermissions?.canDispute && (
                <Button variant="outline" size="sm" className="w-full">
                  Report Issue
                </Button>
              )}
              
              <Button variant="outline" size="sm" className="w-full">
                Contact Support
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
      <FloatingSupportButton />
    </div>
  );
}
