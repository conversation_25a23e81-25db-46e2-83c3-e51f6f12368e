"use client";

import { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { Skeleton } from "@repo/ui/components/skeleton";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Search, Package, Truck, CheckCircle, XCircle, AlertTriangle, Calendar, DollarSign, MapPin, Clock, MoreHorizontal, Eye, Plus, Filter, Crown, Grid3X3, List } from "lucide-react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import { MarketplaceHeader } from "../marketplace/MarketplaceHeader";

interface OrdersPageProps {
  className?: string;
}

type OrderStatus = "pending" | "confirmed" | "shipped" | "delivered" | "cancelled" | "disputed" | "all";
type SortBy = "newest" | "oldest" | "amount_high" | "amount_low" | "status";
type ViewMode = "table" | "grid";

export function OrdersPage({ className }: OrdersPageProps) {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<OrderStatus>("all");
  const [sortBy, setSortBy] = useState<SortBy>("newest");
  const [viewMode, setViewMode] = useState<ViewMode>("table");
  const [currentPage, setCurrentPage] = useState(0);
  
  const limit = 10;
  const offset = currentPage * limit;

  // Query user's orders
  const ordersData = useQuery(api.orderQueries.getBuyerOrders, {
    status: statusFilter,
    searchQuery: searchQuery.trim() || undefined,
    sortBy,
    limit,
    offset,
  });

  const isLoading = ordersData === undefined;
  const orders = ordersData?.orders || [];
  const summary = ordersData?.summary;
  const pagination = ordersData?.pagination;

  // Status badge styling - matching inventory page
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300", icon: Clock },
      confirmed: { color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300", icon: CheckCircle },
      shipped: { color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300", icon: Truck },
      delivered: { color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300", icon: Package },
      cancelled: { color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300", icon: XCircle },
      disputed: { color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300", icon: AlertTriangle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge 
        variant="outline" 
        className={`${config.color} text-xs font-light rounded-xl flex items-center gap-1 px-2 py-1`}
      >
        <Icon className="w-3 h-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (!user) {
    return (
      <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
        <MarketplaceHeader />
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center max-w-md">
            <CardContent>
              <Package className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                Sign in to view orders
              </h3>
              <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                Create an account to view your order history and track purchases.
              </p>
              <Button asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
        <MarketplaceHeader />
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading orders...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
      <MarketplaceHeader />
      <div className={`max-w-7xl mx-auto p-6 space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">My Orders</h1>
            <p className="text-neutral-600 dark:text-neutral-400 mt-1">
              {summary ? `${summary.total} orders • ${formatCurrency(summary.totalSpent)} total spent` : 'Track your purchases and order history'}
            </p>
          </div>
          
          <Button 
            asChild
            className="bg-primary text-primary-foreground hover:bg-primary/90 font-light rounded-xl px-6 py-2 transition-all duration-300"
          >
            <Link href="/marketplace">
              <Plus className="w-4 h-4 mr-2" />
              SHOP MORE
            </Link>
          </Button>
        </div>

        {/* Controls */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                <Input
                  placeholder="Search orders by product, brand, or order number..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex items-center gap-3">
                {/* Status Filter */}
                <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as OrderStatus)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="disputed">Disputed</SelectItem>
                  </SelectContent>
                </Select>

                {/* Sort */}
                <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortBy)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="oldest">Oldest First</SelectItem>
                    <SelectItem value="amount_high">Amount: High to Low</SelectItem>
                    <SelectItem value="amount_low">Amount: Low to High</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                  </SelectContent>
                </Select>

                {/* View Mode */}
                <div className="flex border rounded-lg overflow-hidden">
                  <Button
                    variant={viewMode === "table" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("table")}
                    className="rounded-none"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-none"
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Content */}
        {orders && orders.length > 0 ? (
          viewMode === "table" ? (
            // Table view
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="border-b border-border">
                      <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Order</TableHead>
                      <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Product</TableHead>
                      <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Order #</TableHead>
                      <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Date</TableHead>
                      <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Amount</TableHead>
                      <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Status</TableHead>
                      <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Tracking</TableHead>
                      <TableHead className="w-12"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orders.map((order) => (
                      <TableRow key={order._id} className="hover:bg-primary/5 transition-colors duration-300">
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-primary/5 rounded-xl overflow-hidden border border-border">
                              {order.product?.images && order.product.images[0] ? (
                                <img
                                  src={order.product.images[0]}
                                  alt={order.product.title}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    const img = e.target as HTMLImageElement;
                                    const fallback = img.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                                    img.style.display = 'none';
                                    if (fallback) {
                                      fallback.classList.remove('hidden');
                                      fallback.classList.add('flex');
                                    }
                                  }}
                                />
                              ) : (
                                <div className="w-full h-full bg-primary/5 flex items-center justify-center">
                                  <Package className="w-4 h-4 text-muted-foreground" />
                                </div>
                              )}
                              <div className="absolute inset-0 items-center justify-center fallback-icon hidden">
                                <Package className="w-4 h-4 text-muted-foreground" />
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-light text-foreground text-sm">{order.product?.title || "Product Unavailable"}</p>
                            <p className="text-muted-foreground text-xs font-light">{order.product?.brand} • {order.product?.condition}</p>
                          </div>
                        </TableCell>
                        <TableCell className="font-light text-muted-foreground">#{order.orderNumber}</TableCell>
                        <TableCell className="font-light text-muted-foreground">{formatDate(order.orderDate)}</TableCell>
                        <TableCell className="font-light text-primary">{formatCurrency(order.totalAmount)}</TableCell>
                        <TableCell>
                          {getStatusBadge(order.orderStatus)}
                        </TableCell>
                        <TableCell className="font-light text-muted-foreground">
                          {order.trackingNumber ? (
                            <div className="flex items-center gap-1">
                              <Truck className="w-4 h-4" />
                              <span className="font-mono text-xs">{order.trackingNumber}</span>
                            </div>
                          ) : (
                            "—"
                          )}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="p-1 h-8 w-8">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/orders/${order._id}`}>
                                  <Eye className="w-4 h-4 mr-2" />
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                              {order.product && (
                                <DropdownMenuItem asChild>
                                  <Link href={`/marketplace/product/${order.product._id}`}>
                                    <Package className="w-4 h-4 mr-2" />
                                    View Product
                                  </Link>
                                </DropdownMenuItem>
                              )}
                              {order.seller && (
                                <DropdownMenuItem asChild>
                                  <Link href={`/seller/${order.seller._id}`}>
                                    <Crown className="w-4 h-4 mr-2" />
                                    View Seller
                                  </Link>
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            // Grid view
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {orders.map((order) => (
                <Card key={order._id} className="group hover:shadow-lg transition-shadow duration-200">
                  <CardContent className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <Link href={`/orders/${order._id}`}>
                        <div className="relative aspect-square bg-neutral-100 dark:bg-neutral-800">
                          {order.product?.images && order.product.images[0] ? (
                            <>
                              <img
                                src={order.product.images[0]}
                                alt={order.product.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                                onError={(e) => {
                                  const img = e.target as HTMLImageElement;
                                  const fallback = img.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                                  img.style.display = 'none';
                                  if (fallback) {
                                    fallback.classList.remove('hidden');
                                    fallback.classList.add('flex');
                                  }
                                }}
                              />
                              <div className="absolute inset-0 items-center justify-center fallback-icon hidden">
                                <Package className="w-16 h-16 text-neutral-400" />
                              </div>
                            </>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="w-16 h-16 text-neutral-400" />
                            </div>
                          )}
                        </div>
                      </Link>

                      {/* Status badge */}
                      <div className="absolute top-3 left-3">
                        {getStatusBadge(order.orderStatus)}
                      </div>

                      {/* Order number badge */}
                      <Badge className="absolute top-3 right-3 bg-black/80 text-white text-xs">
                        #{order.orderNumber}
                      </Badge>
                    </div>

                    <div className="p-4 space-y-2">
                      <div className="space-y-1">
                        <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                          {order.product?.brand || "Unknown Brand"}
                        </p>
                        <Link href={`/orders/${order._id}`}>
                          <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                            {order.product?.title || "Product Unavailable"}
                          </h3>
                        </Link>
                      </div>

                      <div className="flex items-center justify-between">
                        <p className="text-lg font-bold text-black dark:text-white">
                          {formatCurrency(order.totalAmount)}
                        </p>
                        
                        <p className="text-xs text-neutral-600 dark:text-neutral-400">
                          {formatDate(order.orderDate)}
                        </p>
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Button asChild variant="outline" size="sm" className="flex-1">
                          <Link href={`/orders/${order._id}`}>
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )
        ) : (
          // Empty state
          <Card>
            <CardContent className="p-12 text-center">
              {searchQuery || statusFilter !== "all" ? (
                <>
                  <Search className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                    No orders match your filters
                  </h3>
                  <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                    Try adjusting your search or filter criteria to find what you're looking for.
                  </p>
                  <Button onClick={() => { setSearchQuery(""); setStatusFilter("all"); }} variant="outline">
                    Clear Filters
                  </Button>
                </>
              ) : (
                <>
                  <Package className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                    No orders found
                  </h3>
                  <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                    Start shopping to see your orders here. Browse our luxury marketplace to find amazing products.
                  </p>
                  <Button asChild>
                    <Link href="/marketplace">
                      <Plus className="w-4 h-4 mr-2" />
                      START SHOPPING
                    </Link>
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {pagination && pagination.total > pagination.limit && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Showing {pagination.offset + 1} to {Math.min(pagination.offset + pagination.limit, pagination.total)} of {pagination.total} orders
            </p>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                disabled={currentPage === 0}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={!pagination.hasMore}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
