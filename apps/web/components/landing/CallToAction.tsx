"use client";

import { But<PERSON> } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { useAuth } from "@/hooks/useBetterAuth";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";

interface CallToActionProps {
  onSubscribe: () => void;
  onSellerApplication: () => void;
  userApplication?: any;
  showDetails?: boolean;
}

export function CallToAction({ onSubscribe, onSellerApplication, userApplication, showDetails }: CallToActionProps) {
  const { user } = useAuth();
  return (
    <section className="py-20 px-6 bg-background">
      <div className="container mx-auto max-w-4xl">
        {/* Main CTA Card */}
        <Card className="bg-card border-border p-8 mb-8 shadow-sm">
          <div className="text-center">
            <h2 className="text-2xl font-light text-card-foreground mb-4">
              Subscribe here to gain access
            </h2>
            <p className="mb-8 max-w-2xl mx-auto font-light">
              Get unlimited access to our curated marketplace of authenticated luxury goods for just $20/month.
            </p>
            <Button 
              size="lg"
              className="bg-primary text-primary-foreground hover:bg-accent font-light px-8"
              onClick={onSubscribe}
            >
              Start Your Subscription
            </Button>
          </div>
        </Card>

        {/* Seller CTA */}
        <div className="text-center">
          <p className="mb-4 font-light text-primary">
            Are you an industry leading seller?
          </p>
          
          {userApplication ? (
            // Show application status for existing users
            <div className="space-y-4">
              <div className="bg-muted/20 border border-border rounded-lg p-6 max-w-md mx-auto">
                <h3 className="text-lg font-medium text-foreground mb-2">Application Status</h3>
                <div className="text-sm text-muted-foreground space-y-2">
                  <p><strong>Status:</strong> {userApplication.status === "pending" ? "Under Review" : userApplication.status}</p>
                  <p><strong>Submitted:</strong> {new Date(userApplication.submittedAt).toLocaleDateString()}</p>
                  <p><strong>Business:</strong> {userApplication.businessName}</p>
                </div>
                
                {showDetails && (
                  <div className="mt-4 pt-4 border-t border-border">
                    <h4 className="font-medium text-foreground mb-2">Application Details</h4>
                    <div className="text-sm text-muted-foreground space-y-2">
                      <p><strong>Business Type:</strong> {userApplication.businessType}</p>
                      <p><strong>Specialties:</strong> {userApplication.specialties?.join(", ") || "N/A"}</p>
                      <p><strong>Monthly Volume:</strong> {userApplication.monthlyVolume}</p>
                      {userApplication.notes && (
                        <p><strong>Notes:</strong> {userApplication.notes}</p>
                      )}
                      {userApplication.rejectionReason && (
                        <p><strong>Rejection Reason:</strong> {userApplication.rejectionReason}</p>
                      )}
                    </div>
                  </div>
                )}
                
                <Button 
                  variant="outline"
                  size="sm"
                  onClick={onSellerApplication}
                  className="mt-4 w-full"
                >
                  {showDetails ? "Hide Details" : "View Full Details"}
                </Button>
              </div>
            </div>
          ) : (
            // Show apply button for new users
            <Button 
              variant="outline"
              size="lg"
              onClick={onSellerApplication}
              className="text-xs"
            >
              Apply to become a verified seller here
            </Button>
          )}
        </div>
      </div>
    </section>
  );
}


