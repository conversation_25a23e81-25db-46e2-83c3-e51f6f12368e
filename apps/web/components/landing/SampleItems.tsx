"use client";

import { Badge } from "@repo/ui/src/components/badge";
import { Lock, Rocket } from "lucide-react";
import Image from "next/image";

const sampleItems = [
  {
    title: "Hermès Birkin 30",
    subtitle: "Togo Gold Hardware",
    price: "$****",
    image: "/purse2.png"
  },
  {
    title: "Rolex Daytona",
    subtitle: "116500LN White Dial",
    price: "$*****",
    image: "/watch2.png"
  },
  {
    title: "LV Card Holder",
    subtitle: "Monogram Eclipse",
    price: "$***",
    image: "/card.png"
  },
  {
    title: "Chanel No. 5",
    subtitle: "Eau de Parfum 100ml",
    price: "$***",
    image: "/bottle.png"
  },
  {
    title: "Dior B23 High-Top",
    subtitle: "Oblique Canvas",
    price: "$****",
    image: "/pants.png"
  }
];

export function SampleItems() {
  return (
    <section className="py-0 md:py-16 px-6 bg-background">
      <div className="container mx-auto">
        <div className="text-center mb-10">
          <h2 className="text-base font-medium text-muted-foreground tracking-tight mb-2 uppercase" style={{ letterSpacing: "0.02em" }}>
            A Curated Sneak Peek
          </h2>
        </div>
        {/* Responsive grid, no horizontal scroll, cards are wider and spaced out */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 md:gap-10 justify-center items-stretch max-w-7xl mx-auto pb-2">
          {sampleItems.map((item, index) => (
            <div
              key={index}
              className="relative bg-card rounded-2xl shadow-lg w-full flex flex-col hover:shadow-xl transition-shadow duration-200 mx-2 my-4"
              style={{
                pointerEvents: "none",
                minWidth: "240px",
                maxWidth: "100%",
                width: "100%",
                flex: "1 1 0%"
              }}
            >
              {/* Product Image */}
              <div className="relative overflow-hidden rounded-t-2xl bg-muted" style={{ height: "220px" }}>
                <Image
                  src={item.image}
                  alt={item.title}
                  fill
                  style={{
                    objectFit: "cover",
                    filter: "blur(4px) brightness(0.95)",
                    opacity: 0.75
                  }}
                  draggable={false}
                  sizes="(max-width: 768px) 100vw, 240px"
                  priority={index < 2}
                />
                {/* Lock icon overlay */}
                <div className="absolute bottom-3 right-3 bg-primary/90 rounded-full p-1.5 flex items-center justify-center shadow">
                  <Lock className="w-5 h-5 text-primary-foreground" />
                </div>
              </div>
              {/* Product Info */}
              <div className="px-7 pt-5 pb-7 flex flex-col gap-2">
                <h3 className="text-lg font-light text-card-foreground mb-0 line-clamp-2">{item.title}</h3>
                <p className="text-sm text-muted mb-1">{item.subtitle}</p>
                <div className="flex justify-between items-center mt-1">
                  <span
                    className="text-4xl font-light text-card-foreground select-none"
                    style={{ filter: "blur(8px)", opacity: 0.7 }}
                  >
                    {item.price}
                  </span>
                </div>
                <Badge className="text-xs mt-2">
                  <Rocket className="w-4 h-4 mr-1" />
                  <span className="text-xs">Express Ship</span>
                </Badge>
              </div>
            </div>
          ))}
        </div>
        {/* Subscribe prompt */}
        <div className="text-center mt-12">
          <p className="text-muted-foreground text-lg font-light">
            Subscribe to unlock full access to our curated marketplace
          </p>
        </div>
      </div>
    </section>
  );
}
