"use client";

import Image from "next/image";

const brands = [
  { name: "Chrome Hearts", logo: "/logos/ch.png" },
  { name: "<PERSON><PERSON><PERSON>", logo: "/logos/h2.png" },
  { name: "<PERSON>", logo: "/logos/s.png" },
  { name: "<PERSON>", logo: "/logos/nike.png" },
  { name: "<PERSON><PERSON>", logo: "/logos/dior.png" },
  { name: "<PERSON><PERSON>", logo: "/logos/chanel.png" },
  { name: "<PERSON>", logo: "/logos/lv.png" },
  { name: "Goyard", logo: "/logos/goyard.png" },
  { name: "<PERSON><PERSON>", logo: "/logos/gucci.png" },
  { name: "<PERSON>", logo: "/logos/jordan.png" }
];

export function BrandCarousel() {
  return (
    <section className="py-2 bg-secondary overflow-hidden">
      <div className="relative">
        {/* Moving brand logos - infinite loop */}
        <div className="w-full">
          <div
            className="flex space-x-16 animate-[marquee_30s_linear_infinite]"
            style={{
              width: "max-content",
            }}
          >
            {[...brands, ...brands].map((brand, index) => (
              <div
                key={index}
                className="flex-shrink-0 flex items-center justify-center w-32 h-16"
              >
                <Image
                  src={brand.logo}
                  alt={brand.name}
                  width={48}
                  height={48}
                  className="object-contain opacity-70 hover:opacity-90 transition-opacity grayscale"
                  style={{ maxHeight: "48px", width: "auto" }}
                  priority={index < brands.length}
                />
              </div>
            ))}
          </div>
        </div>
        {/* Gradient overlays for smooth infinite scroll effect */}
        <div className="pointer-events-none absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-secondary to-transparent z-10" />
        <div className="pointer-events-none absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-secondary to-transparent z-10" />
      </div>
      <style jsx global>{`
        @keyframes marquee {
          0% {
            transform: translateX(0%);
          }
          100% {
            transform: translateX(-50%);
          }
        }
      `}</style>
    </section>
  );
}
