"use client";

import { X, <PERSON>, ChevronRight } from "lucide-react";
import { SupportScreen } from "./types";
import { SupportBottomNavigation } from "./SupportBottomNavigation";

interface SupportHelpScreenProps {
  onClose: () => void;
  onNavigate: (screen: SupportScreen) => void;
  adminUsers?: Array<{
    _id: string;
    name: string;
    profileImage?: string;
  }>;
  unreadCount?: number;
}

export function SupportHelpScreen({ 
  onClose, 
  onNavigate, 
  adminUsers,
  unreadCount
}: SupportHelpScreenProps) {
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-background px-4 py-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-foreground">Help</h3>
          <button 
            onClick={onClose}
            className="p-1 hover:bg-muted rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-muted-foreground" />
          </button>
        </div>
      </div>
      
      <div className="flex-1 bg-background overflow-y-auto">
        {/* Search Bar */}
        <div className="p-4 border-b border-border">
          <div className="relative">
            <input 
              type="text" 
              placeholder="Search for help"
              className="w-full p-3 pl-4 pr-10 border border-border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all bg-background text-foreground placeholder:text-muted-foreground"
            />
            <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          </div>
        </div>
        
        {/* Help Categories - Will be populated from Convex */}
        <div className="divide-y divide-border">
          {/* This will be populated with actual help categories from the admin panel */}
          <div className="p-4 hover:bg-muted/50 cursor-pointer">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="font-medium text-foreground">Help Categories</div>
                <div className="text-sm text-muted-foreground">Articles will appear here</div>
              </div>
              <ChevronRight className="w-4 h-4 text-muted-foreground flex-shrink-0" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Bottom Navigation */}
      <SupportBottomNavigation
        activeScreen="help"
        onNavigate={onNavigate}
        unreadCount={unreadCount}
      />
    </div>
  );
}
