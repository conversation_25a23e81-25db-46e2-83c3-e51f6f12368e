"use client";

import { Home, MessageCircle, HelpCircle } from "lucide-react";
import { SupportScreen } from "./types";

interface SupportBottomNavigationProps {
  activeScreen: SupportScreen;
  onNavigate: (screen: SupportScreen) => void;
  unreadCount?: number;
}

export function SupportBottomNavigation({ 
  activeScreen, 
  onNavigate, 
  unreadCount 
}: SupportBottomNavigationProps) {
  return (
    <div className="bg-background border-t border-border px-4 py-3">
      <div className="flex justify-around">
        <button 
          onClick={() => onNavigate("home")}
          className={`flex flex-col items-center py-2 px-4 ${activeScreen === "home" ? "text-primary" : "text-muted-foreground"}`}
        >
          <Home className="w-5 h-5 mb-1" />
          <span className="text-xs font-medium">Home</span>
        </button>
        
        <button 
          onClick={() => onNavigate("messages")}
          className={`flex flex-col items-center py-2 px-4 relative ${activeScreen === "messages" || activeScreen === "conversation" ? "text-primary" : "text-muted-foreground"}`}
        >
          <div className="relative">
            <MessageCircle className="w-5 h-5 mb-1" />
            {unreadCount !== undefined && unreadCount !== null && unreadCount > 0 && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                {unreadCount > 9 ? "9+" : unreadCount}
              </div>
            )}
          </div>
          <span className="text-xs font-medium">Messages</span>
        </button>
        
        {/* TODO: Create Help Screen */}
        {/* <button 
          onClick={() => onNavigate("help")}
          className={`flex flex-col items-center py-2 px-4 ${activeScreen === "help" ? "text-primary" : "text-muted-foreground"}`}
        >
          <HelpCircle className="w-5 h-5 mb-1" />
          <span className="text-xs font-medium">Help</span>
        </button> */}
      </div>
    </div>
  );
}
