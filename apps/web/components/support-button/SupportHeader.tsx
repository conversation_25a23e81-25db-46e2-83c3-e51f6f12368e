"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, MoreHori<PERSON><PERSON>, <PERSON> } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";

interface SupportHeaderProps {
  title?: string;
  onClose: () => void;
  onBack?: () => void;
  showBackButton?: boolean;
  adminUsers?: Array<{
    _id: string;
    name: string;
    profileImage?: string;
  }>;
  showTeamAvatars?: boolean;
  showStatus?: boolean;
}

export function SupportHeader({ 
  title, 
  onClose, 
  onBack, 
  showBackButton = false,
  adminUsers,
  showTeamAvatars = false,
  showStatus = false
}: SupportHeaderProps) {
  return (
    <div className="bg-background px-4 py-3 border-b border-border">
      <div className="flex items-center space-x-3">
        {showBackButton && onBack && (
          <button 
            onClick={onBack}
            className="p-1 hover:bg-muted rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-muted-foreground" />
          </button>
        )}
        
        {showTeamAvatars && adminUsers && (
          <div className="flex -space-x-2">
            {adminUsers.slice(0, 3).map((admin, index) => (
              <div key={admin._id} className="w-10 h-10 bg-muted rounded-full border-2 border-background overflow-hidden">
                {admin.profileImage ? (
                  <img 
                    src={admin.profileImage} 
                    alt={admin.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center">
                    <span className="text-primary-foreground font-medium text-xs">
                      {admin.name.split(' ').map((n, index) => (
                        <span key={`${admin._id}-initial-${index}`}>{n[0]}</span>
                      )).join('').toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
        
        <div className="flex-1">
          <h3 className="font-semibold text-foreground">{title || "Support"}</h3>
          {showStatus && (
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <Clock className="w-3 h-3" />
              Back tomorrow
            </p>
          )}
        </div>
        
        <button className="p-1 hover:bg-muted rounded-full transition-colors">
          <MoreHorizontal className="w-5 h-5 text-muted-foreground" />
        </button>
        
        <button 
          onClick={onClose}
          className="p-1 hover:bg-muted rounded-full transition-colors"
        >
          <X className="w-5 h-5 text-muted-foreground" />
        </button>
      </div>
    </div>
  );
}
