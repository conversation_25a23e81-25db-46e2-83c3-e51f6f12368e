"use client";

import { useState, useEffect } from "react";
import { Send, ChevronRight, User, Clock, CheckCheck } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { SupportScreen } from "./types";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { motion, AnimatePresence } from "framer-motion";

interface UserConversationScreenProps {
  onClose: () => void;
  onNavigate: (screen: SupportScreen) => void;
  conversation: any;
  onSendMessage: (message: string) => Promise<void>;
}

export function UserConversationScreen({ 
  onClose, 
  onNavigate, 
  conversation,
  onSendMessage
}: UserConversationScreenProps) {
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [expandedMessages, setExpandedMessages] = useState<Set<string>>(new Set());
  const { user } = useAuth();

  // Fetch messages for this conversation
  const messages = useQuery(
    api.messages.getMessagesByConversationId,
    conversation.conversationId ? {
      conversationId: conversation.conversationId,
    } : "skip"
  );

  // Mark messages as read when conversation is opened
  const markAsRead = useMutation(api.messages.markMessagesAsRead);

  useEffect(() => {
    if (conversation.conversationId) {
      markAsRead({ conversationId: conversation.conversationId });
    }
  }, [conversation.conversationId, markAsRead]);

  const handleSendMessage = async () => {
    if (!message.trim()) return;
    
    setIsLoading(true);
    try {
      await onSendMessage(message);
      setMessage("");
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleMessageExpansion = (messageId: string) => {
    setExpandedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const renderHeader = () => {
    const otherParticipant = conversation.otherParticipant;
    const product = conversation.product;
    
    return (
      <div className="border-b border-border bg-card p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button 
              onClick={() => onNavigate("messages")}
              className="p-1 hover:bg-muted rounded-full transition-colors"
            >
              <ChevronRight className="w-4 h-4 text-muted-foreground rotate-180" />
            </button>
            
            <div className="flex items-center space-x-3">
              <Avatar className="w-10 h-10">
                <AvatarImage 
                  src={otherParticipant?.profileImage} 
                  alt={otherParticipant?.name || 'User'} 
                />
                <AvatarFallback>
                  {getInitials(otherParticipant?.name || 'User')}
                </AvatarFallback>
              </Avatar>
              
              <div>
                <h3 className="font-medium text-foreground">
                  {otherParticipant?.name || otherParticipant?.email || 'Unknown User'}
                </h3>
                <p className="text-sm text-muted-foreground font-light">
                  {otherParticipant?.userType === 'consumer' ? 'Buyer' : 'Seller'}
                </p>
              </div>
            </div>
          </div>
          
          {product && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <User className="w-4 h-4" />
              <span>{product.name}</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderMessages = () => {
    if (!messages) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="w-12 h-12 text-muted-foreground/50 mb-3 flex items-center justify-center">
            <Send className="w-8 h-8" />
          </div>
          <h4 className="font-medium text-foreground mb-2">Loading messages...</h4>
        </div>
      );
    }

    if (messages.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="w-12 h-12 text-muted-foreground/50 mb-3 flex items-center justify-center">
            <Send className="w-8 h-8" />
          </div>
          <h4 className="font-medium text-foreground mb-2">No messages yet</h4>
          <p className="text-sm text-muted-foreground">
            Start the conversation by sending a message
          </p>
        </div>
      );
    }

    return (
      <div className="flex-1 overflow-y-auto p-4 space-y-3 bg-gradient-to-b from-background to-muted/10">
        {messages.map((msg) => {
          // Debug: Log the IDs to see what we're comparing
          console.log('Message Debug:', {
            messageId: msg._id,
            messageSenderId: msg.senderId,
            currentUserId: user?._id,
            currentUserUserId: user?.userId,
            isOwnMessage: msg.senderId === user?._id,
            isOwnMessageAlt: msg.senderId === user?.userId
          });
          
          // Try both possible user ID fields
          const isOwnMessage = msg.senderId === user?._id || msg.senderId === user?.userId;
          const isExpanded = expandedMessages.has(msg._id);
          
          return (
            <motion.div
              key={msg._id}
              className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} group`}
              layout
            >
              <div className={`flex flex-col max-w-[75%] ${isOwnMessage ? 'items-end' : 'items-start'}`}>
                <div className={`flex items-end gap-2 ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
                  {/* Avatar for received messages */}
                  {!isOwnMessage && (
                    <Avatar className="flex-shrink-0">
                      <AvatarImage 
                        src={conversation.otherParticipant?.profileImage} 
                        alt="User" 
                      />
                      <AvatarFallback className="text-xs">
                        {getInitials(conversation.otherParticipant?.name || 'User')}
                      </AvatarFallback>
                    </Avatar>
                  )}
                  
                  {/* Message bubble */}
                  <motion.div 
                    className={`relative rounded-2xl px-4 py-2.5 shadow-sm transition-all duration-200 cursor-pointer ${
                      isOwnMessage
                        ? 'bg-primary text-primary-foreground ml-2 rounded-br-md'
                        : 'bg-card text-foreground border border-border/50 mr-2 rounded-bl-md hover:border-border'
                    }`}
                    onClick={() => toggleMessageExpansion(msg._id)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <p
                      className={`text-sm leading-relaxed break-words ${isOwnMessage ? 'text-white text-right' : 'text-foreground text-left'}`}
                      style={isOwnMessage ? { marginLeft: 'auto' } : {}}
                    >
                      {msg.content}
                    </p>
                  </motion.div>
                </div>
                
                {/* Expanded metadata below bubble */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, height: 0, y: -5 }}
                      animate={{ opacity: 1, height: "auto", y: 0 }}
                      exit={{ opacity: 0, height: 0, y: -5 }}
                      transition={{ duration: 0.2, ease: "easeInOut" }}
                      className={`mt-1 px-2 py-1.5 rounded-lg backdrop-blur-sm ${
                        isOwnMessage ? 'text-right' : 'text-left'
                      }`}
                    >
                      <div className={`flex flex-col gap-1 text-xs text-muted-foreground ${
                        isOwnMessage ? 'justify-end' : 'justify-start'
                      }`}>
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span className="font-light">
                            {formatDistanceToNow(msg._creationTime, { addSuffix: true })}
                          </span>
                        </div>
                        
                        {isOwnMessage && (
                          <div className={`flex items-center ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                            {msg.isRead ? (
                              <div className="flex items-center gap-1">
                                <CheckCheck className="w-3 h-3" />
                                <span>Read</span>
                              </div>
                            ) : (
                              <div className="flex items-center gap-1 font-light">
                                <span>Delivered</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {renderHeader()}
      
      {renderMessages()}
      
      {/* Message Input */}
      <div className="p-4 border-t border-border bg-background">
        <div className="relative">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type your message..."
            className="w-full p-3 pl-4 pr-24 border-2 border-border rounded-full bg-background text-foreground placeholder:text-muted-foreground focus:border-primary focus:outline-none transition-colors"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />
          
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-2">
            <button 
              onClick={handleSendMessage}
              disabled={!message.trim() || isLoading}
              className="p-1.5 bg-primary text-primary-foreground rounded-full hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Send className="w-3 h-3" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
