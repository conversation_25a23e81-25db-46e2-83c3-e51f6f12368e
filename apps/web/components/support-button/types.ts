import type { Id } from "@repo/backend/convex/_generated/dataModel";

export type SupportScreen = "home" | "messages" | "help" | "compose" | "conversation" | "userConversation";

export interface SupportButtonProps {
  isOpen: boolean;
  onToggle: () => void;
  activeScreen: SupportScreen;
  onNavigate: (screen: SupportScreen) => void;
}

// More flexible types that match the actual Convex return types
export interface Conversation {
  _id: Id<"conversations">;
  conversationId: string;
  participantIds: Id<"users">[];
  productId?: Id<"products">;
  lastMessageId?: Id<"messages">;
  lastMessageAt: number;
  lastMessagePreview: string;
  participant1Id: Id<"users">;
  participant2Id: Id<"users">;
  participant1UnreadCount: number;
  participant2UnreadCount: number;
  isActive: boolean;
  updatedAt: number;
  otherParticipant?: {
    _id: Id<"users">;
    _creationTime: number;
    name: string;
    email: string;
    profileImage?: string;
    userType: "consumer" | "seller" | "admin";
    subscriptionPlan?: "basic" | "premium" | "enterprise";
    subscriptionExpiresAt?: number;
    isVerified?: boolean;
    updatedAt: number;
  } | null;
  product?: any;
  unreadCount: number;
}

export interface Message {
  _id: Id<"messages">;
  _creationTime: number;
  conversationId: string;
  senderId: Id<"users">;
  recipientId: Id<"users">;
  productId?: Id<"products">;
  content: string;
  messageType: "text" | "image" | "offer";
  isRead: boolean;
  metadata?: {
    offerAmount?: number;
    imageUrl?: string;
  };
  updatedAt: number;
  sender?: {
    _id: Id<"users">;
    _creationTime: number;
    name: string;
    email: string;
    profileImage?: string;
    userType: "consumer" | "seller" | "admin";
    subscriptionPlan?: "basic" | "premium" | "enterprise";
    subscriptionExpiresAt?: number;
    isVerified?: boolean;
    updatedAt: number;
  } | null;
  recipient?: {
    _id: Id<"users">;
    _creationTime: number;
    name: string;
    email: string;
    profileImage?: string;
    userType: "consumer" | "seller" | "admin";
    subscriptionPlan?: "basic" | "premium" | "enterprise";
    subscriptionExpiresAt?: number;
    isVerified?: boolean;
    updatedAt: number;
  } | null;
  product?: any;
}

// Support item that represents a support ticket
export interface SupportItem {
  _id: Id<"supportTickets">;
  _creationTime: number;
  lastMessagePreview: string;
  lastMessageAt: number;
  unreadCount: number;
  
  // Ticket-specific fields
  ticketNumber: string;
  subject: string;
  status: string;
  priority: string;
  category: string;
  ticket: any;
}

// Support message within a ticket
export interface SupportMessage {
  _id: Id<"supportMessages">;
  ticketId: Id<"supportTickets">;
  senderId: Id<"users">;
  senderType: "customer" | "admin";
  content: string;
  messageType: string;
  updatedAt: number;
  sender?: {
    _id: Id<"users">;
    name: string;
    email: string;
    profileImage?: string;
    userType: "consumer" | "seller" | "admin";
  } | null;
}
