# Support Button Components

This folder contains the modular components for the floating support button feature. The components are split into smaller, more manageable pieces for better maintainability and reusability.

## Structure

```
support-button/
├── index.ts                    # Main exports
├── types.ts                    # TypeScript types and interfaces
├── FloatingSupportButton.tsx   # Main orchestrator component
├── SupportHomeScreen.tsx       # Home screen component
├── SupportMessagesScreen.tsx   # Messages list screen
├── SupportHelpScreen.tsx       # Help and search screen
├── SupportComposeScreen.tsx    # Compose new message screen
├── SupportConversationScreen.tsx # Individual conversation screen
├── SupportHeader.tsx           # Reusable header component
├── SupportBottomNavigation.tsx # Reusable bottom navigation
└── README.md                   # This file
```

## Components

### FloatingSupportButton
The main orchestrator component that manages state and renders the appropriate screen based on user interaction.

**Props:** None (self-contained)

**Features:**
- Manages popup open/close state
- Handles screen navigation
- Manages conversation selection
- Handles message sending
- Integrates with Convex backend

### SupportHomeScreen
The initial screen users see when opening the support button.

**Props:**
- `onClose: () => void` - Function to close the popup
- `onNavigate: (screen: SupportScreen) => void` - Function to navigate to other screens
- `adminUsers?: AdminUser[]` - Array of admin users to display

### SupportMessagesScreen
Displays a list of conversations and allows users to open them.

**Props:**
- `onClose: () => void` - Function to close the popup
- `onNavigate: (screen: SupportScreen) => void` - Function to navigate to other screens
- `conversations?: Conversation[]` - Array of conversations to display
- `onOpenConversation: (conversation: Conversation) => void` - Function to open a conversation

### SupportHelpScreen
Provides help content and search functionality.

**Props:**
- `onClose: () => void` - Function to close the popup
- `onNavigate: (screen: SupportScreen) => void` - Function to navigate to other screens
- `adminUsers?: AdminUser[]` - Array of admin users to display

### SupportComposeScreen
Allows users to compose and send new messages.

**Props:**
- `onClose: () => void` - Function to close the popup
- `onNavigate: (screen: SupportScreen) => void` - Function to navigate to other screens
- `onSendMessage: (message: string) => Promise<void>` - Function to send a message
- `adminUsers?: AdminUser[]` - Array of admin users to display

### SupportConversationScreen
Displays an individual conversation with message history.

**Props:**
- `onClose: () => void` - Function to close the popup
- `onNavigate: (screen: SupportScreen) => void` - Function to navigate to other screens
- `conversation: Conversation` - The conversation to display
- `messages?: Message[]` - Array of messages in the conversation
- `onSendMessage: (message: string) => Promise<void>` - Function to send a message

### SupportHeader
A reusable header component used across different screens.

**Props:**
- `title?: string` - The title to display
- `onClose: () => void` - Function to close the popup
- `onBack?: () => void` - Optional function to go back
- `showBackButton?: boolean` - Whether to show the back button
- `adminUsers?: AdminUser[]` - Array of admin users to display as avatars
- `showTeamAvatars?: boolean` - Whether to show team avatars
- `showStatus?: boolean` - Whether to show status information

### SupportBottomNavigation
A reusable bottom navigation component.

**Props:**
- `activeScreen: SupportScreen` - The currently active screen
- `onNavigate: (screen: SupportScreen) => void` - Function to navigate to other screens
- `unreadCount?: number` - Number of unread messages

## Types

### SupportScreen
```typescript
type SupportScreen = "home" | "messages" | "help" | "compose" | "conversation";
```

### Conversation
```typescript
interface Conversation {
  _id: string;
  otherParticipant?: {
    _id: string;
    name: string;
    profileImage?: string;
  };
  lastMessagePreview: string;
  lastMessageAt: string;
  unreadCount: number;
  productId?: string;
}
```

### Message
```typescript
interface Message {
  _id: string;
  content: string;
  senderId: string;
  recipientId: string;
  messageType: string;
  productId?: string;
}
```

## Usage

To use the support button in your application, simply import and use the `FloatingSupportButton` component:

```tsx
import { FloatingSupportButton } from "@/components/support-button";

export default function MyPage() {
  return (
    <div>
      {/* Your page content */}
      <FloatingSupportButton />
    </div>
  );
}
```

## Benefits of This Structure

1. **Modularity**: Each screen is a separate component, making it easier to maintain and test
2. **Reusability**: Components like `SupportHeader` and `SupportBottomNavigation` can be reused
3. **Type Safety**: Strong TypeScript types ensure consistency across components
4. **Separation of Concerns**: Each component has a single responsibility
5. **Easier Testing**: Individual components can be tested in isolation
6. **Better Performance**: Components can be optimized individually
7. **Easier Debugging**: Issues can be isolated to specific components

## Future Enhancements

- Add animations between screen transitions
- Implement real-time message updates
- Add file upload functionality
- Add emoji picker
- Add message reactions
- Add typing indicators
- Add message search functionality
