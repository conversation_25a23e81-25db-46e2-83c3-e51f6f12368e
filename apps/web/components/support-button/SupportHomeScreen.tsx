"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { SupportScreen } from "./types";
import { SupportBottomNavigation } from "./SupportBottomNavigation";

interface SupportHomeScreenProps {
  onClose: () => void;
  onNavigate: (screen: SupportScreen) => void;
  adminUsers?: Array<{
    _id: string;
    name: string;
    profileImage?: string;
  }>;
  unreadCount?: number;
}

export function SupportHomeScreen({ 
  onClose, 
  onNavigate, 
  adminUsers,
  unreadCount
}: SupportHomeScreenProps) {
  return (
    <div className="flex flex-col h-full">
      {/* Header with gradient fade */}
      <div className="relative">
        {/* Main header background with built-in gradient */}
        <div className="bg-primary text-primary-foreground px-6 pt-8 pb-24 relative z-10">
          <button 
            onClick={onClose}
            className="absolute top-4 right-4 p-2 hover:bg-accent hover:bg-opacity-50 rounded-full transition-colors z-20"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="mb-6">
            <div className="flex -space-x-2 mb-2">
              {adminUsers && adminUsers.length > 0 ? (
                adminUsers.map((admin, index) => (
                  <div key={admin._id} className="w-12 h-12 bg-muted rounded-full border-2 border-primary-foreground overflow-hidden">
                    {admin.profileImage ? (
                      <img 
                        src={admin.profileImage} 
                        alt={admin.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center">
                        <span className="text-primary-foreground font-medium text-sm">
                          {admin.name.split(' ').map((n, index) => (
                            <span key={`${admin._id}-initial-${index}`}>{n[0]}</span>
                          )).join('').toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <>
                  <div className="w-12 h-12 bg-muted rounded-full border-2 border-primary-foreground overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center">
                      <span className="text-primary-foreground font-medium text-sm">HS</span>
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-muted rounded-full border-2 border-primary-foreground overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center">
                      <span className="text-primary-foreground font-medium text-sm">CT</span>
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-muted rounded-full border-2 border-primary-foreground overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center">
                      <span className="text-primary-foreground font-medium text-sm">AM</span>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
          
          <h2 className="text-4xl font-normal mb-2">Hi there 👋</h2>
          <p className="text-2xl font-normal text-primary-foreground/80">How can we help?</p>
        </div>
      </div>
      
      {/* Content area - overlaps with fade */}
      <div className="flex-1 p-4 bg-background -mt-12 relative z-20">
        <div className="space-y-4">
          <button 
            onClick={() => onNavigate("compose")}
            className="w-full p-4 bg-card border border-border rounded-2xl hover:shadow-md transition-all duration-200 text-left group"
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="font-semibold text-foreground mb-1">Send us a message</div>
                <div className="text-sm text-muted-foreground">We typically reply in a few hours</div>
              </div>
              <ArrowRight className="w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors" />
            </div>
          </button>
  
          <button 
            onClick={() => onNavigate("help")}
            className="w-full p-4 bg-card border border-border rounded-xl hover:shadow-md transition-all duration-200 text-left group"
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="font-semibold text-foreground mb-1">Search for help</div>
              </div>
              <Search className="w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors" />
            </div>
          </button>
        </div>
      </div>
      
      {/* Bottom Navigation */}
      <SupportBottomNavigation
        activeScreen="home"
        onNavigate={onNavigate}
        unreadCount={unreadCount}
      />
    </div>
  );
}
