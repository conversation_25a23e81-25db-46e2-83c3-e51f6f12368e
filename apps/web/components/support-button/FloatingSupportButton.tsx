"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { MessageCircle, ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";
import { SupportScreen, SupportItem } from "./types";
import { SupportHomeScreen } from "./SupportHomeScreen";
import { SupportMessagesScreen } from "./SupportMessagesScreen";
import { SupportHelpScreen } from "./SupportHelpScreen";
import { SupportComposeScreen } from "./SupportComposeScreen";
import { SupportConversationScreen } from "./SupportConversationScreen";
import { SupportBottomNavigation } from "./SupportBottomNavigation";
import { UserConversationScreen } from "./UserConversationScreen";

export function FloatingSupportButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeScreen, setActiveScreen] = useState<SupportScreen>("home");
  const [selectedSupportItem, setSelectedSupportItem] = useState<SupportItem | null>(null);
  const [selectedUserConversation, setSelectedUserConversation] = useState<any>(null);
  const { user } = useAuth();

  const adminUsers = useQuery(api.userManagement.getAdminUsers, { limit: 5 });
  const unreadCount = useQuery(api.messages.getUnreadCount);
  
  const sendMessage = useMutation(api.messages.sendMessage);

  const togglePopup = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setActiveScreen("home");
    }
  };

  const navigateToScreen = (screen: SupportScreen) => {
    setActiveScreen(screen);
  };

  const openSupportItem = (supportItem: any) => {
    if (supportItem.type === "ticket") {
      setSelectedSupportItem(supportItem);
      setSelectedUserConversation(null);
      setActiveScreen("conversation");
    } else if (supportItem.type === "message") {
      setSelectedUserConversation(supportItem.conversation);
      setSelectedSupportItem(null);
      setActiveScreen("userConversation");
    }
  };

  const handleSendMessage = async (message: string) => {
    try {
      // Create a support ticket from the message
      // This will be handled by the SupportComposeScreen
      toast.success("Message sent! We'll get back to you soon.");
      setActiveScreen("home");
    } catch (error) {
      toast.error("Failed to send message. Please try again.");
      throw error;
    }
  };

  const handleSendMessageInConversation = async (message: string) => {
    // This is now handled directly in the SupportConversationScreen
    // No need to do anything here
  };

  const handleSendUserMessage = async (message: string) => {
    if (!selectedUserConversation) return;
    
    try {
      await sendMessage({
        recipientId: selectedUserConversation.otherParticipant._id,
        content: message,
        productId: selectedUserConversation.product?._id,
      });
    } catch (error) {
      console.error("Failed to send message:", error);
      toast.error("Failed to send message. Please try again.");
    }
  };

  const renderActiveScreen = () => {
    switch (activeScreen) {
      case "home":
        return (
          <SupportHomeScreen
            onClose={togglePopup}
            onNavigate={navigateToScreen}
            adminUsers={adminUsers}
            unreadCount={unreadCount}
          />
        );
      case "messages":
        return (
          <SupportMessagesScreen
            onClose={togglePopup}
            onNavigate={navigateToScreen}
            onOpenConversation={openSupportItem}
            unreadCount={unreadCount}
          />
        );
      case "compose":
        return (
          <SupportComposeScreen
            onClose={togglePopup}
            onNavigate={navigateToScreen}
            onSendMessage={handleSendMessage}
            adminUsers={adminUsers}
          />
        );
      case "conversation":
        return selectedSupportItem ? (
          <SupportConversationScreen
            onClose={togglePopup}
            onNavigate={navigateToScreen}
            conversation={{ type: "ticket", ticket: selectedSupportItem.ticket }}
            messages={[]} // No messages for support items
            onSendMessage={handleSendMessageInConversation}
          />
        ) : null;
      case "userConversation":
        return selectedUserConversation ? (
          <UserConversationScreen
            onClose={togglePopup}
            onNavigate={navigateToScreen}
            conversation={selectedUserConversation}
            onSendMessage={handleSendUserMessage}
          />
        ) : null;
      case "help":
        return (
          <SupportHelpScreen
            onClose={togglePopup}
            onNavigate={navigateToScreen}
            adminUsers={adminUsers}
            unreadCount={unreadCount}
          />
        );

      default:
        return (
          <SupportHomeScreen
            onClose={togglePopup}
            onNavigate={navigateToScreen}
            adminUsers={adminUsers}
            unreadCount={unreadCount}
          />
        );
    }
  };

  return (
    <>
      <div className="fixed bottom-0 right-0 z-50 p-4">
        {/* Popup Window - positioned above button */}
        <div 
          className={`absolute bottom-20 right-5 rounded-2xl shadow-md transition-all duration-300 overflow-hidden ${
            isOpen 
              ? 'opacity-100 transform translate-y-0 scale-100' 
              : 'opacity-0 transform translate-y-4 scale-95 pointer-events-none'
          }`}
          style={{ width: '400px', height: '700px' }}
        >
          {renderActiveScreen()}
        </div>

        {/* Single Animated Button */}
        <motion.button
          onClick={togglePopup}
          className="w-12 h-12 bg-primary text-accent rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center justify-center relative"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="chevron"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronDown className="w-5 h-5" />
              </motion.div>
            ) : (
              <motion.div
                key="message"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <MessageCircle className="w-5 h-5" />
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* Unread count badge - only show when not open */}
          {!isOpen && unreadCount !== undefined && unreadCount !== null && unreadCount > 0 && (
            <motion.div 
              className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
            >
              {unreadCount > 9 ? "9+" : unreadCount}
            </motion.div>
          )}
        </motion.button>
      </div>
    </>
  );
}
