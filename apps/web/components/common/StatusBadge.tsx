import { Badge } from "@repo/ui/components/badge";
import { AlertCircle, Clock, MessageCircle, CheckCheck, Package, User, Tag } from "lucide-react";

export interface StatusConfig {
  label: string;
  className: string;
  icon: React.ReactNode;
}

export interface StatusBadgeProps {
  status: string;
  type: 'status' | 'priority' | 'category';
  showIcon?: boolean;
  className?: string;
}

const STATUS_CONFIGS: Record<string, StatusConfig> = {
  // Status types
  open: {
    label: "Open",
    className: "bg-blue-100 text-blue-800",
    icon: <AlertCircle className="w-4 h-4" />
  },
  in_progress: {
    label: "In Progress",
    className: "bg-yellow-100 text-yellow-800",
    icon: <Clock className="w-4 h-4" />
  },
  waiting_for_customer: {
    label: "Waiting",
    className: "bg-orange-100 text-orange-800",
    icon: <MessageCircle className="w-4 h-4" />
  },
  resolved: {
    label: "Resolved",
    className: "bg-green-100 text-green-800",
    icon: <CheckCheck className="w-4 h-4" />
  },
  closed: {
    label: "Closed",
    className: "bg-gray-100 text-gray-800",
    icon: <CheckCheck className="w-4 h-4" />
  },
  
  // Priority types
  urgent: {
    label: "Urgent",
    className: "bg-red-100 text-red-800",
    icon: <AlertCircle className="w-4 h-4" />
  },
  high: {
    label: "High",
    className: "bg-orange-100 text-orange-800",
    icon: <AlertCircle className="w-4 h-4" />
  },
  medium: {
    label: "Medium",
    className: "bg-yellow-100 text-yellow-800",
    icon: <Clock className="w-4 h-4" />
  },
  low: {
    label: "Low",
    className: "bg-green-100 text-green-800",
    icon: <CheckCheck className="w-4 h-4" />
  },
  
  // Category types
  general: {
    label: "General",
    className: "bg-gray-100 text-gray-800",
    icon: <Tag className="w-4 h-4" />
  },
  technical: {
    label: "Technical",
    className: "bg-blue-100 text-blue-800",
    icon: <Package className="w-4 h-4" />
  },
  billing: {
    label: "Billing",
    className: "bg-green-100 text-green-800",
    icon: <Tag className="w-4 h-4" />
  },
  order_issue: {
    label: "Order Issue",
    className: "bg-orange-100 text-orange-800",
    icon: <Package className="w-4 h-4" />
  },
  product_issue: {
    label: "Product Issue",
    className: "bg-purple-100 text-purple-800",
    icon: <Package className="w-4 h-4" />
  },
  seller_issue: {
    label: "Seller Issue",
    className: "bg-red-100 text-red-800",
    icon: <User className="w-4 h-4" />
  },
  refund: {
    label: "Refund",
    className: "bg-yellow-100 text-yellow-800",
    icon: <Tag className="w-4 h-4" />
  },
  other: {
    label: "Other",
    className: "bg-gray-100 text-gray-800",
    icon: <Tag className="w-4 h-4" />
  }
};

export function StatusBadge({ status, type, showIcon = true, className = "" }: StatusBadgeProps) {
  const config = STATUS_CONFIGS[status] || {
    label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),
    className: "bg-gray-100 text-gray-800",
    icon: <Tag className="w-4 h-4" />
  };

  return (
    <Badge className={`${config.className} ${className}`}>
      {showIcon && (
        <div className="flex items-center space-x-1">
          {config.icon}
          <span className="capitalize">{config.label}</span>
        </div>
      )}
      {!showIcon && (
        <span className="capitalize">{config.label}</span>
      )}
    </Badge>
  );
}
