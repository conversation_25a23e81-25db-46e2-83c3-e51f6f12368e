import { ReactNode } from "react";
import { Inbox, MessageCircle } from "lucide-react";
import { Badge } from "@repo/ui/components/badge";

export interface ItemListProps {
  title: string;
  items: any[];
  searchQuery?: string;
  onItemSelect: (itemId: string) => void;
  selectedItemId?: string | null;
  renderItem: (item: any) => ReactNode;
  emptyStateIcon?: ReactNode;
  emptyStateMessage?: string;
  emptyStateSubMessage?: string;
  className?: string;
}

export function ItemList({
  title,
  items,
  searchQuery,
  onItemSelect,
  selectedItemId,
  renderItem,
  emptyStateIcon = <MessageCircle className="w-12 h-12 text-muted-foreground mx-auto mb-3" />,
  emptyStateMessage = "No items yet",
  emptyStateSubMessage,
  className = ""
}: ItemListProps) {
  return (
    <div className={`border-r border-border bg-card flex flex-col ${className}`}>
      <div className="p-4 border-b border-border shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm font-light text-primary/80 uppercase tracking-wide">
            <Inbox className="w-4 h-4" />
            {searchQuery ? 'Search Results' : title}
          </div>
          {searchQuery && items && (
            <Badge variant="secondary" className="text-xs">
              {items.length} result{items.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {items?.length === 0 && (
          <div className="p-6 text-center">
            {emptyStateIcon}
            <p className="text-muted-foreground font-light">
              {searchQuery ? 'No items found' : emptyStateMessage}
            </p>
            {searchQuery && emptyStateSubMessage && (
              <p className="text-xs text-muted-foreground mt-2">
                {emptyStateSubMessage}
              </p>
            )}
          </div>
        )}

        {items?.map((item, index) => (
          <div
            key={item._id || `item-${index}`}
            className={`p-4 border-b border-border cursor-pointer hover:bg-muted/50 transition-colors ${
              selectedItemId === item._id ? 'bg-muted' : ''
            }`}
            onClick={() => onItemSelect(item._id)}
          >
            {renderItem(item)}
          </div>
        ))}
      </div>
    </div>
  );
}
