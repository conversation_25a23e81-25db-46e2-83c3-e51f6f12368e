import { Avatar, AvatarFallback } from "@repo/ui/components/avatar";
import { Badge } from "@repo/ui/components/badge";
import { User, Package } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { StatusBadge } from "./StatusBadge";

export interface SupportTicket {
  _id: string;
  subject: string;
  description: string;
  ticketNumber: string;
  status: string;
  priority: string;
  category: string;
  _creationTime?: number;
  user?: {
    name?: string;
    email?: string;
  };
}

export interface SupportTicketItemProps {
  ticket: SupportTicket;
  className?: string;
}

export function SupportTicketItem({ ticket, className = "" }: SupportTicketItemProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`flex items-start space-x-3 ${className}`}>
      <Avatar className="w-10 h-10">
        <AvatarFallback>
          {getInitials(ticket.user?.name || 'User')}
        </AvatarFallback>
      </Avatar>

      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-foreground truncate">
            {ticket.subject}
          </h4>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              {ticket._creationTime ? 
                formatDistanceToNow(ticket._creationTime, { addSuffix: true }) : 
                'Recently'
              }
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2 mb-2">
          <StatusBadge status={ticket.status} type="status" />
          <StatusBadge status={ticket.priority} type="priority" />
        </div>

        <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">#{ticket.ticketNumber}</Badge>
        </div>

        <p className="text-sm text-muted-foreground truncate font-light">
          {ticket.description}
        </p>

        <div className="flex items-center space-x-2 mt-2">
          <div className="flex items-center space-x-1">
            <User className="w-3 h-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground truncate font-light">
              {ticket.user?.name || 'Unknown User'}
            </span>
          </div>
          <span className="text-xs text-muted-foreground">•</span>
          <span className="text-xs text-muted-foreground truncate font-light">
            {ticket.category.replace('_', ' ')}
          </span>
        </div>
      </div>
    </div>
  );
}
