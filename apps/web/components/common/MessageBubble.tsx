import { motion } from "framer-motion";
import { Clock } from "lucide-react";
import { Avatar, AvatarFallback } from "@repo/ui/components/avatar";
import { formatDistanceToNow } from "date-fns";

export interface Message {
  _id: string;
  content: string;
  senderType: 'admin' | 'customer' | 'user';
  _creationTime?: number;
}

export interface MessageBubbleProps {
  message: Message;
  isOwnMessage: boolean;
  senderName?: string;
  showAvatar?: boolean;
  className?: string;
}

export function MessageBubble({ 
  message, 
  isOwnMessage, 
  senderName = "User",
  showAvatar = true,
  className = "" 
}: MessageBubbleProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <motion.div
      className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} group ${className}`}
      layout
    >
      <div className={`flex flex-col max-w-[75%] ${isOwnMessage ? 'items-end' : 'items-start'}`}>
        <div className={`flex items-end gap-2 ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
          {/* Avatar for received messages */}
          {!isOwnMessage && showAvatar && (
            <Avatar className="flex-shrink-0">
              <AvatarFallback className="text-xs">
                {getInitials(senderName)}
              </AvatarFallback>
            </Avatar>
          )}
          
          {/* Message bubble */}
          <motion.div 
            className={`relative rounded-2xl px-4 py-2.5 shadow-sm ${
              isOwnMessage
                ? 'bg-primary text-primary-foreground ml-2 rounded-br-md'
                : 'bg-card text-foreground border border-border/50 mr-2 rounded-bl-md hover:border-border'
            }`}
            whileTap={{ scale: 0.98 }}
          >
            <p
              className={`text-sm leading-relaxed break-words ${isOwnMessage ? 'text-white text-right' : 'text-foreground text-left'}`}
              style={isOwnMessage ? { marginLeft: 'auto' } : {}}
            >
              {message.content}
            </p>
          </motion.div>
        </div>
        
        {/* Message metadata below bubble */}
        <div className={`mt-1 px-2 py-1.5 rounded-lg backdrop-blur-sm ${
          isOwnMessage ? 'text-right' : 'text-left'
        }`}>
          <div className={`flex flex-col gap-1 text-xs text-muted-foreground ${
            isOwnMessage ? 'justify-end' : 'justify-start'
          }`}>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span className="font-light">
                {message._creationTime ? 
                  formatDistanceToNow(message._creationTime, { addSuffix: true }) : 
                  'Recently'
                }
              </span>
            </div>
            
            <span className="font-light">
              {message.senderType === 'admin' ? 'Admin' : 'Customer'}
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
