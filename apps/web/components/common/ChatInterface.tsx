import { ReactNode } from "react";
import { Avatar, AvatarFallback } from "@repo/ui/components/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { MessageCircle } from "lucide-react";
import { MessageBubble, Message } from "./MessageBubble";
import { MessageInput } from "./MessageInput";

export interface ChatInterfaceProps {
  title: string;
  subtitle?: string;
  avatarFallback?: string;
  messages: Message[];
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  headerActions?: ReactNode;
  emptyStateMessage?: string;
  emptyStateDescription?: string;
  className?: string;
}

export function ChatInterface({
  title,
  subtitle,
  avatarFallback = "U",
  messages,
  onSendMessage,
  isLoading = false,
  headerActions,
  emptyStateMessage = "No messages yet",
  emptyStateDescription = "Start a conversation by sending a message",
  className = ""
}: ChatInterfaceProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`flex-1 flex flex-col bg-background ${className}`}>
      {/* Chat Header - Fixed at top */}
      <div className="border-b border-border bg-card p-4 shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="w-10 h-10">
              <AvatarFallback>
                {getInitials(avatarFallback)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-medium text-foreground">
                {title}
              </h3>
              {subtitle && (
                <p className="text-sm text-muted-foreground font-light">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          
          {headerActions && (
            <div className="flex items-center gap-2">
              {headerActions}
            </div>
          )}
        </div>
      </div>

      {/* Messages - Scrollable area between header and input */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3 bg-gradient-to-b from-background to-muted/10 max-h-[calc(100vh-313px)]">
        {messages?.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <MessageCircle className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-light text-foreground mb-2">
                {emptyStateMessage}
              </h3>
              <p className="text-muted-foreground font-light">
                {emptyStateDescription}
              </p>
            </div>
          </div>
        ) : (
          messages?.map((message) => {
            const isOwnMessage = message.senderType === 'admin';
            
            return (
              <MessageBubble
                key={message._id}
                message={message}
                isOwnMessage={isOwnMessage}
                senderName={avatarFallback}
                showAvatar={!isOwnMessage}
              />
            );
          })
        )}
      </div>

      {/* Message Input - Fixed at bottom */}
      <div className="shrink-0 border-t border-border bg-card">
        <MessageInput
          onSendMessage={onSendMessage}
          disabled={isLoading}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
