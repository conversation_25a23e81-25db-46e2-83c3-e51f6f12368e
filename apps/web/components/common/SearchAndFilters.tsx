import { useState } from "react";
import { Search, X, Filter } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";

export interface FilterOption {
  value: string;
  label: string;
}

export interface SearchAndFiltersProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  filters: Record<string, string>;
  onFilterChange: (key: string, value: string) => void;
  filterOptions: Record<string, FilterOption[]>;
  onClearFilters: () => void;
  stats?: React.ReactNode;
  searchPlaceholder?: string;
  className?: string;
}

export function SearchAndFilters({
  searchQuery,
  onSearchChange,
  filters,
  onFilterChange,
  filterOptions,
  onClearFilters,
  stats,
  searchPlaceholder = "Search...",
  className = ""
}: SearchAndFiltersProps) {
  const clearSearch = () => {
    onSearchChange("");
  };

  return (
    <div className={`border-b border-border bg-card ${className}`}>
      <div className="p-3.5">
        <div className="flex items-center justify-between">
          {/* Search and Filters */}
          <div className="flex items-center gap-4">
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 pr-10 rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
                >
                  <X className="w-3 h-3" />
                </Button>
              )}
            </div>
            
            {Object.entries(filterOptions).map(([key, options]) => (
              <Select
                key={key}
                value={filters[key]}
                onValueChange={(value) => onFilterChange(key, value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder={key.charAt(0).toUpperCase() + key.slice(1)} />
                </SelectTrigger>
                <SelectContent>
                  {options.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ))}

            <Button 
              variant="outline" 
              onClick={onClearFilters}
              className="rounded-xl font-light"
            >
              Clear Filters
            </Button>
          </div>

          {/* Stats */}
          {stats && (
            <div className="flex items-center gap-4">
              {stats}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
