# Common Components

This directory contains reusable components that can be used across different parts of the HaulteVault application.

## Components

### SearchAndFilters
A comprehensive search and filter interface with search input, multiple filter dropdowns, and clear functionality.

```tsx
import { SearchAndFilters } from "@/components/common";

<SearchAndFilters
  searchQuery={searchQuery}
  onSearchChange={setSearchQuery}
  filters={filters}
  onFilterChange={handleFilterChange}
  filterOptions={filterOptions}
  onClearFilters={clearFilters}
  stats={stats}
  searchPlaceholder="Search items..."
/>
```

**Props:**
- `searchQuery`: Current search query string
- `onSearchChange`: Function to update search query
- `filters`: Object containing current filter values
- `onFilterChange`: Function to update filters
- `filterOptions`: Object defining available filter options
- `onClearFilters`: Function to clear all filters
- `stats`: Optional React node for displaying statistics
- `searchPlaceholder`: Custom placeholder for search input
- `className`: Additional CSS classes

### StatusBadge
A flexible badge component for displaying different types of statuses with appropriate colors and icons.

```tsx
import { StatusBadge } from "@/components/common";

<StatusBadge status="open" type="status" />
<StatusBadge status="urgent" type="priority" />
<StatusBadge status="technical" type="category" />
```

**Props:**
- `status`: The status string to display
- `type`: Type of status ('status', 'priority', 'category')
- `showIcon`: Whether to show the status icon (default: true)
- `className`: Additional CSS classes

### MessageBubble
A chat message bubble component with proper styling and metadata display.

```tsx
import { MessageBubble } from "@/components/common";

<MessageBubble
  message={message}
  isOwnMessage={true}
  senderName="John Doe"
  showAvatar={true}
/>
```

**Props:**
- `message`: Message object with content and metadata
- `isOwnMessage`: Whether this is the current user's message
- `senderName`: Name of the message sender
- `showAvatar`: Whether to show the sender's avatar
- `className`: Additional CSS classes

### MessageInput
A message input component for chat interfaces with textarea and send button.

```tsx
import { MessageInput } from "@/components/common";

<MessageInput
  onSendMessage={handleSendMessage}
  placeholder="Type your message..."
  disabled={false}
  isLoading={false}
/>
```

**Props:**
- `onSendMessage`: Function called when sending a message
- `placeholder`: Placeholder text for the input
- `disabled`: Whether the input is disabled
- `isLoading`: Whether a message is being sent
- `className`: Additional CSS classes

### ItemList
A reusable list component for displaying items with consistent styling and empty states.

```tsx
import { ItemList } from "@/components/common";

<ItemList
  title="Support Tickets"
  items={tickets}
  searchQuery={searchQuery}
  onItemSelect={handleSelectItem}
  selectedItemId={selectedId}
  renderItem={(item) => <TicketItem ticket={item} />}
  emptyStateMessage="No tickets yet"
/>
```

**Props:**
- `title`: Title displayed in the list header
- `items`: Array of items to display
- `searchQuery`: Current search query for filtering
- `onItemSelect`: Function called when an item is selected
- `selectedItemId`: ID of the currently selected item
- `renderItem`: Function to render each item
- `emptyStateIcon`: Icon to show when no items
- `emptyStateMessage`: Message to show when no items
- `emptyStateSubMessage`: Sub-message for empty state
- `className`: Additional CSS classes

### ChatInterface
A complete chat interface component combining header, messages area, and input.

```tsx
import { ChatInterface } from "@/components/common";

<ChatInterface
  title="Support Ticket #123"
  subtitle="Customer: John Doe"
  avatarFallback="JD"
  messages={messages}
  onSendMessage={handleSendMessage}
  isLoading={false}
  headerActions={headerActions}
/>
```

**Props:**
- `title`: Main title for the chat
- `subtitle`: Optional subtitle
- `avatarFallback`: Text to show in the avatar
- `messages`: Array of messages to display
- `onSendMessage`: Function to handle sending messages
- `isLoading`: Whether a message is being sent
- `headerActions`: Optional actions to show in the header
- `emptyStateMessage`: Message for empty chat
- `emptyStateDescription`: Description for empty chat
- `className`: Additional CSS classes

### SupportTicketItem
A specific component for displaying support ticket items in lists.

```tsx
import { SupportTicketItem } from "@/components/common";

<SupportTicketItem ticket={ticket} />
```

**Props:**
- `ticket`: Support ticket object with all necessary fields
- `className`: Additional CSS classes

## Usage Examples

### Creating a Filter Interface
```tsx
const filterOptions = {
  status: [
    { value: "all", label: "All Status" },
    { value: "open", label: "Open" },
    { value: "closed", label: "Closed" }
  ],
  priority: [
    { value: "all", label: "All Priorities" },
    { value: "high", label: "High" },
    { value: "low", label: "Low" }
  ]
};

<SearchAndFilters
  searchQuery={searchQuery}
  onSearchChange={setSearchQuery}
  filters={filters}
  onFilterChange={handleFilterChange}
  filterOptions={filterOptions}
  onClearFilters={clearFilters}
/>
```

### Creating a Chat Interface
```tsx
<ChatInterface
  title="Customer Support"
  subtitle="Ticket #123"
  avatarFallback="CS"
  messages={messages}
  onSendMessage={handleSendMessage}
  headerActions={
    <Select value={status} onValueChange={setStatus}>
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="open">Open</SelectItem>
        <SelectItem value="closed">Closed</SelectItem>
      </SelectContent>
    </Select>
  }
/>
```

## Styling

All components use Tailwind CSS classes and follow the HaulteVault design system. They use the theme-aware colors defined in `@globals.css`:

- Primary colors (dark brown)
- Accent colors (tan)
- Secondary colors (medium brown)
- Muted colors

## Accessibility

Components include proper ARIA labels, keyboard navigation support, and semantic HTML structure where appropriate.

## Responsiveness

Components are designed to work on both desktop and mobile devices with appropriate responsive behavior.
