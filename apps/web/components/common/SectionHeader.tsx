"use client";

import { ReactNode } from "react";
import { cn } from "@repo/ui/src/lib/utils";

interface SectionHeaderProps {
  /** The main title text */
  title: string;
  /** Optional subtitle or description */
  subtitle?: string;
  /** Additional content to render on the right side (e.g., action buttons) */
  actions?: ReactNode;
  /** Custom className for the container */
  className?: string;
  /** Custom className for the title */
  titleClassName?: string;
  /** Custom className for the subtitle */
  subtitleClassName?: string;
  /** Whether to use uppercase styling for the title */
  uppercase?: boolean;
  /** Size variant for the header */
  size?: "sm" | "md" | "lg";
}

const sizeVariants = {
  sm: {
    title: "text-sm font-light uppercase tracking-wide",
    subtitle: "text-xs",
    spacing: "space-y-1"
  },
  md: {
    title: "text-lg font-light",
    subtitle: "text-sm",
    spacing: "space-y-2"
  },
  lg: {
    title: "text-2xl font-light",
    subtitle: "text-base",
    spacing: "space-y-3"
  }
};

export function SectionHeader({
  title,
  subtitle,
  actions,
  className,
  titleClassName,
  subtitleClassName,
  uppercase = false,
  size = "sm",
}: SectionHeaderProps) {
  const variant = sizeVariants[size];
  
  return (
    <div className={cn("flex items-start justify-between", className)}>
      <div className={variant.spacing}>
        <h3 className={cn(
          variant.title,
          "text-muted-foreground",
          uppercase && "uppercase",
          titleClassName
        )}>
          {title}
        </h3>
        {subtitle && (
          <p className={cn(
            variant.subtitle,
            "text-muted-foreground/70",
            subtitleClassName
          )}>
            {subtitle}
          </p>
        )}
      </div>
      {actions && (
        <div className="flex-shrink-0">
          {actions}
        </div>
      )}
    </div>
  );
}
