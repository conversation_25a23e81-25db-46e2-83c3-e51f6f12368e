"use client";

import { useState, useEffect } from "react";
import { Input } from "@repo/ui/components/input";
import { cn } from "@repo/ui/src/lib/utils";

export interface PriceRange {
  min: number | null;
  max: number | null;
}

interface PriceRangeOption {
  label: string;
  min: number | null;
  max: number | null;
}

interface PriceRangeSelectorProps {
  /** Current price range [min, max] */
  value: [number | null, number | null];
  /** Callback when price range changes */
  onChange: (range: [number | null, number | null]) => void;
  /** Custom placeholder for min input */
  minPlaceholder?: string;
  /** Custom placeholder for max input */
  maxPlaceholder?: string;
  /** Predefined quick filter options */
  quickFilters?: PriceRangeOption[];
  /** Whether to show quick filter buttons */
  showQuickFilters?: boolean;
  /** Custom className for the container */
  className?: string;
  /** Currency symbol to display */
  currency?: string;
}

const DEFAULT_QUICK_FILTERS: PriceRangeOption[] = [
  { label: "Under $5K", min: null, max: 5000 },
  { label: "Under $10K", min: null, max: 10000 },
  { label: "$10K-$20K", min: 10000, max: 20000 },
  { label: "$20K-50K", min: 20000, max: 50000 },
  { label: "$50K-$100K", min: 50000, max: 100000 },
  { label: "$100K+", min: 100000, max: null }
];

export function PriceRangeSelector({
  value,
  onChange,
  minPlaceholder = "0",
  maxPlaceholder = "10000",
  quickFilters = DEFAULT_QUICK_FILTERS,
  showQuickFilters = true,
  className,
  currency = "$",
}: PriceRangeSelectorProps) {
  const [inputs, setInputs] = useState({
    min: value[0]?.toString() || "",
    max: value[1]?.toString() || ""
  });

  // Update inputs when value prop changes
  useEffect(() => {
    setInputs({
      min: value[0]?.toString() || "",
      max: value[1]?.toString() || ""
    });
  }, [value]);

  const handleInputChange = (type: 'min' | 'max', inputValue: string) => {
    setInputs(prev => ({ ...prev, [type]: inputValue }));
    
    let numValue: number | null = null;
    if (inputValue !== "") {
      const parsed = parseInt(inputValue);
      if (!isNaN(parsed)) {
        numValue = parsed;
      }
    }
    
    if (type === 'min') {
      onChange([numValue, value[1]]);
    } else {
      onChange([value[0], numValue]);
    }
  };

  const handleQuickFilterClick = (range: PriceRangeOption) => {
    const isSelected = value[0] === range.min && value[1] === range.max;
    
    if (isSelected) {
      // If already selected, deselect it
      onChange([null, null]);
    } else {
      // If not selected, select it
      onChange([range.min, range.max]);
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Price Input Fields */}
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className="text-xs text-muted-foreground font-light mb-1 block">Min</label>
          <Input
            type="number"
            placeholder={minPlaceholder}
            value={inputs.min}
            onChange={(e) => handleInputChange('min', e.target.value)}
            className="rounded-xl font-light placeholder:text-muted-foreground/50 text-foreground"
          />
        </div>
        <div>
          <label className="text-xs text-muted-foreground font-light mb-1 block">Max</label>
          <Input
            type="number"
            placeholder={maxPlaceholder}
            value={inputs.max}
            onChange={(e) => handleInputChange('max', e.target.value)}
            className="rounded-xl font-light placeholder:text-muted-foreground/50 text-foreground"
          />
        </div>
      </div>

      {/* Quick Price Filter Buttons */}
      {showQuickFilters && quickFilters.length > 0 && (
        <div className="grid grid-cols-2 gap-2">
          {quickFilters.map((range) => {
            const isSelected = value[0] === range.min && value[1] === range.max;
            return (
              <button
                key={range.label}
                onClick={() => handleQuickFilterClick(range)}
                className={cn(
                  "text-sm px-3 py-2 border border-border rounded-xl font-light text-left transition-colors",
                  isSelected
                    ? "bg-accent text-accent-foreground border-accent"
                    : "text-muted-foreground hover:text-accent-foreground hover:bg-accent"
                )}
              >
                {range.label}
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
}
