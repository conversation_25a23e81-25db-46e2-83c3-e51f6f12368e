"use client";

import { useState, useRef, useEffect } from "react";
import { Search, X } from "lucide-react";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { cn } from "@repo/ui/src/lib/utils";

export interface FilterOption {
  name: string;
  count: number;
}

interface FilterSelectorProps {
  /** Array of currently selected option names */
  selectedItems: string[];
  /** Array of available options with counts */
  availableItems: FilterOption[];
  /** Callback when items are selected/deselected */
  onSelectionChange: (items: string[]) => void;
  /** Whether to allow multiple selections (default: true) */
  multiSelect?: boolean;
  /** Maximum number of items to display in the grid */
  maxDisplayedItems?: number;
  /** Maximum number of items to show in search dropdown */
  maxSearchResults?: number;
  /** Custom placeholder for search input */
  searchPlaceholder?: string;
  /** Whether to show the search input */
  showSearch?: boolean;
  /** Whether to show selected item badges */
  showSelectedBadges?: boolean;
  /** Custom className for the container */
  className?: string;
  /** Additional search suggestions (items that might not be in availableItems) */
  searchSuggestions?: FilterOption[];
  /** Number of columns in the grid layout */
  gridCols?: 1 | 2 | 3 | 4;
}

export function FilterSelector({
  selectedItems,
  availableItems,
  onSelectionChange,
  multiSelect = true,
  maxDisplayedItems = 10,
  maxSearchResults = 8,
  searchPlaceholder = "Search...",
  showSearch = true,
  showSelectedBadges = true,
  className,
  searchSuggestions = [],
  gridCols = 2,
}: FilterSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleItemSelect = (itemName: string) => {
    if (multiSelect) {
      if (!selectedItems.includes(itemName)) {
        onSelectionChange([...selectedItems, itemName]);
      }
    } else {
      // Single select: replace current selection
      onSelectionChange([itemName]);
    }
    setSearchQuery("");
    setShowDropdown(false);
  };

  const handleItemRemove = (itemName: string) => {
    onSelectionChange(selectedItems.filter(item => item !== itemName));
  };

  const handleItemToggle = (itemName: string) => {
    if (multiSelect) {
      if (selectedItems.includes(itemName)) {
        handleItemRemove(itemName);
      } else {
        handleItemSelect(itemName);
      }
    } else {
      // Single select: toggle off if already selected, otherwise select
      if (selectedItems.includes(itemName)) {
        onSelectionChange([]);
      } else {
        onSelectionChange([itemName]);
      }
    }
  };

  // Combine available items with search suggestions for search functionality
  const searchableItems = [...new Set([
    ...availableItems.map(item => item.name),
    ...searchSuggestions.map(item => item.name)
  ])].map(name => ({
    name,
    count: availableItems.find(item => item.name === name)?.count || 
           searchSuggestions.find(item => item.name === name)?.count || 0
  }));

  const filteredItems = searchableItems.filter(item => 
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
    !selectedItems.includes(item.name)
  );

  const gridColsClass = {
    1: "grid-cols-1",
    2: "grid-cols-2", 
    3: "grid-cols-3",
    4: "grid-cols-4"
  }[gridCols];

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search Input */}
      {showSearch && (
        <div className="relative" ref={searchRef}>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                setShowDropdown(true);
              }}
              onFocus={() => setShowDropdown(true)}
              className="pl-10 rounded-xl bg-primary/5 border-border font-light placeholder:text-muted-foreground"
            />
          </div>
          
          {/* Search Dropdown */}
          {showDropdown && searchQuery && filteredItems.length > 0 && (
            <div className="absolute top-full left-0 right-0 mt-2 bg-card rounded-2xl p-1 shadow-lg z-50 max-h-48 overflow-y-auto border border-border">
              {filteredItems.slice(0, maxSearchResults).map((item) => (
                <button
                  key={item.name}
                  onClick={() => handleItemSelect(item.name)}
                  className="w-full text-left px-4 py-2 hover:bg-accent/20 hover:text-accent-foreground font-light flex items-center justify-between rounded-xl group transition-colors"
                >
                  <span className="text-sm text-foreground group-hover:text-foreground transition-colors">
                    {item.name}
                  </span>
                  <span className="text-xs text-muted-foreground group-hover:text-accent-foreground transition-colors">
                    {item.count}
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Selected Item Badges */}
      {showSelectedBadges && selectedItems.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedItems.map((item) => (
            <Badge
              key={item}
              variant="primary"
            >
              {item}
              <button
                onClick={() => handleItemRemove(item)}
                className="ml-1 hover:bg-accent rounded-lg p-0.5"
              >
                <X className="w-3 h-3 text-foreground" />
              </button>
            </Badge>
          ))}
        </div>
      )}

      {/* Items Grid */}
      <div className={cn("grid gap-2 max-h-64 overflow-y-auto", gridColsClass)}>
        {availableItems.slice(0, maxDisplayedItems).map((item) => (
          <button
            key={item.name}
            onClick={() => handleItemToggle(item.name)}
            className={cn(
              "text-sm px-3 py-2 border border-border rounded-xl font-light text-left transition-colors group",
              selectedItems.includes(item.name)
                ? "bg-accent text-accent-foreground border-accent"
                : "text-muted-foreground hover:text-foreground hover:bg-accent"
            )}
          > 
            <div className="flex items-center justify-between">
              <span className="truncate text-sm group-hover:text-accent-foreground transition-colors">
                {item.name.replaceAll("_", " ")}
              </span>
              <span className={cn(
                "text-xs ml-2 font-light group-hover:text-accent-foreground transition-colors",
                selectedItems.includes(item.name) ? "text-accent-foreground" : "text-muted-foreground"
              )}>
                {item.count}
              </span>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}
