"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { User } from "lucide-react";
import { useMemo } from "react";

interface ProfileImageProps {
  storageId?: string;
  betterAuthImage?: string;
  name?: string;
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  showFallback?: boolean;
  tempImageUrl?: string; // For immediate display after upload
}

export function ProfileImage({ 
  storageId, 
  betterAuthImage, 
  name, 
  size = "md",
  className = "",
  showFallback = true,
  tempImageUrl
}: ProfileImageProps) {
  // Get the image URL from Convex storage if we have a storage ID
  const imageUrl = useQuery(
    api.userManagement.getProfileImageUrl,
    storageId ? { storageId: storageId as any } : "skip"
  );

  // Determine which image to use (priority: temp > Convex storage > Better Auth > fallback)
  const displayImage = useMemo(() => {
    if (tempImageUrl) return tempImageUrl;
    if (imageUrl) return imageUrl;
    if (betterAuthImage) return betterAuthImage;
    return null;
  }, [tempImageUrl, imageUrl, betterAuthImage]);

  // Size classes
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-10 h-10", 
    lg: "w-16 h-16",
    xl: "w-32 h-32"
  };

  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Avatar className={`${sizeClasses[size]} ${className}`}>
      <AvatarImage 
        src={displayImage || undefined} 
        alt={name || "Profile"} 
      />
      {showFallback && (
        <AvatarFallback className="bg-muted text-muted-foreground">
          {name ? getInitials(name) : <User className="w-4 h-4" />}
        </AvatarFallback>
      )}
    </Avatar>
  );
}
