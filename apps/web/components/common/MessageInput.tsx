import { useState } from "react";
import { Send } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Textarea } from "@repo/ui/components/textarea";

export interface MessageInputProps {
  onSendMessage: (message: string) => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  className?: string;
}

export function MessageInput({
  onSendMessage,
  placeholder = "Type your message...",
  disabled = false,
  isLoading = false,
  className = ""
}: MessageInputProps) {
  const [message, setMessage] = useState("");

  const handleSend = () => {
    if (!message.trim() || disabled || isLoading) return;
    
    onSendMessage(message.trim());
    setMessage("");
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className={`p-4 ${className}`}>
      <div className="flex items-end space-x-2">
        <Textarea
          placeholder={placeholder}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          className="resize-none min-h-[60px] bg-card border-border font-light placeholder:text-muted-foreground transition-all duration-300 focus:ring-2 focus:ring-primary/20 flex-1"
          onKeyDown={handleKeyDown}
          disabled={disabled}
          rows={1}
        />
        <Button
          onClick={handleSend}
          disabled={!message.trim() || disabled || isLoading}
          size="sm"
          className="rounded-xl bg-primary hover:bg-primary/90 transition-colors duration-300 h-[60px] px-4"
        >
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
        </Button>
      </div>
    </div>
  );
}
