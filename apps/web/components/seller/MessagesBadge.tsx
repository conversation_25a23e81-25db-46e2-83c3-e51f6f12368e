"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Badge } from "@repo/ui/components/badge";

export function MessagesBadge() {
  const unreadCount = useQuery(api.messages.getUnreadCount);

  if (!unreadCount || unreadCount === 0) {
    return null;
  }

  return (
    <Badge 
      className="bg-blue-500 text-white text-xs ml-auto" 
      variant="default"
    >
      {unreadCount > 99 ? "99+" : unreadCount}
    </Badge>
  );
}
