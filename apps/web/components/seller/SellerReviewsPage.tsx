"use client";

import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { MarketplaceHeader } from "@/components/marketplace/MarketplaceHeader";
import { FilterSidebar } from "@/components/marketplace/FilterSidebar";
import { SellerProfileHeader } from "./SellerProfileHeader";
import { ReviewableOrders } from "./ReviewableOrders";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Star, User, MessageSquare } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

interface SellerReviewsPageProps {
  sellerId: string;
}

export function SellerReviewsPage({ sellerId }: SellerReviewsPageProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<Id<"orders"> | null>(null);
  const [rating, setRating] = useState(5);
  const [review, setReview] = useState("");
  const [sortBy, setSortBy] = useState<"newest" | "oldest" | "rating_high" | "rating_low">("newest");
  const [ratingFilter, setRatingFilter] = useState<number | null>(null);

  // Fetch seller profile
  const sellerProfile = useQuery(api.sellerQueries.getPublicSellerProfile, { sellerId: sellerId as Id<"users"> });

  // Fetch reviews
  const reviewsData = useQuery(api.sellerReviews.getSellerReviews, {
    sellerId: sellerId as Id<"users">,
    limit: 50,
    verifiedOnly: true,
  });

  // Fetch reviewable orders for current user
  const reviewableOrders = useQuery(api.sellerReviews.getReviewableOrders, {
    sellerId: sellerId as Id<"users">,
  });

  // Add review mutation
  const addReview = useMutation(api.sellerReviews.addSellerReview);

  // Validate seller ID format
  if (!sellerId || sellerId.length === 0) {
    return (
      <div className="min-h-screen bg-card flex items-center justify-center">
        <div className="text-center">
          <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h4 className="text-lg font-medium mb-2">Invalid Seller ID</h4>
          <p className="text-muted-foreground mb-4">
            The seller ID provided is not valid.
          </p>
          <Link
            href="/marketplace"
            className="text-primary hover:underline"
          >
            Back to Marketplace
          </Link>
        </div>
      </div>
    );
  }

  // Loading state
  if (sellerProfile === undefined) {
    return (
      <div className="bg-card min-h-screen">
        <MarketplaceHeader onSearch={() => {}} />
        <div className="container mx-auto px-6 py-12">
          <div className="animate-pulse">
            <div className="h-32 bg-muted rounded-xl mb-6"></div>
            <div className="h-8 bg-muted rounded mb-4 w-1/3"></div>
            <div className="h-4 bg-muted rounded mb-2 w-1/2"></div>
            <div className="h-4 bg-muted rounded mb-8 w-1/4"></div>
          </div>
        </div>
      </div>
    );
  }

  // Seller not found
  if (!sellerProfile) {
    return (
      <div className="bg-card min-h-screen">
        <MarketplaceHeader onSearch={() => {}} />
        <div className="container mx-auto px-6 py-12 text-center">
          <h1 className="text-2xl font-medium text-foreground mb-4">Seller Not Found</h1>
          <p className="text-muted-foreground">The seller you're looking for doesn't exist or has been deactivated.</p>
        </div>
      </div>
    );
  }

  // Error loading reviews
  if (reviewsData === null) {
    return (
      <div className="bg-card min-h-screen">
        <MarketplaceHeader onSearch={() => {}} />
        <div className="container mx-auto px-6 py-12 text-center">
          <h1 className="text-2xl font-medium text-foreground mb-4">Error Loading Reviews</h1>
          <p className="text-muted-foreground">Unable to load reviews at this time. Please try again later.</p>
        </div>
      </div>
    );
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
    });
  };

  const renderStars = (rating: number, size: "sm" | "md" | "lg" = "md") => {
    const sizeClasses = {
      sm: "w-3 h-3",
      md: "w-4 h-4",
      lg: "w-5 h-5",
    };

    return (
      <div className="flex items-center space-x-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? "text-yellow-500 fill-current"
                : "text-gray-300 dark:text-gray-600"
            }`}
          />
        ))}
      </div>
    );
  };

  const handleSubmitReview = async () => {
    if (!selectedOrderId || !rating || !review.trim()) return;

    try {
      await addReview({
        sellerId: sellerId as Id<"users">,
        orderId: selectedOrderId,
        rating,
        review: review.trim(),
      });

      // Reset form and close dialog
      setRating(5);
      setReview("");
      setSelectedOrderId(null);
      setIsReviewDialogOpen(false);
    } catch (error) {
      console.error("Error submitting review:", error);
    }
  };

  console.log("reviewableOrders", reviewableOrders);

  const canReview = reviewableOrders && reviewableOrders.orders.length > 0;

  return (
    <div className="bg-card min-h-screen">
      <MarketplaceHeader onSearch={() => {}} />
      
      <SellerProfileHeader sellerId={sellerId} pathname={pathname} />

      {/* Reviews Section (replacing Products Section) */}
      <div className="container mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* Filter Sidebar - Desktop */}
          <div className="hidden md:block w-72 flex-shrink-0">
            <div className="space-y-6">
              {/* Reviews Summary */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                    Total Reviews
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {reviewsData?.reviews?.length || 0}
                  </div>
                  <p className="text-xs text-neutral-600 dark:text-neutral-400 mt-1">
                    verified reviews
                  </p>
                </CardContent>
              </Card>

              {/* Write Review Button */}
              {canReview && (
                <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="w-full">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Write a Review
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md overflow-hidden">
                    <DialogHeader>
                      <DialogTitle>Write a Review</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Select Order</label>
                        <div className="w-full max-w-full overflow-hidden">
                          <Select
                            value={selectedOrderId || ""}
                            onValueChange={(value) => setSelectedOrderId(value as Id<"orders">)}
                          >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Choose an order to review">
                              {selectedOrderId && reviewableOrders?.orders && (
                                (() => {
                                  const selectedOrder = reviewableOrders.orders.find(o => o._id === selectedOrderId);
                                  return selectedOrder ? (
                                    <div className="flex flex-col text-left w-full min-w-0 max-w-full">
                                      <span className="font-medium truncate max-w-full">Order #{selectedOrder.orderNumber}</span>
                                      <span className="text-sm text-muted-foreground truncate max-w-full block">
                                        {selectedOrder.product?.title || "Product"}
                                      </span>
                                    </div>
                                  ) : null;
                                })()
                              )}
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent className="w-full max-w-md overflow-x-hidden">
                            {reviewableOrders.orders.map((order) => (
                              <SelectItem key={order._id} value={order._id}>
                                <div className="flex flex-col w-full min-w-0">
                                  <span className="font-medium truncate">Order #{order.orderNumber}</span>
                                  <span className="text-sm text-muted-foreground truncate">
                                    {order.product?.title || "Product"}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block">Rating</label>
                        <div className="flex items-center space-x-2">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <button
                              key={star}
                              onClick={() => setRating(star)}
                              className="focus:outline-none"
                            >
                              <Star
                                className={`w-6 h-6 ${
                                  star <= rating
                                    ? "text-yellow-500 fill-current"
                                    : "text-gray-300"
                                }`}
                              />
                            </button>
                          ))}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block">Review</label>
                        <Textarea
                          placeholder="Share your experience with this seller..."
                          value={review}
                          onChange={(e) => setReview(e.target.value)}
                          rows={4}
                        />
                      </div>
                      <Button onClick={handleSubmitReview} className="w-full">
                        Submit Review
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Mobile Filter Button */}
            <div className="md:hidden mb-6">
              <div className="space-y-4">
                {/* Reviews Summary */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                      Total Reviews
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {reviewsData?.reviews?.length || 0}
                    </div>
                    <p className="text-xs text-neutral-600 dark:text-neutral-400 mt-1">
                      verified reviews
                    </p>
                  </CardContent>
                </Card>

                {/* Write Review Button */}
                {canReview && (
                  <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
                    <DialogTrigger asChild>
                      <Button className="w-full">
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Write a Review
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle>Write a Review</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium mb-2 block">Select Order</label>
                          <Select
                            value={selectedOrderId || ""}
                            onValueChange={(value) => setSelectedOrderId(value as Id<"orders">)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Choose an order to review" />
                            </SelectTrigger>
                            <SelectContent>
                              {reviewableOrders.orders.map((order) => (
                                <SelectItem key={order._id} value={order._id}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">Order #{order.orderNumber}</span>
                                    <span className="text-sm text-muted-foreground truncate">
                                      {order.product?.title || "Product"}
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <label className="text-sm font-medium mb-2 block">Rating</label>
                          <div className="flex items-center space-x-2">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <button
                                key={star}
                                onClick={() => setRating(star)}
                                className="focus:outline-none"
                              >
                                <Star
                                  className={`w-6 h-6 ${
                                    star <= rating
                                      ? "text-yellow-500 fill-current"
                                      : "text-gray-300"
                                  }`}
                                />
                              </button>
                            ))}
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium mb-2 block">Review</label>
                          <Textarea
                            placeholder="Share your experience with this seller..."
                            value={review}
                            onChange={(e) => setReview(e.target.value)}
                            rows={4}
                          />
                        </div>
                        <Button onClick={handleSubmitReview} className="w-full">
                          Submit Review
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>


            
            {/* Reviews List */}
            <div className="space-y-4">
              {reviewsData?.reviews && reviewsData.reviews.length > 0 ? (
                reviewsData.reviews.map((reviewItem) => (
                  <Card key={reviewItem._id} className="bg-background/50">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                            <User className="w-5 h-5 text-muted-foreground" />
                          </div>
                          <div>
                            <div className="font-medium">{reviewItem.user?.name || "User"}</div>
                            <div className="text-sm text-muted-foreground">
                              {formatDate(reviewItem.reviewDate)}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          {renderStars(reviewItem.rating, "sm")}
                          <span className="text-sm font-medium ml-2">
                            {reviewItem.rating}.0
                          </span>
                        </div>
                      </div>
                      {reviewItem.review && (
                        <p className="text-foreground">{reviewItem.review}</p>
                      )}
                        {reviewItem.product?.title && (
                        <div className="mt-3 pt-3 border-t border-border">
                          <span className="text-sm text-muted-foreground">
                            Reviewed for: {reviewItem.product.title}
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card className="bg-background/50">
                  <CardContent className="p-12 text-center">
                    <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Reviews Yet</h3>
                    <p className="text-muted-foreground mb-4">
                      This seller hasn't received any reviews yet.
                    </p>
                    {canReview && (
                      <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
                        <DialogTrigger asChild>
                          <Button>
                            <MessageSquare className="w-4 h-4 mr-2" />
                            Be the First to Review
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                          <DialogHeader>
                            <DialogTitle>Write a Review</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium mb-2 block">Select Order</label>
                              <div className="w-full max-w-full overflow-hidden">
                                <Select
                                  value={selectedOrderId || ""}
                                  onValueChange={(value) => setSelectedOrderId(value as Id<"orders">)}
                                >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="Choose an order to review" className="w-full">
                                    {selectedOrderId && reviewableOrders?.orders && (
                                      (() => {
                                        const selectedOrder = reviewableOrders.orders.find(o => o._id === selectedOrderId);
                                        return selectedOrder ? (
                                          <div className="flex flex-col text-left w-full min-w-0 max-w-full">
                                            <span className="font-medium truncate max-w-full">Order #{selectedOrder.orderNumber}</span>
                                            <span className="text-sm text-muted-foreground truncate max-w-full block">
                                              {selectedOrder.product?.title || "Product"}
                                            </span>
                                          </div>
                                        ) : null;
                                      })()
                                    )}
                                  </SelectValue>
                                </SelectTrigger>
                                <SelectContent className="w-full max-w-md">
                                  {reviewableOrders.orders.map((order) => (
                                    <SelectItem key={order._id} value={order._id}>
                                      <div className="flex flex-col w-full min-w-0">
                                        <span className="font-medium truncate">Order #{order.orderNumber}</span>
                                        <span className="text-sm text-muted-foreground truncate">
                                          {order.product?.title || "Product"}
                                        </span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium mb-2 block">Rating</label>
                              <div className="flex items-center space-x-2">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <button
                                    key={star}
                                    onClick={() => setRating(star)}
                                    className="focus:outline-none"
                                  >
                                    <Star
                                      className={`w-6 h-6 ${
                                        star <= rating
                                          ? "text-yellow-500 fill-current"
                                          : "text-gray-300"
                                      }`}
                                    />
                                  </button>
                                ))}
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium mb-2 block">Review</label>
                              <Textarea
                                placeholder="Share your experience with this seller..."
                                value={review}
                                onChange={(e) => setReview(e.target.value)}
                                rows={4}
                            />
                            </div>
                            <Button onClick={handleSubmitReview} className="w-full">
                              Submit Review
                            </Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Reviewable Orders Section */}
            {canReview && (
              <div className="mt-8">
                <ReviewableOrders sellerId={sellerId as Id<"users">} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
