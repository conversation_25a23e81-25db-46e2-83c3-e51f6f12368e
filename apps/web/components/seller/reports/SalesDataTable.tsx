"use client";

import { useState } from "react";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Card } from "@repo/ui/components/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { 
  Search, 
  Download, 
  FileText,
  Calendar,
  DollarSign,
  Package,
  ShoppingBag,
  TrendingUp,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import { SalesData } from "./SalesReportsDashboard";

interface SalesDataTableProps {
  salesData: SalesData[];
  onExport: (format: "csv" | "pdf") => void;
}

type SortField = keyof SalesData;
type SortDirection = "asc" | "desc";

export function SalesDataTable({ 
  salesData, 
  onExport 
}: SalesDataTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<SortField>("date");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Filter data based on search term
  const filteredData = salesData.filter(item => {
    const searchLower = searchTerm.toLowerCase();
    return (
      formatDate(item.date).toLowerCase().includes(searchLower) ||
      formatCurrency(item.revenue).toLowerCase().includes(searchLower) ||
      item.itemsSold.toString().includes(searchLower) ||
      item.orders.toString().includes(searchLower)
    );
  });

  // Sort data
  const sortedData = [...filteredData].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];

    // Handle date sorting
    if (sortField === "date") {
      aValue = new Date(aValue as string).getTime();
      bValue = new Date(bValue as string).getTime();
    }

    if (sortDirection === "asc") {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  // Paginate data
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = sortedData.slice(startIndex, startIndex + itemsPerPage);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="w-4 h-4 text-neutral-400" />;
    }
    return sortDirection === "asc" ? (
      <ArrowUp className="w-4 h-4 text-blue-600" />
    ) : (
      <ArrowDown className="w-4 h-4 text-blue-600" />
    );
  };

  const calculateTotals = () => {
    return filteredData.reduce(
      (totals, item) => ({
        revenue: totals.revenue + item.revenue,
        itemsSold: totals.itemsSold + item.itemsSold,
        orders: totals.orders + item.orders,
        averageOrderValue: 0 // Will calculate after
      }),
      { revenue: 0, itemsSold: 0, orders: 0, averageOrderValue: 0 }
    );
  };

  const totals = calculateTotals();
  totals.averageOrderValue = totals.orders > 0 ? totals.revenue / totals.orders : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-black dark:text-white">
            Detailed Sales Data
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400">
            Complete breakdown of sales performance
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => onExport("csv")}
            className="h-10"
          >
            <Download className="w-4 h-4 mr-2" />
            Export CSV
          </Button>
          
          <Button
            variant="outline"
            onClick={() => onExport("pdf")}
            className="h-10"
          >
            <FileText className="w-4 h-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <DollarSign className="w-8 h-8 text-green-600 dark:text-green-400" />
            <div>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Total Revenue
              </p>
              <p className="text-xl font-bold text-green-600 dark:text-green-400">
                {formatCurrency(totals.revenue)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <Package className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            <div>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Items Sold
              </p>
              <p className="text-xl font-bold text-blue-600 dark:text-blue-400">
                {totals.itemsSold}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <ShoppingBag className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            <div>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Total Orders
              </p>
              <p className="text-xl font-bold text-purple-600 dark:text-purple-400">
                {totals.orders}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-8 h-8 text-orange-600 dark:text-orange-400" />
            <div>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Avg Order Value
              </p>
              <p className="text-xl font-bold text-orange-600 dark:text-orange-400">
                {formatCurrency(totals.averageOrderValue)}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" />
          <Input
            placeholder="Search sales data..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-10"
          />
        </div>

        <Select value={itemsPerPage.toString()} onValueChange={(value) => {
          setItemsPerPage(parseInt(value));
          setCurrentPage(1);
        }}>
          <SelectTrigger className="w-32 h-10">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 per page</SelectItem>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="25">25 per page</SelectItem>
            <SelectItem value="50">50 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Data Table */}
      <Card className="overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-neutral-50 dark:bg-neutral-900">
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("date")}
                  className="h-8 px-2 font-semibold"
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Date
                  {getSortIcon("date")}
                </Button>
              </TableHead>
              
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("revenue")}
                  className="h-8 px-2 font-semibold"
                >
                  <DollarSign className="w-4 h-4 mr-2" />
                  Revenue
                  {getSortIcon("revenue")}
                </Button>
              </TableHead>
              
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("itemsSold")}
                  className="h-8 px-2 font-semibold"
                >
                  <Package className="w-4 h-4 mr-2" />
                  Items Sold
                  {getSortIcon("itemsSold")}
                </Button>
              </TableHead>
              
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("orders")}
                  className="h-8 px-2 font-semibold"
                >
                  <ShoppingBag className="w-4 h-4 mr-2" />
                  Orders
                  {getSortIcon("orders")}
                </Button>
              </TableHead>
              
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("averageOrderValue")}
                  className="h-8 px-2 font-semibold"
                >
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Avg Order Value
                  {getSortIcon("averageOrderValue")}
                </Button>
              </TableHead>
            </TableRow>
          </TableHeader>
          
          <TableBody>
            {paginatedData.map((item, index) => (
              <TableRow key={index} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                <TableCell className="font-medium">
                  {formatDate(item.date)}
                </TableCell>
                
                <TableCell>
                  <span className="font-bold text-green-600 dark:text-green-400">
                    {formatCurrency(item.revenue)}
                  </span>
                </TableCell>
                
                <TableCell>
                  <span className="font-semibold text-blue-600 dark:text-blue-400">
                    {item.itemsSold}
                  </span>
                </TableCell>
                
                <TableCell>
                  <span className="font-semibold text-purple-600 dark:text-purple-400">
                    {item.orders}
                  </span>
                </TableCell>
                
                <TableCell>
                  <span className="font-semibold text-orange-600 dark:text-orange-400">
                    {formatCurrency(item.averageOrderValue)}
                  </span>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, sortedData.length)} of {sortedData.length} results
          </p>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className="w-8 h-8 p-0"
                  >
                    {page}
                  </Button>
                );
              })}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Empty State */}
      {filteredData.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
            No sales data found
          </h3>
          <p className="text-neutral-500 dark:text-neutral-500">
            {searchTerm ? "Try adjusting your search terms" : "Sales data will appear here once you start making sales"}
          </p>
        </div>
      )}
    </div>
  );
}
