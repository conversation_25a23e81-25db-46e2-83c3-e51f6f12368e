"use client";

import { useState, useMemo } from "react";
import { Card } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { 
  BarChart3, 
  TrendingUp, 
  Download,
  Calendar,
  DollarSign,
  Package,
  Users,
  Target
} from "lucide-react";
import { SalesMetricsCards } from "./SalesMetricsCards";
import { SalesChartsSection } from "./SalesChartsSection";
import { TopPerformersSection } from "./TopPerformersSection";
import { DateRangeSelector } from "./DateRangeSelector";
import { SalesDataTable } from "./SalesDataTable";

export interface SalesData {
  date: string;
  revenue: number;
  itemsSold: number;
  orders: number;
  averageOrderValue: number;
}

export interface TopCustomer {
  id: string;
  name: string;
  email: string;
  totalSpent: number;
  orderCount: number;
  lastPurchase: string;
}

export interface CategoryPerformance {
  category: string;
  revenue: number;
  itemsSold: number;
  averagePrice: number;
  conversionRate: number;
}

export interface ProfitableItem {
  id: string;
  title: string;
  category: string;
  costPaid: number;
  salePrice: number;
  profit: number;
  profitMargin: number;
  soldDate: string;
}

export interface DateRange {
  startDate: string;
  endDate: string;
  period: "daily" | "weekly" | "monthly" | "yearly" | "custom";
}

// Mock data
const MOCK_SALES_DATA: SalesData[] = [
  { date: "2024-01-01", revenue: 15000, itemsSold: 3, orders: 3, averageOrderValue: 5000 },
  { date: "2024-01-02", revenue: 8900, itemsSold: 1, orders: 1, averageOrderValue: 8900 },
  { date: "2024-01-03", revenue: 12500, itemsSold: 2, orders: 2, averageOrderValue: 6250 },
  { date: "2024-01-04", revenue: 0, itemsSold: 0, orders: 0, averageOrderValue: 0 },
  { date: "2024-01-05", revenue: 22000, itemsSold: 4, orders: 3, averageOrderValue: 7333 },
  { date: "2024-01-06", revenue: 18500, itemsSold: 2, orders: 2, averageOrderValue: 9250 },
  { date: "2024-01-07", revenue: 9800, itemsSold: 1, orders: 1, averageOrderValue: 9800 },
];

const MOCK_TOP_CUSTOMERS: TopCustomer[] = [
  {
    id: "1",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    totalSpent: 45000,
    orderCount: 6,
    lastPurchase: "2024-01-15"
  },
  {
    id: "2", 
    name: "Michael Chen",
    email: "<EMAIL>",
    totalSpent: 32000,
    orderCount: 4,
    lastPurchase: "2024-01-12"
  },
  {
    id: "3",
    name: "Emma Wilson", 
    email: "<EMAIL>",
    totalSpent: 28500,
    orderCount: 3,
    lastPurchase: "2024-01-10"
  }
];

const MOCK_CATEGORY_PERFORMANCE: CategoryPerformance[] = [
  {
    category: "Handbags",
    revenue: 85000,
    itemsSold: 12,
    averagePrice: 7083,
    conversionRate: 15.2
  },
  {
    category: "Accessories",
    revenue: 32000,
    itemsSold: 8,
    averagePrice: 4000,
    conversionRate: 12.8
  },
  {
    category: "Clothing",
    revenue: 28500,
    itemsSold: 6,
    averagePrice: 4750,
    conversionRate: 9.5
  }
];

const MOCK_PROFITABLE_ITEMS: ProfitableItem[] = [
  {
    id: "1",
    title: "Hermès Birkin 30 Black Togo",
    category: "Handbags",
    costPaid: 8000,
    salePrice: 15000,
    profit: 7000,
    profitMargin: 46.7,
    soldDate: "2024-01-15"
  },
  {
    id: "2",
    title: "Chanel Classic Flap Medium",
    category: "Handbags", 
    costPaid: 5500,
    salePrice: 8900,
    profit: 3400,
    profitMargin: 38.2,
    soldDate: "2024-01-12"
  }
];

export function SalesReportsDashboard() {
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: "2024-01-01",
    endDate: "2024-01-31",
    period: "monthly"
  });

  const [activeTab, setActiveTab] = useState<"overview" | "detailed">("overview");

  // Filter data based on date range
  const filteredSalesData = useMemo(() => {
    return MOCK_SALES_DATA.filter(data => {
      const dataDate = new Date(data.date);
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      return dataDate >= startDate && dataDate <= endDate;
    });
  }, [dateRange]);

  // Calculate summary metrics
  const summaryMetrics = useMemo(() => {
    const totalRevenue = filteredSalesData.reduce((sum, data) => sum + data.revenue, 0);
    const totalItemsSold = filteredSalesData.reduce((sum, data) => sum + data.itemsSold, 0);
    const totalOrders = filteredSalesData.reduce((sum, data) => sum + data.orders, 0);
    const averageSalePrice = totalItemsSold > 0 ? totalRevenue / totalItemsSold : 0;
    const conversionRate = 8.5; // Mock conversion rate
    const activeListingValue = 125000; // Mock active listing value

    return {
      totalRevenue,
      totalItemsSold,
      totalOrders,
      averageSalePrice,
      conversionRate,
      activeListingValue
    };
  }, [filteredSalesData]);

  const handleExportData = (format: "csv" | "pdf") => {
    console.log(`Exporting data as ${format.toUpperCase()}`);
    // Implementation would go here
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-black dark:text-white">
            Sales Reports & Analytics
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-1">
            Track your sales performance and business insights
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => handleExportData("csv")}
            className="h-12 px-6"
          >
            <Download className="w-4 h-4 mr-2" />
            Export CSV
          </Button>
          
          <Button
            variant="outline"
            onClick={() => handleExportData("pdf")}
            className="h-12 px-6"
          >
            <Download className="w-4 h-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Date Range Selector */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <h2 className="text-lg font-semibold text-black dark:text-white">
              Report Period
            </h2>
          </div>
          <Badge variant="outline" className="text-sm">
            {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}
          </Badge>
        </div>
        
        <DateRangeSelector
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
        />
      </Card>

      {/* Key Metrics */}
      <SalesMetricsCards metrics={summaryMetrics} />

      {/* Tab Navigation */}
      <div className="flex items-center space-x-1 bg-neutral-100 dark:bg-neutral-800 p-1 rounded-xl w-fit">
        <Button
          variant={activeTab === "overview" ? "default" : "ghost"}
          onClick={() => setActiveTab("overview")}
          className="h-10 px-6"
        >
          <BarChart3 className="w-4 h-4 mr-2" />
          Overview
        </Button>
        <Button
          variant={activeTab === "detailed" ? "default" : "ghost"}
          onClick={() => setActiveTab("detailed")}
          className="h-10 px-6"
        >
          <TrendingUp className="w-4 h-4 mr-2" />
          Detailed Analysis
        </Button>
      </div>

      {/* Content based on active tab */}
      {activeTab === "overview" ? (
        <div className="space-y-8">
          {/* Charts Section */}
          <SalesChartsSection 
            salesData={filteredSalesData}
            dateRange={dateRange}
          />

          {/* Top Performers */}
          <TopPerformersSection
            topCustomers={MOCK_TOP_CUSTOMERS}
            categoryPerformance={MOCK_CATEGORY_PERFORMANCE}
            profitableItems={MOCK_PROFITABLE_ITEMS}
          />
        </div>
      ) : (
        <div className="space-y-8">
          {/* Detailed Data Table */}
          <SalesDataTable
            salesData={filteredSalesData}
            onExport={handleExportData}
          />
        </div>
      )}
    </div>
  );
}
