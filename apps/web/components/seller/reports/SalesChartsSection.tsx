"use client";

import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign,
  Package,
  ShoppingBag
} from "lucide-react";
import { SalesData, DateRange } from "./SalesReportsDashboard";

interface SalesChartsSectionProps {
  salesData: SalesData[];
  dateRange: DateRange;
}

export function SalesChartsSection({ 
  salesData, 
  dateRange 
}: SalesChartsSectionProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate max values for scaling
  const maxRevenue = Math.max(...salesData.map(d => d.revenue));
  const maxItems = Math.max(...salesData.map(d => d.itemsSold));

  // Simple bar chart component
  const BarChart = ({ 
    data, 
    valueKey, 
    maxValue, 
    color = "blue",
    formatValue = (v: number) => v.toString()
  }: {
    data: SalesData[];
    valueKey: keyof SalesData;
    maxValue: number;
    color?: string;
    formatValue?: (value: number) => string;
  }) => {
    const colorClasses = {
      blue: "bg-blue-500",
      green: "bg-green-500",
      purple: "bg-purple-500",
      orange: "bg-orange-500"
    };

    return (
      <div className="space-y-4">
        <div className="flex items-end space-x-2 h-48">
          {data.map((item, index) => {
            const value = item[valueKey] as number;
            const height = maxValue > 0 ? (value / maxValue) * 100 : 0;
            
            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="relative w-full flex items-end justify-center h-40">
                  <div
                    className={`w-full max-w-8 ${colorClasses[color as keyof typeof colorClasses]} rounded-t transition-all duration-300 hover:opacity-80`}
                    style={{ height: `${height}%` }}
                    title={`${formatDate(item.date)}: ${formatValue(value)}`}
                  />
                </div>
                <div className="text-xs text-neutral-500 mt-2 text-center">
                  {formatDate(item.date)}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Line chart component (simplified)
  const LineChart = ({ 
    data, 
    valueKey, 
    color = "blue",
    formatValue = (v: number) => v.toString()
  }: {
    data: SalesData[];
    valueKey: keyof SalesData;
    color?: string;
    formatValue?: (value: number) => string;
  }) => {
    const maxValue = Math.max(...data.map(d => d[valueKey] as number));
    const minValue = Math.min(...data.map(d => d[valueKey] as number));
    const range = maxValue - minValue;

    const colorClasses = {
      blue: "stroke-blue-500",
      green: "stroke-green-500",
      purple: "stroke-purple-500",
      orange: "stroke-orange-500"
    };

    const points = data.map((item, index) => {
      const value = item[valueKey] as number;
      const x = (index / (data.length - 1)) * 100;
      const y = range > 0 ? ((maxValue - value) / range) * 80 + 10 : 50;
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="space-y-4">
        <div className="relative h-48 w-full">
          <svg className="w-full h-40" viewBox="0 0 100 100" preserveAspectRatio="none">
            <polyline
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              points={points}
              className={colorClasses[color as keyof typeof colorClasses]}
            />
            {data.map((item, index) => {
              const value = item[valueKey] as number;
              const x = (index / (data.length - 1)) * 100;
              const y = range > 0 ? ((maxValue - value) / range) * 80 + 10 : 50;
              
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r="2"
                  fill="currentColor"
                  className={colorClasses[color as keyof typeof colorClasses]}
                  // @ts-ignore
                  title={`${formatDate(item.date)}: ${formatValue(value)}`}
                />
              );
            })}
          </svg>
          <div className="flex justify-between text-xs text-neutral-500 mt-2">
            {data.map((item, index) => (
              <span key={index} className="text-center">
                {formatDate(item.date)}
              </span>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Revenue Trend Chart */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
              <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Revenue Trend
              </h3>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Daily revenue over time
              </p>
            </div>
          </div>
          <Badge variant="outline" className="text-xs">
            {salesData.length} days
          </Badge>
        </div>
        
        <LineChart
          data={salesData}
          valueKey="revenue"
          color="green"
          formatValue={formatCurrency}
        />
      </Card>

      {/* Items Sold Chart */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
              <Package className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Items Sold
              </h3>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Number of items sold daily
              </p>
            </div>
          </div>
          <Badge variant="outline" className="text-xs">
            {salesData.reduce((sum, d) => sum + d.itemsSold, 0)} total
          </Badge>
        </div>
        
        <BarChart
          data={salesData}
          valueKey="itemsSold"
          maxValue={maxItems}
          color="blue"
        />
      </Card>

      {/* Orders Chart */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center">
              <ShoppingBag className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Daily Orders
              </h3>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Number of orders per day
              </p>
            </div>
          </div>
          <Badge variant="outline" className="text-xs">
            {salesData.reduce((sum, d) => sum + d.orders, 0)} total
          </Badge>
        </div>
        
        <BarChart
          data={salesData}
          valueKey="orders"
          maxValue={Math.max(...salesData.map(d => d.orders))}
          color="purple"
        />
      </Card>

      {/* Average Order Value Chart */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Average Order Value
              </h3>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Average value per order
              </p>
            </div>
          </div>
          <Badge variant="outline" className="text-xs">
            Trend
          </Badge>
        </div>
        
        <LineChart
          data={salesData}
          valueKey="averageOrderValue"
          color="orange"
          formatValue={formatCurrency}
        />
      </Card>
    </div>
  );
}
