"use client";

import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  DollarSign, 
  Package, 
  TrendingUp, 
  Target,
  ShoppingBag,
  Percent,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react";

interface SalesMetrics {
  totalRevenue: number;
  totalItemsSold: number;
  totalOrders: number;
  averageSalePrice: number;
  conversionRate: number;
  activeListingValue: number;
}

interface SalesMetricsCardsProps {
  metrics: SalesMetrics;
  previousMetrics?: SalesMetrics; // For comparison
}

export function SalesMetricsCards({ 
  metrics, 
  previousMetrics 
}: SalesMetricsCardsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const getChangeIndicator = (current: number, previous?: number) => {
    if (!previous) return null;
    
    const change = calculateChange(current, previous);
    const isPositive = change > 0;
    const isNegative = change < 0;
    
    if (Math.abs(change) < 0.1) return null;
    
    return (
      <div className={`flex items-center space-x-1 text-sm ${
        isPositive ? 'text-green-600 dark:text-green-400' : 
        isNegative ? 'text-red-600 dark:text-red-400' : 
        'text-neutral-500'
      }`}>
        {isPositive ? (
          <ArrowUpRight className="w-3 h-3" />
        ) : isNegative ? (
          <ArrowDownRight className="w-3 h-3" />
        ) : null}
        <span>{Math.abs(change).toFixed(1)}%</span>
      </div>
    );
  };

  const metricCards = [
    {
      title: "Total Revenue",
      value: formatCurrency(metrics.totalRevenue),
      icon: DollarSign,
      color: "green",
      bgColor: "bg-green-50 dark:bg-green-900/20",
      iconColor: "text-green-600 dark:text-green-400",
      borderColor: "border-green-200 dark:border-green-800",
      change: previousMetrics ? getChangeIndicator(metrics.totalRevenue, previousMetrics.totalRevenue) : null,
      description: "Total sales revenue for the period"
    },
    {
      title: "Items Sold",
      value: metrics.totalItemsSold.toLocaleString(),
      icon: Package,
      color: "blue",
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
      iconColor: "text-blue-600 dark:text-blue-400",
      borderColor: "border-blue-200 dark:border-blue-800",
      change: previousMetrics ? getChangeIndicator(metrics.totalItemsSold, previousMetrics.totalItemsSold) : null,
      description: "Number of items successfully sold"
    },
    {
      title: "Average Sale Price",
      value: formatCurrency(metrics.averageSalePrice),
      icon: TrendingUp,
      color: "purple",
      bgColor: "bg-purple-50 dark:bg-purple-900/20",
      iconColor: "text-purple-600 dark:text-purple-400",
      borderColor: "border-purple-200 dark:border-purple-800",
      change: previousMetrics ? getChangeIndicator(metrics.averageSalePrice, previousMetrics.averageSalePrice) : null,
      description: "Average price per item sold"
    },
    {
      title: "Total Orders",
      value: metrics.totalOrders.toLocaleString(),
      icon: ShoppingBag,
      color: "orange",
      bgColor: "bg-orange-50 dark:bg-orange-900/20",
      iconColor: "text-orange-600 dark:text-orange-400",
      borderColor: "border-orange-200 dark:border-orange-800",
      change: previousMetrics ? getChangeIndicator(metrics.totalOrders, previousMetrics.totalOrders) : null,
      description: "Number of completed orders"
    },
    {
      title: "Conversion Rate",
      value: formatPercentage(metrics.conversionRate),
      icon: Target,
      color: "indigo",
      bgColor: "bg-indigo-50 dark:bg-indigo-900/20",
      iconColor: "text-indigo-600 dark:text-indigo-400",
      borderColor: "border-indigo-200 dark:border-indigo-800",
      change: previousMetrics ? getChangeIndicator(metrics.conversionRate, previousMetrics.conversionRate) : null,
      description: "Percentage of views that convert to sales"
    },
    {
      title: "Active Listing Value",
      value: formatCurrency(metrics.activeListingValue),
      icon: Percent,
      color: "teal",
      bgColor: "bg-teal-50 dark:bg-teal-900/20",
      iconColor: "text-teal-600 dark:text-teal-400",
      borderColor: "border-teal-200 dark:border-teal-800",
      change: previousMetrics ? getChangeIndicator(metrics.activeListingValue, previousMetrics.activeListingValue) : null,
      description: "Total value of all active listings"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {metricCards.map((card, index) => {
        const Icon = card.icon;
        
        return (
          <Card 
            key={index}
            className={`p-6 ${card.bgColor} ${card.borderColor} border transition-all duration-200 hover:shadow-lg`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <p className={`text-sm font-medium ${card.iconColor}`}>
                    {card.title}
                  </p>
                  {card.change}
                </div>
                
                <p className={`text-3xl font-bold ${card.iconColor} mb-1`}>
                  {card.value}
                </p>
                
                <p className="text-xs text-neutral-600 dark:text-neutral-400">
                  {card.description}
                </p>
              </div>
              
              <div className={`w-12 h-12 ${card.bgColor} rounded-xl flex items-center justify-center`}>
                <Icon className={`w-6 h-6 ${card.iconColor}`} />
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
}
