"use client";

import { useState } from "react";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@repo/ui/components/popover";
import { Calendar, ChevronDown } from "lucide-react";

export interface DateRange {
  startDate: string;
  endDate: string;
  period: "daily" | "weekly" | "monthly" | "yearly" | "custom";
}

interface DateRangeSelectorProps {
  dateRange: DateRange;
  onDateRangeChange: (dateRange: DateRange) => void;
}

const PRESET_RANGES = [
  {
    label: "Today",
    period: "daily" as const,
    getValue: () => {
      const today = new Date();
      const dateStr = today.toISOString().split('T')[0];
      return { startDate: dateStr, endDate: dateStr };
    }
  },
  {
    label: "Yesterday",
    period: "daily" as const,
    getValue: () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const dateStr = yesterday.toISOString().split('T')[0];
      return { startDate: dateStr, endDate: dateStr };
    }
  },
  {
    label: "This Week",
    period: "weekly" as const,
    getValue: () => {
      const today = new Date();
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      
      return {
        startDate: startOfWeek.toISOString().split('T')[0],
        endDate: endOfWeek.toISOString().split('T')[0]
      };
    }
  },
  {
    label: "Last Week",
    period: "weekly" as const,
    getValue: () => {
      const today = new Date();
      const startOfLastWeek = new Date(today);
      startOfLastWeek.setDate(today.getDate() - today.getDay() - 7);
      const endOfLastWeek = new Date(startOfLastWeek);
      endOfLastWeek.setDate(startOfLastWeek.getDate() + 6);
      
      return {
        startDate: startOfLastWeek.toISOString().split('T')[0],
        endDate: endOfLastWeek.toISOString().split('T')[0]
      };
    }
  },
  {
    label: "This Month",
    period: "monthly" as const,
    getValue: () => {
      const today = new Date();
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      
      return {
        startDate: startOfMonth.toISOString().split('T')[0],
        endDate: endOfMonth.toISOString().split('T')[0]
      };
    }
  },
  {
    label: "Last Month",
    period: "monthly" as const,
    getValue: () => {
      const today = new Date();
      const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
      
      return {
        startDate: startOfLastMonth.toISOString().split('T')[0],
        endDate: endOfLastMonth.toISOString().split('T')[0]
      };
    }
  },
  {
    label: "This Quarter",
    period: "monthly" as const,
    getValue: () => {
      const today = new Date();
      const quarter = Math.floor(today.getMonth() / 3);
      const startOfQuarter = new Date(today.getFullYear(), quarter * 3, 1);
      const endOfQuarter = new Date(today.getFullYear(), quarter * 3 + 3, 0);
      
      return {
        startDate: startOfQuarter.toISOString().split('T')[0],
        endDate: endOfQuarter.toISOString().split('T')[0]
      };
    }
  },
  {
    label: "This Year",
    period: "yearly" as const,
    getValue: () => {
      const today = new Date();
      const startOfYear = new Date(today.getFullYear(), 0, 1);
      const endOfYear = new Date(today.getFullYear(), 11, 31);
      
      return {
        startDate: startOfYear.toISOString().split('T')[0],
        endDate: endOfYear.toISOString().split('T')[0]
      };
    }
  },
  {
    label: "Last Year",
    period: "yearly" as const,
    getValue: () => {
      const today = new Date();
      const startOfLastYear = new Date(today.getFullYear() - 1, 0, 1);
      const endOfLastYear = new Date(today.getFullYear() - 1, 11, 31);
      
      return {
        startDate: startOfLastYear.toISOString().split('T')[0],
        endDate: endOfLastYear.toISOString().split('T')[0]
      };
    }
  }
];

export function DateRangeSelector({ 
  dateRange, 
  onDateRangeChange 
}: DateRangeSelectorProps) {
  const [isCustomOpen, setIsCustomOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState(dateRange.startDate);
  const [customEndDate, setCustomEndDate] = useState(dateRange.endDate);

  const handlePresetSelect = (preset: typeof PRESET_RANGES[0]) => {
    const { startDate, endDate } = preset.getValue();
    onDateRangeChange({
      startDate: startDate || "",
      endDate: endDate || "",
      period: preset.period
    });
  };

  const handleCustomDateApply = () => {
    if (customStartDate && customEndDate) {
      onDateRangeChange({
        startDate: customStartDate,
        endDate: customEndDate,
        period: "custom"
      });
      setIsCustomOpen(false);
    }
  };

  const formatDateRange = () => {
    const start = new Date(dateRange.startDate);
    const end = new Date(dateRange.endDate);
    
    if (dateRange.startDate === dateRange.endDate) {
      return start.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    }
    
    return `${start.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })} - ${end.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })}`;
  };

  const getActivePeriodLabel = () => {
    const preset = PRESET_RANGES.find(p => {
      const { startDate, endDate } = p.getValue();
      return startDate === dateRange.startDate && endDate === dateRange.endDate;
    });
    
    return preset ? preset.label : "Custom Range";
  };

  return (
    <div className="flex flex-wrap items-center gap-3">
      {/* Preset Buttons */}
      <div className="flex flex-wrap items-center gap-2">
        {PRESET_RANGES.slice(0, 6).map((preset) => {
          const { startDate, endDate } = preset.getValue();
          const isActive = startDate === dateRange.startDate && endDate === dateRange.endDate;
          
          return (
            <Button
              key={preset.label}
              variant={isActive ? "default" : "outline"}
              size="sm"
              onClick={() => handlePresetSelect(preset)}
              className="h-9 px-4"
            >
              {preset.label}
            </Button>
          );
        })}
      </div>

      {/* More Options Dropdown */}
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="h-9 px-4">
            More Options
            <ChevronDown className="w-3 h-3 ml-2" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-48 p-2" align="start">
          <div className="space-y-1">
            {PRESET_RANGES.slice(6).map((preset) => {
              const { startDate, endDate } = preset.getValue();
              const isActive = startDate === dateRange.startDate && endDate === dateRange.endDate;
              
              return (
                <Button
                  key={preset.label}
                  variant={isActive ? "default" : "ghost"}
                  size="sm"
                  onClick={() => handlePresetSelect(preset)}
                  className="w-full justify-start h-8"
                >
                  {preset.label}
                </Button>
              );
            })}
          </div>
        </PopoverContent>
      </Popover>

      {/* Custom Date Range */}
      <Popover open={isCustomOpen} onOpenChange={setIsCustomOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant={dateRange.period === "custom" ? "default" : "outline"} 
            size="sm" 
            className="h-9 px-4"
          >
            <Calendar className="w-3 h-3 mr-2" />
            Custom Range
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="start">
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-black dark:text-white mb-3">
                Custom Date Range
              </h4>
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="startDate" className="text-sm">
                  Start Date
                </Label>
                <Input
                  id="startDate"
                  type="date"
                  value={customStartDate}
                  onChange={(e) => setCustomStartDate(e.target.value)}
                  className="h-9"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="endDate" className="text-sm">
                  End Date
                </Label>
                <Input
                  id="endDate"
                  type="date"
                  value={customEndDate}
                  onChange={(e) => setCustomEndDate(e.target.value)}
                  className="h-9"
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsCustomOpen(false)}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleCustomDateApply}
                disabled={!customStartDate || !customEndDate}
              >
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Current Selection Display */}
      <div className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400 ml-auto">
        <span className="font-medium">{getActivePeriodLabel()}:</span>
        <span>{formatDateRange()}</span>
      </div>
    </div>
  );
}
