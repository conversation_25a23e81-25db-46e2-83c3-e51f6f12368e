"use client";

import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { 
  Users, 
  Package, 
  TrendingUp,
  Crown,
  Star,
  Award,
  Mail,
  Calendar,
  DollarSign,
  Percent
} from "lucide-react";
import { TopCustomer, CategoryPerformance, ProfitableItem } from "./SalesReportsDashboard";

interface TopPerformersSectionProps {
  topCustomers: TopCustomer[];
  categoryPerformance: CategoryPerformance[];
  profitableItems: ProfitableItem[];
}

export function TopPerformersSection({ 
  topCustomers, 
  categoryPerformance, 
  profitableItems 
}: TopPerformersSectionProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
      {/* Top Customers */}
      <Card className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
            <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-black dark:text-white">
              Top Customers
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              By total purchase amount
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {topCustomers.map((customer, index) => (
            <div key={customer.id} className="flex items-center space-x-4 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-xl">
              <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex-shrink-0">
                {index === 0 ? (
                  <Crown className="w-4 h-4 text-yellow-600" />
                ) : index === 1 ? (
                  <Star className="w-4 h-4 text-gray-600" />
                ) : index === 2 ? (
                  <Award className="w-4 h-4 text-orange-600" />
                ) : (
                  <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                    {index + 1}
                  </span>
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-black dark:text-white truncate">
                    {customer.name}
                  </h4>
                  <span className="text-lg font-bold text-green-600 dark:text-green-400">
                    {formatCurrency(customer.totalSpent)}
                  </span>
                </div>
                
                <div className="flex items-center justify-between mt-1">
                  <p className="text-sm text-neutral-600 dark:text-neutral-400 truncate">
                    {customer.email}
                  </p>
                  <span className="text-sm text-neutral-500">
                    {customer.orderCount} orders
                  </span>
                </div>
                
                <div className="flex items-center space-x-2 mt-2">
                  <Calendar className="w-3 h-3 text-neutral-400" />
                  <span className="text-xs text-neutral-500">
                    Last purchase: {formatDate(customer.lastPurchase)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <Button variant="outline" className="w-full mt-4">
          <Mail className="w-4 h-4 mr-2" />
          Contact Top Customers
        </Button>
      </Card>

      {/* Best-Selling Categories */}
      <Card className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
            <Package className="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-black dark:text-white">
              Best-Selling Categories
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Performance by category
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {categoryPerformance.map((category, index) => {
            const maxRevenue = Math.max(...categoryPerformance.map(c => c.revenue));
            const revenuePercentage = (category.revenue / maxRevenue) * 100;
            
            return (
              <div key={category.category} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center justify-center w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full">
                      <span className="text-xs font-bold text-green-600 dark:text-green-400">
                        {index + 1}
                      </span>
                    </div>
                    <h4 className="font-semibold text-black dark:text-white">
                      {category.category}
                    </h4>
                  </div>
                  <span className="text-lg font-bold text-green-600 dark:text-green-400">
                    {formatCurrency(category.revenue)}
                  </span>
                </div>
                
                <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${revenuePercentage}%` }}
                  />
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <p className="text-neutral-500">Items Sold</p>
                    <p className="font-semibold text-black dark:text-white">
                      {category.itemsSold}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-neutral-500">Avg Price</p>
                    <p className="font-semibold text-black dark:text-white">
                      {formatCurrency(category.averagePrice)}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-neutral-500">Conv. Rate</p>
                    <p className="font-semibold text-black dark:text-white">
                      {formatPercentage(category.conversionRate)}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <Button variant="outline" className="w-full mt-4">
          <Package className="w-4 h-4 mr-2" />
          View Category Details
        </Button>
      </Card>

      {/* Most Profitable Items */}
      <Card className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center">
            <TrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-black dark:text-white">
              Most Profitable Items
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Highest profit margins
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {profitableItems.map((item, index) => (
            <div key={item.id} className="p-4 bg-neutral-50 dark:bg-neutral-800 rounded-xl">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="flex items-center justify-center w-6 h-6 bg-purple-100 dark:bg-purple-900 rounded-full">
                      <span className="text-xs font-bold text-purple-600 dark:text-purple-400">
                        {index + 1}
                      </span>
                    </div>
                    <h4 className="font-semibold text-black dark:text-white truncate">
                      {item.title}
                    </h4>
                  </div>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    {item.category}
                  </p>
                </div>
                
                <div className="text-right">
                  <div className="flex items-center space-x-1">
                    <Percent className="w-3 h-3 text-purple-600 dark:text-purple-400" />
                    <span className="text-lg font-bold text-purple-600 dark:text-purple-400">
                      {formatPercentage(item.profitMargin)}
                    </span>
                  </div>
                  <p className="text-sm text-neutral-500">
                    margin
                  </p>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-3 text-sm">
                <div>
                  <p className="text-neutral-500">Cost</p>
                  <p className="font-semibold text-red-600 dark:text-red-400">
                    {formatCurrency(item.costPaid)}
                  </p>
                </div>
                <div>
                  <p className="text-neutral-500">Sale Price</p>
                  <p className="font-semibold text-green-600 dark:text-green-400">
                    {formatCurrency(item.salePrice)}
                  </p>
                </div>
                <div>
                  <p className="text-neutral-500">Profit</p>
                  <p className="font-semibold text-purple-600 dark:text-purple-400">
                    {formatCurrency(item.profit)}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 mt-3 pt-3 border-t border-neutral-200 dark:border-neutral-700">
                <Calendar className="w-3 h-3 text-neutral-400" />
                <span className="text-xs text-neutral-500">
                  Sold: {formatDate(item.soldDate)}
                </span>
              </div>
            </div>
          ))}
        </div>

        <Button variant="outline" className="w-full mt-4">
          <TrendingUp className="w-4 h-4 mr-2" />
          View Profit Analysis
        </Button>
      </Card>
    </div>
  );
}
