"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  DollarSign, 
  Package, 
  TrendingUp, 
  TrendingDown,
  Eye,
  ShoppingCart,
  Target,
  Percent
} from "lucide-react";

export function KeyMetrics() {
  const metrics = useQuery(api.analytics.getKeyMetrics, { 
    compareToLastPeriod: true 
  });

  if (!metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-gray-200 rounded w-24"></div>
              <div className="h-4 w-4 bg-gray-200 rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-20 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-32"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num >= 0 ? '+' : ''}${num.toFixed(1)}%`;
  };

  const getTrendIcon = (change: number | undefined) => {
    if (change === undefined) return null;
    return change >= 0 ? 
      <TrendingUp className="h-4 w-4 text-green-600" /> : 
      <TrendingDown className="h-4 w-4 text-red-600" />;
  };

  const getTrendColor = (change: number | undefined) => {
    if (change === undefined) return "text-muted-foreground";
    return change >= 0 ? "text-green-600" : "text-red-600";
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Revenue */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Total Revenue
          </CardTitle>
          <DollarSign className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(metrics.totalRevenue)}</div>
          {metrics.revenueChange !== undefined && (
            <div className={`flex items-center gap-1 mt-1 text-xs ${getTrendColor(metrics.revenueChange)}`}>
              {getTrendIcon(metrics.revenueChange)}
              <span>{formatPercentage(metrics.revenueChange)} from last period</span>
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-1">
            Last 30 days
          </p>
        </CardContent>
      </Card>

      {/* Items Sold */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Items Sold
          </CardTitle>
          <ShoppingCart className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(metrics.itemsSold)}</div>
          {metrics.itemsSoldChange !== undefined && (
            <div className={`flex items-center gap-1 mt-1 text-xs ${getTrendColor(metrics.itemsSoldChange)}`}>
              {getTrendIcon(metrics.itemsSoldChange)}
              <span>{formatPercentage(metrics.itemsSoldChange)} from last period</span>
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-1">
            Last 30 days
          </p>
        </CardContent>
      </Card>

      {/* Average Sale Price */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Avg. Sale Price
          </CardTitle>
          <Target className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(metrics.averageSalePrice)}</div>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="secondary" className="text-xs">
              Per item
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Based on recent sales
          </p>
        </CardContent>
      </Card>

      {/* Active Listing Value */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Active Inventory
          </CardTitle>
          <Package className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(metrics.activeListingValue)}</div>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="text-xs">
              {formatNumber(metrics.activeListingsCount)} items
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Total listing value
          </p>
        </CardContent>
      </Card>

      {/* Conversion Rate */}
      <Card className="md:col-span-2 lg:col-span-2">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Conversion Rate
          </CardTitle>
          <Percent className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.conversionRate.toFixed(2)}%</div>
          <div className="flex items-center gap-2 mt-1">
            <Badge 
              variant={metrics.conversionRate >= 5 ? "default" : "secondary"} 
              className="text-xs"
            >
              {metrics.conversionRate >= 5 ? "Good" : "Needs Improvement"}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Sales vs. total product views
          </p>
        </CardContent>
      </Card>

      {/* Performance Summary */}
      <Card className="md:col-span-2 lg:col-span-2">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Performance Summary
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm">Revenue Growth</span>
              <span className={`text-sm font-medium ${getTrendColor(metrics.revenueChange)}`}>
                {metrics.revenueChange !== undefined ? formatPercentage(metrics.revenueChange) : "N/A"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Sales Volume</span>
              <span className={`text-sm font-medium ${getTrendColor(metrics.itemsSoldChange)}`}>
                {metrics.itemsSoldChange !== undefined ? formatPercentage(metrics.itemsSoldChange) : "N/A"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Inventory Turnover</span>
              <span className="text-sm font-medium text-blue-600">
                {metrics.activeListingsCount > 0 ? 
                  `${((metrics.itemsSold / metrics.activeListingsCount) * 100).toFixed(1)}%` : 
                  "0%"
                }
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
