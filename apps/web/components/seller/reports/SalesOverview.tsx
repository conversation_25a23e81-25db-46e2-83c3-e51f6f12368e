"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  PieChart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip,
  Legend
} from "recharts";
import { TrendingUp, Package, DollarSign, Users } from "lucide-react";

interface SalesOverviewProps {
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
}

export function SalesOverview({ dateRange }: SalesOverviewProps) {
  const analytics = useQuery(api.analytics.getSalesAnalytics, {
    startDate: dateRange.from?.getTime(),
    endDate: dateRange.to?.getTime(),
  });

  const topPerformers = useQuery(api.analytics.getTopPerformers, {
    startDate: dateRange.from?.getTime(),
    endDate: dateRange.to?.getTime(),
    limit: 5,
  });

  if (!analytics || !topPerformers) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-32"></div>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  // Prepare data for charts
  const categoryData = topPerformers.topCategories.map((category, index) => ({
    name: category.name,
    value: category.revenue,
    sales: category.sales,
    color: `hsl(${index * 60}, 70%, 50%)`,
  }));

  const paymentMethodData = analytics.salesWithProducts.reduce((acc: any[], sale) => {
    const existing = acc.find(item => item.method === sale.paymentMethod);
    if (existing) {
      existing.count += 1;
      existing.revenue += sale.salePrice;
    } else {
      acc.push({
        method: sale.paymentMethod.replace('_', ' ').toUpperCase(),
        count: 1,
        revenue: sale.salePrice,
      });
    }
    return acc;
  }, []);

  const monthlyTrend = analytics.timeSeriesData.slice(-6); // Last 6 periods

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">{formatCurrency(analytics.totalRevenue)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Items Sold</p>
                <p className="text-2xl font-bold">{analytics.totalItems}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg. Sale Price</p>
                <p className="text-2xl font-bold">{formatCurrency(analytics.averageSalePrice)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Unique Customers</p>
                <p className="text-2xl font-bold">{topPerformers.topCustomers.length}</p>
              </div>
              <Users className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Revenue Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue by Category</CardTitle>
          </CardHeader>
          <CardContent>
            {categoryData.length > 0 ? (
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={categoryData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {categoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>  
                    <Tooltip formatter={(value: number, name: string) => [formatCurrency(value), name === "value" ? "Revenue" : ""] as [string | number, string]} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                No category data available
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payment Methods */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
          </CardHeader>
          <CardContent>
            {paymentMethodData.length > 0 ? (
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={paymentMethodData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="method" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip 
                      formatter={(value: number, name: string) => [
                        name === "count" ? value : formatCurrency(value), 
                        name === "count" ? "Transactions" : "Revenue"
                      ] as [string | number, string]}
                    />
                    <Bar dataKey="count" fill="#0088FE" name="count" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                No payment data available
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Sales Trend */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Sales Trend</CardTitle>
        </CardHeader>
        <CardContent>
          {monthlyTrend.length > 0 ? (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={monthlyTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  />
                  <YAxis tick={{ fontSize: 12 }} tickFormatter={formatCurrency} />
                  <Tooltip 
                    formatter={(value: number, name: string) => [formatCurrency(value), name === "value" ? "Revenue" : ""] as [string | number, string]}
                    labelFormatter={(label) => `Date: ${new Date(label).toLocaleDateString()}`}
                  />
                  <Bar dataKey="revenue" fill="#00C49F" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center text-muted-foreground">
              No trend data available
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Best Category</h4>
              <p className="text-lg font-semibold">
                {categoryData.length > 0 && categoryData[0]?.name ? categoryData[0].name : "N/A"}
              </p>
              <p className="text-sm text-muted-foreground">
                {categoryData.length > 0 && categoryData[0]?.value ? formatCurrency(categoryData[0].value) : "No data"}
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Most Used Payment</h4>
              <p className="text-lg font-semibold">
                {paymentMethodData.length > 0 ? 
                  paymentMethodData.reduce((max, method) => 
                    method.count > max.count ? method : max
                  ).method : "N/A"
                }
              </p>
              <p className="text-sm text-muted-foreground">
                {paymentMethodData.length > 0 ? 
                  `${paymentMethodData.reduce((max, method) => 
                    method.count > max.count ? method : max
                  ).count} transactions` : "No data"
                }
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Growth Trend</h4>
              <div className="flex items-center gap-2">
                <Badge variant={monthlyTrend.length >= 2 && 
                  monthlyTrend[monthlyTrend.length - 1]?.revenue && monthlyTrend[0]?.revenue && (monthlyTrend[monthlyTrend.length - 1]?.revenue || 0) > (monthlyTrend[0]?.revenue || 0) ? 
                  "default" : "secondary"
                }>
                  {monthlyTrend.length >= 2 ? 
                    (monthlyTrend[monthlyTrend.length - 1]?.revenue && monthlyTrend[0]?.revenue && (monthlyTrend[monthlyTrend.length - 1]?.revenue || 0) > (monthlyTrend[0]?.revenue || 0) ? 
                      "Increasing" : "Decreasing") :
                    "Insufficient Data"
                  }
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Based on recent periods
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
