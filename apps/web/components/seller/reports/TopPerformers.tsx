"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import { 
  Users, 
  Package, 
  Tag, 
  Crown,
  TrendingUp,
  Mail,
  DollarSign,
  ArrowUpDown
} from "lucide-react";

interface TopPerformersProps {
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
}

type SortField = "totalSpent" | "purchases" | "name";
type SortDirection = "asc" | "desc";

export function TopPerformers({ dateRange }: TopPerformersProps) {
  const [customerSort, setCustomerSort] = useState<{ field: SortField; direction: SortDirection }>({
    field: "totalSpent",
    direction: "desc"
  });
  
  const topPerformers = useQuery(api.analytics.getTopPerformers, {
    startDate: dateRange.from?.getTime(),
    endDate: dateRange.to?.getTime(),
    limit: 10,
  });

  if (!topPerformers) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-32"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[...Array(5)].map((_, j) => (
                    <div key={j} className="h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleSort = (field: SortField) => {
    setCustomerSort(prev => ({
      field,
      direction: prev.field === field && prev.direction === "desc" ? "asc" : "desc"
    }));
  };

  const sortedCustomers = [...topPerformers.topCustomers].sort((a, b) => {
    const { field, direction } = customerSort;
    let aValue: any = a[field];
    let bValue: any = b[field];
    
    if (field === "name") {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }
    
    if (direction === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const getRankIcon = (index: number) => {
    switch (index) {
      case 0:
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 1:
        return <Crown className="h-4 w-4 text-gray-400" />;
      case 2:
        return <Crown className="h-4 w-4 text-amber-600" />;
      default:
        return <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold">Top Performers</h2>
        <p className="text-sm text-muted-foreground">
          Your best customers, categories, and products
        </p>
      </div>

      {/* Top Customers */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Top Customers
          </CardTitle>
          <Badge variant="secondary">
            {topPerformers.topCustomers.length} customers
          </Badge>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">Rank</TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleSort("name")}
                      className="h-auto p-0 font-medium"
                    >
                      Customer
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleSort("totalSpent")}
                      className="h-auto p-0 font-medium"
                    >
                      Total Spent
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleSort("purchases")}
                      className="h-auto p-0 font-medium"
                    >
                      Purchases
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                  </TableHead>
                  <TableHead>Avg. Order</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedCustomers.map((customer, index) => (
                  <TableRow key={customer.email}>
                    <TableCell className="flex items-center justify-center">
                      {getRankIcon(index)}
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{customer.name}</p>
                        <p className="text-sm text-muted-foreground flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {customer.email}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(customer.totalSpent)}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {customer.purchases} orders
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {formatCurrency(customer.totalSpent / customer.purchases)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Categories */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Best-Selling Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPerformers.topCategories.map((category, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getRankIcon(index)}
                    <div>
                      <p className="font-medium">{category.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {category.sales} sales
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(category.revenue)}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatCurrency(category.revenue / category.sales)} avg
                    </p>
                  </div>
                </div>
              ))}
              {topPerformers.topCategories.length === 0 && (
                <p className="text-center text-muted-foreground py-8">
                  No category data available
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Top Products */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Most Profitable Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPerformers.topProducts.map((product, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getRankIcon(index)}
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{product.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {product.brand} • {product.sales} sold
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">
                      {product.profit ? formatCurrency(product.profit) : formatCurrency(product.revenue)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {formatCurrency(product.averagePrice)} avg
                    </p>
                  </div>
                </div>
              ))}
              {topPerformers.topProducts.length === 0 && (
                <p className="text-center text-muted-foreground py-8">
                  No product data available
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Performance Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-2xl font-bold">
                {topPerformers.topCustomers.length > 0 ? 
                  formatCurrency(topPerformers.topCustomers[0]?.totalSpent || 0) : 
                  "$0"
                }
              </p>
              <p className="text-sm text-muted-foreground">Top Customer Value</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Tag className="h-8 w-8 text-blue-600" />
              </div>
              <p className="text-2xl font-bold">
                {topPerformers.topCategories.length > 0 ? 
                  topPerformers.topCategories[0]?.name || "N/A" : 
                  "N/A"
                }
              </p>
              <p className="text-sm text-muted-foreground">Best Category</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
              <p className="text-2xl font-bold">
                {topPerformers.topCustomers.reduce((sum, customer) => sum + customer.purchases, 0)}
              </p>
              <p className="text-sm text-muted-foreground">Total Orders</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
