"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Package, DollarSign, Eye, TrendingUp } from "lucide-react";

export function DashboardOverview() {
  const stats = useQuery(api.sellers.getDashboardStats);

  if (!stats) {
    return <div>Loading stats...</div>;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Total Inventory Value
          </CardTitle>
          <DollarSign className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {formatCurrency(stats.totalRevenue)}
          </div>
          <p className="text-xs text-neutral-600 dark:text-neutral-400 mt-1">
            Avg: {formatCurrency(stats.monthlyRevenue)}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Active Listings
          </CardTitle>
          <Package className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.activeProducts}</div>
          <p className="text-xs text-neutral-600 dark:text-neutral-400 mt-1">
            of {stats.totalProducts} total
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Recent Sales (30d)
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.completedOrders}</div>
          <div className="flex items-center gap-2 mt-1">
            <Badge className="text-xs">
              {formatCurrency(stats.totalRevenue)}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Total Views
          </CardTitle>
          <Eye className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.totalOrders)}</div>
          <p className="text-xs text-neutral-600 dark:text-neutral-400 mt-1">
            All time
          </p>
        </CardContent>
      </Card>
    </div>
  );
}