"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Badge } from "@repo/ui/components/badge";
import { 
  Upload, 
  X, 
  Plus,
  Loader2,
  ArrowLeft,
  Save,
  Eye,
  AlertCircle,
  CheckCircle,
  Building2,
  User,
  CreditCard,
  Mail,
  Phone,
  MapPin,
  Tag,
  Check,
  ChevronsUpDown
} from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";
import { SupplierFormData } from "./types";

interface SupplierFormProps {
  onSave: (data: SupplierFormData) => Promise<void>;
  onCancel: () => void;
  isEditing: boolean;
  initialData?: Partial<SupplierFormData>;
}

const PAYMENT_TERMS = [
  "Net 30",
  "Net 60", 
  "Net 90",
  "COD",
  "Immediate",
  "Custom"
];

const DEFAULT_TAGS = [
  "wholesale",
  "consignment", 
  "reliable",
  "premium",
  "local",
  "international",
  "fast-shipping",
  "bulk-orders"
];

export function SupplierForm({ onSave, onCancel, isEditing, initialData }: SupplierFormProps) {
  const { user } = useAuth();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tagSearch, setTagSearch] = useState("");
  const [showTagSelector, setShowTagSelector] = useState(false);

  // Create supplier mutation
  const createSupplier = useMutation(api.supplierManagement.createSupplier);

  const [formData, setFormData] = useState<SupplierFormData>({
    supplierName: "",
    supplierEmail: "",
    supplierPhone: "",
    supplierAddress: "",
    contactPerson: "",
    paymentTerms: "",
    creditLimit: 0,
    notes: "",
    tags: [],
    ...initialData
  });

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }));
    }
  }, [initialData]);

  const updateFormData = (field: keyof SupplierFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const toggleTag = (tag: string) => {
    const updatedTags = formData.tags.includes(tag)
      ? formData.tags.filter(t => t !== tag)
      : [...formData.tags, tag];
    updateFormData("tags", updatedTags);
  };

  const getStandardTags = () => {
    return DEFAULT_TAGS;
  };

  const validateForm = (): boolean => {
    return !!(
      formData.supplierName.trim() && 
      formData.contactPerson.trim() && 
      formData.supplierEmail.trim() && 
      formData.paymentTerms.trim()
    );
  };

  const handleSubmit = async (shouldPublish: boolean = false) => {
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.supplierName.trim()) throw new Error("Supplier name is required");
      if (!formData.contactPerson.trim()) throw new Error("Contact person is required");
      if (!formData.supplierEmail.trim()) throw new Error("Email is required");
      if (!formData.paymentTerms.trim()) throw new Error("Payment terms are required");

      const supplierData = {
        supplierName: formData.supplierName.trim(),
        supplierEmail: formData.supplierEmail.trim(),
        supplierPhone: formData.supplierPhone.trim() || undefined,
        supplierAddress: formData.supplierAddress.trim() || undefined,
        contactPerson: formData.contactPerson.trim(),
        paymentTerms: formData.paymentTerms.trim(),
        creditLimit: formData.creditLimit || undefined,
        notes: formData.notes.trim() || undefined,
        tags: formData.tags,
      };

      console.log("Supplier data:", supplierData);

      if (isEditing) {
        // TODO: Handle edit case
        await onSave(formData);
        toast.success("Supplier updated successfully!");
      } else {
        // Create new supplier
        await createSupplier(supplierData);
        toast.success("Supplier created successfully!");
      }

      router.push("/seller/suppliers");

    } catch (error) {
      console.error("Supplier creation error:", error);
      toast.error(`Failed to create supplier: ${error}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    if (amount <= 0) return "";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="bg-background">
      {/* Header Bar - Full Width */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={onCancel}
                className="rounded-xl font-light"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-light text-primary tracking-wide">
                  ADD SUPPLIER
                </h1>
                <p className="text-sm text-muted-foreground font-light">
                  Create a new supplier relationship
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={onCancel}
                className="rounded-xl font-light"
                disabled={isSubmitting}
              >
                CANCEL
              </Button>
              <Button
                variant="outline"
                onClick={() => handleSubmit(false)}
                disabled={isSubmitting || !validateForm()}
                className="rounded-xl font-light"
              >
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                SAVE DRAFT
              </Button>
              <Button
                onClick={() => handleSubmit(true)}
                disabled={isSubmitting || !validateForm()}
                className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light px-6"
              >
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Eye className="w-4 h-4 mr-2" />
                )}
                CREATE SUPPLIER
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Supplier Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Supplier Name *</Label>
                  <Input
                    placeholder="Enter supplier name"
                    value={formData.supplierName}
                    onChange={(e) => updateFormData("supplierName", e.target.value)}
                    className="rounded-xl bg-primary/5 border-border font-light"
                  />
                </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Contact Person *</Label>
                  <Input
                    placeholder="Primary contact name"
                    value={formData.contactPerson}
                    onChange={(e) => updateFormData("contactPerson", e.target.value)}
                    className="rounded-xl bg-primary/5 border-border font-light"
                  />
                </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Email Address *</Label>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.supplierEmail}
                    onChange={(e) => updateFormData("supplierEmail", e.target.value)}
                    className="rounded-xl bg-primary/5 border-border font-light"
                  />
                </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Phone Number</Label>
                  <Input
                    type="tel"
                    placeholder="+****************"
                    value={formData.supplierPhone}
                    onChange={(e) => updateFormData("supplierPhone", e.target.value)}
                    className="rounded-xl bg-primary/5 border-border font-light"
                  />
                </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Address</Label>
                  <Textarea
                    placeholder="Full business address"
                    value={formData.supplierAddress}
                    onChange={(e) => updateFormData("supplierAddress", e.target.value)}
                    rows={3}
                    className="rounded-xl bg-primary/5 border-border font-light resize-none"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Business Terms */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Business Terms</CardTitle>
                <CardDescription className="font-light">
                  Payment and credit terms for this supplier
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Payment Terms *</Label>
                  <select
                    value={formData.paymentTerms}
                    onChange={(e) => updateFormData("paymentTerms", e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-xl bg-primary/5 font-light focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="">Select payment terms</option>
                    {PAYMENT_TERMS.map((term) => (
                      <option key={term} value={term}>{term}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Credit Limit</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground font-light">$</span>
                    <Input
                      type="number"
                      placeholder="0.00"
                      value={formData.creditLimit || ""}
                      onChange={(e) => updateFormData("creditLimit", parseFloat(e.target.value) || 0)}
                      className="rounded-xl bg-primary/5 border-border font-light pl-8"
                    />
                  </div>
                  {formData.creditLimit > 0 && (
                    <p className="text-xs text-muted-foreground mt-1 font-light">
                      {formatCurrency(formData.creditLimit)}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Tags & Notes */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Tags & Categorization</CardTitle>
                <CardDescription className="font-light">
                  Organize and categorize your supplier
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Tags</Label>
                  <Button
                    variant="outline"
                    onClick={() => setShowTagSelector(!showTagSelector)}
                    className="w-full justify-between rounded-xl bg-primary/5 border-border font-light text-left"
                  >
                    {formData.tags.length > 0 ? `${formData.tags.length} tag(s) selected` : "Select tags"}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>

                  {formData.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {formData.tags.map((tag) => (
                        <Badge 
                          key={tag} 
                          variant="secondary" 
                          className="bg-accent text-accent-foreground font-light"
                        >
                          {tag}
                          <X
                            className="w-3 h-3 ml-1 cursor-pointer"
                            onClick={() => toggleTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}

                  {showTagSelector && (
                    <Card className="mt-4 rounded-xl border-border">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-lg font-light">Select Tags</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {/* Tag Grid */}
                        <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto">
                          {getStandardTags().map((tag) => (
                            <Button
                              key={tag}
                              variant={formData.tags.includes(tag) ? "default" : "outline"}
                              size="sm"
                              onClick={() => toggleTag(tag)}
                              className="rounded-lg font-light h-12"
                            >
                              {tag}
                            </Button>
                          ))}
                        </div>

                        {/* Custom Tag Input */}
                        <div className="space-y-2">
                          <div className="flex gap-2">
                            <Input
                              id="custom-tag-input"
                              placeholder="Enter custom tag"
                              value={tagSearch}
                              onChange={(e) => setTagSearch(e.target.value)}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  e.preventDefault();
                                  if (tagSearch.trim() && !formData.tags.includes(tagSearch.trim())) {
                                    toggleTag(tagSearch.trim());
                                    setTagSearch("");
                                  }
                                }
                              }}
                              className="rounded-xl bg-primary/5 border-border font-light"
                            />
                            <Button
                              type="button"
                              size="sm"
                              onClick={() => {
                                if (tagSearch.trim() && !formData.tags.includes(tagSearch.trim())) {
                                  toggleTag(tagSearch.trim());
                                  setTagSearch("");
                                }
                              }}
                              className="rounded-xl font-light px-4"
                            >
                              Add
                            </Button>
                          </div>
                          <p className="text-xs text-muted-foreground font-light">
                            Press Enter or click Add to include custom tag
                          </p>
                        </div>

                        <div className="flex gap-2 pt-4">
                          <Button
                            variant="outline"
                            onClick={() => setShowTagSelector(false)}
                            className="flex-1 rounded-xl font-light"
                          >
                            CANCEL
                          </Button>
                          <Button
                            onClick={() => setShowTagSelector(false)}
                            className="flex-1 bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light"
                          >
                            APPLY
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Additional Notes</Label>
                  <Textarea
                    placeholder="Any additional information about this supplier..."
                    value={formData.notes}
                    onChange={(e) => updateFormData("notes", e.target.value)}
                    rows={4}
                    className="rounded-xl bg-primary/5 border-border font-light resize-none"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Status Sidebar */}
          <div className="space-y-6">
            {/* Form Status */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  {formData.supplierName ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Supplier name entered</span>
                </div>
                <div className="flex items-center gap-2">
                  {formData.contactPerson ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Contact person entered</span>
                </div>
                <div className="flex items-center gap-2">
                  {formData.supplierEmail ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Email entered</span>
                </div>
                <div className="flex items-center gap-2">
                  {formData.paymentTerms ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Payment terms selected</span>
                </div>
                <div className="flex items-center gap-2">
                  {formData.creditLimit > 0 ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Credit limit set</span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  onClick={() => router.push('/seller/suppliers')}
                  className="w-full rounded-xl font-light"
                >
                  <Building2 className="w-4 h-4 mr-2" />
                  View All Suppliers
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push('/seller/suppliers/list')}
                  className="w-full rounded-xl font-light"
                >
                  <User className="w-4 h-4 mr-2" />
                  Supplier Directory
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
