"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@repo/ui/components/dialog";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { 
  Tabs,
  Tabs<PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@repo/ui/components/tabs";
import { 
  ArrowLeft,
  Plus, 
  DollarSign, 
  Package, 
  Clock, 
  AlertTriangle,
  Building2,
  Phone,
  Mail,
  MapPin,
  User,
  Tag,
  Calendar,
  Edit,
  Eye,
  TrendingUp,
  CreditCard,
  Receipt
} from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";

interface TransactionFormData {
  transactionType: "purchase" | "payment" | "refund" | "consignment_fee" | "adjustment";
  amount: number;
  currency: string;
  transactionDate: number;
  dueDate?: number;
  paymentMethod?: string;
  referenceNumber?: string;
  description: string;
  notes?: string;
}

export function SupplierProfile({ 
  supplierId, 
  onBack 
}: { 
  supplierId: string; 
  onBack: () => void;
}) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [isTransactionDialogOpen, setIsTransactionDialogOpen] = useState(false);
  const [transactionForm, setTransactionForm] = useState<TransactionFormData>({
    transactionType: "purchase",
    amount: 0,
    currency: "USD",
    transactionDate: Date.now(),
    description: "",
  });

  // Only make queries if we have a valid user with a valid _id
  const shouldSkipQueries = !user || !user._id || typeof user._id !== 'string';

  // Fetch supplier data
  const supplier = useQuery(
    api.supplierManagement.getSupplier, 
    { supplierId: supplierId as any }
  );

  const transactions = useQuery(
    api.supplierManagement.getSupplierTransactions, 
    { supplierId: supplierId as any }
  );

  // Fetch products from this supplier
  const products = useQuery(
    api.productQueries.getSellerInventory, 
    shouldSkipQueries ? "skip" : {
      status: undefined,
      limit: 1000,
      includeMetrics: true,
    }
  );

  // Filter products by supplier
  const supplierProducts = products?.products?.filter(product => 
    (product as any).sourceInfo?.source === supplier?.supplierName
  ) || [];

  // Mutations
  const createTransaction = useMutation(api.supplierManagement.createSupplierTransaction);

  const handleCreateTransaction = async () => {
    try {
      await createTransaction({
        supplierId: supplierId as any,
        transactionType: transactionForm.transactionType,
        amount: transactionForm.amount,
        currency: transactionForm.currency,
        transactionDate: transactionForm.transactionDate,
        dueDate: transactionForm.dueDate,
        paymentMethod: transactionForm.paymentMethod,
        referenceNumber: transactionForm.referenceNumber,
        description: transactionForm.description,
        notes: transactionForm.notes,
      });
      
      setIsTransactionDialogOpen(false);
      setTransactionForm({
        transactionType: "purchase",
        amount: 0,
        currency: "USD",
        transactionDate: Date.now(),
        description: "",
      });
    } catch (error) {
      console.error("Error creating transaction:", error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const formatDateTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  // Calculate analytics
  const analytics = {
    totalSpent: transactions?.filter(t => t.transactionType === "purchase" && t.status === "completed")
      .reduce((sum, t) => sum + t.amount, 0) || 0,
    totalPaid: transactions?.filter(t => t.transactionType === "payment" && t.status === "completed")
      .reduce((sum, t) => sum + t.amount, 0) || 0,
    outstandingBalance: 0,
    pendingTransactions: transactions?.filter(t => t.status === "pending").length || 0,
    overdueTransactions: transactions?.filter(t => 
      t.status === "pending" && t.dueDate && t.dueDate < Date.now()
    ).length || 0,
    activeProducts: supplierProducts.filter(p => p.status === "active" || p.status === "draft").length,
    totalProducts: supplierProducts.length,
  };

  analytics.outstandingBalance = analytics.totalSpent - analytics.totalPaid;

  if (!user || user.userType !== "seller") {
    return (
      <div className="p-6 text-center">
        <p className="text-muted-foreground">Seller access required</p>
      </div>
    );
  }

  // Show loading state while user ID is being resolved
  if (shouldSkipQueries) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Loading user information...</p>
      </div>
    );
  }

  if (!supplier) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Loading supplier...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-foreground">{supplier.supplierName}</h1>
          <p className="text-muted-foreground">Supplier Profile & Analytics</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.totalSpent)}</div>
            <p className="text-xs text-muted-foreground">
              All time purchases
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.outstandingBalance)}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.pendingTransactions} pending
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.activeProducts}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.totalProducts} total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <Badge variant={supplier.isActive ? "default" : "secondary"}>
              {supplier.isActive ? "Active" : "Inactive"}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics.overdueTransactions > 0 ? (
                <Badge variant="destructive">
                  {analytics.overdueTransactions} Overdue
                </Badge>
              ) : (
                "Good Standing"
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Supplier Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Supplier Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  {supplier.contactPerson && (
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Contact Person</p>
                        <p className="text-sm text-muted-foreground">{supplier.contactPerson}</p>
                      </div>
                    </div>
                  )}
                  {supplier.supplierEmail && (
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-sm text-muted-foreground">{supplier.supplierEmail}</p>
                      </div>
                    </div>
                  )}
                  {supplier.supplierPhone && (
                    <div className="flex items-center gap-3">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Phone</p>
                        <p className="text-sm text-muted-foreground">{supplier.supplierPhone}</p>
                      </div>
                    </div>
                  )}
                  {supplier.supplierAddress && (
                    <div className="flex items-center gap-3">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Address</p>
                        <p className="text-sm text-muted-foreground">{supplier.supplierAddress}</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Business Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Receipt className="h-5 w-5" />
                  Business Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  {supplier.paymentTerms && (
                    <div>
                      <p className="font-medium">Payment Terms</p>
                      <p className="text-sm text-muted-foreground">{supplier.paymentTerms}</p>
                    </div>
                  )}
                  {supplier.creditLimit && (
                    <div>
                      <p className="font-medium">Credit Limit</p>
                      <p className="text-sm text-muted-foreground">{formatCurrency(supplier.creditLimit)}</p>
                    </div>
                  )}
                  <div>
                    <p className="font-medium">Relationship Start Date</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(supplier.relationshipStartDate)}
                    </p>
                  </div>
                  {supplier.lastContactDate && (
                    <div>
                      <p className="font-medium">Last Contact</p>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(supplier.lastContactDate)}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Notes and Tags */}
          {(supplier.notes || supplier.tags.length > 0) && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {supplier.notes && (
                <Card>
                  <CardHeader>
                    <CardTitle>Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">{supplier.notes}</p>
                  </CardContent>
                </Card>
              )}
              {supplier.tags.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Tags</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {supplier.tags.map((tag, index) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        {/* Transactions Tab */}
        <TabsContent value="transactions" className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Transaction History</h3>
            <Dialog open={isTransactionDialogOpen} onOpenChange={setIsTransactionDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Transaction
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Transaction</DialogTitle>
                  <DialogDescription>
                    Record a new transaction with this supplier.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="transactionType">Transaction Type *</Label>
                    <Select 
                      value={transactionForm.transactionType} 
                      onValueChange={(value: any) => setTransactionForm({ ...transactionForm, transactionType: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="purchase">Purchase</SelectItem>
                        <SelectItem value="payment">Payment</SelectItem>
                        <SelectItem value="refund">Refund</SelectItem>
                        <SelectItem value="consignment_fee">Consignment Fee</SelectItem>
                        <SelectItem value="adjustment">Adjustment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount *</Label>
                    <Input
                      id="amount"
                      type="number"
                      value={transactionForm.amount}
                      onChange={(e) => setTransactionForm({ ...transactionForm, amount: parseFloat(e.target.value) || 0 })}
                      placeholder="0.00"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="transactionDate">Transaction Date *</Label>
                    <Input
                      id="transactionDate"
                      type="date"
                      value={new Date(transactionForm.transactionDate).toISOString().split('T')[0]}
                      onChange={(e) => setTransactionForm({ ...transactionForm, transactionDate: new Date(e.target.value).getTime() })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dueDate">Due Date</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={transactionForm.dueDate ? new Date(transactionForm.dueDate).toISOString().split('T')[0] : ''}
                      onChange={(e) => setTransactionForm({ ...transactionForm, dueDate: e.target.value ? new Date(e.target.value).getTime() : undefined })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="paymentMethod">Payment Method</Label>
                    <Input
                      id="paymentMethod"
                      value={transactionForm.paymentMethod || ''}
                      onChange={(e) => setTransactionForm({ ...transactionForm, paymentMethod: e.target.value })}
                      placeholder="e.g., Credit Card, Bank Transfer"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="referenceNumber">Reference Number</Label>
                    <Input
                      id="referenceNumber"
                      value={transactionForm.referenceNumber || ''}
                      onChange={(e) => setTransactionForm({ ...transactionForm, referenceNumber: e.target.value })}
                      placeholder="Invoice or reference number"
                    />
                  </div>
                  <div className="space-y-2 col-span-2">
                    <Label htmlFor="description">Description *</Label>
                    <Input
                      id="description"
                      value={transactionForm.description}
                      onChange={(e) => setTransactionForm({ ...transactionForm, description: e.target.value })}
                      placeholder="Transaction description"
                    />
                  </div>
                  <div className="space-y-2 col-span-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      value={transactionForm.notes || ''}
                      onChange={(e) => setTransactionForm({ ...transactionForm, notes: e.target.value })}
                      placeholder="Additional notes"
                      rows={3}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsTransactionDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateTransaction} disabled={!transactionForm.description || transactionForm.amount <= 0}>
                    Add Transaction
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <Card>
            <CardContent className="pt-6">
              {!transactions || transactions.length === 0 ? (
                <div className="text-center py-8">
                  <Receipt className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No transactions found</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Add your first transaction to start tracking financial interactions
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction._id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <Badge variant={
                              transaction.transactionType === "purchase" ? "default" :
                              transaction.transactionType === "payment" ? "secondary" :
                              transaction.transactionType === "refund" ? "outline" :
                              "destructive"
                            }>
                              {transaction.transactionType.charAt(0).toUpperCase() + transaction.transactionType.slice(1)}
                            </Badge>
                            <Badge variant={
                              transaction.status === "completed" ? "default" :
                              transaction.status === "pending" ? "secondary" :
                              transaction.status === "overdue" ? "destructive" :
                              "outline"
                            }>
                              {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                            </Badge>
                            {transaction.dueDate && transaction.status === "pending" && transaction.dueDate < Date.now() && (
                              <Badge variant="destructive">Overdue</Badge>
                            )}
                          </div>
                          <h4 className="font-medium">{transaction.description}</h4>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2 text-sm text-muted-foreground">
                            <div>
                              <span>Amount:</span>
                              <div className="font-medium text-foreground">
                                {formatCurrency(transaction.amount)}
                              </div>
                            </div>
                            <div>
                              <span>Date:</span>
                              <div className="font-medium text-foreground">
                                {formatDate(transaction.transactionDate)}
                              </div>
                            </div>
                            {transaction.dueDate && (
                              <div>
                                <span>Due:</span>
                                <div className="font-medium text-foreground">
                                  {formatDate(transaction.dueDate)}
                                </div>
                              </div>
                            )}
                            {transaction.paymentMethod && (
                              <div>
                                <span>Method:</span>
                                <div className="font-medium text-foreground">
                                  {transaction.paymentMethod}
                                </div>
                              </div>
                            )}
                          </div>
                          {transaction.notes && (
                            <p className="text-sm text-muted-foreground mt-2">{transaction.notes}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Products Tab */}
        <TabsContent value="products" className="space-y-6">
          <h3 className="text-lg font-semibold">Products from {supplier.supplierName}</h3>
          
          <Card>
            <CardContent className="pt-6">
              {supplierProducts.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No products found</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Products from this supplier will appear here
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {supplierProducts.map((product) => (
                    <div key={product._id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold text-lg">{product.title}</h4>
                            <Badge variant={
                              product.status === "active" ? "default" :
                              product.status === "draft" ? "secondary" :
                              product.status === "sold" ? "outline" :
                              "destructive"
                            }>
                              {product.status.charAt(0).toUpperCase() + product.status.slice(1)}
                            </Badge>
                            <Badge variant="outline">{product.brand}</Badge>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">List Price:</span>
                              <div className="font-medium">{formatCurrency(product.price)}</div>
                            </div>
                            {(product as any).sourceInfo?.costPaid && (
                              <div>
                                <span className="text-muted-foreground">Cost Paid:</span>
                                <div className="font-medium">{formatCurrency((product as any).sourceInfo.costPaid)}</div>
                              </div>
                            )}
                            <div>
                              <span className="text-muted-foreground">Category:</span>
                              <div className="font-medium">{product.category}</div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Condition:</span>
                              <div className="font-medium">{product.condition}</div>
                            </div>
                          </div>

                          {(product as any).sourceInfo?.purchaseDate && (
                            <div className="mt-2 text-sm text-muted-foreground">
                              Purchased: {formatDate((product as any).sourceInfo.purchaseDate)}
                            </div>
                          )}
                        </div>

                        <div className="flex gap-2 ml-4">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <h3 className="text-lg font-semibold">Financial Analytics</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Spending Trends
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Spent:</span>
                    <span className="font-bold text-lg">{formatCurrency(analytics.totalSpent)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Paid:</span>
                    <span className="font-bold text-lg">{formatCurrency(analytics.totalPaid)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Outstanding Balance:</span>
                    <span className={`font-bold text-lg ${
                      analytics.outstandingBalance > 0 ? 'text-destructive' : 'text-green-600'
                    }`}>
                      {formatCurrency(analytics.outstandingBalance)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Pending Transactions:</span>
                    <span className="font-bold text-lg">{analytics.pendingTransactions}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Overdue Transactions:</span>
                    <span className={`font-bold text-lg ${
                      analytics.overdueTransactions > 0 ? 'text-destructive' : 'text-green-600'
                    }`}>
                      {analytics.overdueTransactions}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Transactions:</span>
                    <span className="font-bold text-lg">{transactions?.length || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Inventory Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary">{analytics.totalProducts}</div>
                  <p className="text-sm text-muted-foreground">Total Products</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">{analytics.activeProducts}</div>
                  <p className="text-sm text-muted-foreground">Active Products</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">
                    {analytics.totalProducts - analytics.activeProducts}
                  </div>
                  <p className="text-sm text-muted-foreground">Sold/Archived</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
