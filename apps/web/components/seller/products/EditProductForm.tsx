"use client";

import { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Badge } from "@repo/ui/components/badge";
import { Progress } from "@repo/ui/components/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from "@repo/ui/components/tabs";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@repo/ui/components/command";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { 
  Upload, 
  X, 
  Image as ImageIcon,
  Plus,
  Loader2,
  ArrowLeft,
  Save,
  Eye,
  AlertCircle,
  CheckCircle,
  Package,
  Crown,
  ShoppingBag,
  Calendar,
  DollarSign,
  Settings,
  Trash2,
  Check,
  ChevronsUpDown,
  MoreVertical,
  Archive,
  EyeOff
} from "lucide-react";

// Import designers data
import designersData from "@/lib/designers.json";

interface ProductFormData {
  title: string;
  brand: string;
  sizes: string[];
  price: string;
  description: string;
  images: File[];
  uploadedImageIds: string[];
  uploadedImageUrls: { [key: string]: string }; // Store URLs for newly uploaded images
  removedImageIds: string[];
  // Additional fields from schema
  category: string;
  subcategory: string;
  model: string;
  condition: string;
  color: string;
  material: string;
  year: string;
  originalPrice: string;
  sku: string;
  tags: string[];
  weight: string;
  dimensions: {
    length: string;
    width: string;
    height: string;
  };
  shippingCost: string;
  ownershipType: "owned" | "consigned";
  consignmentInfo: {
    consignorName: string;
    consignorEmail: string;
    consignorPhone: string;
    commissionRate: string;
    minimumPrice: string;
    specialTerms: string;
  };
  sourceInfo: {
    source: string;
    costPaid: string;
    paymentMethod: string;
    purchaseDate: string;
    receipt: string;
  };
  metaTitle: string;
  metaDescription: string;
}

interface EditProductFormProps {
  productId: string;
}

const CONDITIONS = [
  { value: "new", label: "New", description: "Never worn/used, with tags" },
  { value: "like_new", label: "Like New", description: "Minimal signs of wear" },
  { value: "good", label: "Good", description: "Some signs of wear" },
  { value: "fair", label: "Fair", description: "More noticeable wear" },
];

// Shoe sizes mapping
const SHOE_SIZES = {
  US: ["5", "5.5", "6", "6.5", "7", "7.5", "8", "8.5", "9", "9.5", "10", "10.5", "11", "11.5", "12", "12.5", "13", "14"],
  UK: ["4", "4.5", "5", "5.5", "6", "6.5", "7", "7.5", "8", "8.5", "9", "9.5", "10", "10.5", "11", "11.5", "12"],
  EU: ["35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48"],
  JP: ["22", "22.5", "23", "23.5", "24", "24.5", "25", "25.5", "26", "26.5", "27", "27.5", "28", "28.5", "29", "30"],
  KR: ["225", "230", "235", "240", "245", "250", "255", "260", "265", "270", "275", "280", "285", "290", "295", "300"],
};

interface Designer {
  name: string;
  slug: string;
}

export function EditProductForm({ productId }: EditProductFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sizeType, setSizeType] = useState<keyof typeof SHOE_SIZES | "CUSTOM">("US");
  const [showSizeSelector, setShowSizeSelector] = useState(false);
  const [customSizeInput, setCustomSizeInput] = useState("");
  const [brandSearch, setBrandSearch] = useState("");
  const [open, setOpen] = useState(false);
  const [imageUploadProgress, setImageUploadProgress] = useState<{ [key: string]: number }>({});
  const [primaryImageId, setPrimaryImageId] = useState<string>("");

  // Load existing product data
  const existingProduct = useQuery(api.productQueries.getProductDetails, {
    productId: productId as Id<"products">
  });

  const [formData, setFormData] = useState<ProductFormData>({
    title: "",
    brand: "",
    sizes: [],
    price: "",
    description: "",
    images: [],
    uploadedImageIds: [],
    uploadedImageUrls: {},
    removedImageIds: [],
    // Additional fields
    category: "",
    subcategory: "",
    model: "",
    condition: "new",
    color: "",
    material: "",
    year: "",
    originalPrice: "",
    sku: "",
    tags: [],
    weight: "",
    dimensions: {
      length: "",
      width: "",
      height: "",
    },
    shippingCost: "",
    ownershipType: "owned",
    consignmentInfo: {
      consignorName: "",
      consignorEmail: "",
      consignorPhone: "",
      commissionRate: "",
      minimumPrice: "",
      specialTerms: "",
    },
    sourceInfo: existingProduct?.sourceInfo ? {
      source: existingProduct.sourceInfo.source || "",
      costPaid: existingProduct.sourceInfo.costPaid?.toString() || "",
      paymentMethod: existingProduct.sourceInfo.paymentMethod || "",
      purchaseDate: (existingProduct.sourceInfo.purchaseDate ? new Date(existingProduct.sourceInfo.purchaseDate).toISOString().split('T')[0] : "") || "",
      receipt: existingProduct.sourceInfo.receipt || "",
    } : {
      source: "",
      costPaid: "",
      paymentMethod: "",
      purchaseDate: "",
      receipt: "",
    },
    metaTitle: "",
    metaDescription: "",
  });

    // Debug logging
    useEffect(() => {
      console.log("Existing product:", existingProduct);
      console.log("Form data:", formData);
    }, [existingProduct, formData]);  

  // Load product data when available
  useEffect(() => {
    if (existingProduct) {
      setFormData({
        title: existingProduct.title || "",
        brand: existingProduct.brand || "",
        sizes: existingProduct.size ? existingProduct.size.split(", ") : [],
        price: existingProduct.price?.toString() || "",
        description: existingProduct.description || "",
        images: [],
        uploadedImageIds: existingProduct.imageStorageIds || [],
        uploadedImageUrls: {},
        removedImageIds: [],
        // Additional fields that are actually returned by getProductDetails
        category: typeof existingProduct.category === 'string' ? existingProduct.category : existingProduct.category?.name || "",
        subcategory: "",
        model: "",
        condition: existingProduct.condition === "excellent" ? "like_new" : 
                   existingProduct.condition === "very_good" ? "good" : 
                   (existingProduct.condition || "new"),
        color: existingProduct.color || "",
        material: existingProduct.material || "",
        year: existingProduct.year?.toString() || "",
        originalPrice: existingProduct.originalPrice?.toString() || "",
        sku: "",
        tags: existingProduct.tags || [],
        weight: existingProduct.weight?.toString() || "",
        dimensions: existingProduct.dimensions ? {
          length: existingProduct.dimensions.length?.toString() || "",
          width: existingProduct.dimensions.width?.toString() || "",
          height: existingProduct.dimensions.height?.toString() || "",
        } : { length: "", width: "", height: "" },
        shippingCost: existingProduct.shippingCost?.toString() || "",
        ownershipType: "owned",
        consignmentInfo: {
          consignorName: "",
          consignorEmail: "",
          consignorPhone: "",
          commissionRate: "",
          minimumPrice: "",
          specialTerms: "",
        },
        sourceInfo: existingProduct?.sourceInfo ? {
          source: existingProduct.sourceInfo.source || "",
          costPaid: existingProduct.sourceInfo.costPaid?.toString() || "",
          paymentMethod: existingProduct.sourceInfo.paymentMethod || "",
          purchaseDate: (existingProduct.sourceInfo.purchaseDate ? new Date(existingProduct.sourceInfo.purchaseDate).toISOString().split('T')[0] : "") || "",
          receipt: existingProduct.sourceInfo.receipt || "",
        } : {
          source: "",
          costPaid: "",
          paymentMethod: "",
          purchaseDate: "",
          receipt: "",
        },
        metaTitle: "",
        metaDescription: "",
      });

      // Set primary image if it exists
      if ((existingProduct as any).primaryImageId) {
        setPrimaryImageId((existingProduct as any).primaryImageId);
      } else if (existingProduct.imageStorageIds && existingProduct.imageStorageIds.length > 0) {
        // Default to first image if no primary is set
        setPrimaryImageId(existingProduct.imageStorageIds[0] as string);
      }
    }
  }, [existingProduct]);

  const designers = designersData as Designer[];
  const filteredDesigners = designers.filter(designer =>
    designer.name.toLowerCase().includes(brandSearch.toLowerCase())
  );
  
  // Mutations
  const updateProduct = useMutation(api.productManagement.updateProduct);
  const publishProduct = useMutation(api.productManagement.publishProduct);
  const removeProduct = useMutation(api.productManagement.removeProduct);
  const generateUploadUrl = useMutation(api.productManagement.generateImageUploadUrl);

  // Fetch categories
  const categories = useQuery(api.categories.getCategories);



  const updateFormData = useCallback((field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  const toggleSize = (size: string) => {
    const updatedSizes = formData.sizes.includes(size)
      ? formData.sizes.filter(s => s !== size)
      : [...formData.sizes, size];
    updateFormData("sizes", updatedSizes);
  };

  const getSizesForType = () => {
    if (sizeType === "CUSTOM") return [];
    return SHOE_SIZES[sizeType] || [];
  };

  const getStandardSizes = () => {
    return Object.values(SHOE_SIZES).flat();
  };

  const validateForm = () => {
    return (
      formData.title.trim() && 
      formData.brand?.trim() && 
      formData.sizes.length > 0 && 
      formData.price && 
      parseFloat(formData.price) > 0
    );
  };

  const handleImageUpload = async (files: FileList | null) => {
    if (!files) return;
    
    const newImages = Array.from(files);
    console.log("Starting upload for images:", newImages.map(f => f.name));
    
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...newImages]
    }));

    // Upload images immediately
    for (const image of newImages) {
      try {
        console.log("Uploading image:", image.name);
        const uploadUrl = await generateUploadUrl();
        console.log("Got upload URL:", uploadUrl);
        
        const xhr = new XMLHttpRequest();
        
        xhr.upload.addEventListener('progress', (e) => {
          if (e.lengthComputable) {
            const progress = Math.round((e.loaded / e.total) * 100);
            setImageUploadProgress(prev => ({
              ...prev,
              [image.name]: progress
            }));
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            const result = JSON.parse(xhr.responseText);
            console.log("Upload successful for", image.name, "Storage ID:", result.storageId);
            
            // Create a temporary URL for the uploaded image
            const imageUrl = URL.createObjectURL(image);
            
            setFormData(prev => ({
              ...prev,
              uploadedImageIds: [...prev.uploadedImageIds, result.storageId],
              uploadedImageUrls: {
                ...prev.uploadedImageUrls,
                [result.storageId]: imageUrl
              }
            }));
            
            // Remove from images array since it's now uploaded
            setFormData(prev => ({
              ...prev,
              images: prev.images.filter(img => img !== image)
            }));
            
            // Clear progress
            setImageUploadProgress(prev => {
              const newProgress = { ...prev };
              delete newProgress[image.name];
              return newProgress;
            });
          }
        });

        xhr.addEventListener('error', () => {
          console.error("Upload failed for", image.name);
          toast.error(`Failed to upload ${image.name}`);
          // Remove failed image
          setFormData(prev => ({
            ...prev,
            images: prev.images.filter(img => img !== image)
          }));
          // Clear progress
          setImageUploadProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[image.name];
            return newProgress;
          });
        });

        xhr.open('POST', uploadUrl);
        xhr.setRequestHeader('Content-Type', image.type);
        xhr.send(image);
      } catch (error) {
        console.error('Upload error for', image.name, ':', error);
        toast.error(`Failed to upload ${image.name}`);
        // Remove failed image
        setFormData(prev => ({
          ...prev,
          images: prev.images.filter(img => img !== image)
        }));
      }
    }
  };

  const removeImage = (imageId: string) => {
    setFormData(prev => {
      const newUploadedImageUrls = { ...prev.uploadedImageUrls };
      delete newUploadedImageUrls[imageId];
      
      return {
        ...prev,
        uploadedImageIds: prev.uploadedImageIds.filter(id => id !== imageId),
        uploadedImageUrls: newUploadedImageUrls,
        removedImageIds: [...prev.removedImageIds, imageId]
      };
    });
  };

  const removeNewImage = (image: File) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter(img => img !== image)
    }));
  };

  const setPrimaryImage = (imageId: string) => {
    setPrimaryImageId(imageId);
  };

  const reorderImages = (fromIndex: number, toIndex: number) => {
    const newOrder = [...formData.uploadedImageIds];
    const [movedImage] = newOrder.splice(fromIndex, 1);
    newOrder.splice(toIndex, 0, movedImage as string);
    setFormData(prev => ({
      ...prev,
      uploadedImageIds: newOrder
    }));
  };

  const handleSubmit = async (shouldPublish: boolean = false) => {
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.title.trim()) throw new Error("Product name is required");
      if (!formData.brand?.trim()) throw new Error("Brand is required");
      if (formData.sizes.length === 0) throw new Error("At least one size is required");
      if (!formData.price || parseFloat(formData.price) <= 0) throw new Error("Valid price is required");

      const updateData: any = {
        productId: productId as Id<"products">,
        title: formData.title.trim(),
        description: formData.description.trim() || `${formData.brand || ""} ${formData.title}`,
        price: parseFloat(formData.price),
        brand: formData.brand?.trim() || "",
        size: formData.sizes.join(", "), // Join sizes as string
        condition: formData.condition,
        color: formData.color?.trim() || "",
        material: formData.material?.trim() || "",
        yearPurchased: formData.year ? parseInt(formData.year) : undefined,
        originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,
        tags: formData.tags,
        shippingWeight: formData.weight ? parseFloat(formData.weight) : undefined,
        dimensions: formData.dimensions.length && formData.dimensions.width && formData.dimensions.height ? {
          length: parseFloat(formData.dimensions.length),
          width: parseFloat(formData.dimensions.width),
          height: parseFloat(formData.dimensions.height),
        } : undefined,
      };

      // Handle image updates
      if (formData.removedImageIds.length > 0) {
        updateData.removeImageIds = formData.removedImageIds as Id<"_storage">[];
      }
      
      if (formData.uploadedImageIds.length > 0) {
        updateData.newImages = formData.uploadedImageIds as Id<"_storage">[];
      }

      // Add primary image if selected
      if (primaryImageId) {
        updateData.primaryImageId = primaryImageId;
      }

      console.log("Sending update data:", updateData);
      console.log("Form data state:", formData);

      await updateProduct(updateData);

      if (shouldPublish && existingProduct?.status === "draft") {
        // Publish the product if it's currently a draft
        await publishProduct({ productId: productId as Id<"products"> });
        toast.success("Product updated and published successfully!");
      } else {
        toast.success("Product updated successfully!");
      }

      router.push("/seller/products");

    } catch (error) {
      console.error("Product update error:", error);
      toast.error(`Failed to update product: ${error}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (isNaN(num)) return "";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(num);
  };

  const handlePublish = async () => {
    if (!validateForm()) {
      toast.error("Please complete all required fields before publishing");
      return;
    }
    
    setIsSubmitting(true);
    try {
      await publishProduct({ productId: productId as Id<"products"> });
      toast.success("Product published successfully!");
      router.push("/seller/products");
    } catch (error) {
      toast.error(`Failed to publish product: ${error}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUnlist = async () => {
    setIsSubmitting(true);
    try {
      // For now, we'll use the removeProduct mutation to archive it
      // In a real implementation, you'd want a dedicated unlist mutation that changes status to "draft"
      // This is a temporary solution until a proper unlist mutation is added to the backend
      await removeProduct({
        productId: productId as Id<"products">,
        reason: "Unlisted by seller"
      });
      toast.success("Product unlisted successfully!");
      router.push("/seller/products");
    } catch (error) {
      toast.error(`Failed to unlist product: ${error}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleArchive = async () => {
    setIsSubmitting(true);
    try {
      await removeProduct({
        productId: productId as Id<"products">,
        reason: "Archived from edit page"
      });
      toast.success("Product archived successfully!");
      router.push("/seller/products");
    } catch (error) {
      toast.error(`Failed to archive product: ${error}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!existingProduct) {
    return (
      <div className="p-6 flex items-center justify-center min-h-screen bg-neutral-50 dark:bg-neutral-950">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black dark:border-white mx-auto mb-4"></div>
          <p className="text-neutral-600 dark:text-neutral-400">Loading product...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-4">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => router.back()}
                  className="rounded-xl font-light"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  BACK
                </Button>
                <div className="h-8 w-px bg-border" />
                <div>
                  <div className="flex items-center gap-3">
                    <h1 className="text-2xl font-light text-primary">
                      Edit Product
                    </h1>
                    <Badge 
                      variant="outline" 
                      className={`text-xs font-light rounded-xl ${
                        existingProduct?.status === 'active' ? 'bg-green-100 text-green-800 border-green-200' :
                        existingProduct?.status === 'draft' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                        existingProduct?.status === 'sold' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                        existingProduct?.status === 'archived' ? 'bg-gray-100 text-gray-800 border-gray-200' :
                        'bg-gray-100 text-gray-800 border-gray-200'
                      }`}
                    >
                      {existingProduct?.status === 'active' ? 'Listed' : 
                       existingProduct?.status === 'draft' ? 'Draft' :
                       existingProduct?.status === 'sold' ? 'Sold' :
                       existingProduct?.status === 'archived' ? 'Archived' :
                       existingProduct?.status || 'Unknown'}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground font-light">
                    Update your product listing
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="rounded-xl font-light"
                disabled={isSubmitting}
              >
                CANCEL
              </Button>
              
              {/* Save/Update Button */}
              <Button
                variant="outline"
                onClick={() => handleSubmit(false)}
                disabled={isSubmitting || !validateForm()}
                className="rounded-xl font-light"
              >
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                SAVE CHANGES
              </Button>

              {/* Status-specific primary action */}
              {existingProduct?.status === "draft" && (
                <Button
                  onClick={handlePublish}
                  disabled={isSubmitting || !validateForm()}
                  className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light px-6"
                >
                  {isSubmitting ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Eye className="w-4 h-4 mr-2" />
                  )}
                  PUBLISH
                </Button>
              )}

              {/* More Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-xl font-light px-3"
                    disabled={isSubmitting}
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  {existingProduct?.status === "draft" && (
                    <DropdownMenuItem onClick={handlePublish} disabled={!validateForm()}>
                      <Eye className="w-4 h-4 mr-2" />
                      Publish Product
                    </DropdownMenuItem>
                  )}
                  
                  <DropdownMenuSeparator />
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                        <Archive className="w-4 h-4 mr-2" />
                        Archive Product
                      </DropdownMenuItem>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Archive Product</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to archive this product? This will remove it from the marketplace and inventory.
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleArchive}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Archive Product
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-6">
        <div className="max-w-7xl mx-auto">
          <Card className="rounded-2xl border border-border bg-card shadow-sm">
            <CardContent className="p-8">
              <div className="space-y-8">
                {/* Basic Information Section */}
                <div className="space-y-6">
                  <div className="border-b border-border pb-4">
                    <h2 className="text-xl font-light text-foreground">Basic Information</h2>
                    <p className="text-sm text-muted-foreground font-light">Essential product details</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-8">
                    {/* Product Name */}
                    <div className="space-y-2">
                      <Label htmlFor="title" className="text-sm font-light text-foreground">
                        Product Name *
                      </Label>
                      <Input
                        id="title"
                        placeholder="Enter product name"
                        value={formData.title}
                        onChange={(e) => updateFormData("title", e.target.value)}
                        className="rounded-xl bg-primary/5 border-border font-light"
                      />
                    </div>

                    {/* Brand Selection */}
                    <div className="space-y-2">
                      <Label className="text-sm font-light text-foreground">Brand *</Label>
                      <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={open}
                            className="w-full justify-between rounded-xl bg-primary/5 border-border font-light"
                          >
                            {formData.brand || "Select brand..."}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" align="start">
                          <Command>
                            <CommandInput 
                              placeholder="Search brands..." 
                              value={brandSearch}
                              onValueChange={setBrandSearch}
                            />
                            <CommandEmpty>
                              <div className="p-2">
                                <Button
                                  variant="ghost"
                                  onClick={() => {
                                    updateFormData("brand", brandSearch);
                                    setOpen(false);
                                    setBrandSearch("");
                                  }}
                                  className="w-full text-left"
                                >
                                  <Plus className="w-4 h-4 mr-2" />
                                  Add "{brandSearch}"
                                </Button>
                              </div>
                            </CommandEmpty>
                            <CommandGroup>
                              <CommandList>
                                {filteredDesigners.slice(0, 50).map((designer) => (
                                  <CommandItem
                                    key={designer.slug}
                                    onSelect={() => {
                                      updateFormData("brand", designer.name);
                                      setOpen(false);
                                      setBrandSearch("");
                                    }}
                                  >
                                    <Check
                                      className={`mr-2 h-4 w-4 ${
                                        formData.brand === designer.name ? "opacity-100" : "opacity-0"
                                      }`}
                                    />
                                    {designer.name}
                                  </CommandItem>
                                ))}
                              </CommandList>
                            </CommandGroup>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {/* Description */}
                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-light text-foreground">
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      placeholder="Describe your product..."
                      value={formData.description}
                      onChange={(e) => updateFormData("description", e.target.value)}
                      className="rounded-xl bg-primary/5 border-border font-light min-h-[120px]"
                    />
                  </div>

                  {/* Size Selection */}
                  <div className="space-y-2">
                    <Label className="text-sm font-light text-foreground">Sizes *</Label>
                    <Popover open={showSizeSelector} onOpenChange={setShowSizeSelector}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-between rounded-xl bg-primary/5 border-border font-light"
                        >
                          {formData.sizes.length > 0 ? `${formData.sizes.length} size(s) selected` : "Select sizes"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>

                      {formData.sizes.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {formData.sizes.map((size) => (
                            <Badge
                              key={size}
                              variant="secondary"
                              className="bg-accent text-accent-foreground rounded-xl font-light cursor-pointer hover:bg-accent/80"
                              onClick={() => toggleSize(size)}
                            >
                              {size}
                              <X className="w-3 h-3 ml-1" />
                            </Badge>
                          ))}
                        </div>
                      )}

                      <PopoverContent className="w-96 p-6" align="start">
                        <div className="space-y-4">
                          <h4 className="font-medium text-sm uppercase tracking-wide">Select Size</h4>
                          
                          {/* Size Type Buttons */}
                          <div className="flex flex-wrap gap-2">
                            {(Object.keys(SHOE_SIZES) as Array<keyof typeof SHOE_SIZES>).map((type) => (
                              <Button
                                key={type}
                                variant={sizeType === type ? "default" : "outline"}
                                size="sm"
                                onClick={() => setSizeType(type)}
                                className="rounded-xl font-light"
                              >
                                {type}
                              </Button>
                            ))}
                            <Button
                              variant={sizeType === "CUSTOM" ? "default" : "outline"}
                              size="sm"
                              onClick={() => setSizeType("CUSTOM")}
                              className="rounded-xl font-light bg-accent text-accent-foreground hover:bg-accent/90"
                            >
                              CUSTOM
                            </Button>
                          </div>

                          {/* Size Grid */}
                          {sizeType !== "CUSTOM" && (
                            <div className="grid grid-cols-4 gap-2">
                              {getSizesForType().map((size) => (
                                <Button
                                  key={size}
                                  variant={formData.sizes.includes(size) ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => toggleSize(size)}
                                  className="rounded-xl font-light"
                                >
                                  {size}
                                </Button>
                              ))}
                            </div>
                          )}

                          {/* Custom Size Input */}
                          {sizeType === "CUSTOM" && (
                            <div className="space-y-2">
                              <div className="flex gap-2">
                                <Input
                                  id="custom-size-input"
                                  placeholder="Enter custom size (e.g., 42mm, XL, Size 10)"
                                  onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                      e.preventDefault();
                                      const value = (e.target as HTMLInputElement).value;
                                      if (value.trim()) {
                                        toggleSize(value.trim());
                                        (e.target as HTMLInputElement).value = "";
                                      }
                                    }
                                  }}
                                  className="rounded-xl bg-primary/5 border-border font-light"
                                />
                                <Button
                                  type="button"
                                  size="sm"
                                  onClick={() => {
                                    const input = document.getElementById("custom-size-input") as HTMLInputElement;
                                    const value = input?.value;
                                    if (value?.trim()) {
                                      toggleSize(value.trim());
                                      input.value = "";
                                    }
                                  }}
                                  className="rounded-xl font-light px-4"
                                >
                                  Add
                                </Button>
                              </div>
                              <p className="text-sm text-muted-foreground font-light">
                                Press Enter or click Add to include custom size
                              </p>
                              
                              {/* Show custom sizes */}
                              <div>
                                <p className="text-sm font-light text-muted-foreground mb-2">Custom sizes added:</p>
                                <div className="flex flex-wrap gap-1">
                                  {formData.sizes.filter(size => !getStandardSizes().includes(size)).map((size) => (
                                    <Badge
                                      key={size}
                                      variant="secondary"
                                      className="bg-accent text-accent-foreground rounded-xl font-light cursor-pointer hover:bg-accent/80"
                                      onClick={() => toggleSize(size)}
                                    >
                                      {size}
                                      <X className="w-3 h-3 ml-1" />
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          )}

                          <div className="flex gap-2 pt-4">
                            <Button 
                              variant="outline" 
                              onClick={() => setShowSizeSelector(false)}
                              className="flex-1 rounded-xl font-light"
                            >
                              CANCEL
                            </Button>
                            <Button 
                              onClick={() => setShowSizeSelector(false)}
                              className="flex-1 bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light"
                            >
                              APPLY
                            </Button>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Product Details Section */}
                <div className="space-y-6">
                  <div className="border-b border-border pb-4">
                    <h2 className="text-xl font-light text-foreground">Product Details</h2>
                    <p className="text-sm text-muted-foreground font-light">Additional product specifications</p>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-8">
                    {/* Category */}
                    <div className="space-y-2">
                      <Label htmlFor="category" className="text-sm font-light text-foreground">
                        Category
                      </Label>
                      <Select value={formData.category} onValueChange={(value) => updateFormData("category", value)}>
                        <SelectTrigger className="rounded-xl bg-primary/5 border-border font-light">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="clothing">Clothing</SelectItem>
                          <SelectItem value="sneakers">Sneakers</SelectItem>
                          <SelectItem value="collectibles">Collectibles</SelectItem>
                          <SelectItem value="accessories">Accessories</SelectItem>
                          <SelectItem value="handbags">Handbags</SelectItem>
                          <SelectItem value="jewelry">Jewelry</SelectItem>
                          <SelectItem value="watches">Watches</SelectItem>
                          <SelectItem value="sunglasses">Sunglasses</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Condition */}
                    <div className="space-y-2">
                      <Label htmlFor="condition" className="text-sm font-light text-foreground">
                        Condition
                      </Label>
                      <Select value={formData.condition} onValueChange={(value) => updateFormData("condition", value)}>
                        <SelectTrigger className="rounded-xl bg-primary/5 border-border font-light">
                          <SelectValue placeholder="Select condition" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="new">New</SelectItem>
                          <SelectItem value="like_new">Like New</SelectItem>
                          <SelectItem value="good">Good</SelectItem>
                          <SelectItem value="fair">Fair</SelectItem>
                        </SelectContent>
                      </Select>
                      {existingProduct?.condition === "excellent" && (
                        <p className="text-xs text-muted-foreground">
                          Note: "Excellent" condition has been mapped to "Like New" for compatibility
                        </p>
                      )}
                      {existingProduct?.condition === "very_good" && (
                        <p className="text-xs text-muted-foreground">
                          Note: "Very Good" condition has been mapped to "Good" for compatibility
                        </p>
                      )}
                    </div>

                    {/* Year */}
                    <div className="space-y-2">
                      <Label htmlFor="year" className="text-sm font-light text-foreground">
                        Year
                      </Label>
                      <Input
                        id="year"
                        type="number"
                        placeholder="e.g., 2020"
                        value={formData.year}
                        onChange={(e) => updateFormData("year", e.target.value)}
                        className="rounded-xl bg-primary/5 border-border font-light"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-8">
                    {/* Color */}
                    <div className="space-y-2">
                      <Label htmlFor="color" className="text-sm font-light text-foreground">
                        Color
                      </Label>
                      <Input
                        id="color"
                        placeholder="e.g., Black, Navy, Red"
                        value={formData.color}
                        onChange={(e) => updateFormData("color", e.target.value)}
                        className="rounded-xl bg-primary/5 border-border font-light"
                      />
                    </div>

                    {/* Material */}
                    <div className="space-y-2">
                      <Label htmlFor="material" className="text-sm font-light text-foreground">
                        Material
                      </Label>
                      <Input
                        id="material"
                        placeholder="e.g., Leather, Canvas, Cotton"
                        value={formData.material}
                        onChange={(e) => updateFormData("material", e.target.value)}
                        className="rounded-xl bg-primary/5 border-border font-light"
                      />
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="space-y-2">
                    <Label htmlFor="tags" className="text-sm font-light text-foreground">
                      Tags
                    </Label>
                    <Input
                      id="tags"
                      placeholder="Enter tags separated by commas (e.g., luxury, designer, vintage)"
                      value={formData.tags.join(", ")}
                      onChange={(e) => updateFormData("tags", e.target.value.split(",").map(tag => tag.trim()).filter(tag => tag))}
                      className="rounded-xl bg-primary/5 border-border font-light"
                    />
                  </div>

                  {/* Dimensions */}
                  <div className="space-y-2">
                    <Label className="text-sm font-light text-foreground">
                      Dimensions (inches)
                    </Label>
                    <div className="grid grid-cols-3 gap-8">
                      <div className="space-y-1">
                        <Label htmlFor="length" className="text-xs text-muted-foreground">Length</Label>
                        <Input
                          id="length"
                          type="number"
                          step="0.1"
                          placeholder="0.0"
                          value={formData.dimensions.length}
                          onChange={(e) => updateFormData("dimensions", { ...formData.dimensions, length: e.target.value })}
                          className="rounded-xl bg-primary/5 border-border font-light"
                        />
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="width" className="text-xs text-muted-foreground">Width</Label>
                        <Input
                          id="width"
                          type="number"
                          step="0.1"
                          placeholder="0.0"
                          value={formData.dimensions.width}
                          onChange={(e) => updateFormData("dimensions", { ...formData.dimensions, width: e.target.value })}
                          className="rounded-xl bg-primary/5 border-border font-light"
                        />
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="height" className="text-xs text-muted-foreground">Height</Label>
                        <Input
                          id="height"
                          type="number"
                          step="0.1"
                          placeholder="0.0"
                          value={formData.dimensions.height}
                          onChange={(e) => updateFormData("dimensions", { ...formData.dimensions, height: e.target.value })}
                          className="rounded-xl bg-primary/5 border-border font-light"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Pricing & Shipping Section */}
                <div className="space-y-6">
                  <div className="border-b border-border pb-4">
                    <h2 className="text-xl font-light text-foreground">Pricing & Shipping</h2>
                    <p className="text-sm text-muted-foreground font-light">Financial and delivery information</p>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-8">
                    {/* Price */}
                    <div className="space-y-2">
                      <Label htmlFor="price" className="text-sm font-light text-foreground">
                        List Price *
                      </Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground font-light">
                          $
                        </span>
                        <Input
                          id="price"
                          type="number"
                          placeholder="0.00"
                          value={formData.price}
                          onChange={(e) => updateFormData("price", e.target.value)}
                          className="pl-8 rounded-xl bg-primary/5 border-border font-light"
                        />
                      </div>
                      {formData.price && (
                        <p className="text-sm text-muted-foreground font-light">
                          {formatCurrency(formData.price)}
                        </p>
                      )}
                    </div>

                    {/* Original Price */}
                    <div className="space-y-2">
                      <Label htmlFor="originalPrice" className="text-sm font-light text-foreground">
                        Original Price
                      </Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground font-light">
                          $
                        </span>
                        <Input
                          id="originalPrice"
                          type="number"
                          placeholder="0.00"
                          value={formData.originalPrice}
                          onChange={(e) => updateFormData("originalPrice", e.target.value)}
                          className="pl-8 rounded-xl bg-primary/5 border-border font-light"
                        />
                      </div>
                    </div>

                    {/* Weight */}
                    <div className="space-y-2">
                      <Label htmlFor="weight" className="text-sm font-light text-foreground">
                        Weight (lbs)
                      </Label>
                      <Input
                        id="weight"
                        type="number"
                        step="0.1"
                        placeholder="0.0"
                        value={formData.weight}
                        onChange={(e) => updateFormData("weight", e.target.value)}
                        className="rounded-xl bg-primary/5 border-border font-light"
                      />
                    </div>
                  </div>

                  {/* Shipping Cost */}
                  <div className="space-y-2">
                    <Label htmlFor="shippingCost" className="text-sm font-light text-foreground">
                      Shipping Cost
                    </Label>
                    <div className="relative max-w-xs">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground font-light">
                        $
                      </span>
                      <Input
                        id="shippingCost"
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        value={formData.shippingCost}
                        onChange={(e) => updateFormData("shippingCost", e.target.value)}
                        className="pl-8 rounded-xl bg-primary/5 border-border font-light"
                      />
                    </div>
                  </div>
                </div>

                {/* Images Section */}
                <div className="space-y-6">
                  <div className="border-b border-border pb-4">
                    <h2 className="text-xl font-light text-foreground">Product Images</h2>
                    <p className="text-sm text-muted-foreground font-light">Upload and manage product photos</p>
                  </div>

                  {/* Image Upload Area */}
                  <div className="border-2 border-dashed border-border rounded-xl p-8 text-center hover:border-accent transition-colors">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={(e) => handleImageUpload(e.target.files)}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer">
                      <div className="flex flex-col items-center gap-3">
                        <Upload className="w-12 h-12 text-muted-foreground" />
                        <div>
                          <p className="text-lg font-medium">Click to upload images</p>
                          <p className="text-sm text-muted-foreground">PNG, JPG, GIF up to 10MB each</p>
                        </div>
                      </div>
                    </label>
                  </div>

                  {/* New Images (not yet uploaded) */}
                  {formData.images.length > 0 && (
                    <div className="space-y-3">
                      <p className="text-sm font-light text-muted-foreground">New images to upload:</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        {formData.images.map((image, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={URL.createObjectURL(image)}
                              alt={`New image ${index + 1}`}
                              className="w-full h-28 object-cover rounded-lg border border-border"
                            />
                            <button
                              onClick={() => removeNewImage(image)}
                              className="absolute top-2 right-2 bg-destructive text-destructive-foreground rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Upload Progress */}
                  {Object.keys(imageUploadProgress).length > 0 && (
                    <div className="space-y-3">
                      {Object.entries(imageUploadProgress).map(([fileName, progress]) => (
                        <div key={fileName} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="font-light">{fileName}</span>
                            <span className="text-muted-foreground">{progress}%</span>
                          </div>
                          <Progress value={progress} className="h-2" />
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Newly Uploaded Images */}
                  {formData.uploadedImageIds.length > 0 && (
                    <div className="space-y-3">
                      <p className="text-sm font-light text-muted-foreground">Newly uploaded images:</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        {formData.uploadedImageIds.map((imageId, index) => {
                          const imageUrl = formData.uploadedImageUrls[imageId];
                          
                          return (
                            <div key={imageId} className="relative group">
                              {imageUrl ? (
                                <img
                                  src={imageUrl}
                                  alt={`Uploaded image ${index + 1}`}
                                  className="w-full h-28 object-cover rounded-lg border border-border cursor-pointer hover:opacity-90 transition-opacity"
                                  onClick={() => setPrimaryImage(imageId)}
                                />
                              ) : (
                                <div className="w-full h-28 bg-muted rounded-lg border border-border flex items-center justify-center cursor-pointer hover:bg-muted/80 transition-colors"
                                     onClick={() => setPrimaryImage(imageId)}>
                                  <ImageIcon className="w-8 h-8 text-muted-foreground" />
                                </div>
                              )}
                              <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                                {index + 1}
                              </div>
                              {primaryImageId === imageId && (
                                <div className="absolute top-2 left-2 bg-yellow-500 text-white p-1 rounded-full">
                                  <Crown className="w-3 h-3" />
                                </div>
                              )}
                              <button
                                onClick={() => removeImage(imageId)}
                                className="absolute top-2 right-2 bg-destructive text-destructive-foreground rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <X className="w-3 h-3" />
                              </button>
                              <div className="absolute bottom-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                                NEW
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      <p className="text-xs text-muted-foreground font-light">
                        Click on an image to set it as primary. These images will be saved when you click "SAVE CHANGES"
                      </p>
                    </div>
                  )}

                  {/* Existing Images */}
                  {existingProduct?.images && existingProduct.images.length > 0 && (
                    <div className="space-y-3">
                      <p className="text-sm font-light text-muted-foreground">Current product images:</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        {existingProduct.images.map((imageUrl, index) => (
                          <div key={imageUrl} className="relative group">
                            <img
                              src={imageUrl}
                              alt={`Product image ${index + 1}`}
                              className="w-full h-28 object-cover rounded-lg border border-border cursor-pointer hover:opacity-90 transition-opacity"
                              onClick={() => {
                                const storageId = existingProduct.imageStorageIds?.[index];
                                if (storageId) {
                                  setPrimaryImage(storageId);
                                }
                              }}
                            />
                            <button
                              onClick={() => {
                                const storageId = existingProduct.imageStorageIds?.[index];
                                if (storageId) {
                                  removeImage(storageId);
                                }
                              }}
                              className="absolute top-2 right-2 bg-destructive text-destructive-foreground rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <X className="w-3 h-3" />
                            </button>
                            <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                              {index + 1}
                            </div>
                            {primaryImageId === existingProduct.imageStorageIds?.[index] && (
                              <div className="absolute top-2 left-2 bg-yellow-500 text-white p-1 rounded-full">
                                <Crown className="w-3 h-3" />
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                      <p className="text-xs text-muted-foreground font-light">
                        Click on an image to set it as primary. Drag and drop to reorder images.
                      </p>
                    </div>
                  )}

                  {/* No Images Message */}
                  {(!existingProduct?.images || existingProduct.images.length === 0) && formData.images.length === 0 && Object.keys(imageUploadProgress).length === 0 && (
                    <div className="text-center py-12 text-muted-foreground">
                      <ImageIcon className="w-16 h-16 mx-auto mb-3 opacity-50" />
                      <p className="text-sm font-light">No images uploaded yet</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

