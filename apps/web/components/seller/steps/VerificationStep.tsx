"use client";

import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { Upload, File, Check, X } from "lucide-react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent } from "@repo/ui/components/card";
import { FormField } from "../FormField";

interface VerificationStepProps {
  form: UseFormReturn<any>;
}

interface UploadedFile {
  name: string;
  size: number;
  type: string;
  url?: string;
}

export function VerificationStep({ form }: VerificationStepProps) {
  const [uploadedFiles, setUploadedFiles] = useState<{
    idDocument?: UploadedFile;
    businessLicense?: UploadedFile;
    taxDocument?: UploadedFile;
  }>({});

  const { setValue } = form;

  const handleFileUpload = (fileType: string, file: File) => {
    const uploadedFile: UploadedFile = {
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file),
    };

    setUploadedFiles(prev => ({
      ...prev,
      [fileType]: uploadedFile,
    }));

    setValue(fileType, file);
  };

  const removeFile = (fileType: string) => {
    setUploadedFiles(prev => {
      const newFiles = { ...prev };
      delete newFiles[fileType as keyof typeof newFiles];
      return newFiles;
    });
    setValue(fileType, undefined);
  };

  const FileUploadCard = ({ 
    title, 
    description, 
    fileType, 
    required = false 
  }: { 
    title: string; 
    description: string; 
    fileType: string; 
    required?: boolean;
  }) => {
    const uploadedFile = uploadedFiles[fileType as keyof typeof uploadedFiles];

    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-neutral-900 dark:text-neutral-100 flex items-center">
                {title}
                {required && <span className="text-red-500 ml-1">*</span>}
              </h4>
              <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
                {description}
              </p>
            </div>

            {uploadedFile ? (
              <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950 rounded-lg border border-green-200 dark:border-green-800">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-green-900 dark:text-green-100">
                      {uploadedFile.name}
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-400">
                      {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(fileType)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            ) : (
              <div className="border-2 border-dashed border-neutral-300 dark:border-neutral-700 rounded-lg p-6 text-center hover:border-neutral-400 dark:hover:border-neutral-600 transition-colors">
                <Upload className="w-8 h-8 text-neutral-400 mx-auto mb-2" />
                <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-2">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-neutral-500 dark:text-neutral-500">
                  PDF, JPG, PNG up to 10MB
                </p>
                <input
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFileUpload(fileType, file);
                    }
                  }}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
          Document Verification
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400">
          Upload the required documents to verify your identity and business.
        </p>
      </div>

      <div className="space-y-6">
        <FileUploadCard
          title="Government-Issued ID"
          description="Driver's license, passport, or state ID (required)"
          fileType="idDocument"
          required
        />

        <FileUploadCard
          title="Business License"
          description="Business registration or license document (if applicable)"
          fileType="businessLicense"
        />

        <FileUploadCard
          title="Tax Document"
          description="W-9, 1099, or business tax return (recommended)"
          fileType="taxDocument"
        />
      </div>

      <div className="bg-amber-50 dark:bg-amber-950 rounded-lg p-4">
        <h4 className="font-medium text-amber-900 dark:text-amber-100 mb-2">
          Document Requirements
        </h4>
        <ul className="text-sm text-amber-800 dark:text-amber-200 space-y-1">
          <li>• All documents must be clear and legible</li>
          <li>• Personal information must match your application</li>
          <li>• Documents should be current and not expired</li>
          <li>• File size limit: 10MB per document</li>
        </ul>
      </div>
    </div>
  );
}