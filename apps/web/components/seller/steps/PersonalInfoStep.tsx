"use client";

import { UseFormReturn } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { FormField } from "../FormField";

interface PersonalInfoStepProps {
  form: UseFormReturn<any>;
  user?: any;
}

export function PersonalInfoStep({ form, user }: PersonalInfoStepProps) {
  const { register, formState: { errors } } = form;

  // Check if we have user data to disable fields
  const nameParts = user?.name?.split(' ') || [];
  const hasFirstName = Boolean(nameParts[0]);
  const hasLastName = Boolean(nameParts.slice(1).join(' '));
  const hasUserEmail = <PERSON><PERSON><PERSON>(user?.email);
  const hasUserPhone = <PERSON><PERSON>an(user?.phone);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
          Personal Information
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400">
          Please provide your personal details for verification purposes.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          label="First Name"
          error={errors.firstName?.message as string}
          required
        >
          <Input
            {...register("firstName")}
            placeholder="John"
            disabled={hasFirstName}
            className={`${errors.firstName ? "border-red-500" : ""} ${hasFirstName ? "bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400" : ""}`}
          />
          {hasFirstName && (
            <p className="text-xs text-neutral-500 mt-1">Pre-filled from your account</p>
          )}
        </FormField>

        <FormField
          label="Last Name"
          error={errors.lastName?.message as string}
          required
        >
          <Input
            {...register("lastName")}
            placeholder="Doe"
            disabled={hasLastName}
            className={`${errors.lastName ? "border-red-500" : ""} ${hasLastName ? "bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400" : ""}`}
          />
          {hasLastName && (
            <p className="text-xs text-neutral-500 mt-1">Pre-filled from your account</p>
          )}
        </FormField>

        <FormField
          label="Email Address"
          error={errors.email?.message as string}
          required
        >
          <Input
            {...register("email")}
            type="email"
            placeholder="<EMAIL>"
            disabled={hasUserEmail}
            className={`${errors.email ? "border-red-500" : ""} ${hasUserEmail ? "bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400" : ""}`}
          />
          {hasUserEmail && (
            <p className="text-xs text-neutral-500 mt-1">Pre-filled from your account</p>
          )}
        </FormField>

        <FormField
          label="Phone Number"
          error={errors.phone?.message as string}
          required
        >
          <Input
            {...register("phone")}
            type="tel"
            placeholder="+****************"
            disabled={hasUserPhone}
            className={`${errors.phone ? "border-red-500" : ""} ${hasUserPhone ? "bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400" : ""}`}
          />
          {hasUserPhone && (
            <p className="text-xs text-neutral-500 mt-1">Pre-filled from your account</p>
          )}
        </FormField>

        <FormField
          label="Date of Birth"
          error={errors.dateOfBirth?.message as string}
          required
        >
          <Input
            {...register("dateOfBirth")}
            type="date"
            className={errors.dateOfBirth ? "border-red-500" : ""}
          />
        </FormField>
      </div>

      {(hasFirstName || hasLastName || hasUserEmail || hasUserPhone) && (
        <div className="bg-green-50 dark:bg-green-950 rounded-lg p-4">
          <p className="text-sm text-green-800 dark:text-green-200">
            <strong>Account Information:</strong> Some fields have been pre-filled with information from your account. These fields are locked to ensure consistency.
          </p>
        </div>
      )}

      <div className="bg-blue-50 dark:bg-blue-950 rounded-lg p-4">
        <p className="text-sm text-blue-800 dark:text-blue-200">
          <strong>Privacy Notice:</strong> Your personal information is encrypted and will only be used for verification and account management purposes.
        </p>
      </div>
    </div>
  );
}