"use client";

import { UseFormReturn } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { FormField } from "../FormField";

interface ExperienceStepProps {
  form: UseFormReturn<any>;
}

const PLATFORMS = [
  "eBay", "Amazon", "Etsy", "Poshmark", "TheRealReal", "Vestiaire Collective",
  "Grailed", "StockX", "GOAT", "Rebag", "Fashionphile", "Other"
];

const SPECIALTIES = [
  "Designer Handbags", "Luxury Watches", "Fine Jewelry", "Designer Clothing",
  "Sneakers", "Vintage Items", "Art & Collectibles", "Accessories"
];

export function ExperienceStep({ form }: ExperienceStepProps) {
  const { register, formState: { errors }, setValue, watch } = form;
  const previousPlatforms = watch("previousPlatforms") || [];
  const specialties = watch("specialties") || [];
  const yearsExperience = watch("yearsExperience");
  const monthlyVolume = watch("monthlyVolume");

  const handlePlatformChange = (platform: string, checked: boolean) => {
    if (checked) {
      setValue("previousPlatforms", [...previousPlatforms, platform]);
    } else {
      setValue("previousPlatforms", previousPlatforms.filter((p: string) => p !== platform));
    }
  };

  const handleSpecialtyChange = (specialty: string, checked: boolean) => {
    if (checked) {
      setValue("specialties", [...specialties, specialty]);
    } else {
      setValue("specialties", specialties.filter((s: string) => s !== specialty));
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
          Selling Experience
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400">
          Help us understand your background in luxury goods sales.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          label="Years of Experience"
          error={errors.yearsExperience?.message as string}
          required
        >
          <Select
            value={yearsExperience}
            onValueChange={(value) => setValue("yearsExperience", value)}
          >
            <SelectTrigger className={errors.yearsExperience ? "border-red-500" : ""}>
              <SelectValue placeholder="Select experience level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0-1">Less than 1 year</SelectItem>
              <SelectItem value="1-3">1-3 years</SelectItem>
              <SelectItem value="3-5">3-5 years</SelectItem>
              <SelectItem value="5-10">5-10 years</SelectItem>
              <SelectItem value="10+">10+ years</SelectItem>
            </SelectContent>
          </Select>
        </FormField>

        <FormField
          label="Monthly Sales Volume"
          error={errors.monthlyVolume?.message as string}
          required
        >
          <Select
            value={monthlyVolume}
            onValueChange={(value) => setValue("monthlyVolume", value)}
          >
            <SelectTrigger className={errors.monthlyVolume ? "border-red-500" : ""}>
              <SelectValue placeholder="Select monthly volume" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0-5k">$0 - $5,000</SelectItem>
              <SelectItem value="5k-25k">$5,000 - $25,000</SelectItem>
              <SelectItem value="25k-100k">$25,000 - $100,000</SelectItem>
              <SelectItem value="100k+">$100,000+</SelectItem>
            </SelectContent>
          </Select>
        </FormField>
      </div>

      <FormField
        label="Previous Selling Platforms"
        error={errors.previousPlatforms?.message as string}
        required
        description="Select all platforms where you have sold luxury goods"
      >
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {PLATFORMS.map((platform) => (
            <div key={platform} className="flex items-center space-x-2">
              <Checkbox
                id={platform}
                checked={previousPlatforms.includes(platform)}
                onCheckedChange={(checked) => handlePlatformChange(platform, checked as boolean)}
              />
              <Label htmlFor={platform} className="text-sm">
                {platform}
              </Label>
            </div>
          ))}
        </div>
      </FormField>

      <FormField
        label="Product Specialties"
        error={errors.specialties?.message as string}
        required
        description="What types of luxury goods do you specialize in?"
      >
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {SPECIALTIES.map((specialty) => (
            <div key={specialty} className="flex items-center space-x-2">
              <Checkbox
                id={specialty}
                checked={specialties.includes(specialty)}
                onCheckedChange={(checked) => handleSpecialtyChange(specialty, checked as boolean)}
              />
              <Label htmlFor={specialty} className="text-sm">
                {specialty}
              </Label>
            </div>
          ))}
        </div>
      </FormField>
    </div>
  );
}