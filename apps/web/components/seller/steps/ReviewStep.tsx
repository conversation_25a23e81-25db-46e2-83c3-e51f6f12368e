"use client";

import { UseFormReturn } from "react-hook-form";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Label } from "@repo/ui/components/label";
import { Badge } from "@repo/ui/components/badge";
import { FormField } from "../FormField";
import { Check, FileText, User, Building, CreditCard } from "lucide-react";

interface ReviewStepProps {
  form: UseFormReturn<any>;
}

export function ReviewStep({ form }: ReviewStepProps) {
  const { watch, setValue, formState: { errors } } = form;
  const formData = watch();

  const termsAccepted = watch("termsAccepted");
  const privacyAccepted = watch("privacyAccepted");

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
          Review Your Application
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400">
          Please review all information before submitting your application.
        </p>
      </div>

      {/* Application Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center">
              <User className="w-4 h-4 mr-2" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Name:</span>
              <span className="font-medium">{formData.firstName} {formData.lastName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Email:</span>
              <span className="font-medium">{formData.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Phone:</span>
              <span className="font-medium">{formData.phone}</span>
            </div>
          </CardContent>
        </Card>

        {/* Business Information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center">
              <Building className="w-4 h-4 mr-2" />
              Business Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Business:</span>
              <span className="font-medium">{formData.businessName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Type:</span>
              <span className="font-medium capitalize">{formData.businessType?.replace('_', ' ')}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Tax ID:</span>
              <span className="font-medium">{formData.taxId}</span>
            </div>
          </CardContent>
        </Card>

        {/* Experience */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center">
              <FileText className="w-4 h-4 mr-2" />
              Experience
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Experience:</span>
              <span className="font-medium">{formData.yearsExperience} years</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Volume:</span>
              <span className="font-medium">{formData.monthlyVolume}</span>
            </div>
            <div>
              <span className="text-neutral-600 dark:text-neutral-400">Specialties:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {formData.specialties?.slice(0, 3).map((specialty: string) => (
                  <Badge key={specialty} variant="secondary" className="text-xs">
                    {specialty}
                  </Badge>
                ))}
                {formData.specialties?.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{formData.specialties.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>


      </div>

      {/* Terms and Conditions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Terms and Conditions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField error={errors.termsAccepted?.message as string}>
            <div className="flex items-start space-x-3">
              <Checkbox
                id="terms"
                checked={termsAccepted}
                onCheckedChange={(checked) => setValue("termsAccepted", checked)}
                className={errors.termsAccepted ? "border-red-500" : ""}
              />
              <div className="space-y-1">
                <Label htmlFor="terms" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  I agree to the Terms of Service and Seller Agreement
                </Label>
                <p className="text-xs text-neutral-600 dark:text-neutral-400">
                  By checking this box, you agree to our seller terms, commission structure, and marketplace policies.
                </p>
              </div>
            </div>
          </FormField>

          <FormField error={errors.privacyAccepted?.message as string}>
            <div className="flex items-start space-x-3">
              <Checkbox
                id="privacy"
                checked={privacyAccepted}
                onCheckedChange={(checked) => setValue("privacyAccepted", checked)}
                className={errors.privacyAccepted ? "border-red-500" : ""}
              />
              <div className="space-y-1">
                <Label htmlFor="privacy" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  I agree to the Privacy Policy
                </Label>
                <p className="text-xs text-neutral-600 dark:text-neutral-400">
                  You consent to our collection and use of your information as described in our Privacy Policy.
                </p>
              </div>
            </div>
          </FormField>
        </CardContent>
      </Card>

      <div className="bg-green-50 dark:bg-green-950 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Check className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
          <div>
            <h4 className="font-medium text-green-900 dark:text-green-100 mb-1">
              Ready to Submit
            </h4>
            <p className="text-sm text-green-800 dark:text-green-200">
              Your application will be reviewed within 2-3 business days. You'll receive an email notification once the review is complete.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}