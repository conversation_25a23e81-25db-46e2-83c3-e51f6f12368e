"use client";

import { UseFormReturn } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { FormField } from "../FormField";

interface BusinessInfoStepProps {
  form: UseFormReturn<any>;
}

export function BusinessInfoStep({ form }: BusinessInfoStepProps) {
  const { register, formState: { errors }, setValue, watch } = form;
  const businessType = watch("businessType");

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
          Business Information
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400">
          Tell us about your business for tax and legal compliance.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          label="Business Name"
          error={errors.businessName?.message as string}
          required
        >
          <Input
            {...register("businessName")}
            placeholder="Luxury Goods LLC"
            className={errors.businessName ? "border-red-500" : ""}
          />
        </FormField>

        <FormField
          label="Business Type"
          error={errors.businessType?.message as string}
          required
        >
          <Select
            value={businessType}
            onValueChange={(value) => setValue("businessType", value)}
          >
            <SelectTrigger className={errors.businessType ? "border-red-500" : ""}>
              <SelectValue placeholder="Select business type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="individual">Individual/Sole Proprietor</SelectItem>
              <SelectItem value="llc">LLC</SelectItem>
              <SelectItem value="corporation">Corporation</SelectItem>
              <SelectItem value="partnership">Partnership</SelectItem>
            </SelectContent>
          </Select>
        </FormField>

        <FormField
          label="Tax ID / EIN"
          error={errors.taxId?.message as string}
          required
        >
          <Input
            {...register("taxId")}
            placeholder="12-3456789"
            className={errors.taxId ? "border-red-500" : ""}
          />
        </FormField>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium text-neutral-900 dark:text-neutral-100">
          Business Address
        </h4>
        
        <div className="grid grid-cols-1 gap-4">
          <FormField
            label="Street Address"
            error={errors.businessAddress?.message as string}
            required
          >
            <Input
              {...register("businessAddress")}
              placeholder="123 Main Street"
              className={errors.businessAddress ? "border-red-500" : ""}
            />
          </FormField>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormField
              label="City"
              error={errors.businessCity?.message as string}
              required
            >
              <Input
                {...register("businessCity")}
                placeholder="New York"
                className={errors.businessCity ? "border-red-500" : ""}
              />
            </FormField>

            <FormField
              label="State"
              error={errors.businessState?.message as string}
              required
            >
              <Input
                {...register("businessState")}
                placeholder="NY"
                className={errors.businessState ? "border-red-500" : ""}
              />
            </FormField>

            <FormField
              label="ZIP Code"
              error={errors.businessZip?.message as string}
              required
            >
              <Input
                {...register("businessZip")}
                placeholder="10001"
                className={errors.businessZip ? "border-red-500" : ""}
              />
            </FormField>
          </div>

          <FormField
            label="Country"
            error={errors.businessCountry?.message as string}
            required
          >
            <Input
              {...register("businessCountry")}
              placeholder="United States"
              className={errors.businessCountry ? "border-red-500" : ""}
            />
          </FormField>
        </div>
      </div>
    </div>
  );
}