"use client";

import { useState } from "react";
import { Button } from "@repo/ui/components/button";
import { Textarea } from "@repo/ui/components/textarea";
import { 
  Bold, 
  Italic, 
  List, 
  ListOrdered, 
  Link,
  Eye,
  Edit
} from "lucide-react";

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export function RichTextEditor({ value, onChange, placeholder }: RichTextEditorProps) {
  const [isPreview, setIsPreview] = useState(false);

  const insertText = (before: string, after: string = "") => {
    const textarea = document.getElementById("description") as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    
    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end);
    onChange(newText);

    // Restore cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + before.length, end + before.length);
    }, 0);
  };

  const formatText = (format: string) => {
    switch (format) {
      case "bold":
        insertText("**", "**");
        break;
      case "italic":
        insertText("*", "*");
        break;
      case "bullet":
        insertText("\n• ");
        break;
      case "number":
        insertText("\n1. ");
        break;
      case "link":
        insertText("[", "](url)");
        break;
    }
  };

  const renderPreview = (text: string) => {
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" class="text-blue-600 underline">$1</a>')
      .replace(/\n• /g, '<br/>• ')
      .replace(/\n\d+\. /g, '<br/>1. ')
      .replace(/\n/g, '<br/>');
  };

  return (
    <div className="space-y-2">
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border border-neutral-200 dark:border-neutral-700 rounded-t-md bg-neutral-50 dark:bg-neutral-800">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => formatText("bold")}
          className="h-8 w-8 p-0"
        >
          <Bold className="h-4 w-4" />
        </Button>
        
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => formatText("italic")}
          className="h-8 w-8 p-0"
        >
          <Italic className="h-4 w-4" />
        </Button>

        <div className="w-px h-6 bg-neutral-300 dark:bg-neutral-600 mx-1" />

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => formatText("bullet")}
          className="h-8 w-8 p-0"
        >
          <List className="h-4 w-4" />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => formatText("number")}
          className="h-8 w-8 p-0"
        >
          <ListOrdered className="h-4 w-4" />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => formatText("link")}
          className="h-8 w-8 p-0"
        >
          <Link className="h-4 w-4" />
        </Button>

        <div className="flex-1" />

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setIsPreview(!isPreview)}
          className="h-8 px-3"
        >
          {isPreview ? (
            <>
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </>
          ) : (
            <>
              <Eye className="h-4 w-4 mr-1" />
              Preview
            </>
          )}
        </Button>
      </div>

      {/* Editor/Preview */}
      {isPreview ? (
        <div 
          className="min-h-32 p-3 border border-neutral-200 dark:border-neutral-700 rounded-b-md bg-white dark:bg-neutral-900 prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: renderPreview(value) }}
        />
      ) : (
        <Textarea
          id="description"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="min-h-32 border-t-0 rounded-t-none focus:ring-0 focus:border-neutral-300 dark:focus:border-neutral-600"
          rows={6}
        />
      )}

      {/* Help text */}
      <div className="text-xs text-neutral-600 dark:text-neutral-400">
        Use **bold**, *italic*, • bullets, 1. numbers, [links](url)
      </div>
    </div>
  );
}