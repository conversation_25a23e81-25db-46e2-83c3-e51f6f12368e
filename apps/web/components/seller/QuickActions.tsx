import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Plus, Upload, BarChart3, FileText, Settings } from "lucide-react";
import Link from "next/link";

export function QuickActions() {
  const actions = [
    {
      title: "Add New Product",
      description: "List a new item for sale",
      icon: Plus,
      href: "/seller/products/new",
      variant: "default" as const,
    },
    {
      title: "Bulk Upload",
      description: "Upload multiple products",
      icon: Upload,
      href: "/seller/products/bulk-upload",
      variant: "outline" as const,
    },
    {
      title: "View Reports",
      description: "Analyze your performance",
      icon: BarChart3,
      href: "/seller/reports",
      variant: "outline" as const,
    },
    {
      title: "Generate Invoice",
      description: "Create a new invoice",
      icon: FileText,
      href: "/seller/invoices/new",
      variant: "outline" as const,
    },
    {
      title: "Account Settings",
      description: "Manage your account",
      icon: Settings,
      href: "/seller/settings",
      variant: "outline" as const,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {actions.map((action) => (
            <Link key={action.title} href={action.href}>
              <Button 
                variant={action.variant} 
                className="w-full justify-start h-auto p-4"
              >
                <action.icon className="h-5 w-5 mr-3 flex-shrink-0" />
                <div className="text-left">
                  <div className="font-medium">{action.title}</div>
                  <div className="text-sm opacity-70">{action.description}</div>
                </div>
              </Button>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}