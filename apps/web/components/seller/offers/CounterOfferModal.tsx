"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { 
  DollarSign, 
  TrendingUp, 
  AlertCircle,
  CheckCircle2
} from "lucide-react";
import { toast } from "sonner";

interface Offer {
  _id: string;
  offerAmount: number;
  product: {
    title: string;
    brand: string;
    price: number;
  };
}

interface CounterOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
  offer: Offer;
}

export function CounterOfferModal({ isOpen, onClose, offer }: CounterOfferModalProps) {
  const [counterAmount, setCounterAmount] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const counterOffer = useMutation(api.offerManagement.counterOffer);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!counterAmount || parseFloat(counterAmount) <= 0) {
      toast.error("Please enter a valid counter amount");
      return;
    }

    const amount = parseFloat(counterAmount);
    if (amount > offer.product.price * 1.5) {
      toast.error("Counter amount cannot exceed 150% of listing price");
      return;
    }

    if (amount <= offer.offerAmount) {
      toast.error("Counter offer must be higher than the buyer's offer");
      return;
    }

    setIsSubmitting(true);

    try {
      await counterOffer({
        offerId: offer._id as any,
        counterAmount: amount,
        message: message.trim() || undefined,
      });

      toast.success("Counter offer sent successfully!");
      setCounterAmount("");
      setMessage("");
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to send counter offer");
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const maxCounter = offer.product.price * 1.5;
  const minCounter = offer.offerAmount + 1; // Must be higher than buyer's offer

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-blue-500" />
            Counter Offer
          </DialogTitle>
          <DialogDescription>
            Send a counter offer for {offer.product.title} by {offer.product.brand}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Current Offer Info */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">Buyer's Offer</span>
              <span className="text-lg font-semibold text-foreground">
                {formatCurrency(offer.offerAmount)}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">Listing Price</span>
              <span className="text-sm text-foreground">
                {formatCurrency(offer.product.price)}
              </span>
            </div>
          </div>

          {/* Counter Amount */}
          <div className="space-y-3">
            <Label htmlFor="counterAmount" className="text-sm font-medium">
              Your Counter Offer
            </Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                id="counterAmount"
                type="number"
                step="0.01"
                min={minCounter}
                max={maxCounter}
                value={counterAmount}
                onChange={(e) => setCounterAmount(e.target.value)}
                placeholder="Enter your counter offer"
                className="pl-10"
                required
              />
            </div>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Min: {formatCurrency(minCounter)}</span>
              <span>Max: {formatCurrency(maxCounter)}</span>
            </div>
          </div>

          {/* Message */}
          <div className="space-y-3">
            <Label htmlFor="message" className="text-sm font-medium">
              Message to Buyer (Optional)
            </Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Explain your counter offer or add any notes..."
              className="min-h-[80px] resize-none"
              maxLength={500}
            />
            <div className="text-xs text-muted-foreground text-right">
              {message.length}/500 characters
            </div>
          </div>

          <Separator />

          {/* Counter Offer Guidelines */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-foreground">Counter Offer Guidelines</h4>
            <div className="space-y-2 text-xs text-muted-foreground">
              <div className="flex items-start gap-2">
                <CheckCircle2 className="w-3 h-3 mt-0.5 flex-shrink-0" />
                <span>Counter must be higher than the buyer's offer</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle2 className="w-3 h-3 mt-0.5 flex-shrink-0" />
                <span>Buyer has 7 days to respond to your counter</span>
              </div>
              <div className="flex items-start gap-2">
                <AlertCircle className="w-3 h-3 mt-0.5 flex-shrink-0" />
                <span>If buyer accepts, product will be reserved</span>
              </div>
            </div>
          </div>

          <DialogFooter className="gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !counterAmount || parseFloat(counterAmount) < minCounter}
              className="min-w-[120px] bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? "Sending..." : "Send Counter"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
