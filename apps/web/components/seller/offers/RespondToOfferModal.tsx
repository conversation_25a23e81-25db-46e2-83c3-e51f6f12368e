"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { 
  MessageSquare, 
  Package, 
  User, 
  DollarSign, 
  Send,
  X
} from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Textarea } from "@repo/ui/components/textarea";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";

interface RespondToOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
  offer: {
    _id: string;
    offerAmount: number;
    message?: string;
    status: string;
    sellerResponse?: {
      message: string;
      responseType: "info" | "negotiate" | "other";
      timestamp: number;
    };
    product: {
      _id: string;
      title: string;
      brand: string;
      price: number;
    } | null;
    buyer: {
      _id: string;
      name: string;
      email: string;
    } | null;
  } | null;
}

export function RespondToOfferModal({ isOpen, onClose, offer }: RespondToOfferModalProps) {
  const [responseMessage, setResponseMessage] = useState("");
  const [responseType, setResponseType] = useState<"info" | "negotiate" | "other">("info");
  const [isLoading, setIsLoading] = useState(false);

  // Mutations
  const respondToOffer = useMutation(api.offerManagement.respondToOffer);

  // Reset message when modal opens/closes
  const handleClose = () => {
    setResponseMessage("");
    setResponseType("info");
    setIsLoading(false);
    onClose();
  };

  const handleSubmit = async () => {
    if (!offer || !responseMessage.trim()) {
      toast.error("Please enter a response message");
      return;
    }

    setIsLoading(true);
    try {
      await respondToOffer({
        offerId: offer._id as any,
        message: responseMessage.trim(),
        responseType,
      });
      toast.success("Response sent successfully!");
      handleClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to send response");
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (!offer) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <MessageSquare className="w-6 h-6 text-accent" />
            {offer.sellerResponse ? "Update Offer Response" : "Respond to Offer"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Offer Summary */}
          <Card className="border border-border rounded-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground">
                Offer Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Product Info */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Package className="w-4 h-4 text-primary" />
                    <span className="text-sm text-muted-foreground font-light">Product:</span>
                  </div>
                  <div className="pl-6">
                    <h4 className="font-light text-foreground">
                      {offer.product?.title || "Product Title Unavailable"}
                    </h4>
                    <p className="text-sm text-muted-foreground font-light">
                      {offer.product?.brand || "Brand Unavailable"}
                    </p>
                  </div>
                </div>

                {/* Offer Details */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-accent" />
                    <span className="text-sm text-muted-foreground font-light">Offer Amount:</span>
                  </div>
                  <div className="pl-6">
                    <div className="text-lg font-light text-foreground">
                      {formatCurrency(offer.offerAmount)}
                    </div>
                    {offer.product?.price && (
                      <p className="text-sm text-muted-foreground font-light">
                        {Math.round((offer.offerAmount / offer.product.price) * 100)}% of list price
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Buyer Info */}
              <div className="flex items-center gap-2 pt-2 border-t border-border">
                <User className="w-4 h-4 text-secondary" />
                <span className="text-sm text-muted-foreground font-light">Buyer:</span>
                <span className="font-light text-foreground">
                  {offer.buyer?.name || "Unknown Buyer"}
                </span>
                <span className="text-sm text-muted-foreground font-light">
                  ({offer.buyer?.email || "No email"})
                </span>
              </div>

              {/* Original Message */}
              {offer.message && (
                <div className="pt-2 border-t border-border">
                  <div className="flex items-center gap-2 mb-2">
                    <MessageSquare className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground font-light">Original Message:</span>
                  </div>
                  <div className="pl-6 p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm text-foreground font-light">
                      {offer.message}
                    </p>
                  </div>
                </div>
              )}

              {/* Existing Seller Response */}
              {offer.sellerResponse && (
                <div className="pt-2 border-t border-border">
                  <div className="flex items-center gap-2 mb-2">
                    <MessageSquare className="w-4 h-4 text-accent" />
                    <span className="text-sm text-muted-foreground font-light">Your Previous Response:</span>
                    <Badge variant="outline" className="text-xs">
                      {offer.sellerResponse.responseType.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>
                  <div className="pl-6 p-3 bg-accent/10 rounded-lg border border-accent/20">
                    <p className="text-sm text-foreground font-light">
                      {offer.sellerResponse.message}
                    </p>
                    <p className="text-xs text-muted-foreground mt-2">
                      Sent {new Date(offer.sellerResponse.timestamp).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Response Form */}
          <Card className="border border-border rounded-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground">
                {offer.sellerResponse ? "Update Your Response" : "Your Response"}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="response-message" className="text-sm font-light text-foreground">
                  {offer.sellerResponse ? "Updated Message to Buyer" : "Message to Buyer"}
                </Label>
                <Textarea
                  id="response-message"
                  placeholder={
                    offer.sellerResponse 
                      ? "Update your response to the buyer here..."
                      : "Type your response to the buyer here. You can ask questions, provide additional information, or explain your thoughts on the offer..."
                  }
                  value={responseMessage}
                  onChange={(e) => setResponseMessage(e.target.value)}
                  className="min-h-[120px] resize-none rounded-xl border-border font-light"
                  disabled={isLoading}
                />
                <p className="text-xs text-muted-foreground font-light">
                  {offer.sellerResponse 
                    ? "This will update your previous response to the buyer."
                    : "This message will be sent to the buyer. You can use this to ask questions, request more information, or provide context about your decision."
                  }
                </p>
                
                {!offer.sellerResponse && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-xs text-blue-800 font-light">
                      💡 <strong>Tip:</strong> You can only send one response per offer. Make it count by being clear and helpful!
                    </p>
                  </div>
                )}
              </div>

              {/* Response Type Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-light text-foreground">
                  Response Type
                </Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="response-info"
                      name="response-type"
                      value="info"
                      checked={responseType === "info"}
                      onChange={(e) => setResponseType(e.target.value as "info" | "negotiate" | "other")}
                      className="text-accent"
                    />
                    <Label htmlFor="response-info" className="text-sm font-light text-foreground">
                      Request Information
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="response-negotiate"
                      name="response-type"
                      value="negotiate"
                      checked={responseType === "negotiate"}
                      onChange={(e) => setResponseType(e.target.value as "info" | "negotiate" | "other")}
                      className="text-accent"
                    />
                    <Label htmlFor="response-negotiate" className="text-sm font-light text-foreground">
                      Negotiate Terms
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="response-other"
                      name="response-type"
                      value="other"
                      checked={responseType === "other"}
                      onChange={(e) => setResponseType(e.target.value as "info" | "negotiate" | "other")}
                      className="text-accent"
                    />
                    <Label htmlFor="response-other" className="text-sm font-light text-foreground">
                      Other
                    </Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-3 justify-end pt-4 border-t border-border">
            <Button 
              variant="outline" 
              onClick={handleClose}
              disabled={isLoading}
              className="font-light"
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit}
              disabled={isLoading || !responseMessage.trim()}
              className="font-light bg-accent hover:bg-accent/90"
            >
              <Send className="w-4 h-4 mr-2" />
              {isLoading ? "Sending..." : (offer.sellerResponse ? "Update Response" : "Send Response")}
            </Button>
          </div>

          {/* Response Information */}
          <div className="pt-4 border-t border-border">
            <div className="text-center">
              <p className="text-xs text-muted-foreground font-light">
                {offer.sellerResponse 
                  ? "Your response will be updated and the buyer will be notified."
                  : "After sending your response, the buyer will be notified and can continue the conversation through the messaging system."
                }
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
