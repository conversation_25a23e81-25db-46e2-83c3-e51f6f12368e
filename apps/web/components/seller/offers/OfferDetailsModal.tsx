"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { 
  X, 
  Package, 
  User, 
  DollarSign, 
  Calendar, 
  MessageSquare, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  Ban,
  Building2,
  Phone,
  Mail,
  MapPin,
  Percent,
  TrendingUp,
  Eye
} from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Separator } from "@repo/ui/components/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";

interface OfferDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  offer: {
    _id: string;
    offerAmount: number;
    message?: string;
    status: string;
    counterOffer?: number;
    counterMessage?: string;
    sellerResponse?: {
      message: string;
      responseType: "info" | "negotiate" | "other";
      timestamp: number;
    };
    expiresAt: number;
    _creationTime: number;
    product: {
      _id: string;
      title: string;
      brand: string;
      price: number;
      images: string[];
      condition: string;
      description?: string;
    } | null;
    buyer: {
      _id: string;
      name: string;
      email: string;
    } | null;
  } | null;
}

const STATUS_COLORS = {
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  countered: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  accepted: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  declined: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  expired: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  withdrawn: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
};

const STATUS_ICONS = {
  pending: Clock,
  countered: MessageSquare,
  accepted: CheckCircle,
  declined: XCircle,
  expired: AlertTriangle,
  withdrawn: Ban,
};

export function OfferDetailsModal({ isOpen, onClose, offer }: OfferDetailsModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Mutations
  const acceptOffer = useMutation(api.offerManagement.acceptOffer);
  const declineOffer = useMutation(api.offerManagement.declineOffer);

  if (!offer) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatExpiryDate = (timestamp: number) => {
    const now = Date.now();
    const timeLeft = timestamp - now;
    const daysLeft = Math.ceil(timeLeft / (1000 * 60 * 60 * 24));
    
    if (daysLeft <= 0) return "Expired";
    if (daysLeft === 1) return "Expires tomorrow";
    return `Expires in ${daysLeft} days`;
  };

  const getPercentageOfListPrice = () => {
    if (!offer.product?.price) return 0;
    return Math.round((offer.offerAmount / offer.product.price) * 100);
  };

  const handleAcceptOffer = async () => {
    if (!offer) return;
    
    setIsLoading(true);
    try {
      await acceptOffer({ offerId: offer._id as any });
      toast.success("Offer accepted successfully!");
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to accept offer");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeclineOffer = async () => {
    if (!offer) return;
    
    setIsLoading(true);
    try {
      await declineOffer({ offerId: offer._id as any });
      toast.success("Offer declined successfully!");
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to decline offer");
    } finally {
      setIsLoading(false);
    }
  };

  const StatusIcon = STATUS_ICONS[offer.status as keyof typeof STATUS_ICONS] || Clock;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Package className="w-6 h-6 text-primary" />
            Offer Details
            <Badge 
              variant="outline" 
              className={`ml-auto ${STATUS_COLORS[offer.status as keyof typeof STATUS_COLORS] || 'bg-gray-100 text-gray-800'}`}
            >
              <StatusIcon className="w-3 h-3 mr-1" />
              {offer.status?.charAt(0).toUpperCase() + offer.status?.slice(1)}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Product Information */}
          <Card className="border border-border rounded-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                <Package className="w-5 h-5 text-primary" />
                Product Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-light text-lg text-foreground mb-2">
                    {offer.product?.title || "Product Title Unavailable"}
                  </h3>
                  <p className="text-muted-foreground font-light mb-4">
                    {offer.product?.brand || "Brand Unavailable"}
                  </p>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground font-light">List Price:</span>
                      <span className="font-light text-foreground">
                        {offer.product?.price ? formatCurrency(offer.product.price) : "—"}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground font-light">Condition:</span>
                      <Badge variant="outline" className="font-light">
                        {offer.product?.condition?.replace('_', ' ').toUpperCase() || "—"}
                      </Badge>
                    </div>
                    {offer.product?.description && (
                      <div>
                        <span className="text-muted-foreground font-light block mb-2">Description:</span>
                        <p className="text-foreground font-light text-sm">
                          {offer.product.description}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-center">
                  <div className="w-32 h-32 bg-primary/5 rounded-xl overflow-hidden border border-border">
                    {offer.product?.images && offer.product.images.length > 0 ? (
                      <img 
                        src={offer.product.images[0]}
                        alt={offer.product.title || "Product"}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback to placeholder if image fails to load
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                    ) : null}
                    <div className={`w-full h-full bg-primary/5 flex items-center justify-center ${offer.product?.images && offer.product.images.length > 0 ? 'hidden' : ''}`}>
                      <Package className="w-12 h-12 text-muted-foreground" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Offer Information */}
          <Card className="border border-border rounded-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                <DollarSign className="w-5 h-5 text-accent" />
                Offer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-primary/5 rounded-xl border border-border">
                  <div className="text-2xl font-light text-primary mb-1">
                    {formatCurrency(offer.offerAmount)}
                  </div>
                  <p className="text-sm text-muted-foreground font-light">Offer Amount</p>
                </div>
                
                <div className="text-center p-4 bg-accent/5 rounded-xl border border-border">
                  <div className="text-2xl font-light text-accent mb-1">
                    {getPercentageOfListPrice()}%
                  </div>
                  <p className="text-sm text-muted-foreground font-light">Of List Price</p>
                </div>
                
                <div className="text-center p-4 bg-secondary/5 rounded-xl border border-border">
                  <div className="text-2xl font-light text-secondary mb-1">
                    {offer.product?.price ? formatCurrency(offer.product.price - offer.offerAmount) : "—"}
                  </div>
                  <p className="text-sm text-muted-foreground font-light">Difference</p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-foreground mb-3">Timeline</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground font-light">Received:</span>
                      <span className="font-light text-foreground">
                        {formatDate(offer._creationTime)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground font-light">Expires:</span>
                      <span className="font-light text-foreground">
                        {formatExpiryDate(offer.expiresAt)}
                      </span>
                    </div>
                  </div>
                </div>

                {offer.message && (
                  <div>
                    <h4 className="font-medium text-foreground mb-3">Buyer Message</h4>
                    <div className="p-3 bg-muted/50 rounded-lg">
                      <p className="text-sm text-foreground font-light">
                        {offer.message}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {offer.status === "countered" && (
                <>
                  <Separator />
                  <div>
                    <h4 className="font-medium text-foreground mb-3 flex items-center gap-2">
                      <MessageSquare className="w-4 h-4 text-accent" />
                      Counter Offer
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="text-center p-4 bg-accent/5 rounded-xl border border-border">
                        <div className="text-xl font-light text-accent mb-1">
                          {offer.counterOffer ? formatCurrency(offer.counterOffer) : "—"}
                        </div>
                        <p className="text-sm text-muted-foreground font-light">Counter Amount</p>
                      </div>
                      {offer.counterMessage && (
                        <div>
                          <span className="text-muted-foreground font-light block mb-2">Message:</span>
                          <div className="p-3 bg-accent/10 rounded-lg">
                            <p className="text-sm text-foreground font-light">
                              {offer.counterMessage}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Seller Response */}
              {offer.sellerResponse && (
                <>
                  <Separator />
                  <div>
                    <h4 className="font-medium text-foreground mb-3 flex items-center gap-2">
                      <MessageSquare className="w-4 h-4 text-secondary" />
                      Your Response
                      <Badge variant="outline" className="text-xs">
                        {offer.sellerResponse.responseType.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </h4>
                    <div className="p-3 bg-secondary/10 rounded-lg border border-secondary/20">
                      <p className="text-sm text-foreground font-light">
                        {offer.sellerResponse.message}
                      </p>
                      <p className="text-xs text-muted-foreground mt-2">
                        Sent {new Date(offer.sellerResponse.timestamp).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Buyer Information */}
          <Card className="border border-border rounded-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                <User className="w-5 h-5 text-secondary" />
                Buyer Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {offer.buyer ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-secondary" />
                      </div>
                      <div>
                        <h3 className="font-light text-lg text-foreground">
                          {offer.buyer.name}
                        </h3>
                        <p className="text-muted-foreground font-light">
                          {offer.buyer.email}
                        </p>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Mail className="w-4 h-4" />
                        <span className="font-light">{offer.buyer.email}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-center">
                    <div className="w-32 h-32 bg-secondary/5 rounded-xl overflow-hidden border border-border flex items-center justify-center">
                      <User className="w-12 h-12 text-muted-foreground" />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <User className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                  <p className="text-muted-foreground font-light">Buyer information unavailable</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          {offer.status === "pending" && (
            <div className="flex gap-3 justify-end pt-4 border-t border-border">
              <Button 
                variant="outline" 
                onClick={handleDeclineOffer}
                disabled={isLoading}
                className="font-light"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Decline Offer
              </Button>
              <Button 
                onClick={handleAcceptOffer}
                disabled={isLoading}
                className="font-light"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Accept Offer
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
