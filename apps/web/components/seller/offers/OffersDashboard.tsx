"use client";

import { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { 
  Search, 
  Plus, 
  FileText, 
  DollarSign, 
  Package, 
  TrendingUp, 
  Users, 
  Clock, 
  Target,
  Download,
  Mail,
  BarChart3,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Filter,
  Eye,
  MapPin,
  CreditCard,
  Percent,
  Star,
  Info,
  Building2,
  Phone,
  Edit,
  Trash2,
  MoreHorizontal,
  MessageSquare,
  Handshake,
  Ban,
  ArrowUpDown
} from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/hooks/useBetterAuth";
import { useRouter } from "next/navigation";
import { OfferDetailsModal } from "./OfferDetailsModal";
import { RespondToOfferModal } from "./RespondToOfferModal";
import { OfferMessageModal } from "../../offers/OfferMessageModal";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/alert-dialog";

export function OffersDashboard() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("overview");
  const [timeRange, setTimeRange] = useState("30d");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedOfferId, setSelectedOfferId] = useState<string | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isRespondModalOpen, setIsRespondModalOpen] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState<any>(null);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);

  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // Backend queries
  const offerCounts = useQuery(api.offerManagement.getSellerOfferCounts);
  const allOffers = useQuery(api.offerManagement.getSellerOffers, {
    status: statusFilter === "all" ? undefined : statusFilter as any
  });
  
  // Get unread message count for seller
  const unreadMessageCount = useQuery(api.offerManagement.getUnreadOfferMessageCount);

  // Data processing
  const filteredOffers = allOffers?.filter(offer =>
    offer.product?.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    offer.buyer?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    offer.buyer?.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    offer.product?.brand.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Calculate metrics
  const totalOffers = offerCounts?.total || 0;
  const pendingOffers = offerCounts?.pending || 0;
  const counteredOffers = offerCounts?.countered || 0;
  const acceptedOffers = 0; // Not available in current API
  const declinedOffers = 0; // Not available in current API

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getOfferStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "default";
      case "countered": return "secondary";
      case "accepted": return "default";
      case "declined": return "destructive";
      case "expired": return "outline";
      case "withdrawn": return "outline";
      default: return "secondary";
    }
  };

  const getOfferStatusIcon = (status: string) => {
    switch (status) {
      case "pending": return <Clock className="w-4 h-4" />;
      case "countered": return <MessageSquare className="w-4 h-4" />;
      case "accepted": return <CheckCircle className="w-4 h-4" />;
      case "declined": return <XCircle className="w-4 h-4" />;
      case "expired": return <AlertTriangle className="w-4 h-4" />;
      case "withdrawn": return <Ban className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  const topOffers = useMemo(() => {
    if (!allOffers) return [];
    
    return allOffers
      .filter(offer => offer.status === "pending")
      .sort((a, b) => (b.offerAmount || 0) - (a.offerAmount || 0))
      .slice(0, 5);
  }, [allOffers]);

  const recentOffers = useMemo(() => {
    if (!allOffers) return [];
    
    return allOffers
      .sort((a, b) => new Date(b._creationTime).getTime() - new Date(a._creationTime).getTime())
      .slice(0, 5);
  }, [allOffers]);

  const handleOfferAction = (offerId: string, action: string) => {
    switch (action) {
      case "view":
        setSelectedOfferId(offerId);
        setIsDetailsModalOpen(true);
        break;
      case "respond":
        setSelectedOfferId(offerId);
        setIsRespondModalOpen(true);
        break;
      case "message":
        const offer = allOffers?.find(o => o._id === offerId);
        if (offer) {
          setSelectedOffer(offer);
          setIsMessageModalOpen(true);
        }
        break;
    }
  };

  const handleOpenMessage = (offer: any) => {
    setSelectedOffer(offer);
    setIsMessageModalOpen(true);
  };

  const handleCloseMessage = () => {
    setIsMessageModalOpen(false);
    setSelectedOffer(null);
  };

  const handleAddOffer = () => {
    // Navigate to add offer page or open modal
    router.push('/seller/offers/new');
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Offer Management</h1>
          <p className="text-muted-foreground font-light">
            Manage product offers, respond to buyers, and track negotiation status
          </p>
          {unreadMessageCount && unreadMessageCount > 0 && (
            <div className="flex items-center gap-2 mt-2">
              <MessageSquare className="w-4 h-4 text-blue-600" />
              <span className="text-sm text-blue-600 font-medium">
                {unreadMessageCount} unread message{unreadMessageCount === 1 ? '' : 's'}
              </span>
            </div>
          )}
        </div>
        
        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            className="font-light"
            onClick={() => router.push('/seller/offers/export')}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button 
            onClick={handleAddOffer}
            className="font-light"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Offer
          </Button>
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40 rounded-xl">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32 rounded-xl">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="countered">Countered</SelectItem>
              <SelectItem value="accepted">Accepted</SelectItem>
              <SelectItem value="declined">Declined</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search offers, buyers, products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rounded-xl"
          />
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-primary/5 rounded-xl border border-border">
          <TabsTrigger value="overview" className="rounded-xl font-light">Overview</TabsTrigger>
          <TabsTrigger value="offers" className="rounded-xl font-light">Offers</TabsTrigger>
          <TabsTrigger value="analytics" className="rounded-xl font-light">Analytics</TabsTrigger>
          <TabsTrigger value="management" className="rounded-xl font-light">Management</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Offers */}
            <Card className="bg-primary text-primary-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-light text-primary-foreground/80 uppercase tracking-wide">
                    <DollarSign className="w-4 h-4 mr-2 inline" />
                    Total Offers
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-primary-foreground">
                  {totalOffers}
                </div>
                <p className="text-xs text-primary-foreground/80 font-light">
                  All time offers received
                </p>
              </CardContent>
            </Card>

            {/* Pending Offers */}
            <Card className="bg-amber-500 text-white border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-light text-white/80 uppercase tracking-wide">
                  <Clock className="w-4 h-4 mr-2 inline" />
                  Pending Offers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-white">
                  {pendingOffers}
                </div>
                <p className="text-xs text-white/80 font-light">
                  Awaiting your response
                </p>
              </CardContent>
            </Card>

            {/* Countered Offers */}
            <Card className="bg-accent text-accent-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-light text-accent-foreground/80 uppercase tracking-wide">
                  <MessageSquare className="w-4 h-4 mr-2 inline" />
                  Countered Offers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-accent-foreground">
                  {counteredOffers}
                </div>
                <p className="text-xs text-accent-foreground/80 font-light">
                  Waiting for buyer response
                </p>
              </CardContent>
            </Card>

            {/* Accepted Offers */}
            <Card className="bg-green-500 text-white border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-light text-white/80 uppercase tracking-wide">
                  <CheckCircle className="w-4 h-4 mr-2 inline" />
                  Accepted Offers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-white">
                  {acceptedOffers}
                </div>
                <p className="text-xs text-white/80 font-light">
                  Successful negotiations
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Key Metrics Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Offers by Amount */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <Star className="w-5 h-5" />
                  Top Offers by Amount
                </CardTitle>
              </CardHeader>
              <CardContent>
                {topOffers.length === 0 ? (
                  <div className="text-center py-8">
                    <DollarSign className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                    <p className="text-muted-foreground font-light">No pending offers</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {topOffers.map((offer, index) => (
                      <div key={offer._id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <span className="text-sm font-light text-primary">#{index + 1}</span>
                          </div>
                          <div>
                            <p className="font-light text-foreground">{offer.product?.title}</p>
                            <p className="text-xs text-muted-foreground font-light">
                              {offer.buyer?.name} • {offer.buyer?.email}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-light text-foreground">{formatCurrency(offer.offerAmount)}</p>
                          <p className="text-xs text-muted-foreground font-light">
                            {offer.product?.price ? `${Math.round((offer.offerAmount / offer.product.price) * 100)}%` : '—'}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Recent Offer Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentOffers.map((offer) => (
                    <div key={offer._id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${
                          offer.status === "pending" ? "bg-amber-500" : 
                          offer.status === "accepted" ? "bg-green-500" : 
                          offer.status === "declined" ? "bg-red-500" : "bg-gray-500"
                        }`} />
                        <div>
                          <p className="font-light text-foreground">{offer.product?.title}</p>
                          <p className="text-xs text-muted-foreground font-light">
                            {offer.buyer?.name} • {formatCurrency(offer.offerAmount)}
                          </p>
                        </div>
                      </div>
                      <Badge variant={getOfferStatusColor(offer.status)} className="font-light text-xs">
                        {offer.status?.charAt(0).toUpperCase() + offer.status?.slice(1)}
                      </Badge>
                    </div>
                  ))}
                  {recentOffers.length === 0 && (
                    <div className="text-center py-8">
                      <Clock className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                      <p className="text-muted-foreground font-light">No recent activity</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Offers List Tab */}
        <TabsContent value="offers" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-light text-foreground">Offers List</h2>
              <p className="text-muted-foreground font-light">View and manage all your product offers</p>
            </div>
            <Button 
              variant="outline" 
              className="font-light"
              onClick={() => router.push('/seller/offers/list')}
            >
              <Eye className="h-4 w-4 mr-2" />
              View Full List
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">{totalOffers}</div>
                <p className="text-sm text-muted-foreground font-light">Total Offers</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">{pendingOffers}</div>
                <p className="text-sm text-muted-foreground font-light">Pending</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">{counteredOffers}</div>
                <p className="text-sm text-muted-foreground font-light">Countered</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">{acceptedOffers}</div>
                <p className="text-sm text-muted-foreground font-light">Accepted</p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Offers Preview */}
          <Card className="rounded-xl border border-border bg-card shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground">Recent Offers</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredOffers.length === 0 ? (
                <div className="text-center py-12">
                  <DollarSign className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                  <h3 className="text-lg font-light mb-2">No offers found</h3>
                  <p className="text-muted-foreground font-light mb-4">
                    Offers will appear here when buyers make them on your products
                  </p>
                  <Button 
                    onClick={() => router.push('/seller/products')}
                    className="font-light"
                  >
                    <Package className="h-4 w-4 mr-2" />
                    View Products
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredOffers.slice(0, 5).map((offer) => (
                    <Card key={offer._id} className="border border-border rounded-xl">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-4 mb-4">
                              <div>
                                <h3 className="font-light text-lg text-foreground">
                                  {offer.product?.title}
                                </h3>
                                <p className="text-muted-foreground font-light">
                                  {offer.buyer?.name} • {offer.buyer?.email}
                                </p>
                              </div>
                              <Badge variant={getOfferStatusColor(offer.status)} className="font-light">
                                {offer.status?.charAt(0).toUpperCase() + offer.status?.slice(1)}
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-muted-foreground font-light">Offer Amount</p>
                                <p className="font-light text-foreground">{formatCurrency(offer.offerAmount)}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">List Price</p>
                                <p className="font-light text-foreground">
                                  {offer.product?.price ? formatCurrency(offer.product.price) : "—"}
                                </p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Percentage</p>
                                <p className="font-light text-foreground">
                                  {offer.product?.price ? `${Math.round((offer.offerAmount / offer.product.price) * 100)}%` : "—"}
                                </p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Received</p>
                                <p className="font-light text-foreground">
                                  {new Date(offer._creationTime).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                            
                            {offer.message && (
                              <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                                <p className="text-sm text-muted-foreground font-light">
                                  <span className="font-medium">Message:</span> {offer.message}
                                </p>
                              </div>
                            )}
                          </div>
                          
                          <div className="flex gap-2">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="font-light"
                              onClick={() => handleOfferAction(offer._id, "view")}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="font-light text-blue-600 border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                              onClick={() => handleOpenMessage(offer)}
                            >
                              <MessageSquare className="h-4 w-4 mr-2" />
                              Message
                            </Button>
                            {offer.status === "pending" && (
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="font-light"
                                onClick={() => handleOfferAction(offer._id, "respond")}
                              >
                                <MessageSquare className="h-4 w-4 mr-2" />
                                Respond
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                  
                  {filteredOffers.length > 5 && (
                    <div className="text-center py-4">
                      <Button 
                        variant="outline" 
                        className="font-light"
                        onClick={() => router.push('/seller/offers/list')}
                      >
                        View All {totalOffers} Offers
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div>
            <h2 className="text-xl font-light text-foreground">Offer Analytics</h2>
            <p className="text-muted-foreground font-light">Performance insights and negotiation trends</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Offer Status Distribution */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Offer Status Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-light text-foreground">Pending</span>
                    <span className="font-light text-foreground">{pendingOffers}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-amber-500 h-2 rounded-full" 
                      style={{ width: `${totalOffers > 0 ? (pendingOffers / totalOffers) * 100 : 0}%` }}
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="font-light text-foreground">Countered</span>
                    <span className="font-light text-foreground">{counteredOffers}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-accent h-2 rounded-full" 
                      style={{ width: `${totalOffers > 0 ? (counteredOffers / totalOffers) * 100 : 0}%` }}
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="font-light text-foreground">Accepted</span>
                    <span className="font-light text-foreground">{acceptedOffers}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full" 
                      style={{ width: `${totalOffers > 0 ? (acceptedOffers / totalOffers) * 100 : 0}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Offer Amount Analysis */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  Offer Amount Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-light text-foreground">High Offers (&gt;90% of list)</span>
                      <span className="font-light text-foreground">
                        {filteredOffers.filter(o => o.product?.price && (o.offerAmount / o.product.price) > 0.9).length}
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full" 
                        style={{ 
                          width: `${totalOffers > 0 ? (filteredOffers.filter(o => o.product?.price && (o.offerAmount / o.product.price) > 0.9).length / totalOffers) * 100 : 0}%` 
                        }} 
                      />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-light text-foreground">Medium Offers (70-90% of list)</span>
                      <span className="font-light text-foreground">
                        {filteredOffers.filter(o => o.product?.price && (o.offerAmount / o.product.price) >= 0.7 && (o.offerAmount / o.product.price) <= 0.9).length}
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-amber-500 h-2 rounded-full" 
                        style={{ 
                          width: `${totalOffers > 0 ? (filteredOffers.filter(o => o.product?.price && (o.offerAmount / o.product.price) >= 0.7 && (o.offerAmount / o.product.price) <= 0.9).length / totalOffers) * 100 : 0}%` 
                        }} 
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Management Tab */}
        <TabsContent value="management" className="space-y-6">
          <div>
            <h2 className="text-xl font-light text-foreground">Offer Management Tools</h2>
            <p className="text-muted-foreground font-light">Tools and utilities for managing product offers</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Package className="w-12 h-12 text-primary mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Manage Products</h3>
                <p className="text-muted-foreground font-light mb-4">
                  Update product listings and pricing to attract better offers
                </p>
                <Button className="w-full font-light" onClick={() => router.push('/seller/products')}>
                  <Package className="w-4 h-4 mr-2" />
                  View Products
                </Button>
              </CardContent>
            </Card>

            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Download className="w-12 h-12 text-accent mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Export Offer Data</h3>
                <p className="text-muted-foreground font-light mb-4">
                  Download comprehensive offer reports and analytics
                </p>
                <Button variant="outline" className="w-full font-light">
                  <Download className="w-4 h-4 mr-2" />
                  Export Data
                </Button>
              </CardContent>
            </Card>

            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <BarChart3 className="w-12 h-12 text-secondary mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Performance Reports</h3>
                <p className="text-muted-foreground font-light mb-4">
                  Generate detailed offer performance and conversion reports
                </p>
                <Button variant="outline" className="w-full font-light">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  View Reports
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Offer Details Modal */}
      <OfferDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedOfferId(null);
        }}
        offer={selectedOfferId ? allOffers?.find(o => o._id === selectedOfferId) || null : null}
      />

      {/* Respond to Offer Modal */}
      <RespondToOfferModal
        isOpen={isRespondModalOpen}
        onClose={() => {
          setIsRespondModalOpen(false);
          setSelectedOfferId(null);
        }}
        offer={selectedOfferId ? allOffers?.find(o => o._id === selectedOfferId) || null : null}
      />

      {/* Offer Message Modal */}
      <OfferMessageModal
        isOpen={isMessageModalOpen}
        onClose={handleCloseMessage}
        offer={selectedOffer}
      />
    </div>
  );
}
