"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Badge } from "@repo/ui/components/badge";

interface PendingOffersBadgeProps {
  variant?: "main" | "submenu";
}

export function PendingOffersBadge({ variant = "main" }: PendingOffersBadgeProps) {
  const offerCounts = useQuery(api.offerManagement.getSellerOfferCounts);

  if (!offerCounts) {
    return null;
  }

  // For main menu, show pending count with amber color
  if (variant === "main") {
    if (offerCounts.pending === 0 && offerCounts.total === 0) {
      return null;
    }
    
    // Show pending count prominently
    if (offerCounts.pending > 0) {
      return (
        <Badge 
          className="bg-amber-500 text-white text-xs ml-auto" 
          variant="default"
          title={`${offerCounts.pending} pending offer${offerCounts.pending !== 1 ? 's' : ''} • ${offerCounts.total} total offer${offerCounts.total !== 1 ? 's' : ''}`}
        >
          {offerCounts.pending > 99 ? "99+" : offerCounts.pending}
        </Badge>
      );
    }
    
    // Show total count if no pending offers but some total offers
    if (offerCounts.total > 0) {
      return (
        <Badge 
          className="bg-gray-500 text-white text-xs ml-auto" 
          variant="default"
          title={`${offerCounts.total} total offer${offerCounts.total !== 1 ? 's' : ''} • No pending offers`}
        >
          {offerCounts.total > 99 ? "99+" : offerCounts.total}
        </Badge>
      );
    }
  }

  // For submenu, show pending count with amber color
  if (variant === "submenu") {
    if (offerCounts.pending === 0) {
      return null;
    }
    
    return (
      <Badge 
        className="bg-amber-500 text-white text-xs ml-auto" 
        variant="default"
        title={`${offerCounts.pending} pending offer${offerCounts.pending !== 1 ? 's' : ''}`}
      >
        {offerCounts.pending > 99 ? "99+" : offerCounts.pending}
      </Badge>
    );
  }

  return null;
}
