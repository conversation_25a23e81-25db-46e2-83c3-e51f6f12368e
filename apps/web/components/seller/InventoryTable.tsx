"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Checkbox } from "@repo/ui/components/checkbox";
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Archive,
  ShoppingCart,
  Package,
  Plus
} from "lucide-react";
import Image from "next/image";

export function InventoryTable() {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  
  const products = useQuery(api.products.getFilteredProducts, { 
    limit: 50 
  });

  if (!products) {
    return <div>Loading products...</div>;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "default";
      case "draft":
        return "secondary";
      case "sold":
        return "destructive";
      case "archived":
        return "outline";
      default:
        return "secondary";
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.map(p => p._id));
    } else {
      setSelectedProducts([]);
    }
  };

  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId]);
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    }
  };

  return (
    <Card>
      <CardContent className="p-0">
        {/* Bulk Actions */}
        {selectedProducts.length > 0 && (
          <div className="p-4 border-b border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800">
            <div className="flex items-center gap-4">
              <span className="text-sm text-neutral-600 dark:text-neutral-400">
                {selectedProducts.length} selected
              </span>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Archive className="h-4 w-4 mr-2" />
                  Archive
                </Button>
                <Button variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-2" />
                  Bulk Edit
                </Button>
                <Button variant="destructive" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-neutral-200 dark:border-neutral-700">
              <tr className="text-left">
                <th className="p-4 w-12">
                  <Checkbox
                    checked={selectedProducts.length === products.length}
                    onCheckedChange={handleSelectAll}
                  />
                </th>
                <th className="p-4 w-20">Image</th>
                <th className="p-4">Product</th>
                <th className="p-4">Category</th>
                <th className="p-4">Price</th>
                <th className="p-4">Status</th>
                <th className="p-4">Views</th>
                <th className="p-4">Created</th>
                <th className="p-4 w-12">Actions</th>
              </tr>
            </thead>
            <tbody>
              {products.map((product) => (
                <tr 
                  key={product._id} 
                  className="border-b border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800/50"
                >
                  <td className="p-4">
                    <Checkbox
                      checked={selectedProducts.includes(product._id)}
                      onCheckedChange={(checked) => 
                        handleSelectProduct(product._id, checked as boolean)
                      }
                    />
                  </td>
                  <td className="p-4">
                    <div className="w-12 h-12 bg-neutral-100 dark:bg-neutral-800 rounded-md overflow-hidden">
                      {product.primaryImageId ? (
                        <Image
                          src={`/api/storage/${product.primaryImageId}`}
                          alt={product.title}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="h-6 w-6 text-neutral-400" />
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="p-4">
                    <div>
                      <div className="font-medium text-neutral-900 dark:text-neutral-100">
                        {product.title}
                      </div>
                      <div className="text-sm text-neutral-600 dark:text-neutral-400">
                        {product.brand}
                      </div>
                      {product.sku && (
                        <div className="text-xs text-neutral-500 dark:text-neutral-500">
                          SKU: {product.sku}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">
                      {product.category}
                    </span>
                  </td>
                  <td className="p-4">
                    <div className="font-medium">
                      {formatCurrency(product.price)}
                    </div>
                    {product.originalPrice && product.originalPrice > product.price && (
                      <div className="text-sm text-neutral-500 line-through">
                        {formatCurrency(product.originalPrice)}
                      </div>
                    )}
                  </td>
                  <td className="p-4">
                    <Badge variant={getStatusColor(product.status)}>
                      {product.status.charAt(0).toUpperCase() + product.status.slice(1)}
                    </Badge>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-1">
                      <Eye className="h-4 w-4 text-neutral-400" />
                      <span className="text-sm">{product.views}</span>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">
                      {new Date(product._creationTime).toLocaleDateString()}
                    </span>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {products.length === 0 && (
          <div className="p-8 text-center">
            <Package className="h-12 w-12 text-neutral-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2">
              No products yet
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-4">
              Start by adding your first product to your inventory.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}