"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { ArrowRight, Package } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export function RecentSales() {
  // TODO: Get actual seller ID from auth context
  const sellerId = "seller123" as any;
  
  const sales = useQuery(api.sales.getRecentSales, { 
    sellerId,
    days: 7 
  });

  if (!sales) {
    return <div>Loading recent sales...</div>;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "default";
      case "shipped":
        return "secondary";
      case "pending":
        return "outline";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Sales</CardTitle>
        <Link href="/seller/sales">
          <Button variant="outline" size="sm">
            View All
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        {sales.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-neutral-600 dark:text-neutral-400">
              No recent sales in the last 7 days
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {sales.map((sale) => (
              <div 
                key={sale._id}
                className="flex items-center gap-4 p-4 border border-neutral-200 dark:border-neutral-700 rounded-lg"
              >
                <div className="w-12 h-12 bg-neutral-100 dark:bg-neutral-800 rounded-md overflow-hidden flex-shrink-0">
                  {sale.product?.primaryImageId ? (
                    <Image
                      src={`/api/storage/${sale.product.primaryImageId}`}
                      alt={sale.product.title || "Product"}
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Package className="h-6 w-6 text-neutral-400" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="font-medium text-neutral-900 dark:text-neutral-100 truncate">
                    {sale.product?.title || "Unknown Product"}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    {new Date(sale._creationTime).toLocaleDateString()}
                  </div>
                </div>

                {/* <div className="text-right">
                  <div className="font-medium">
                    {formatCurrency(sale.offlineSale?.salePrice || 0)}
                  </div>
                  <Badge variant={getStatusColor(sale.offlineSale?.status || "")} className="mt-1">
                    {sale.offlineSale?.status.charAt(0).toUpperCase() + sale.offlineSale?.status.slice(1)}
                  </Badge>
                </div> */}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}