"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp, Filter, X } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { SellerFilterState } from "./SellerDirectoryContent";

interface SellerDirectoryFiltersProps {
  filters: SellerFilterState;
  onFilterChange: (filters: Partial<SellerFilterState>) => void;
  onClearFilters: () => void;
  activeFilterCount: number;
}

const categories = [
  "clothing",
  "sneakers", 
  "collectibles",
  "accessories",
  "handbags",
  "jewelry",
  "watches",
  "sunglasses"
];

const businessTypes = [
  "individual",
  "llc",
  "corporation",
  "partnership"
];

const ratingOptions = [
  { value: 4.5, label: "4.5+ Stars" },
  { value: 4.0, label: "4.0+ Stars" },
  { value: 3.5, label: "3.5+ Stars" },
  { value: 3.0, label: "3.0+ Stars" },
];

export function SellerDirectoryFilters({
  filters,
  onFilterChange,
  onClearFilters,
  activeFilterCount,
}: SellerDirectoryFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleCategoryChange = (category: string) => {
    onFilterChange({ category: filters.category === category ? "" : category });
  };

  const handleBusinessTypeChange = (businessType: string) => {
    onFilterChange({ businessType: filters.businessType === businessType ? "" : businessType });
  };

  const handleRatingChange = (rating: number) => {
    onFilterChange({ minRating: filters.minRating === rating ? 0 : rating });
  };

  return (
    <div className="bg-white rounded-lg border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-primary" />
          <h3 className="font-semibold text-lg">Filters</h3>
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFilterCount}
            </Badge>
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </div>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="mb-4 p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm font-medium">Active Filters:</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="h-6 px-2 text-xs"
            >
              <X className="h-3 w-3 mr-1" />
              Clear All
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {filters.category && (
              <Badge variant="outline" className="text-xs">
                Category: {filters.category}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onFilterChange({ category: "" })}
                  className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}
            {filters.businessType && (
              <Badge variant="outline" className="text-xs">
                Type: {filters.businessType}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onFilterChange({ businessType: "" })}
                  className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}
            {filters.minRating > 0 && (
              <Badge variant="outline" className="text-xs">
                Rating: {filters.minRating}+
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onFilterChange({ minRating: 0 })}
                  className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* Filter Options */}
      {isExpanded && (
        <div className="space-y-6">
          {/* Categories */}
          <div>
            <h4 className="font-medium mb-3">Categories</h4>
            <div className="space-y-2">
              {categories.map((category) => (
                <label key={category} className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="category"
                    value={category}
                    checked={filters.category === category}
                    onChange={() => handleCategoryChange(category)}
                    className="text-primary focus:ring-primary"
                  />
                  <span className="text-sm capitalize">{category}</span>
                </label>
              ))}
            </div>
          </div>

          <Separator />

          {/* Business Types */}
          <div>
            <h4 className="font-medium mb-3">Business Type</h4>
            <div className="space-y-2">
              {businessTypes.map((type) => (
                <label key={type} className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="businessType"
                    value={type}
                    checked={filters.businessType === type}
                    onChange={() => handleBusinessTypeChange(type)}
                    className="text-primary focus:ring-primary"
                  />
                  <span className="text-sm capitalize">{type}</span>
                </label>
              ))}
            </div>
          </div>

          <Separator />

          {/* Rating Filter */}
          <div>
            <h4 className="font-medium mb-3">Minimum Rating</h4>
            <div className="space-y-2">
              {ratingOptions.map((option) => (
                <label key={option.value} className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="rating"
                    value={option.value}
                    checked={filters.minRating === option.value}
                    onChange={() => handleRatingChange(option.value)}
                    className="text-primary focus:ring-primary"
                  />
                  <span className="text-sm">{option.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Clear Filters Button */}
      {activeFilterCount > 0 && (
        <Button
          variant="outline"
          onClick={onClearFilters}
          className="w-full mt-4"
        >
          Clear All Filters
        </Button>
      )}
    </div>
  );
}
