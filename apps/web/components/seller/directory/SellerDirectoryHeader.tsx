"use client";

import { Store, Star, Users } from "lucide-react";

interface SellerDirectoryHeaderProps {}

export function SellerDirectoryHeader({}: SellerDirectoryHeaderProps) {

  return (
    <div className="bg-gradient-to-r from-primary/10 to-accent/10 border-b">
      <div className="container mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Store className="h-12 w-12 text-primary mr-3" />
            <h1 className="text-4xl font-bold text-primary">Seller Directory</h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover trusted sellers and boutique stores offering premium luxury items. 
            Browse by category, rating, or business type to find the perfect seller for your needs.
          </p>
        </div>

        {/* Search Instructions */}
        <div className="max-w-2xl mx-auto text-center">
          <p className="text-lg text-muted-foreground mb-4">
            Use the search bar above to find specific sellers, or browse using the filters below
          </p>
        </div>

      </div>
    </div>
  );
}
