"use client";

import { useState, useEffect, useMemo } from "react";
import { useQuery } from "convex/react";
import { useSearchParams } from "next/navigation";
import { api } from "@repo/backend/convex/_generated/api";
import { MarketplaceHeader } from "@/components/marketplace/MarketplaceHeader";
import { 
  SellerDirectoryHeader,
  SellerDirectoryFilters,
  SellerDirectoryGrid,
  SellerDirectoryPagination
} from "./index";

export interface SellerFilterState {
  searchQuery: string;
  category: string;
  businessType: string;
  minRating: number;
  sortBy: "rating" | "products" | "joinedDate" | "businessName";
  sortOrder: "asc" | "desc";
}

const initialFilters: SellerFilterState = {
  searchQuery: "",
  category: "",
  businessType: "",
  minRating: 0,
  sortBy: "rating",
  sortOrder: "desc",
};

export function SellerDirectoryContent() {
  const searchParams = useSearchParams();
  const [filters, setFilters] = useState<SellerFilterState>(initialFilters);
  const [currentPage, setCurrentPage] = useState(0);
  const [allSellers, setAllSellers] = useState<any[]>([]);
  const ITEMS_PER_PAGE = 24;

  // Handle URL search parameters
  useEffect(() => {
    const searchQuery = searchParams.get('search');
    if (searchQuery) {
      setFilters(prev => ({ ...prev, searchQuery }));
    }
  }, [searchParams]);

  // Fetch sellers with filters and pagination
  const sellersResult = useQuery(api.sellerQueries.getPublicSellers, {
    searchQuery: filters.searchQuery || undefined,
    category: filters.category || undefined,
    businessType: filters.businessType || undefined,
    minRating: filters.minRating > 0 ? filters.minRating : undefined,
    sortBy: filters.sortBy,
    sortOrder: filters.sortOrder,
    limit: ITEMS_PER_PAGE,
    offset: currentPage * ITEMS_PER_PAGE,
  });

  // Update allSellers when new results come in
  useEffect(() => {
    if (sellersResult?.sellers) {
      if (currentPage === 0) {
        setAllSellers(sellersResult.sellers);
      } else {
        setAllSellers(prev => [...prev, ...sellersResult.sellers]);
      }
    }
  }, [sellersResult?.sellers, currentPage]);

  const handleFilterChange = (newFilters: Partial<SellerFilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(0);
    setAllSellers([]);
  };

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }));
    setCurrentPage(0);
    setAllSellers([]);
  };

  const clearFilters = () => {
    setFilters(initialFilters);
    setCurrentPage(0);
    setAllSellers([]);
  };

  const handleSortChange = (newSort: SellerFilterState["sortBy"]) => {
    setFilters(prev => ({ ...prev, sortBy: newSort }));
    setCurrentPage(0);
    setAllSellers([]);
  };

  const handleSortOrderChange = (newOrder: "asc" | "desc") => {
    setFilters(prev => ({ ...prev, sortOrder: newOrder }));
    setCurrentPage(0);
    setAllSellers([]);
  };

  const handleLoadMore = () => {
    if (sellersResult?.pagination?.hasMore) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const activeFilterCount = useMemo(() => {
    return (
      (filters.searchQuery ? 1 : 0) +
      (filters.category ? 1 : 0) +
      (filters.businessType ? 1 : 0) +
      (filters.minRating > 0 ? 1 : 0)
    );
  }, [filters]);

  return (
    <div className="bg-card min-h-screen">
      <MarketplaceHeader onSearch={handleSearch} />
      <SellerDirectoryHeader />
      
      <div className="container mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* Filter Sidebar - Desktop */}
          <div className="hidden lg:block w-80 flex-shrink-0">
            <SellerDirectoryFilters
              filters={filters}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
              activeFilterCount={activeFilterCount}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Mobile Filter Button */}
            <div className="lg:hidden mb-6">
              <SellerDirectoryFilters
                filters={filters}
                onFilterChange={handleFilterChange}
                onClearFilters={clearFilters}
                activeFilterCount={activeFilterCount}
              />
            </div>
            
            <SellerDirectoryGrid
              sellers={allSellers}
              sortBy={filters.sortBy}
              sortOrder={filters.sortOrder}
              onSortChange={handleSortChange}
              onSortOrderChange={handleSortOrderChange}
              isLoading={sellersResult === undefined}
              activeFilterCount={activeFilterCount}
            />

            {/* Pagination */}
            {sellersResult?.pagination && (
              <SellerDirectoryPagination
                pagination={sellersResult.pagination}
                onPageChange={handleLoadMore}
                hasMore={sellersResult.pagination.hasMore}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
