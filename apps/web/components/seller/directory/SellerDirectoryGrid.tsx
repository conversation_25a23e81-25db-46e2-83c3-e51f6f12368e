"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  ArrowUpDown, 
  Star, 
  Store, 
  Package, 
  Calendar,
  MapPin,
  Building2
} from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { SellerFilterState } from "./SellerDirectoryContent";

interface SellerDirectoryGridProps {
  sellers: any[];
  sortBy: SellerFilterState["sortBy"];
  sortOrder: "asc" | "desc";
  onSortChange: (sortBy: SellerFilterState["sortBy"]) => void;
  onSortOrderChange: (order: "asc" | "desc") => void;
  isLoading: boolean;
  activeFilterCount: number;
}

const sortOptions = [
  { value: "rating", label: "Rating", icon: Star },
  { value: "products", label: "Products", icon: Package },
  { value: "joinedDate", label: "Joined Date", icon: Calendar },
  { value: "businessName", label: "Business Name", icon: Building2 },
];

export function SellerDirectoryGrid({
  sellers,
  sortBy,
  sortOrder,
  onSortChange,
  onSortOrderChange,
  isLoading,
  activeFilterCount,
}: SellerDirectoryGridProps) {
  const router = useRouter();

  const handleSortChange = (newSortBy: SellerFilterState["sortBy"]) => {
    if (newSortBy === sortBy) {
      // Toggle sort order if same field
      onSortOrderChange(sortOrder === "asc" ? "desc" : "asc");
    } else {
      // Set new sort field with default desc order
      onSortChange(newSortBy);
      onSortOrderChange("desc");
    }
  };

  const handleSellerClick = (sellerId: string) => {
    router.push(`/seller/${sellerId}`);
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString("en-US", { 
      year: "numeric", 
      month: "short" 
    });
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600";
    if (rating >= 4.0) return "text-blue-600";
    if (rating >= 3.5) return "text-yellow-600";
    return "text-gray-600";
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-muted rounded w-48 animate-pulse" />
          <div className="h-8 bg-muted rounded w-32 animate-pulse" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="bg-white rounded-lg border p-6 animate-pulse">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-16 h-16 bg-muted rounded-full" />
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                </div>
              </div>
              <div className="space-y-2">
                <div className="h-3 bg-muted rounded w-full" />
                <div className="h-3 bg-muted rounded w-2/3" />
                <div className="h-3 bg-muted rounded w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (sellers.length === 0) {
    return (
      <div className="text-center py-12">
        <Store className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-muted-foreground mb-2">
          {activeFilterCount > 0 ? "No sellers found" : "No sellers available"}
        </h3>
        <p className="text-muted-foreground">
          {activeFilterCount > 0 
            ? "Try adjusting your filters to find more sellers."
            : "Check back later for new sellers joining the platform."
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Results and Sort */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold">Sellers</h2>
          <Badge variant="secondary">{sellers.length} results</Badge>
        </div>

        {/* Sort Options */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Sort by:</span>
          {sortOptions.map((option) => {
            const Icon = option.icon;
            const isActive = sortBy === option.value;
            return (
              <Button
                key={option.value}
                variant={isActive ? "default" : "outline"}
                size="sm"
                onClick={() => handleSortChange(option.value as SellerFilterState["sortBy"])}
                className="flex items-center gap-2"
              >
                <Icon className="h-4 w-4" />
                {option.label}
                {isActive && (
                  <ArrowUpDown className="h-3 w-3" />
                )}
              </Button>
            );
          })}
        </div>
      </div>

      {/* Sellers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sellers.map((seller) => (
          <div
            key={seller.userId}
            className="bg-white rounded-lg border p-6 hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => handleSellerClick(seller.userId)}
          >
            {/* Seller Header */}
            <div className="flex items-center gap-4 mb-4">
              <Avatar className="w-16 h-16">
                <AvatarImage src={seller.profileImage} alt={seller.businessName} />
                <AvatarFallback className="text-lg font-semibold bg-primary/10 text-primary">
                  {seller.businessName.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-lg truncate" title={seller.businessName}>
                  {seller.businessName}
                </h3>
                <p className="text-sm text-muted-foreground capitalize">
                  {seller.businessType || "Individual"}
                </p>
              </div>
            </div>

            {/* Rating and Stats */}
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-1">
                <Star className={`h-4 w-4 ${getRatingColor(seller.rating)} fill-current`} />
                <span className={`font-medium ${getRatingColor(seller.rating)}`}>
                  {seller.rating ? seller.rating.toFixed(1) : "N/A"}
                </span>
                <span className="text-sm text-muted-foreground">
                  ({seller.reviewCount || 0})
                </span>
              </div>
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Package className="h-4 w-4" />
                {seller.activeProducts} products
              </div>
            </div>

            {/* Categories */}
            {seller.categories && seller.categories.length > 0 && (
              <div className="mb-4">
                <div className="flex flex-wrap gap-1">
                  {seller.categories.slice(0, 3).map((category: string) => (
                    <Badge key={category} variant="outline" className="text-xs capitalize">
                      {category}
                    </Badge>
                  ))}
                  {seller.categories.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{seller.categories.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Location and Join Date */}
            <div className="space-y-2 text-sm text-muted-foreground">
              {seller.address && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span className="truncate">
                    {seller.address.city}, {seller.address.state}
                  </span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>Joined {formatDate(seller.joinedDate)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
