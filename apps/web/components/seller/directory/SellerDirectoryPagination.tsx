"use client";

import { Ch<PERSON>ron<PERSON>ef<PERSON>, ChevronR<PERSON>, Loader2 } from "lucide-react";
import { Button } from "@repo/ui/components/button";

interface SellerDirectoryPaginationProps {
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
    totalPages: number;
    currentPage: number;
  };
  onPageChange: () => void;
  hasMore: boolean;
}

export function SellerDirectoryPagination({
  pagination,
  onPageChange,
  hasMore,
}: SellerDirectoryPaginationProps) {
  const { total, limit, currentPage, totalPages } = pagination;
  const startItem = (currentPage - 1) * limit + 1;
  const endItem = Math.min(currentPage * limit, total);

  return (
    <div className="flex items-center justify-between border-t pt-6">
      {/* Results Info */}
      <div className="text-sm text-muted-foreground">
        Showing {startItem} to {endItem} of {total} sellers
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center gap-2">
        <div className="text-sm text-muted-foreground mr-4">
          Page {currentPage} of {totalPages}
        </div>
        
        {hasMore && (
          <Button
            onClick={onPageChange}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Loader2 className="h-4 w-4" />
            Load More
          </Button>
        )}
      </div>
    </div>
  );
}
