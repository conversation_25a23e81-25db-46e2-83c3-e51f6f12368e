"use client";

import { useState } from "react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { cn } from "@repo/ui/lib/utils";
import { 
  LayoutDashboard,
  Package,
  Plus,
  ShoppingBag,
  FileText,
  BarChart3,
  Settings,
  User,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Bell,
  HelpCircle
} from "lucide-react";

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
  href?: string;
  children?: NavigationItem[];
}

interface SellerNavigationProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  pendingOrders?: number;
  className?: string;
}

const NAVIGATION_ITEMS: NavigationItem[] = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
    href: "/seller/dashboard"
  },
  {
    id: "inventory",
    label: "Inventory",
    icon: Package,
    children: [
      {
        id: "inventory-list",
        label: "All Products",
        icon: Package,
      },
      {
        id: "add-product",
        label: "Add Product",
        icon: Plus,
      }
    ]
  },
  {
    id: "sales",
    label: "Sales",
    icon: ShoppingBag,
    children: [
      {
        id: "sales-history",
        label: "Sales History",
        icon: ShoppingBag,
      },
      {
        id: "pending-orders",
        label: "Pending Orders",
        icon: FileText,
        badge: 3
      }
    ]
  },
  {
    id: "invoicing",
    label: "Invoicing",
    icon: FileText,
    children: [
      {
        id: "invoices",
        label: "All Invoices",
        icon: FileText,
      },
      {
        id: "create-invoice",
        label: "Create Invoice",
        icon: Plus,
      }
    ]
  },
  {
    id: "reports",
    label: "Reports",
    icon: BarChart3,
    children: [
      {
        id: "analytics",
        label: "Analytics",
        icon: BarChart3,
      },
      {
        id: "performance",
        label: "Performance",
        icon: BarChart3,
      }
    ]
  }
];

const BOTTOM_ITEMS: NavigationItem[] = [
  {
    id: "settings",
    label: "Settings",
    icon: Settings,
  },
  {
    id: "help",
    label: "Help & Support",
    icon: HelpCircle,
  }
];

export function SellerNavigation({ 
  activeSection, 
  onSectionChange, 
  pendingOrders = 0,
  className 
}: SellerNavigationProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>(["inventory", "sales"]);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const isActive = activeSection === item.id;
    const isExpanded = expandedItems.includes(item.id);
    const hasChildren = item.children && item.children.length > 0;
    const Icon = item.icon;

    return (
      <div key={item.id}>
        <Button
          variant="ghost"
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.id);
            } else {
              onSectionChange(item.id);
            }
          }}
          className={cn(
            "w-full justify-start h-12 px-3 mb-1 transition-all duration-200",
            level > 0 && "ml-6 h-10",
            isActive 
              ? "bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200" 
              : "hover:bg-neutral-100 dark:hover:bg-neutral-800",
            isCollapsed && level === 0 && "justify-center px-0"
          )}
        >
          <Icon className={cn(
            "flex-shrink-0",
            isCollapsed && level === 0 ? "w-6 h-6" : "w-5 h-5 mr-3"
          )} />
          
          {(!isCollapsed || level > 0) && (
            <>
              <span className="flex-1 text-left font-medium">
                {item.label}
              </span>
              
              {item.badge && (
                <Badge 
                  variant="destructive" 
                  className="ml-2 h-5 min-w-[20px] text-xs"
                >
                  {item.badge}
                </Badge>
              )}
              
              {item.id === "pending-orders" && pendingOrders > 0 && (
                <Badge 
                  variant="destructive" 
                  className="ml-2 h-5 min-w-[20px] text-xs"
                >
                  {pendingOrders}
                </Badge>
              )}
              
              {hasChildren && (
                <ChevronRight className={cn(
                  "w-4 h-4 ml-2 transition-transform duration-200",
                  isExpanded && "rotate-90"
                )} />
              )}
            </>
          )}
        </Button>

        {hasChildren && isExpanded && (!isCollapsed || level > 0) && (
          <div className="space-y-1">
            {item.children?.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn(
      "flex flex-col h-full bg-white dark:bg-neutral-900 border-r border-neutral-200 dark:border-neutral-800 transition-all duration-300",
      isCollapsed ? "w-20" : "w-72",
      className
    )}>
      {/* Header */}
      <div className="p-6 border-b border-neutral-200 dark:border-neutral-800">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div>
              <h2 className="text-xl font-bold text-black dark:text-white">
                HauteVault
              </h2>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Seller Dashboard
              </p>
            </div>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2"
          >
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4" />
            ) : (
              <ChevronLeft className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Navigation Items */}
      <div className="flex-1 p-4 space-y-2 overflow-y-auto">
        {NAVIGATION_ITEMS.map(item => renderNavigationItem(item))}
      </div>

      {/* Bottom Section */}
      <div className="p-4 border-t border-neutral-200 dark:border-neutral-800 space-y-2">
        {/* Notifications */}
        {!isCollapsed && (
          <div className="mb-4">
            <Button
              variant="outline"
              className="w-full justify-start h-10"
            >
              <Bell className="w-4 h-4 mr-3" />
              <span className="flex-1 text-left">Notifications</span>
              <Badge variant="destructive" className="h-5 min-w-[20px] text-xs">
                2
              </Badge>
            </Button>
          </div>
        )}

        {/* Bottom Navigation Items */}
        {BOTTOM_ITEMS.map(item => renderNavigationItem(item))}

        {/* User Profile */}
        <div className={cn(
          "pt-4 border-t border-neutral-200 dark:border-neutral-800",
          isCollapsed && "text-center"
        )}>
          {isCollapsed ? (
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-center p-2"
            >
              <User className="w-6 h-6" />
            </Button>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center space-x-3 p-2">
                <div className="w-8 h-8 bg-neutral-200 dark:bg-neutral-700 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-black dark:text-white">
                    John Seller
                  </p>
                  <p className="text-xs text-neutral-500">
                    Premium Seller
                  </p>
                </div>
              </div>
              
              <Button
                variant="ghost"
                className="w-full justify-start h-10 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                <LogOut className="w-4 h-4 mr-3" />
                Sign Out
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
