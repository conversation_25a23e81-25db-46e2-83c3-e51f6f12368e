"use client";

import { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@repo/ui/components/chart";
import { TrendingUp, Package } from "lucide-react";

interface SalesChartProps {
  className?: string;
}

export function SalesChart({ className }: SalesChartProps) {
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d" | "1y">("30d");
  const [activeChart, setActiveChart] = useState<"owned" | "consigned">("owned");

  // Fetch sales data
  const salesData = useQuery(api.sellerQueries.getSellerSalesChart, {
    timeRange,
  });

  // Chart configuration
  const chartConfig = {
    owned: {
      label: "Owned",
      color: "hsl(var(--primary))",
    },
    consigned: {
      label: "Consigned", 
      color: "hsl(var(--accent))",
    },
  } satisfies ChartConfig;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const chartTotals = useMemo(() => {
    if (!salesData?.summary) {
      return { owned: 0, consigned: 0 };
    }
    
    return {
      owned: salesData.summary.totalOwnedSold,
      consigned: salesData.summary.totalConsignedSold,
    };
  }, [salesData?.summary]);

  if (!salesData) {
    return (
      <Card className={`rounded-xl border border-border bg-card py-0 shadow-sm ${className}`}>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Sales Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground font-light">Loading sales data...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`rounded-xl py-0 border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300 ${className}`}>
      <CardHeader className="flex flex-col items-stretch border-b !p-0 sm:flex-row">
        <div className="flex flex-1 flex-col justify-center gap-1 px-6 pt-4 pb-3 sm:!py-0">
          <CardTitle className="text-primary text-lg font-light flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Sales Performance
          </CardTitle>
          <div className="text-sm text-muted-foreground font-light">
            {salesData.summary.totalSold} products sold • {formatCurrency(salesData.summary.totalRevenue)} revenue
          </div>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex items-center gap-4 px-6 py-4 sm:py-6">
          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as "7d" | "30d" | "90d" | "1y")}>
            <SelectTrigger className="w-32 rounded-xl bg-primary/5 border-border font-light">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="rounded-xl border border-border bg-card shadow-xl">
              <SelectItem value="7d" className="rounded-xl font-light">Last 7 days</SelectItem>
              <SelectItem value="30d" className="rounded-xl font-light">Last 30 days</SelectItem>
              <SelectItem value="90d" className="rounded-xl font-light">Last 90 days</SelectItem>
              <SelectItem value="1y" className="rounded-xl font-light">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Chart Type Tabs */}
        <div className="flex">
          {(["owned", "consigned"] as const).map((key) => (
            <button
              key={key}
              data-active={activeChart === key}
              className="data-[active=true]:bg-muted/50 relative z-30 flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left even:border-l sm:border-t-0 sm:border-l sm:px-8 sm:py-6"
              onClick={() => setActiveChart(key)}
            >
              <span className="text-muted-foreground text-xs font-light">
                {chartConfig[key].label} Sold
              </span>
              <span className="text-lg leading-none font-light sm:text-3xl">
                {chartTotals[key].toLocaleString()}
              </span>
            </button>
          ))}
        </div>
      </CardHeader>
      
      <CardContent className="px-6 pt-4 pb-6">
        {salesData.summary.totalSold === 0 ? (
          <div className="flex items-center justify-center h-[250px]">
            <div className="text-center">
              <Package className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
              <p className="text-muted-foreground font-light">No sales yet</p>
              <p className="text-sm text-muted-foreground/70 font-light">Sales will appear here once you start selling products</p>
            </div>
          </div>
        ) : (
          <ChartContainer
            config={chartConfig}
            className="aspect-auto h-[250px] w-full"
          >
            <BarChart
              accessibilityLayer
              data={salesData.chartData}
              margin={{
                left: 12,
                right: 12,
                top: 12,
                bottom: 12,
              }}
            >
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="period"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                minTickGap={16}
                tick={{
                  fill: "hsl(var(--muted-foreground))",
                  fontSize: 12,
                  fontWeight: "light"
                }}
              />
              <YAxis 
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                allowDecimals={false}
                domain={[0, 'dataMax']}
                tick={{
                  fill: "hsl(var(--muted-foreground))",
                  fontSize: 12,
                  fontWeight: "light"
                }}
              />
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent hideLabel />}
              />
              <Bar 
                dataKey={activeChart} 
                fill={`var(--color-${activeChart})`}
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ChartContainer>
        )}
        
        {/* Summary Stats */}
        {salesData.summary.totalSold > 0 && (
          <div className="mt-6 pt-4 border-t border-border">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground font-light">Average Sale Price</p>
                <p className="font-light text-foreground">{formatCurrency(salesData.summary.averageSalePrice)}</p>
              </div>
              <div>
                <p className="text-muted-foreground font-light">Total Revenue</p>
                <p className="font-light text-foreground">{formatCurrency(salesData.summary.totalRevenue)}</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
