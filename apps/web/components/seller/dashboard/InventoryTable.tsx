"use client";

import { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Checkbox } from "@repo/ui/components/checkbox";
import { SellOfflineModal } from "../offline/SellOfflineModal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Copy,
  ShoppingCart,
  Archive,
  Star,
  TrendingUp,
  Calendar
} from "lucide-react";

interface InventoryItem {
  id: string;
  title: string;
  category: string;
  brand: string;
  price: number;
  originalPrice?: number;
  status: "active" | "sold" | "draft" | "archived";
  condition: string;
  images: string[];
  _creationTime: number;
  updatedAt: number;
  views: number;
  saves: number;
  featured: boolean;
}

interface InventoryTableProps {
  items: InventoryItem[];
  onEdit: (item: InventoryItem) => void;
  onDelete: (itemId: string) => void;
  onSellOffline: (itemId: string) => void;
  onToggleFeatured: (itemId: string) => void;
  onBulkAction: (action: string, itemIds: string[]) => void;
}

const STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  sold: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  draft: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  archived: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
};

export function InventoryTable({
  items,
  onEdit,
  onDelete,
  onSellOffline,
  onToggleFeatured,
  onBulkAction
}: InventoryTableProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>("updatedAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [sellOfflineModal, setSellOfflineModal] = useState<{
    isOpen: boolean;
    product: InventoryItem | null;
  }>({ isOpen: false, product: null });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(items.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  const handleBulkAction = (action: string) => {
    if (selectedItems.length > 0) {
      onBulkAction(action, selectedItems);
      setSelectedItems([]);
    }
  };

  const handleSellOffline = (item: InventoryItem) => {
    setSellOfflineModal({ isOpen: true, product: item });
  };

  const handleSellOfflineComplete = (saleData: any) => {
    console.log("Offline sale completed:", saleData);
    onSellOffline(saleData.product.id);
    setSellOfflineModal({ isOpen: false, product: null });
  };

  const sortedItems = [...items].sort((a, b) => {
    const aValue = a[sortBy as keyof InventoryItem];
    const bValue = b[sortBy as keyof InventoryItem];
    
    if (sortOrder === "asc") {
      return aValue && bValue ? (aValue < bValue ? -1 : aValue > bValue ? 1 : 0) : 0;
    } else {
      return aValue && bValue ? (aValue > bValue ? -1 : aValue < bValue ? 1 : 0) : 0;
    }
  });

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-blue-900 dark:text-blue-300">
              {selectedItems.length} item{selectedItems.length > 1 ? 's' : ''} selected
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkAction("feature")}
            >
              <Star className="w-4 h-4 mr-2" />
              Feature
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkAction("archive")}
            >
              <Archive className="w-4 h-4 mr-2" />
              Archive
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkAction("delete")}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="border border-neutral-200 dark:border-neutral-800 rounded-xl overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-neutral-50 dark:bg-neutral-900">
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedItems.length === items.length && items.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead className="w-20">Image</TableHead>
              <TableHead>Product</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Performance</TableHead>
              <TableHead>Updated</TableHead>
              <TableHead className="w-12">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedItems.map((item) => (
              <TableRow key={item.id} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                <TableCell>
                  <Checkbox
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={(checked) => handleSelectItem(item.id, checked as boolean)}
                  />
                </TableCell>
                
                <TableCell>
                  <div className="w-16 h-16 bg-neutral-200 dark:bg-neutral-700 rounded-lg overflow-hidden">
                    {item.images[0] ? (
                      <img
                        src={item.images[0]}
                        alt={item.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <span className="text-neutral-400 text-xs">No Image</span>
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-black dark:text-white line-clamp-1">
                        {item.title}
                      </h3>
                      {item.featured && (
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      )}
                    </div>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      {item.brand} • {item.condition}
                    </p>
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant="outline" className="text-xs">
                    {item.category}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    <p className="font-semibold text-black dark:text-white">
                      {formatCurrency(item.price)}
                    </p>
                    {item.originalPrice && item.originalPrice > item.price && (
                      <p className="text-sm text-neutral-500 line-through">
                        {formatCurrency(item.originalPrice)}
                      </p>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge className={STATUS_COLORS[item.status]}>
                    {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2 text-sm">
                      <Eye className="w-4 h-4 text-neutral-400" />
                      <span className="text-neutral-600 dark:text-neutral-400">
                        {item.views}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <TrendingUp className="w-4 h-4 text-neutral-400" />
                      <span className="text-neutral-600 dark:text-neutral-400">
                        {item.saves}
                      </span>
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(item._creationTime.toString())}</span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem onClick={() => onEdit(item)}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Product
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem>
                        <Eye className="w-4 h-4 mr-2" />
                        View Product
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem>
                        <Copy className="w-4 h-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem onClick={() => onToggleFeatured(item.id)}>
                        <Star className="w-4 h-4 mr-2" />
                        {item.featured ? "Remove Feature" : "Feature Product"}
                      </DropdownMenuItem>
                      
                      <DropdownMenuSeparator />
                      
                      {item.status === "active" && (
                        <DropdownMenuItem onClick={() => handleSellOffline(item)}>
                          <ShoppingCart className="w-4 h-4 mr-2" />
                          Sell Offline
                        </DropdownMenuItem>
                      )}
                      
                      <DropdownMenuItem>
                        <Archive className="w-4 h-4 mr-2" />
                        Archive
                      </DropdownMenuItem>
                      
                      <DropdownMenuSeparator />
                      
                      <DropdownMenuItem 
                        onClick={() => onDelete(item.id)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Empty State */}
      {items.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center mx-auto mb-4">
            <Archive className="w-8 h-8 text-neutral-400" />
          </div>
          <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
            No inventory items
          </h3>
          <p className="text-neutral-500 dark:text-neutral-500 mb-6">
            Start by adding your first luxury product to the inventory
          </p>
          <Button className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200">
            Add Your First Product
          </Button>
        </div>
      )}

      {/* Sell Offline Modal */}
      {sellOfflineModal.product && (
        <SellOfflineModal
          isOpen={sellOfflineModal.isOpen}
          onClose={() => setSellOfflineModal({ isOpen: false, product: null })}
          product={{
            id: sellOfflineModal.product.id,
            title: sellOfflineModal.product.title,
            brand: sellOfflineModal.product.brand,
            price: sellOfflineModal.product.price,
            category: sellOfflineModal.product.category,
            condition: sellOfflineModal.product.condition,
            images: sellOfflineModal.product.images
          }}
          onSaleComplete={handleSellOfflineComplete}
        />
      )}
    </div>
  );
}
