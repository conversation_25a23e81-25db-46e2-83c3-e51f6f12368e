"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Calendar } from "@repo/ui/components/calendar";
import { Filter, ChevronDown, ChevronUp, CalendarIcon } from "lucide-react";
import { cn } from "@repo/ui/src/lib/utils";
import { Label } from "@repo/ui/src/components/label";
import { useFilterVisibility, type DashboardFilters } from "@/hooks/usePersistentFilters";
import { format } from "date-fns";

interface DashboardFiltersHeaderProps {
  filters: DashboardFilters;
  onFiltersChange: (filters: Partial<DashboardFilters>) => void;
  onClearFilters: () => void;
}

export function DashboardFiltersHeader({ 
  filters, 
  onFiltersChange, 
  onClearFilters 
}: DashboardFiltersHeaderProps) {
  
  // Local state for text inputs to enable debouncing
  const [localFilters, setLocalFilters] = useState({
    productTitle: filters.productTitle,
    productSku: filters.productSku,
  });

  // Persistent filter visibility state
  const { showFilters, setShowFilters } = useFilterVisibility();
  
  // Debounce ref to prevent rapid-fire calendar selections
  const selectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Date range state
  const [dateRange, setDateRange] = useState<{from?: Date; to?: Date}>(() => {
    const from = filters.soldStartDate ? new Date(filters.soldStartDate) : undefined;
    const to = filters.soldEndDate ? new Date(filters.soldEndDate) : undefined;
    return { from, to };
  });

  // Debounced update function
  const debouncedUpdate = useCallback(() => {
    const timer = setTimeout(() => {
      // Only update if the local values are different from the actual filters
      if (localFilters.productTitle !== filters.productTitle) {
        onFiltersChange({ productTitle: localFilters.productTitle });
      }
      if (localFilters.productSku !== filters.productSku) {
        onFiltersChange({ productSku: localFilters.productSku });
      }
    }, 500); // 500ms debounce delay

    return () => clearTimeout(timer);
  }, [localFilters, filters, onFiltersChange]);

  // Effect to trigger debounced updates
  useEffect(() => {
    const cleanup = debouncedUpdate();
    return cleanup;
  }, [debouncedUpdate]);

  // Update local state when external filters change (e.g., from clear filters)
  useEffect(() => {
    setLocalFilters({
      productTitle: filters.productTitle,
      productSku: filters.productSku,
    });
  }, [filters.productTitle, filters.productSku]);

  // Update date range when external filters change
  useEffect(() => {
    const from = filters.soldStartDate ? new Date(filters.soldStartDate) : undefined;
    const to = filters.soldEndDate ? new Date(filters.soldEndDate) : undefined;
    setDateRange({ from, to });
  }, [filters.soldStartDate, filters.soldEndDate]);

  const updateFilter = (key: keyof DashboardFilters, value: string) => {
    if (key === 'productTitle' || key === 'productSku') {
      // Update local state for text inputs (debounced)
      setLocalFilters(prev => ({ ...prev, [key]: value }));
    } else {
      // Update immediately for non-text inputs (selects, dates, checkboxes)
      onFiltersChange({ [key]: value });
    }
  };

  const handleDateRangeSelect = useCallback((range: { from?: Date; to?: Date } | undefined) => {
    if (!range) {
      setDateRange({ from: undefined, to: undefined });
      onFiltersChange({
        soldStartDate: "",
        soldEndDate: ""
      });
      return;
    }

    // Smart selection logic: Accept single dates and intentional ranges only
    const isSingleDateSelection = range.from && range.to && 
      range.from.getTime() === range.to.getTime();
    
    const isValidRange = range.from && range.to && range.from.getTime() !== range.to.getTime();
    
    // For single date selection, allow clearing on second click
    if (isSingleDateSelection && dateRange?.from && range.from &&
        range.from.getTime() === dateRange.from.getTime() &&
        (!dateRange.to || dateRange.from.getTime() === dateRange.to.getTime())) {
      // Clear selection on same date click
      setDateRange({ from: undefined, to: undefined });
      onFiltersChange({
        soldStartDate: "",
        soldEndDate: ""
      });
      return;
    }

    // Only process valid selections (single dates or intentional ranges)
    if (!isSingleDateSelection && !isValidRange) {
      return; // Ignore auto-generated intermediate ranges
    }

    // Normalize dates to avoid timezone issues
    const normalizedRange = {
      from: range.from ? new Date(range.from.getFullYear(), range.from.getMonth(), range.from.getDate()) : undefined,
      to: range.to ? new Date(range.to.getFullYear(), range.to.getMonth(), range.to.getDate()) : undefined,
    };
    
    setDateRange(normalizedRange);
    
    const updates: Partial<DashboardFilters> = {};
    
    if (normalizedRange.from) {
      updates.soldStartDate = format(normalizedRange.from, "yyyy-MM-dd");
    } else {
      updates.soldStartDate = "";
    }
    
    if (normalizedRange.to) {
      updates.soldEndDate = format(normalizedRange.to, "yyyy-MM-dd");
    } else {
      updates.soldEndDate = "";
    }
    
    onFiltersChange(updates);
  }, [onFiltersChange, dateRange]);

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
    if (key === 'soldEndDate') return false; // Don't count default end date as active filter
    if (key === 'ownershipType' || key === 'listingStatus' || key === 'storeLocation' || key === 'category') {
      return value !== 'all';
    }
    return value !== '';
  });

  return (
    <Card className="border-none shadow-none p-0 m-0">
      <CardHeader className={`p-0 gap-0 ${showFilters ? "mb-0" : "mb-6"}`}>
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center gap-4 flex-wrap">
            <Button 
              onClick={() => setShowFilters(!showFilters)}
              className={`rounded-xl font-light px-6 py-2 transition-all duration-300 ${
                hasActiveFilters 
                  ? 'bg-accent text-accent-foreground hover:bg-accent/90' 
                  : 'bg-primary text-primary-foreground hover:bg-primary/90'
              }`}
            >
              <Filter className="w-4 h-4 mr-2" />
              ALL FILTERS
              {hasActiveFilters && (
                <span className="ml-2 bg-primary/20 text-xs px-2 py-1 rounded-lg">
                  {Object.entries(filters).filter(([key, value]) => {
                    if (key === 'soldEndDate') return false;
                    if (key === 'ownershipType' || key === 'listingStatus' || key === 'storeLocation' || key === 'category') {
                      return value !== 'all';
                    }
                    return value !== '';
                  }).length}
                </span>
              )}
              {showFilters ? (
                <ChevronUp className="w-4 h-4 ml-2" />
              ) : (
                <ChevronDown className="w-4 h-4 ml-2" />
              )}
            </Button>
          </div>

          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <Button 
              variant="outline" 
              onClick={onClearFilters}
              className="rounded-xl font-light"
            >
              Clear Filters
            </Button>
          )}
        </div>
      </CardHeader>
      
      {/* Collapsible Filter Content */}
      {showFilters && (
        <CardContent className={`transition-all duration-300 ease-in-out ${showFilters ? "mb-6" : "mb-6 mt-6"}`}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 animate-in slide-in-from-top-2 duration-300">
          {/* Product Title */}
          <div>
            <Label className="block text-sm font-light uppercase tracking-wide mb-2">
              Product Title
            </Label>
            <Input
              placeholder="Enter Product Title"
              value={localFilters.productTitle}
              onChange={(e) => updateFilter('productTitle', e.target.value)}
              className="rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* Product SKU */}
          <div>
            <Label className="block text-sm font-light uppercase tracking-wide mb-2">
              Product SKU
            </Label>
            <Input
              placeholder="Enter Product SKU"
              value={localFilters.productSku}
              onChange={(e) => updateFilter('productSku', e.target.value)}
              className="rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* Store Location */}
          <div>
            <Label className="block text-sm font-light uppercase tracking-wide mb-2">
              Store Location
            </Label>
            <Select value={filters.storeLocation} onValueChange={(value) => updateFilter('storeLocation', value)}>
              <SelectTrigger className="rounded-xl bg-primary/5 border-border font-light transition-all duration-300 focus:ring-2 focus:ring-primary/20 w-full">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent className="rounded-xl border border-border bg-card shadow-xl">
                <SelectItem value="all" className="rounded-xl font-light">All</SelectItem>
                <SelectItem value="main" className="rounded-xl font-light">Main Store</SelectItem>
                <SelectItem value="warehouse" className="rounded-xl font-light">Warehouse</SelectItem>
                <SelectItem value="online" className="rounded-xl font-light">Online Only</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Category */}
          <div>
            <Label className="block text-sm font-light uppercase tracking-wide mb-2">
              Category
            </Label>
            <Select value={filters.category} onValueChange={(value) => updateFilter('category', value)}>
              <SelectTrigger className="rounded-xl bg-primary/5 border-border font-light transition-all duration-300 focus:ring-2 focus:ring-primary/20 w-full">
                <SelectValue placeholder="all" />
              </SelectTrigger>
              <SelectContent className="rounded-xl border border-border bg-card shadow-xl">
                <SelectItem value="all" className="rounded-xl font-light">All</SelectItem>
                <SelectItem value="clothing" className="rounded-xl font-light">Clothing</SelectItem>
                <SelectItem value="sneakers" className="rounded-xl font-light">Sneakers</SelectItem>
                <SelectItem value="handbags" className="rounded-xl font-light">Handbags</SelectItem>
                <SelectItem value="accessories" className="rounded-xl font-light">Accessories</SelectItem>
                <SelectItem value="watches" className="rounded-xl font-light">Watches</SelectItem>
                <SelectItem value="jewelry" className="rounded-xl font-light">Jewelry</SelectItem>
                <SelectItem value="sunglasses" className="rounded-xl font-light">Sunglasses</SelectItem>
                <SelectItem value="collectibles" className="rounded-xl font-light">Collectibles</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Date Range Picker */}
          <div className="md:col-span-2">
            <Label className="block text-sm font-light uppercase tracking-wide mb-2">
              Sold Date Range
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left rounded-xl bg-primary/5 border-border font-light transition-all duration-300 focus:ring-2 focus:ring-primary/20",
                    !dateRange?.from && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {(() => {
                    if (!dateRange?.from) {
                      return <span>Pick a date range</span>;
                    }
                    
                    if (dateRange.to) {
                      return (
                        <>
                          {format(dateRange.from, "LLL dd, y")} -{" "}
                          {format(dateRange.to, "LLL dd, y")}
                        </>
                      );
                    } else {
                      return (
                        <>
                          {format(dateRange.from, "LLL dd, y")} - <span className="text-muted-foreground">End date</span>
                        </>
                      );
                    }
                  })()}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="rounded-xl border border-border bg-card">
                  {/* Quick Select Presets */}
                  <div className="p-4 border-b border-border">
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const today = new Date();
                          handleDateRangeSelect({ from: today, to: today });
                        }}
                        className="text-xs font-light"
                      >
                        Today
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const today = new Date();
                          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                          handleDateRangeSelect({ from: weekAgo, to: today });
                        }}
                        className="text-xs font-light"
                      >
                        Last 7 days
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const today = new Date();
                          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                          handleDateRangeSelect({ from: monthAgo, to: today });
                        }}
                        className="text-xs font-light"
                      >
                        Last 30 days
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          handleDateRangeSelect(undefined);
                        }}
                        className="text-xs font-light"
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                  
                  {/* Calendar */}
                  <Calendar
                    mode="range"
                    defaultMonth={dateRange?.from}
                    selected={dateRange?.from ? { from: dateRange.from, to: dateRange.to } : undefined}
                    onSelect={handleDateRangeSelect}
                    numberOfMonths={2}
                    className="rounded-b-xl"
                  />
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Ownership Type */}
          <div>
            <Label className="block text-sm font-light uppercase tracking-wide mb-2">
              Ownership Type
            </Label>
            <Select value={filters.ownershipType} onValueChange={(value) => updateFilter('ownershipType', value as "all" | "owned" | "consigned")}>
              <SelectTrigger className="rounded-xl bg-primary/5 border-border font-light transition-all duration-300 focus:ring-2 focus:ring-primary/20 w-full">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent className="rounded-xl border border-border bg-card shadow-xl">
                <SelectItem value="all" className="rounded-xl font-light">All</SelectItem>
                <SelectItem value="owned" className="rounded-xl font-light">Owned</SelectItem>
                <SelectItem value="consigned" className="rounded-xl font-light">Consigned</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Listing Status */}
          <div>
            <Label className="block text-sm font-light uppercase tracking-wide mb-2">
              Listing Status
            </Label>
            <Select value={filters.listingStatus} onValueChange={(value) => updateFilter('listingStatus', value as "all" | "listed" | "unlisted")}>
              <SelectTrigger className="rounded-xl bg-primary/5 border-border font-light transition-all duration-300 focus:ring-2 focus:ring-primary/20 w-full">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent className="rounded-xl border border-border bg-card shadow-xl">
                <SelectItem value="all" className="rounded-xl font-light">All</SelectItem>
                <SelectItem value="listed" className="rounded-xl font-light">Listed</SelectItem>
                <SelectItem value="unlisted" className="rounded-xl font-light">Unlisted</SelectItem>
              </SelectContent>
            </Select>
          </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
