"use client";

import { useState, useMemo } from "react";
import { SellerNavigation } from "./SellerNavigation";
import { SellerDashboardOverview } from "./SellerDashboardOverview";
import { InventoryTable } from "./InventoryTable";

import { InvoiceManagement } from "../offline/InvoiceManagement";
import { SalesReportsDashboard } from "../reports/SalesReportsDashboard";
import { Card } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import {
  Plus,
  Package,
  TrendingUp
} from "lucide-react";

interface DashboardStats {
  totalInventoryValue: number;
  activeListings: number;
  totalSales: number;
  monthlyRevenue: number;
  pendingOrders: number;
  totalViews: number;
  savedItems: number;
  averagePrice: number;
}

interface InventoryItem {
  id: string;
  title: string;
  category: string;
  brand: string;
  price: number;
  originalPrice?: number;
  status: "active" | "sold" | "draft" | "archived";
  condition: string;
  images: string[];
  updatedAt: number;
  views: number;
  saves: number;
  featured: boolean;
  _creationTime: number;
}

interface FilterState {
  search: string;
  category: string;
  status: string;
  condition: string;
  priceMin: string;
  priceMax: string;
  dateRange: string;
  featured: boolean;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

const MOCK_STATS: DashboardStats = {
  totalInventoryValue: 125000,
  activeListings: 24,
  totalSales: 156,
  monthlyRevenue: 18500,
  pendingOrders: 3,
  totalViews: 12450,
  savedItems: 89,
  averagePrice: 5208,
};

const MOCK_RECENT_SALES = [
  {
    id: "1",
    productTitle: "Hermès Birkin 30 Black Togo",
    productImage: "/api/placeholder/400/400",
    salePrice: 12500,
    soldAt: "2024-01-15T10:30:00Z",
    buyerName: "Sarah M.",
  },
  {
    id: "2",
    productTitle: "Chanel Classic Flap Medium",
    productImage: "/api/placeholder/400/400",
    salePrice: 8900,
    soldAt: "2024-01-14T15:45:00Z",
    buyerName: "Jennifer L.",
  },
];

const MOCK_INVENTORY: InventoryItem[] = [
  {
    id: "1",
    title: "Hermès Birkin 30 Black Togo Leather",
    category: "Handbags",
    brand: "Hermès",
    price: 15000,
    originalPrice: 16000,
    status: "active",
    condition: "Excellent",
    images: ["/api/placeholder/400/400"],
    _creationTime: 1715769600000,
    updatedAt: 1715769600000,
    views: 245,
    saves: 18,
    featured: true,
  },
  {
    id: "2",
    title: "Chanel Classic Flap Medium Black Caviar",
    category: "Handbags",
    brand: "Chanel",
    price: 9500,
    status: "active",
    condition: "Like New",
    images: ["/api/placeholder/400/400"],
    _creationTime: 1715769600000,
    updatedAt: 1715769600000,
    views: 189,
    saves: 12,
    featured: false,
  },
  {
    id: "3",
    title: "Louis Vuitton Neverfull MM Monogram",
    category: "Handbags",
    brand: "Louis Vuitton",
    price: 1800,
    status: "sold",
    condition: "Good",
    images: ["/api/placeholder/400/400"],
    _creationTime: 1715769600000,
    updatedAt: 1715769600000,
    views: 156,
    saves: 8,
    featured: false,
  },
];

export function SellerDashboard() {
  const [activeSection, setActiveSection] = useState("dashboard");
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    category: "",
    status: "",
    condition: "",
    priceMin: "",
    priceMax: "",
    dateRange: "",
    featured: false,
    sortBy: "updatedAt",
    sortOrder: "desc",
  });

  // Filter inventory based on current filters
  const filteredInventory = useMemo(() => {
    return MOCK_INVENTORY.filter(item => {
      // Search filter
      if (filters.search && !item.title.toLowerCase().includes(filters.search.toLowerCase()) &&
          !item.brand.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }
      
      // Category filter
      if (filters.category && item.category !== filters.category) {
        return false;
      }
      
      // Status filter
      if (filters.status && item.status !== filters.status) {
        return false;
      }
      
      // Condition filter
      if (filters.condition && item.condition.toLowerCase().replace(" ", "-") !== filters.condition) {
        return false;
      }
      
      // Price range filter
      if (filters.priceMin && item.price < parseInt(filters.priceMin)) {
        return false;
      }
      if (filters.priceMax && item.price > parseInt(filters.priceMax)) {
        return false;
      }
      
      // Featured filter
      if (filters.featured && !item.featured) {
        return false;
      }
      
      return true;
    });
  }, [filters]);

  const categories = Array.from(new Set(MOCK_INVENTORY.map(item => item.category)));

  const handleAddProduct = () => {
    // Navigate to the new product page
    window.location.href = "/seller/products/new";
  };

  const handleEditProduct = (item: InventoryItem) => {
    console.log("Edit product:", item);
  };

  const handleDeleteProduct = (itemId: string) => {
    console.log("Delete product:", itemId);
  };

  const handleSellOffline = (itemId: string) => {
    console.log("Sell offline:", itemId);
  };

  const handleToggleFeatured = (itemId: string) => {
    console.log("Toggle featured:", itemId);
  };

  const handleBulkAction = (action: string, itemIds: string[]) => {
    console.log("Bulk action:", action, itemIds);
  };

  const renderContent = () => {
    switch (activeSection) {
      case "dashboard":
        return (
          <SellerDashboardOverview
            stats={MOCK_STATS}
            recentSales={MOCK_RECENT_SALES}
            onAddProduct={handleAddProduct}
          />
        );
        
      case "inventory-list":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-foreground">
                  Inventory Management
                </h1>
                <p className="text-lg text-muted-foreground mt-1 font-light">
                  Manage your luxury product inventory
                </p>
              </div>
              
              <Button 
                onClick={handleAddProduct}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-light rounded-xl px-6 py-2 transition-all duration-300"
              >
                <Plus className="w-4 h-4 mr-2" />
                ADD PRODUCT
              </Button>
            </div>

            {/* Filters would go here - using enhanced inventory management instead */}

            <InventoryTable
              items={filteredInventory}
              onEdit={handleEditProduct}
              onDelete={handleDeleteProduct}
              onSellOffline={handleSellOffline}
              onToggleFeatured={handleToggleFeatured}
              onBulkAction={handleBulkAction}
            />
          </div>
        );
        
      case "add-product":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Add New Product
              </h1>
              <p className="text-lg text-muted-foreground mt-1 font-light">
                List a new luxury item in your inventory
              </p>
            </div>
            
            <Card className="p-8 border-border">
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-primary/5 rounded-xl flex items-center justify-center mx-auto mb-4 border border-border">
                  <Package className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-light text-primary mb-2">
                  Add Product Form
                </h3>
                <p className="text-muted-foreground font-light">
                  Product creation form will be implemented here
                </p>
              </div>
            </Card>
          </div>
        );
        
      case "sales-history":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Sales History
              </h1>
              <p className="text-lg text-muted-foreground mt-1 font-light">
                Track your sales performance and revenue
              </p>
            </div>
            
            <Card className="p-8 border-border">
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-primary/5 rounded-xl flex items-center justify-center mx-auto mb-4 border border-border">
                  <TrendingUp className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-light text-primary mb-2">
                  Sales History
                </h3>
                <p className="text-muted-foreground font-light">
                  Sales history component will be implemented here
                </p>
              </div>
            </Card>
          </div>
        );
        
      case "invoicing":
      case "invoices":
        return (
          <InvoiceManagement />
        );
        
      case "reports":
      case "analytics":
      case "performance":
        return (
          <SalesReportsDashboard />
        );
        
      default:
        return (
          <SellerDashboardOverview
            stats={MOCK_STATS}
            recentSales={MOCK_RECENT_SALES}
            onAddProduct={handleAddProduct}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-background flex">
      {/* Navigation Sidebar */}
      <SellerNavigation
        activeSection={activeSection}
        onSectionChange={setActiveSection}
        pendingOrders={MOCK_STATS.pendingOrders}
      />
      
      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto">
          <div className="p-8">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
}
