"use client";

import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { 
  DollarSign, 
  Package, 
  TrendingUp, 
  ShoppingBag,
  Eye,
  Heart,
  Clock,
  ArrowUpRight,
  Plus
} from "lucide-react";

interface DashboardStats {
  totalInventoryValue: number;
  activeListings: number;
  totalSales: number;
  monthlyRevenue: number;
  pendingOrders: number;
  totalViews: number;
  savedItems: number;
  averagePrice: number;
}

interface RecentSale {
  id: string;
  productTitle: string;
  productImage: string;
  salePrice: number;
  soldAt: string;
  buyerName: string;
}

interface SellerDashboardOverviewProps {
  stats: DashboardStats;
  recentSales: RecentSale[];
  onAddProduct: () => void;
}

export function SellerDashboardOverview({ 
  stats, 
  recentSales, 
  onAddProduct 
}: SellerDashboardOverviewProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-black dark:text-white">
            Seller Dashboard
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-1">
            Manage your luxury inventory and track performance
          </p>
        </div>
        
        <Button 
          onClick={onAddProduct}
          className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 h-12 px-6 rounded-xl"
        >
          <Plus className="w-5 h-5 mr-2" />
          Add Product
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Inventory Value */}
        <Card className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-700 dark:text-green-400">
                Total Inventory Value
              </p>
              <p className="text-3xl font-bold text-green-900 dark:text-green-300 mt-2">
                {formatCurrency(stats.totalInventoryValue)}
              </p>
              <p className="text-sm text-green-600 dark:text-green-500 mt-1">
                {stats.activeListings} active items
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </Card>

        {/* Active Listings */}
        <Card className="p-6 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700 dark:text-blue-400">
                Active Listings
              </p>
              <p className="text-3xl font-bold text-blue-900 dark:text-blue-300 mt-2">
                {stats.activeListings}
              </p>
              <p className="text-sm text-blue-600 dark:text-blue-500 mt-1">
                Avg: {formatCurrency(stats.averagePrice)}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
              <Package className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </Card>

        {/* Monthly Revenue */}
        <Card className="p-6 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 border-purple-200 dark:border-purple-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700 dark:text-purple-400">
                Monthly Revenue
              </p>
              <p className="text-3xl font-bold text-purple-900 dark:text-purple-300 mt-2">
                {formatCurrency(stats.monthlyRevenue)}
              </p>
              <p className="text-sm text-purple-600 dark:text-purple-500 mt-1">
                {stats.totalSales} sales this month
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </Card>

        {/* Pending Orders */}
        <Card className="p-6 bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 border-orange-200 dark:border-orange-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-700 dark:text-orange-400">
                Pending Orders
              </p>
              <p className="text-3xl font-bold text-orange-900 dark:text-orange-300 mt-2">
                {stats.pendingOrders}
              </p>
              <p className="text-sm text-orange-600 dark:text-orange-500 mt-1">
                Require attention
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </Card>
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Total Views
              </p>
              <p className="text-2xl font-bold text-black dark:text-white mt-1">
                {stats.totalViews.toLocaleString()}
              </p>
            </div>
            <Eye className="w-8 h-8 text-neutral-400" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Items Saved
              </p>
              <p className="text-2xl font-bold text-black dark:text-white mt-1">
                {stats.savedItems.toLocaleString()}
              </p>
            </div>
            <Heart className="w-8 h-8 text-neutral-400" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Total Sales
              </p>
              <p className="text-2xl font-bold text-black dark:text-white mt-1">
                {stats.totalSales}
              </p>
            </div>
            <ShoppingBag className="w-8 h-8 text-neutral-400" />
          </div>
        </Card>
      </div>

      {/* Recent Sales */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-black dark:text-white">
            Recent Sales
          </h2>
          <Button variant="outline" className="text-sm">
            View All
            <ArrowUpRight className="w-4 h-4 ml-2" />
          </Button>
        </div>

        {recentSales.length > 0 ? (
          <div className="space-y-4">
            {recentSales.slice(0, 5).map((sale) => (
              <div key={sale.id} className="flex items-center space-x-4 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-xl">
                <div className="w-16 h-16 bg-neutral-200 dark:bg-neutral-700 rounded-lg overflow-hidden">
                  <img
                    src={sale.productImage}
                    alt={sale.productTitle}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                <div className="flex-1">
                  <h3 className="font-semibold text-black dark:text-white">
                    {sale.productTitle}
                  </h3>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Sold to {sale.buyerName}
                  </p>
                </div>
                
                <div className="text-right">
                  <p className="text-lg font-bold text-green-600 dark:text-green-400">
                    {formatCurrency(sale.salePrice)}
                  </p>
                  <p className="text-sm text-neutral-500">
                    {formatDate(sale.soldAt)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <ShoppingBag className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
              No sales yet
            </h3>
            <p className="text-neutral-500 dark:text-neutral-500">
              Your recent sales will appear here once you start selling
            </p>
          </div>
        )}
      </Card>

      {/* Quick Actions */}
      <Card className="p-6">
        <h2 className="text-xl font-bold text-black dark:text-white mb-6">
          Quick Actions
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button 
            variant="outline" 
            className="h-16 flex-col space-y-2"
            onClick={onAddProduct}
          >
            <Plus className="w-6 h-6" />
            <span>Add New Product</span>
          </Button>
          
          <Button variant="outline" className="h-16 flex-col space-y-2">
            <Package className="w-6 h-6" />
            <span>Manage Inventory</span>
          </Button>
          
          <Button variant="outline" className="h-16 flex-col space-y-2">
            <TrendingUp className="w-6 h-6" />
            <span>View Reports</span>
          </Button>
        </div>
      </Card>
    </div>
  );
}
