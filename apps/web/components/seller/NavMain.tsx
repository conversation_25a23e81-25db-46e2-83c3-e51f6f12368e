"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import Link from "next/link"
import { usePathname, useSearchParams } from "next/navigation"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@repo/ui/components/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@repo/ui/components/sidebar"
import { MessagesBadge } from "./MessagesBadge"
import { PendingOffersBadge } from "./PendingOffersBadge"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Seller Tools</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const isActive = pathname === item.url || pathname.startsWith(item.url + '/')
          
          return (
            <Collapsible key={item.title} asChild defaultOpen={isActive}>
              <SidebarMenuItem>
                <SidebarMenuButton asChild tooltip={item.title} isActive={isActive}>
                  <Link href={item.url}>
                    <item.icon />
                    <span>{item.title}</span>
                    {item.title === "Messages" && <MessagesBadge />}
                    {item.title === "Offers" && <PendingOffersBadge variant="main" />}
                  </Link>
                </SidebarMenuButton>
                {item.items?.length ? (
                  <>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuAction className="data-[state=open]:rotate-90">
                        <ChevronRight />
                        <span className="sr-only">Toggle</span>
                      </SidebarMenuAction>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {item.items?.map((subItem) => {
                          // Check if this is a query parameter URL
                          const isQueryParamUrl = subItem.url.includes('?')
                          let isSubActive = false
                          
                          if (isQueryParamUrl) {
                            const [baseUrl, queryString] = subItem.url.split('?')
                            const urlParams = new URLSearchParams(queryString)
                            
                            // Check if base path matches and if there are specific query params
                            if (urlParams.has('status')) {
                              // For status filters, check if the current status matches
                              const statusParam = urlParams.get('status')
                              const currentStatus = searchParams.get('status')
                              isSubActive = pathname === baseUrl && statusParam === currentStatus
                            } else {
                              // For other query params, do exact match
                              isSubActive = pathname === baseUrl && urlParams.toString() === searchParams.toString()
                            }
                          } else {
                            // For non-query URLs that represent "all items" pages, check if we're on the base path with no status filter
                            if ((subItem.url.endsWith('/list') || subItem.url === '/seller/products') && pathname === subItem.url) {
                              // "All" pages are active only when there are no status filters
                              isSubActive = !searchParams.has('status')
                            } else if (subItem.url.includes('?') === false && pathname === subItem.url) {
                              // For URLs without query params, check if they're base paths that could have status filters
                              // This covers cases like /seller/products, /seller/suppliers, /seller/offers
                              const basePath = subItem.url
                              const hasStatusFilter = searchParams.has('status')
                              isSubActive = !hasStatusFilter
                            } else {
                              // For other non-query URLs, check exact path match
                              isSubActive = pathname === subItem.url
                            }
                          }
                          
                          return (
                            <SidebarMenuSubItem key={subItem.title}>
                              <SidebarMenuSubButton asChild isActive={isSubActive}>
                                <Link href={subItem.url}>
                                  <span>{subItem.title}</span>
                                  {item.title === "Offers" && subItem.title === "Pending" && <PendingOffersBadge variant="submenu" />}
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          )
                        })}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </>
                ) : null}
              </SidebarMenuItem>
            </Collapsible>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
