"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { ImageUpload } from "./ImageUpload";
import { ProductPreview } from "./ProductPreview";
import { RichTextEditor } from "./RichTextEditor";
import { Save, Eye, Send, Clock } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useBetterAuth";

const productSchema = z.object({
  // Customer-facing fields
  title: z.string().min(1, "Title is required").max(100, "Title too long"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  price: z.number().min(0.01, "Price must be greater than 0"),
  categoryId: z.string().min(1, "Category is required"),
  brand: z.string().min(1, "Brand is required"),
  condition: z.enum(["new", "like_new", "good", "fair"]),
  images: z.array(z.string()).min(1, "At least one image is required"),
  estimatedDeliveryDays: z.number().optional(),
  
  // Internal fields
  source: z.string().optional(),
  costPaid: z.number().optional(),
  paymentMethod: z.string().optional(),
  purchaseDate: z.date().optional(),
  internalNotes: z.string().optional(),
  
  // Additional fields
  originalPrice: z.number().optional(),
  sku: z.string().optional(),
  size: z.string().optional(),
  color: z.string().optional(),
  material: z.string().optional(),
  year: z.number().optional(),
  tags: z.array(z.string()),
  weight: z.number().optional(),
  shippingCost: z.number().optional(),
});

type ProductFormData = z.infer<typeof productSchema>;

interface ProductFormProps {
  productId?: string;
}

export function ProductForm({ productId }: ProductFormProps) {
  const router = useRouter();
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  
  const { user } = useAuth();
  const sellerId = user?._id;

  const createProduct = useMutation(api.productManagement.createProduct);
  const updateProduct = useMutation(api.productManagement.updateProduct);
  const publishProduct = useMutation(api.productManagement.publishProduct);
  
  const existingProduct = useQuery(
    api.productQueries.getProductDetails,
    productId ? { productId: productId as any } : "skip"
  );

  const categories = useQuery(api.categories.getCategories);

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      title: "",
      description: "",
      price: 0,
      categoryId: "",
      brand: "",
      condition: "good",
      images: [],
      tags: [],
      estimatedDeliveryDays: undefined,
      source: undefined,
      costPaid: undefined,
      paymentMethod: undefined,
      purchaseDate: undefined,
      internalNotes: undefined,
      originalPrice: undefined,
      sku: undefined,
      size: undefined,
      color: undefined,
      material: undefined,
      year: undefined,
      weight: undefined,
      shippingCost: undefined,
    },
  });

  const { watch, setValue, handleSubmit, formState: { errors, isDirty } } = form;
  const watchedValues = watch();

  // Load existing product data
  useEffect(() => {
    if (existingProduct) {
      const data = existingProduct;
      form.reset({
        title: data.title || "",
        description: data.description || "",
        price: data.price || 0,
        categoryId: typeof data.category === 'string' ? data.category : (data.category as any)?._id || "",
        brand: data.brand || "",
        condition: (data.condition === "excellent" || data.condition === "very_good") ? "good" : (data.condition as "new" | "like_new" | "good" | "fair") || "good",
        images: Array.isArray(data.images) ? data.images : [],
        estimatedDeliveryDays: undefined, // Field doesn't exist in current schema
        source: undefined, // Field doesn't exist in current schema  
        costPaid: undefined, // Field doesn't exist in current schema
        paymentMethod: undefined, // Field doesn't exist in current schema
        purchaseDate: undefined, // Field doesn't exist in current schema
        internalNotes: undefined, // Field doesn't exist in current schema
        originalPrice: data.originalPrice,
        sku: undefined, // Field doesn't exist in current schema
        size: data.size,
        color: data.color,
        material: data.material,
        year: undefined, // Field doesn't exist in current schema (was yearPurchased)
        tags: Array.isArray(data.tags) ? data.tags : [],
        weight: undefined, // Field doesn't exist in current schema (was shippingWeight)
        shippingCost: undefined, // Field doesn't exist in current schema
      });
    }
  }, [existingProduct, form]);

  // Auto-save functionality
  useEffect(() => {
    if (!isDirty) return;

    const timeoutId = setTimeout(async () => {
      await handleAutoSave();
    }, 2000); // Auto-save after 2 seconds of inactivity

    return () => clearTimeout(timeoutId);
  }, [watchedValues, isDirty]);

  const handleAutoSave = async () => {
    if (!isDirty || !sellerId) return;
    
    setIsSaving(true);
    try {
      const data = form.getValues();
      
      if (productId) {
        // Update existing product
        await updateProduct({
          productId: productId as any,
          title: data.title,
          description: data.description,
          price: data.price,
          categoryId: data.categoryId as any,
          brand: data.brand,
          condition: data.condition as "new" | "like_new" | "good" | "fair",
          tags: data.tags,
          originalPrice: data.originalPrice,
          size: data.size,
          color: data.color,
          material: data.material,
          yearPurchased: data.year,
          privateNotes: data.internalNotes,
          shippingWeight: data.weight,
        });
      } else {
        // Create new product - this would need to be handled differently
        // as we need required fields like categoryId and isAuthentic
        console.log("Auto-save skipped for new products");
        return;
      }
      
      setLastSaved(new Date());
      toast.success("Draft saved automatically");
    } catch (error) {
      console.error("Auto-save failed:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const onSaveDraft = async (data: ProductFormData) => {
    if (!sellerId) {
      toast.error("User not authenticated");
      return;
    }

    setIsSaving(true);
    try {
      if (productId) {
        // Update existing product
        await updateProduct({
          productId: productId as any,
          title: data.title,
          description: data.description,
          price: data.price,
          categoryId: data.categoryId as any,
          brand: data.brand,
          condition: data.condition as "new" | "like_new" | "good" | "fair",
          tags: data.tags,
          originalPrice: data.originalPrice,
          size: data.size,
          color: data.color,
          material: data.material,
          yearPurchased: data.year,
          privateNotes: data.internalNotes,
          shippingWeight: data.weight,
        });
      } else {
        // Create new product
        const result = await createProduct({
          title: data.title,
          description: data.description,
          price: data.price,
          categoryId: data.categoryId as any,
          brand: data.brand || "",
          condition: data.condition as "new" | "like_new" | "good" | "fair",
          images: data.images as any[],
          tags: data.tags,
          originalPrice: data.originalPrice,
          size: data.size,
          color: data.color,
          material: data.material,
          yearPurchased: data.year,
          shippingWeight: data.weight,
          isAuthentic: true, // Default to true, could be made configurable
          privateNotes: data.internalNotes,
        });
        
        // If successful, navigate to edit page with the new product ID
        if (result?.productId) {
          router.push(`/seller/products/${result.productId}/edit`);
          return;
        }
      }
      
      setLastSaved(new Date());
      toast.success("Draft saved successfully");
    } catch (error) {
      toast.error("Failed to save draft");
      console.error("Save draft error:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const onPublish = async (data: ProductFormData) => {
    if (!productId) {
      toast.error("Please save as draft first");
      return;
    }

    if (!sellerId) {
      toast.error("User not authenticated");
      return;
    }

    try {
      await publishProduct({
        productId: productId as any,
      });
      
      toast.success("Product published successfully");
      router.push("/seller/products");
    } catch (error) {
      toast.error("Failed to publish product");
      console.error("Publish error:", error);
    }
  };

  if (!categories) {
    return <div>Loading...</div>;
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Form */}
      <div className="lg:col-span-2">
        <form className="space-y-6">
          {/* Auto-save indicator */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isSaving && (
                <div className="flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                  <Clock className="h-4 w-4 animate-spin" />
                  Saving...
                </div>
              )}
              {lastSaved && !isSaving && (
                <div className="text-sm text-neutral-600 dark:text-neutral-400">
                  Last saved: {lastSaved.toLocaleTimeString()}
                </div>
              )}
            </div>
            
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsPreviewMode(!isPreviewMode)}
              >
                <Eye className="h-4 w-4 mr-2" />
                {isPreviewMode ? "Edit" : "Preview"}
              </Button>
            </div>
          </div>

          {isPreviewMode ? (
            <ProductPreview data={watchedValues} />
          ) : (
            <Tabs defaultValue="public" className="space-y-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="public">Customer Information</TabsTrigger>
                <TabsTrigger value="internal">Internal Information</TabsTrigger>
              </TabsList>

              <TabsContent value="public" className="space-y-6">
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="title">Title *</Label>
                      <Input
                        id="title"
                        {...form.register("title")}
                        placeholder="e.g., Chanel Classic Flap Bag"
                      />
                      {errors.title && (
                        <p className="text-sm text-red-500 mt-1">{errors.title.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="description">Description *</Label>
                      <RichTextEditor
                        value={watchedValues.description}
                        onChange={(value) => setValue("description", value)}
                        placeholder="Describe your item in detail..."
                      />
                      {errors.description && (
                        <p className="text-sm text-red-500 mt-1">{errors.description.message}</p>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="price">Price *</Label>
                        <Input
                          id="price"
                          type="number"
                          step="0.01"
                          {...form.register("price", { valueAsNumber: true })}
                          placeholder="0.00"
                        />
                        {errors.price && (
                          <p className="text-sm text-red-500 mt-1">{errors.price.message}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="originalPrice">Original Price</Label>
                        <Input
                          id="originalPrice"
                          type="number"
                          step="0.01"
                          {...form.register("originalPrice", { valueAsNumber: true })}
                          placeholder="0.00"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="category">Category *</Label>
                        <Select
                          value={watchedValues.categoryId}
                          onValueChange={(value) => setValue("categoryId", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category._id} value={category._id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.categoryId && (
                          <p className="text-sm text-red-500 mt-1">{errors.categoryId.message}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="brand">Brand *</Label>
                        <Input
                          id="brand"
                          {...form.register("brand")}
                          placeholder="e.g., Chanel"
                        />
                        {errors.brand && (
                          <p className="text-sm text-red-500 mt-1">{errors.brand.message}</p>
                        )}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="condition">Condition *</Label>
                      <Select
                        value={watchedValues.condition}
                        onValueChange={(value) => setValue("condition", value as any)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="new">New</SelectItem>
                          <SelectItem value="like_new">Like New</SelectItem>
                          <SelectItem value="good">Good</SelectItem>
                          <SelectItem value="fair">Fair</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Images */}
                <Card>
                  <CardHeader>
                    <CardTitle>Images *</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ImageUpload
                      images={watchedValues.images}
                      onChange={(images) => setValue("images", images)}
                    />
                    {errors.images && (
                      <p className="text-sm text-red-500 mt-2">{errors.images.message}</p>
                    )}
                  </CardContent>
                </Card>

                {/* Additional Details */}
                <Card>
                  <CardHeader>
                    <CardTitle>Additional Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="size">Size</Label>
                        <Input
                          id="size"
                          {...form.register("size")}
                          placeholder="e.g., Medium"
                        />
                      </div>

                      <div>
                        <Label htmlFor="color">Color</Label>
                        <Input
                          id="color"
                          {...form.register("color")}
                          placeholder="e.g., Black"
                        />
                      </div>

                      <div>
                        <Label htmlFor="material">Material</Label>
                        <Input
                          id="material"
                          {...form.register("material")}
                          placeholder="e.g., Leather"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="year">Year</Label>
                        <Input
                          id="year"
                          type="number"
                          {...form.register("year", { valueAsNumber: true })}
                          placeholder="2023"
                        />
                      </div>

                      <div>
                        <Label htmlFor="estimatedDeliveryDays">Estimated Delivery (days)</Label>
                        <Input
                          id="estimatedDeliveryDays"
                          type="number"
                          {...form.register("estimatedDeliveryDays", { valueAsNumber: true })}
                          placeholder="3-5"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="internal" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Internal Information</CardTitle>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      This information is only visible to you and will not be shown to customers.
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="source">Source / Where Purchased</Label>
                      <Input
                        id="source"
                        {...form.register("source")}
                        placeholder="e.g., Boutique, Online, Consignment"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="costPaid">Cost Paid</Label>
                        <Input
                          id="costPaid"
                          type="number"
                          step="0.01"
                          {...form.register("costPaid", { valueAsNumber: true })}
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <Label htmlFor="paymentMethod">Payment Method</Label>
                        <Input
                          id="paymentMethod"
                          {...form.register("paymentMethod")}
                          placeholder="e.g., Credit Card, Cash"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="purchaseDate">Purchase Date</Label>
                      <Input
                        id="purchaseDate"
                        type="date"
                        {...form.register("purchaseDate", { valueAsDate: true })}
                      />
                    </div>

                    <div>
                      <Label htmlFor="internalNotes">Internal Notes</Label>
                      <Textarea
                        id="internalNotes"
                        {...form.register("internalNotes")}
                        placeholder="Any additional notes for your reference..."
                        rows={4}
                      />
                    </div>

                    <div>
                      <Label htmlFor="sku">SKU</Label>
                      <Input
                        id="sku"
                        {...form.register("sku")}
                        placeholder="Internal SKU or reference number"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </form>
      </div>

      {/* Actions Sidebar */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleSubmit(onSaveDraft)}
              variant="outline"
              className="w-full"
              disabled={isSaving}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>

            <Button
              onClick={handleSubmit(onPublish)}
              className="w-full"
              disabled={!productId || isSaving}
            >
              <Send className="h-4 w-4 mr-2" />
              Publish Product
            </Button>

            {!productId && (
              <p className="text-xs text-neutral-600 dark:text-neutral-400">
                Save as draft first before publishing
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Publishing Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Status:</span>
                <Badge variant={existingProduct?.status === "active" ? "default" : "secondary"}>
                  {existingProduct?.status || "Draft"}
                </Badge>
              </div>
              
              {existingProduct?.publishedAt && (
                <div className="flex items-center justify-between">
                  <span className="text-sm">Published:</span>
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">
                    {new Date(existingProduct.publishedAt).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}