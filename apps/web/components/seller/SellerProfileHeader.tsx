import React from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { User, Star, ShoppingBag, CheckCircle, ChevronLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface SellerProfileHeaderProps {
  sellerId: string;
  pathname: string;
}

export function SellerProfileHeader({ sellerId, pathname }: SellerProfileHeaderProps) {
  // Fetch seller profile
  const sellerProfile = useQuery(api.sellerQueries.getPublicSellerProfile, { 
    sellerId: sellerId as Id<"users"> 
  });

  if (!sellerProfile) {
    return null;
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
    });
  };

  const getVerificationBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle className="w-3 h-3" />
            Verified Seller
          </Badge>
        );
      case "pending":
        return <Badge variant="secondary">Pending Verification</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="outline">Unverified</Badge>;
    }
  };

  return (
    <div className="bg-gradient-to-br from-primary/5 to-accent/5 border-b border-border">
      <div className="container mx-auto px-6 py-12">
        <div className="flex flex-col md:flex-row gap-8 items-start">
          {/* Profile Image */}
          <div className="flex-shrink-0">
            <div className="w-24 h-24 md:w-32 md:h-32 rounded-full bg-muted border-4 border-background shadow-lg overflow-hidden">
              {sellerProfile.profileImage ? (
                <Image
                  src={sellerProfile.profileImage}
                  alt={sellerProfile.name}
                  width={128}
                  height={128}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <User className="w-12 h-12 md:w-16 md:h-16 text-muted-foreground" />
                </div>
              )}
            </div>
          </div>

          {/* Profile Info */}
          <div className="flex-1 space-y-4">
            <div>
              <h1 className="text-3xl md:text-4xl font-light text-foreground mb-2">
                {sellerProfile.businessName || sellerProfile.name}
              </h1>
              {sellerProfile.businessName && (
                <p className="text-lg text-muted-foreground">{sellerProfile.name}</p>
              )}
            </div>

            {/* Verification & Rating */}
            <div className="flex flex-wrap items-center gap-3">
              {getVerificationBadge(sellerProfile.verificationStatus)}
              {pathname.endsWith("/reviews") ? (
                <Link
                  href={`/seller/${sellerId}`}
                  className="flex items-center gap-1 hover:opacity-80 transition-opacity cursor-pointer"
                >
                  <ChevronLeft className="w-4 h-4 text-accent" />
                  <span className="font-medium">View items</span>
                </Link>
              ) : (
                <Link
                  href={`/seller/${sellerId}/reviews`}
                  className="flex items-center gap-1 hover:opacity-80 transition-opacity cursor-pointer"
                >
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{sellerProfile.rating ? sellerProfile.rating.toFixed(1) : "0.0"}</span>
                  <span className="text-muted-foreground">
                    ({sellerProfile.reviewCount || 0} review{(sellerProfile.reviewCount || 0) !== 1 ? 's' : ''})
                  </span>
                </Link>
              )}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                    Active Items
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {sellerProfile.stats.activeProducts}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                    Items Sold
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {sellerProfile.stats.soldProducts}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                    Business Type
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {sellerProfile.businessType}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                    Member Since
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatDate(sellerProfile.joinedDate)}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
