"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { ApplicationStatus } from "./ApplicationStatus";

const applicationSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  company: z.string().min(2, "Company name is required"),
  productSpecialty: z.string().min(2, "Product specialty is required"),
  numberOfProducts: z.string().min(1, "Number of products is required"),
  link: z.string().url("Please enter a valid URL"),
});

export const ApplicationData = {
  firstName: "",
  lastName: "",
  email: "",
  company: "",
  productSpecialty: "",
};

type ApplicationFormData = z.infer<typeof applicationSchema>;

export function SellerApplicationForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  const { user, isLoading } = useAuth();
  const submitApplication = useMutation(api.sellerApplicationsSimple.submitApplication);
  
  // Check if user already has an application
  const existingApplication = useQuery(
    api.sellerApplicationsSimple.getUserApplication,
    user?.email ? { email: user.email } : "skip"
  );

  const form = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationSchema),
  });

  // Pre-fill form with user data when available
  useEffect(() => {
    if (user) {
      // Parse full name if available
      const nameParts = user.name?.split(' ') || [];
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      // Set the form values
      form.setValue('firstName', firstName);
      // Only set lastName if it's not empty, otherwise leave it for user to fill
      if (lastName) {
        form.setValue('lastName', lastName);
      }
      form.setValue('email', user.email || '');
    }
  }, [user, form]);

  const { handleSubmit, formState: { errors } } = form;

  const onSubmit = async (data: ApplicationFormData) => {
    setIsSubmitting(true);
    
    try {
      // Transform form data to match the simpler sellerApplications schema
      const applicationData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: "******-000-0000", // Valid placeholder phone number
        dateOfBirth: "N/A", // Required field
        businessName: data.company,
        businessType: "individual" as const,
        taxId: "N/A",
        businessAddress: "N/A",
        businessCity: "N/A",
        businessState: "N/A",
        businessZip: "N/A",
        businessCountry: "N/A",
        yearsExperience: "1",
        previousPlatforms: [],
        monthlyVolume: "1-10",
        specialties: [data.productSpecialty],
        termsAccepted: true,
        privacyAccepted: true,
      };

      // Submit to Convex backend
      await submitApplication(applicationData);
      
      toast.success("Application submitted successfully!", {
        description: "We'll review your application and get back to you soon.",
      });
      
      setIsSubmitted(true);
    } catch (error: any) {
      console.error("Submission error:", error);
      toast.error("Failed to submit application", {
        description: error?.message || "Please try again or contact support.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="pt-6">
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-muted-foreground font-light">Loading...</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="pt-6">
            <div className="text-center min-h-[400px] flex items-center justify-center">
              <div>
                <h2 className="text-xl font-semibold mb-4 text-foreground">Authentication Required</h2>
                <p className="text-muted-foreground mb-6 font-light">
                  Please sign in to submit a seller application.
                </p>
                <Button asChild className="rounded-xl font-medium transition-all duration-300 hover:ring-2 hover:ring-primary/20">
                  <a href="/login">Sign In</a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If user already has an application, show status instead of form
  if (existingApplication !== undefined && existingApplication) {
    return <ApplicationStatus />;
  }

  if (isSubmitted) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-foreground">Application Submitted!</h2>
              <p className="text-muted-foreground font-light">
                Thank you for your application. We'll review it and get back to you soon.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
        <CardHeader>
          <CardTitle className="text-2xl font-light tracking-wide text-foreground">Seller Application</CardTitle>
        </CardHeader>

        <CardContent className="pt-0">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-medium text-foreground">First Name</Label>
                <Input
                  id="firstName"
                  {...form.register("firstName")}
                  placeholder="Enter your first name"
                  className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                />
                {errors.firstName && (
                  <p className="text-sm text-red-600 font-medium">{errors.firstName.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-medium text-foreground">Last Name</Label>
                <Input
                  id="lastName"
                  {...form.register("lastName")}
                  placeholder="Enter your last name"
                  className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                />
                {errors.lastName && (
                  <p className="text-sm text-red-600 font-medium">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium text-foreground">Email</Label>
              <Input
                id="email"
                type="email"
                {...form.register("email")}
                placeholder="Enter your email address"
                className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
              />
              {errors.email && (
                <p className="text-sm text-red-600 font-medium">{errors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="company" className="text-sm font-medium text-foreground">Company</Label>
              <Input
                id="company"
                {...form.register("company")}
                placeholder="Enter your company name"
                className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
              />
              {errors.company && (
                <p className="text-sm text-red-600 font-medium">{errors.company.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="productSpecialty" className="text-sm font-medium text-foreground">Product Specialty</Label>
              <Input
                id="productSpecialty"
                {...form.register("productSpecialty")}
                placeholder="e.g., Luxury watches, Designer bags, Sneakers"
                className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
              />
              {errors.productSpecialty && (
                <p className="text-sm text-red-600 font-medium">{errors.productSpecialty.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="numberOfProducts" className="text-sm font-medium text-foreground">Number of Products</Label>
              <Select onValueChange={(value) => form.setValue("numberOfProducts", value)}>
                <SelectTrigger className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-primary transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4 placeholder:text-muted">
                  <SelectValue placeholder="Select number of products" />
                </SelectTrigger>
                <SelectContent className="bg-card border-border rounded-xl shadow-xl">
                  <SelectItem value="1-10" className="rounded-lg hover:bg-muted/20">1-10 products</SelectItem>
                  <SelectItem value="11-50" className="rounded-lg hover:bg-muted/20">11-50 products</SelectItem>
                  <SelectItem value="51-100" className="rounded-lg hover:bg-muted/20">51-100 products</SelectItem>
                  <SelectItem value="100+" className="rounded-lg hover:bg-muted/20">100+ products</SelectItem>
                </SelectContent>
              </Select>
              {errors.numberOfProducts && (
                <p className="text-sm text-red-600 font-medium">{errors.numberOfProducts.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="link" className="text-sm font-medium text-foreground">Website/Store Link</Label>
              <Input
                id="link"
                type="url"
                {...form.register("link")}
                placeholder="https://your-store.com"
                className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
              />
              {errors.link && (
                <p className="text-sm text-red-600 font-medium">{errors.link.message}</p>
              )}
            </div>

            <Button 
              type="submit" 
              className="w-full h-12 rounded-xl font-medium text-sm transition-all duration-300 hover:ring-2 hover:ring-primary/20"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Submit Application"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}