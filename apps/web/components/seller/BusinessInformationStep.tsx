"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Card } from "@repo/ui/components/card";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { 
  Building, 
  FileText, 
  MapPin, 
  ArrowRight, 
  ArrowLeft, 
  CheckCircle, 
  AlertCircle 
} from "lucide-react";

interface ApplicationData {
  businessName: string;
  businessType: string;
  taxId: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
}

interface BusinessInformationStepProps {
  data: ApplicationData;
  onUpdate: (data: Partial<ApplicationData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

const BUSINESS_TYPES = [
  { value: "individual", label: "Individual/Sole Proprietorship" },
  { value: "llc", label: "Limited Liability Company (LLC)" },
  { value: "corporation", label: "Corporation" },
  { value: "partnership", label: "Partnership" },
];

const US_STATES = [
  "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware",
  "Florida", "Georgia", "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky",
  "Louisiana", "Maine", "Maryland", "Massachusetts", "Michigan", "Minnesota", "Mississippi",
  "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire", "New Jersey", "New Mexico",
  "New York", "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", "Pennsylvania",
  "Rhode Island", "South Carolina", "South Dakota", "Tennessee", "Texas", "Utah", "Vermont",
  "Virginia", "Washington", "West Virginia", "Wisconsin", "Wyoming"
];

export function BusinessInformationStep({ 
  data, 
  onUpdate, 
  onNext, 
  onPrev 
}: BusinessInformationStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = (field: string, value: string) => {
    switch (field) {
      case "businessName":
        return value.length < 2 ? "Business name is required" : "";
      case "taxId":
        const taxIdRegex = /^\d{2}-\d{7}$|^\d{9}$/;
        return !taxIdRegex.test(value.replace(/\D/g, "")) ? "Please enter a valid Tax ID (EIN)" : "";
      case "businessType":
        return !value ? "Please select a business type" : "";
      case "street":
        return value.length < 5 ? "Street address is required" : "";
      case "city":
        return value.length < 2 ? "City is required" : "";
      case "state":
        return !value ? "State is required" : "";
      case "zipCode":
        const zipRegex = /^\d{5}(-\d{4})?$/;
        return !zipRegex.test(value) ? "Please enter a valid ZIP code" : "";
      default:
        return "";
    }
  };

  const handleFieldChange = (field: string, value: string) => {
    if (field.startsWith("address.")) {
      const addressField = field.split(".")[1];
      onUpdate({
        address: {
          ...data.address,
          [addressField as string]: value,
        },
      });
    } else {
      onUpdate({ [field]: value });
    }
    
    if (touched[field]) {
      const error = validateField(field, value);
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const handleFieldBlur = (field: string, value: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    const error = validateField(field, value);
    setErrors(prev => ({ ...prev, [field]: error }));
  };

  const validateForm = () => {
    const fields = ["businessName", "taxId", "businessType", "street", "city", "state", "zipCode"];
    const newErrors: Record<string, string> = {};
    
    fields.forEach(field => {
      let value: string;
      if (field === "street" || field === "city" || field === "state" || field === "zipCode") {
        value = data.address[field as keyof typeof data.address];
      } else {
        value = data[field as keyof ApplicationData] as string;
      }
      const error = validateField(field, value);
      if (error) newErrors[field] = error;
    });
    
    setErrors(newErrors);
    setTouched(fields.reduce((acc, field) => ({ ...acc, [field]: true }), {}));
    
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  const getFieldStatus = (field: string) => {
    let value: string;
    if (field === "street" || field === "city" || field === "state" || field === "zipCode") {
      value = data.address[field as keyof typeof data.address];
    } else {
      value = data[field as keyof ApplicationData] as string;
    }
    
    const error = errors[field];
    const isTouched = touched[field];
    
    if (!isTouched) return "default";
    if (error) return "error";
    if (value) return "success";
    return "default";
  };

  return (
    <div className="p-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800 rounded-2xl flex items-center justify-center mx-auto">
          <Building className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h2 className="text-3xl font-bold text-black dark:text-white mb-2">
            Business Information
          </h2>
          <p className="text-lg text-neutral-600 dark:text-neutral-400">
            Tell us about your business for verification purposes
          </p>
        </div>
      </div>

      {/* Form Fields */}
      <div className="space-y-6 max-w-2xl mx-auto">
        {/* Business Name */}
        <div className="space-y-2">
          <Label htmlFor="businessName" className="text-base font-semibold text-black dark:text-white">
            Business Name *
          </Label>
          <div className="relative">
            <Input
              id="businessName"
              type="text"
              value={data.businessName}
              onChange={(e) => handleFieldChange("businessName", e.target.value)}
              onBlur={(e) => handleFieldBlur("businessName", e.target.value)}
              placeholder="Enter your business name"
              className={`pl-12 h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                getFieldStatus("businessName") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("businessName") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : "border-neutral-300 focus:border-black dark:focus:border-white"
              }`}
            />
            <Building className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
            {getFieldStatus("businessName") === "success" && (
              <CheckCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
            )}
            {getFieldStatus("businessName") === "error" && (
              <AlertCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
            )}
          </div>
          {errors.businessName && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.businessName}
            </p>
          )}
        </div>

        {/* Business Type and Tax ID */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label className="text-base font-semibold text-black dark:text-white">
              Business Type *
            </Label>
            <Select
              value={data.businessType}
              onValueChange={(value) => handleFieldChange("businessType", value)}
            >
              <SelectTrigger className={`h-14 text-lg rounded-xl border-2 ${
                getFieldStatus("businessType") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("businessType") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : "border-neutral-300 focus:border-black dark:focus:border-white"
              }`}>
                <SelectValue placeholder="Select business type" />
              </SelectTrigger>
              <SelectContent>
                {BUSINESS_TYPES.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.businessType && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.businessType}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="taxId" className="text-base font-semibold text-black dark:text-white">
              Tax ID (EIN) *
            </Label>
            <div className="relative">
              <Input
                id="taxId"
                type="text"
                value={data.taxId}
                onChange={(e) => handleFieldChange("taxId", e.target.value)}
                onBlur={(e) => handleFieldBlur("taxId", e.target.value)}
                placeholder="XX-XXXXXXX"
                className={`pl-12 h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                  getFieldStatus("taxId") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("taxId") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : "border-neutral-300 focus:border-black dark:focus:border-white"
                }`}
              />
              <FileText className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
              {getFieldStatus("taxId") === "success" && (
                <CheckCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
              )}
              {getFieldStatus("taxId") === "error" && (
                <AlertCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
              )}
            </div>
            {errors.taxId && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.taxId}
              </p>
            )}
          </div>
        </div>

        {/* Address Section */}
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-black dark:text-white flex items-center">
            <MapPin className="w-5 h-5 mr-2" />
            Business Address
          </h3>
          
          {/* Street Address */}
          <div className="space-y-2">
            <Label htmlFor="street" className="text-base font-semibold text-black dark:text-white">
              Street Address *
            </Label>
            <Input
              id="street"
              type="text"
              value={data.address.street}
              onChange={(e) => handleFieldChange("address.street", e.target.value)}
              onBlur={(e) => handleFieldBlur("street", e.target.value)}
              placeholder="Enter street address"
              className={`h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                getFieldStatus("street") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("street") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : "border-neutral-300 focus:border-black dark:focus:border-white"
              }`}
            />
            {errors.street && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.street}
              </p>
            )}
          </div>

          {/* City, State, ZIP */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city" className="text-base font-semibold text-black dark:text-white">
                City *
              </Label>
              <Input
                id="city"
                type="text"
                value={data.address.city}
                onChange={(e) => handleFieldChange("address.city", e.target.value)}
                onBlur={(e) => handleFieldBlur("city", e.target.value)}
                placeholder="City"
                className={`h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                  getFieldStatus("city") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("city") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : "border-neutral-300 focus:border-black dark:focus:border-white"
                }`}
              />
              {errors.city && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.city}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label className="text-base font-semibold text-black dark:text-white">
                State *
              </Label>
              <Select
                value={data.address.state}
                onValueChange={(value) => handleFieldChange("address.state", value)}
              >
                <SelectTrigger className={`h-14 text-lg rounded-xl border-2 ${
                  getFieldStatus("state") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("state") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : "border-neutral-300 focus:border-black dark:focus:border-white"
                }`}>
                  <SelectValue placeholder="State" />
                </SelectTrigger>
                <SelectContent>
                  {US_STATES.map((state) => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.state && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.state}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="zipCode" className="text-base font-semibold text-black dark:text-white">
                ZIP Code *
              </Label>
              <Input
                id="zipCode"
                type="text"
                value={data.address.zipCode}
                onChange={(e) => handleFieldChange("address.zipCode", e.target.value)}
                onBlur={(e) => handleFieldBlur("zipCode", e.target.value)}
                placeholder="12345"
                className={`h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                  getFieldStatus("zipCode") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("zipCode") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : "border-neutral-300 focus:border-black dark:focus:border-white"
                }`}
              />
              {errors.zipCode && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.zipCode}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6 max-w-2xl mx-auto">
        <Button
          onClick={onPrev}
          variant="outline"
          className="h-14 px-8 text-lg font-semibold rounded-xl border-2"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Previous
        </Button>
        
        <Button
          onClick={handleNext}
          className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 h-14 px-8 text-lg font-semibold rounded-xl"
        >
          Continue
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>
      </div>
    </div>
  );
}
