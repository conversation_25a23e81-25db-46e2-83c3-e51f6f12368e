"use client";

import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  Lock, 
  DollarSign, 
  CreditCard, 
  Calendar,
  MapPin,
  FileText,
  Shield,
  Info
} from "lucide-react";
import { ProductFormData } from "./ProductForm";

interface InternalInfoSectionProps {
  data: ProductFormData["internalInfo"];
  onUpdate: (updates: Partial<ProductFormData["internalInfo"]>) => void;
}

const PAYMENT_METHODS = [
  "Cash",
  "Credit Card",
  "Debit Card",
  "PayPal",
  "Bank Transfer",
  "Check",
  "Store Credit",
  "Trade/Exchange",
  "Other"
];

const SOURCE_SUGGESTIONS = [
  "Estate Sale",
  "Auction House",
  "Consignment Store",
  "Private Seller",
  "Retail Store",
  "Online Marketplace",
  "Trade Show",
  "Collector",
  "Gift",
  "Personal Collection"
];

export function InternalInfoSection({ 
  data, 
  onUpdate 
}: InternalInfoSectionProps) {
  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9.]/g, "");
    const number = parseFloat(numericValue);
    return isNaN(number) ? 0 : number;
  };

  const formatDate = (value: string) => {
    // Ensure date is in YYYY-MM-DD format
    return value;
  };

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-xl flex items-center justify-center">
          <Lock className="w-5 h-5 text-orange-600 dark:text-orange-400" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-black dark:text-white">
            Internal Information
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400">
            Private seller information (not visible to customers)
          </p>
        </div>
      </div>

      {/* Privacy Notice */}
      <div className="flex items-start space-x-3 p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl">
        <Shield className="w-5 h-5 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
        <div>
          <h3 className="font-semibold text-orange-900 dark:text-orange-300 mb-1">
            Private Information
          </h3>
          <p className="text-sm text-orange-800 dark:text-orange-400">
            This information is only visible to you and helps track your inventory costs and sources. 
            It will never be shown to customers or other users.
          </p>
        </div>
      </div>

      {/* Purchase Information */}
      <Card className="p-6 bg-neutral-50 dark:bg-neutral-800 border-0">
        <div className="space-y-6">
          <div className="flex items-center space-x-2 mb-4">
            <h3 className="text-lg font-semibold text-black dark:text-white">
              Purchase Details
            </h3>
            <Badge variant="outline" className="text-xs">
              Optional
            </Badge>
          </div>

          {/* Source and Cost */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="source" className="text-base font-semibold text-black dark:text-white">
                Source / Where Purchased
              </Label>
              <div className="relative">
                <Input
                  id="source"
                  value={data.source}
                  onChange={(e) => onUpdate({ source: e.target.value })}
                  placeholder="e.g., Estate sale, Auction house, Private seller"
                  className="h-12 text-base rounded-xl pl-12"
                  list="source-suggestions"
                />
                <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                <datalist id="source-suggestions">
                  {SOURCE_SUGGESTIONS.map((suggestion) => (
                    <option key={suggestion} value={suggestion} />
                  ))}
                </datalist>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="costPaid" className="text-base font-semibold text-black dark:text-white">
                Cost Paid
              </Label>
              <div className="relative">
                <Input
                  id="costPaid"
                  type="number"
                  value={data.costPaid || ""}
                  onChange={(e) => onUpdate({ costPaid: formatCurrency(e.target.value) })}
                  placeholder="0.00"
                  className="h-12 text-base rounded-xl pl-12"
                  min="0"
                  step="0.01"
                />
                <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
              </div>
              <p className="text-sm text-neutral-500 dark:text-neutral-400">
                Amount you paid for this item
              </p>
            </div>
          </div>

          {/* Payment Method and Purchase Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-base font-semibold text-black dark:text-white">
                Payment Method
              </Label>
              <Select value={data.paymentMethod} onValueChange={(value) => onUpdate({ paymentMethod: value })}>
                <SelectTrigger className="h-12 text-base rounded-xl">
                  <div className="flex items-center">
                    <CreditCard className="w-5 h-5 mr-3 text-neutral-400" />
                    <SelectValue placeholder="Select payment method" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {PAYMENT_METHODS.map((method) => (
                    <SelectItem key={method} value={method}>
                      {method}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="purchaseDate" className="text-base font-semibold text-black dark:text-white">
                Purchase Date
              </Label>
              <div className="relative">
                <Input
                  id="purchaseDate"
                  type="date"
                  value={data.purchaseDate}
                  onChange={(e) => onUpdate({ purchaseDate: formatDate(e.target.value) })}
                  className="h-12 text-base rounded-xl pl-12"
                />
                <Calendar className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Internal Notes */}
      <Card className="p-6 bg-neutral-50 dark:bg-neutral-800 border-0">
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-semibold text-black dark:text-white">
              Internal Notes
            </h3>
            <Badge variant="outline" className="text-xs">
              Optional
            </Badge>
          </div>

          <div className="space-y-2">
            <Label htmlFor="internalNotes" className="text-base font-semibold text-black dark:text-white">
              Private Notes
            </Label>
            <Textarea
              id="internalNotes"
              value={data.internalNotes}
              onChange={(e) => onUpdate({ internalNotes: e.target.value })}
              placeholder="Add any private notes about this item... condition details, authentication info, repair history, etc."
              className="min-h-[120px] text-base rounded-xl resize-none"
              maxLength={1000}
            />
            <div className="flex justify-between items-center">
              <p className="text-sm text-neutral-500 dark:text-neutral-400">
                Use this space for any private information about the item
              </p>
              <p className="text-sm text-neutral-500 dark:text-neutral-400">
                {data.internalNotes.length}/1000
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800 dark:text-blue-400">
              <p className="font-medium mb-1">Suggested notes to include:</p>
              <ul className="space-y-1 text-xs">
                <li>• Authentication details or certificates</li>
                <li>• Condition specifics not mentioned in description</li>
                <li>• Repair or restoration history</li>
                <li>• Provenance or ownership history</li>
                <li>• Storage conditions or special care instructions</li>
              </ul>
            </div>
          </div>
        </div>
      </Card>

      {/* Profit Calculation */}
      {data.costPaid > 0 && (
        <Card className="p-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <DollarSign className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-300">
              Profit Calculation
            </h3>
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-sm text-green-700 dark:text-green-400">Cost Paid</p>
              <p className="text-xl font-bold text-green-900 dark:text-green-300">
                ${data.costPaid.toFixed(2)}
              </p>
            </div>
            <div>
              <p className="text-sm text-green-700 dark:text-green-400">Selling Price</p>
              <p className="text-xl font-bold text-green-900 dark:text-green-300">
                $0.00
              </p>
            </div>
            <div>
              <p className="text-sm text-green-700 dark:text-green-400">Potential Profit</p>
              <p className="text-xl font-bold text-green-900 dark:text-green-300">
                $0.00
              </p>
            </div>
          </div>
          
          <p className="text-xs text-green-600 dark:text-green-500 mt-3 text-center">
            Profit calculation will update when you set a selling price
          </p>
        </Card>
      )}
    </div>
  );
}
