"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@repo/ui/components/button";
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered,
  Quote,
  Link,
  Type,
  AlignLeft,
  AlignCenter,
  AlignRight
} from "lucide-react";

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export function RichTextEditor({ 
  value, 
  onChange, 
  placeholder = "Start typing...",
  className = ""
}: RichTextEditorProps) {
  const [isFocused, setIsFocused] = useState(false);
  const editorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  const handleInput = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  const executeCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    handleInput();
  };

  const formatButtons = [
    {
      command: "bold",
      icon: Bold,
      title: "Bold",
      shortcut: "Ctrl+B"
    },
    {
      command: "italic",
      icon: Italic,
      title: "Italic",
      shortcut: "Ctrl+I"
    },
    {
      command: "underline",
      icon: Underline,
      title: "Underline",
      shortcut: "Ctrl+U"
    },
    {
      command: "insertUnorderedList",
      icon: List,
      title: "Bullet List"
    },
    {
      command: "insertOrderedList",
      icon: ListOrdered,
      title: "Numbered List"
    },
    {
      command: "formatBlock",
      icon: Quote,
      title: "Quote",
      value: "blockquote"
    },
    {
      command: "justifyLeft",
      icon: AlignLeft,
      title: "Align Left"
    },
    {
      command: "justifyCenter",
      icon: AlignCenter,
      title: "Align Center"
    },
    {
      command: "justifyRight",
      icon: AlignRight,
      title: "Align Right"
    }
  ];

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          executeCommand('bold');
          break;
        case 'i':
          e.preventDefault();
          executeCommand('italic');
          break;
        case 'u':
          e.preventDefault();
          executeCommand('underline');
          break;
      }
    }
  };

  const insertLink = () => {
    const url = prompt('Enter URL:');
    if (url) {
      executeCommand('createLink', url);
    }
  };

  const formatHeading = (level: string) => {
    executeCommand('formatBlock', `h${level}`);
  };

  return (
    <div className={`border-2 rounded-xl overflow-hidden transition-all duration-200 ${
      isFocused 
        ? "border-black dark:border-white" 
        : "border-neutral-300 dark:border-neutral-600"
    } ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700">
        <div className="flex items-center space-x-1">
          {/* Text Formatting */}
          <div className="flex items-center space-x-1 pr-3 border-r border-neutral-300 dark:border-neutral-600">
            {formatButtons.slice(0, 3).map((button) => {
              const Icon = button.icon;
              return (
                <Button
                  key={button.command}
                  variant="ghost"
                  size="sm"
                  onClick={() => executeCommand(button.command, button.value)}
                  title={`${button.title}${button.shortcut ? ` (${button.shortcut})` : ''}`}
                  className="h-8 w-8 p-0"
                >
                  <Icon className="w-4 h-4" />
                </Button>
              );
            })}
          </div>

          {/* Lists */}
          <div className="flex items-center space-x-1 pr-3 border-r border-neutral-300 dark:border-neutral-600">
            {formatButtons.slice(3, 5).map((button) => {
              const Icon = button.icon;
              return (
                <Button
                  key={button.command}
                  variant="ghost"
                  size="sm"
                  onClick={() => executeCommand(button.command, button.value)}
                  title={button.title}
                  className="h-8 w-8 p-0"
                >
                  <Icon className="w-4 h-4" />
                </Button>
              );
            })}
          </div>

          {/* Alignment */}
          <div className="flex items-center space-x-1 pr-3 border-r border-neutral-300 dark:border-neutral-600">
            {formatButtons.slice(6, 9).map((button) => {
              const Icon = button.icon;
              return (
                <Button
                  key={button.command}
                  variant="ghost"
                  size="sm"
                  onClick={() => executeCommand(button.command, button.value)}
                  title={button.title}
                  className="h-8 w-8 p-0"
                >
                  <Icon className="w-4 h-4" />
                </Button>
              );
            })}
          </div>

          {/* Quote and Link */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => executeCommand('formatBlock', 'blockquote')}
              title="Quote"
              className="h-8 w-8 p-0"
            >
              <Quote className="w-4 h-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={insertLink}
              title="Insert Link"
              className="h-8 w-8 p-0"
            >
              <Link className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Heading Dropdown */}
        <div className="flex items-center space-x-2">
          <select
            onChange={(e) => formatHeading(e.target.value)}
            className="text-sm bg-transparent border border-neutral-300 dark:border-neutral-600 rounded px-2 py-1"
            defaultValue=""
          >
            <option value="">Normal</option>
            <option value="1">Heading 1</option>
            <option value="2">Heading 2</option>
            <option value="3">Heading 3</option>
          </select>
        </div>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onKeyDown={handleKeyDown}
        className="min-h-[200px] p-4 text-black dark:text-white bg-white dark:bg-neutral-900 focus:outline-none"
        style={{
          lineHeight: '1.6',
        }}
        suppressContentEditableWarning={true}
        data-placeholder={placeholder}
      />

      {/* Character Count */}
      <div className="flex justify-between items-center p-3 bg-neutral-50 dark:bg-neutral-800 border-t border-neutral-200 dark:border-neutral-700 text-sm text-neutral-600 dark:text-neutral-400">
        <div className="flex items-center space-x-4">
          <span>Rich text formatting available</span>
        </div>
        <span>{value.replace(/<[^>]*>/g, '').length} characters</span>
      </div>

      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
        
        [contenteditable] h1 {
          font-size: 1.5rem;
          font-weight: bold;
          margin: 0.5rem 0;
        }
        
        [contenteditable] h2 {
          font-size: 1.25rem;
          font-weight: bold;
          margin: 0.5rem 0;
        }
        
        [contenteditable] h3 {
          font-size: 1.125rem;
          font-weight: bold;
          margin: 0.5rem 0;
        }
        
        [contenteditable] ul, [contenteditable] ol {
          margin: 0.5rem 0;
          padding-left: 1.5rem;
        }
        
        [contenteditable] li {
          margin: 0.25rem 0;
        }
        
        [contenteditable] blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 1rem;
          margin: 0.5rem 0;
          font-style: italic;
          color: #6b7280;
        }
        
        [contenteditable] a {
          color: #3b82f6;
          text-decoration: underline;
        }
        
        [contenteditable] p {
          margin: 0.5rem 0;
        }
      `}</style>
    </div>
  );
}
