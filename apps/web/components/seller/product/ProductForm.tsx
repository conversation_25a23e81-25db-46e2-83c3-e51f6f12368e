"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@repo/ui/components/tabs";
import { Badge } from "@repo/ui/components/badge";
import { 
  Save, 
  Eye, 
  Upload, 
  ArrowLeft,
  Clock,
  CheckCircle,
  AlertCircle,
  Globe,
  Lock
} from "lucide-react";
import { CustomerInfoSection } from "./CustomerInfoSection";
import { ImageUploadSection } from "./ImageUploadSection";
import { InternalInfoSection } from "./InternalInfoSection";
import { ProductPreview } from "./ProductPreview";

export interface ProductFormData {
  // Customer-facing information
  title: string;
  description: string;
  price: number;
  category: string;
  brand: string;
  condition: string;
  images: File[];
  estimatedDeliveryDays: number;
  
  // Internal information
  internalInfo: {
    source: string;
    costPaid: number;
    paymentMethod: string;
    purchaseDate: string;
    internalNotes: string;
  };
  
  // Form metadata
  status: "draft" | "active";
  id?: string;
}

interface ProductFormProps {
  initialData?: Partial<ProductFormData>;
  onSave: (data: ProductFormData) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
}

const CATEGORIES = [
  "Clothing",
  "Sneakers", 
  "Collectibles",
  "Accessories",
  "Handbags"
];

const CONDITIONS = [
  "New",
  "Like New",
  "Excellent",
  "Good",
  "Fair"
];

export function ProductForm({ 
  initialData, 
  onSave, 
  onCancel, 
  isEditing = false 
}: ProductFormProps) {
  const [activeTab, setActiveTab] = useState("customer");
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState<ProductFormData>({
    title: "",
    description: "",
    price: 0,
    category: "",
    brand: "",
    condition: "",
    images: [],
    estimatedDeliveryDays: 3,
    internalInfo: {
      source: "",
      costPaid: 0,
      paymentMethod: "",
      purchaseDate: "",
      internalNotes: "",
    },
    status: "draft",
    ...initialData,
  });

  // Auto-save functionality
  useEffect(() => {
    if (hasUnsavedChanges) {
      const autoSaveTimer = setTimeout(() => {
        handleAutoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(autoSaveTimer);
    }
  }, [formData, hasUnsavedChanges]);

  const updateFormData = (updates: Partial<ProductFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    setHasUnsavedChanges(true);
  };

  const updateInternalInfo = (updates: Partial<ProductFormData["internalInfo"]>) => {
    setFormData(prev => ({
      ...prev,
      internalInfo: { ...prev.internalInfo, ...updates }
    }));
    setHasUnsavedChanges(true);
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.title.trim()) {
      errors.title = "Product title is required";
    }

    if (!formData.price || formData.price <= 0) {
      errors.price = "Price must be greater than 0";
    }

    if (!formData.category) {
      errors.category = "Category is required";
    }

    if (!formData.condition) {
      errors.condition = "Condition is required";
    }

    if (formData.images.length === 0) {
      errors.images = "At least one image is required";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAutoSave = async () => {
    if (!hasUnsavedChanges) return;

    try {
      setIsSaving(true);
      const draftData = { ...formData, status: "draft" as const };
      await onSave(draftData);
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error("Auto-save failed:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveDraft = async () => {
    try {
      setIsSaving(true);
      const draftData = { ...formData, status: "draft" as const };
      await onSave(draftData);
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error("Save draft failed:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsSaving(true);
      const publishData = { ...formData, status: "active" as const };
      await onSave(publishData);
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error("Publish failed:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case "customer":
        return <Globe className="w-4 h-4" />;
      case "internal":
        return <Lock className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const isFormValid = Object.keys(validationErrors).length === 0 && 
                     formData.title && 
                     formData.price > 0 && 
                     formData.category && 
                     formData.condition &&
                     formData.images.length > 0;

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-950">
      <div className="container mx-auto px-6 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={onCancel}
              className="p-2"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            
            <div>
              <h1 className="text-3xl font-bold text-black dark:text-white">
                {isEditing ? "Edit Product" : "Add New Product"}
              </h1>
              <div className="flex items-center space-x-4 mt-2">
                <Badge variant={formData.status === "active" ? "default" : "secondary"}>
                  {formData.status === "active" ? "Published" : "Draft"}
                </Badge>
                
                {lastSaved && (
                  <div className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Last saved {lastSaved.toLocaleTimeString()}</span>
                  </div>
                )}
                
                {isSaving && (
                  <div className="flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400">
                    <Clock className="w-4 h-4 animate-spin" />
                    <span>Saving...</span>
                  </div>
                )}
                
                {hasUnsavedChanges && !isSaving && (
                  <div className="flex items-center space-x-2 text-sm text-orange-600 dark:text-orange-400">
                    <AlertCircle className="w-4 h-4" />
                    <span>Unsaved changes</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => setIsPreviewOpen(true)}
              className="h-12 px-6 rounded-xl"
            >
              <Eye className="w-5 h-5 mr-2" />
              Preview
            </Button>
            
            <Button
              variant="outline"
              onClick={handleSaveDraft}
              disabled={isSaving}
              className="h-12 px-6 rounded-xl"
            >
              <Save className="w-5 h-5 mr-2" />
              Save Draft
            </Button>
            
            <Button
              onClick={handlePublish}
              disabled={!isFormValid || isSaving}
              className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 h-12 px-6 rounded-xl"
            >
              <Upload className="w-5 h-5 mr-2" />
              {formData.status === "active" ? "Update" : "Publish"}
            </Button>
          </div>
        </div>

        {/* Form Content */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Form Sections */}
          <div className="xl:col-span-2">
            <Card className="p-8 bg-white dark:bg-neutral-900 rounded-3xl shadow-xl">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
                <TabsList className="grid w-full grid-cols-2 h-14 p-1 bg-neutral-100 dark:bg-neutral-800 rounded-xl">
                  <TabsTrigger 
                    value="customer" 
                    className="flex items-center space-x-2 h-12 rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-neutral-700"
                  >
                    {getTabIcon("customer")}
                    <span>Customer Info</span>
                  </TabsTrigger>
                  <TabsTrigger 
                    value="internal"
                    className="flex items-center space-x-2 h-12 rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-neutral-700"
                  >
                    {getTabIcon("internal")}
                    <span>Internal Info</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="customer" className="space-y-8">
                  <CustomerInfoSection
                    data={formData}
                    onUpdate={updateFormData}
                    categories={CATEGORIES}
                    conditions={CONDITIONS}
                    errors={validationErrors}
                  />
                  
                  <ImageUploadSection
                    images={formData.images}
                    onImagesChange={(images) => updateFormData({ images })}
                    error={validationErrors.images}
                  />
                </TabsContent>

                <TabsContent value="internal" className="space-y-8">
                  <InternalInfoSection
                    data={formData.internalInfo}
                    onUpdate={updateInternalInfo}
                  />
                </TabsContent>
              </Tabs>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="xl:col-span-1">
            <div className="sticky top-8">
              <ProductPreview
                data={formData}
                isVisible={!isPreviewOpen}
              />
            </div>
          </div>
        </div>

        {/* Full Preview Modal */}
        {isPreviewOpen && (
          <ProductPreview
            data={formData}
            isVisible={true}
            isModal={true}
            onClose={() => setIsPreviewOpen(false)}
          />
        )}
      </div>
    </div>
  );
}
