"use client";

import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  DollarSign, 
  Package, 
  Tag, 
  Truck,
  AlertCircle,
  CheckCircle,
  Info
} from "lucide-react";
import { RichTextEditor } from "./RichTextEditor";
import { ProductFormData } from "./ProductForm";

interface CustomerInfoSectionProps {
  data: ProductFormData;
  onUpdate: (updates: Partial<ProductFormData>) => void;
  categories: string[];
  conditions: string[];
  errors: Record<string, string>;
}

const DELIVERY_OPTIONS = [
  { value: 1, label: "1 day (Express)" },
  { value: 2, label: "2 days (Fast)" },
  { value: 3, label: "3 days (Standard)" },
  { value: 5, label: "5 days (Economy)" },
  { value: 7, label: "1 week" },
  { value: 14, label: "2 weeks" },
];

export function CustomerInfoSection({ 
  data, 
  onUpdate, 
  categories, 
  conditions, 
  errors 
}: CustomerInfoSectionProps) {
  const getFieldStatus = (field: string) => {
    if (errors[field]) return "error";
    
    switch (field) {
      case "title":
        return data.title ? "success" : "default";
      case "price":
        return data.price > 0 ? "success" : "default";
      case "category":
        return data.category ? "success" : "default";
      case "condition":
        return data.condition ? "success" : "default";
      default:
        return "default";
    }
  };

  const formatPrice = (value: string) => {
    // Remove non-numeric characters except decimal point
    const numericValue = value.replace(/[^0-9.]/g, "");
    const number = parseFloat(numericValue);
    return isNaN(number) ? 0 : number;
  };

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
          <Package className="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-black dark:text-white">
            Product Information
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400">
            Information visible to customers
          </p>
        </div>
      </div>

      {/* Basic Information */}
      <Card className="p-6 bg-neutral-50 dark:bg-neutral-800 border-0">
        <div className="space-y-6">
          {/* Product Title */}
          <div className="space-y-2">
            <Label htmlFor="title" className="text-base font-semibold text-black dark:text-white">
              Product Title *
            </Label>
            <div className="relative">
              <Input
                id="title"
                value={data.title}
                onChange={(e) => onUpdate({ title: e.target.value })}
                placeholder="Enter a descriptive product title"
                className={`h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                  getFieldStatus("title") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("title") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : "border-neutral-300 focus:border-black dark:focus:border-white"
                }`}
                maxLength={100}
              />
              {getFieldStatus("title") === "success" && (
                <CheckCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
              )}
              {getFieldStatus("title") === "error" && (
                <AlertCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
              )}
            </div>
            <div className="flex justify-between items-center">
              {errors.title && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.title}
                </p>
              )}
              <p className="text-sm text-neutral-500 ml-auto">
                {data.title.length}/100
              </p>
            </div>
          </div>

          {/* Category and Brand */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-base font-semibold text-black dark:text-white">
                Category *
              </Label>
              <Select value={data.category} onValueChange={(value) => onUpdate({ category: value })}>
                <SelectTrigger className={`h-14 text-lg rounded-xl border-2 ${
                  getFieldStatus("category") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("category") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : "border-neutral-300 focus:border-black dark:focus:border-white"
                }`}>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.category}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand" className="text-base font-semibold text-black dark:text-white">
                Brand
              </Label>
              <div className="relative">
                <Input
                  id="brand"
                  value={data.brand}
                  onChange={(e) => onUpdate({ brand: e.target.value })}
                  placeholder="Enter brand name"
                  className="h-14 text-lg rounded-xl border-2 pl-12"
                />
                <Tag className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
              </div>
            </div>
          </div>

          {/* Price and Condition */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="price" className="text-base font-semibold text-black dark:text-white">
                Price *
              </Label>
              <div className="relative">
                <Input
                  id="price"
                  type="number"
                  value={data.price || ""}
                  onChange={(e) => onUpdate({ price: formatPrice(e.target.value) })}
                  placeholder="0.00"
                  className={`h-14 text-lg rounded-xl border-2 pl-12 ${
                    getFieldStatus("price") === "error"
                      ? "border-red-300 focus:border-red-500"
                      : getFieldStatus("price") === "success"
                      ? "border-green-300 focus:border-green-500"
                      : "border-neutral-300 focus:border-black dark:focus:border-white"
                  }`}
                  min="0"
                  step="0.01"
                />
                <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                {getFieldStatus("price") === "success" && (
                  <CheckCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
                )}
                {getFieldStatus("price") === "error" && (
                  <AlertCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
                )}
              </div>
              {errors.price && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.price}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label className="text-base font-semibold text-black dark:text-white">
                Condition *
              </Label>
              <Select value={data.condition} onValueChange={(value) => onUpdate({ condition: value })}>
                <SelectTrigger className={`h-14 text-lg rounded-xl border-2 ${
                  getFieldStatus("condition") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("condition") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : "border-neutral-300 focus:border-black dark:focus:border-white"
                }`}>
                  <SelectValue placeholder="Select condition" />
                </SelectTrigger>
                <SelectContent>
                  {conditions.map((condition) => (
                    <SelectItem key={condition} value={condition}>
                      {condition}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.condition && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.condition}
                </p>
              )}
            </div>
          </div>

          {/* Estimated Delivery */}
          <div className="space-y-2">
            <Label className="text-base font-semibold text-black dark:text-white">
              Estimated Delivery
            </Label>
            <Select 
              value={data.estimatedDeliveryDays.toString()} 
              onValueChange={(value) => onUpdate({ estimatedDeliveryDays: parseInt(value) })}
            >
              <SelectTrigger className="h-14 text-lg rounded-xl border-2">
                <div className="flex items-center">
                  <Truck className="w-5 h-5 mr-3 text-neutral-400" />
                  <SelectValue placeholder="Select delivery time" />
                </div>
              </SelectTrigger>
              <SelectContent>
                {DELIVERY_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </Card>

      {/* Product Description */}
      <Card className="p-6 bg-neutral-50 dark:bg-neutral-800 border-0">
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Label className="text-base font-semibold text-black dark:text-white">
              Product Description
            </Label>
            <Badge variant="outline" className="text-xs">
              Optional
            </Badge>
          </div>
          
          <RichTextEditor
            value={data.description}
            onChange={(value) => onUpdate({ description: value })}
            placeholder="Describe your product in detail. Include materials, dimensions, authenticity details, and any unique features..."
          />
          
          <div className="flex items-start space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800 dark:text-blue-400">
              <p className="font-medium mb-1">Tips for great descriptions:</p>
              <ul className="space-y-1 text-xs">
                <li>• Include specific details about materials and craftsmanship</li>
                <li>• Mention any signs of wear or unique characteristics</li>
                <li>• Add authenticity information if applicable</li>
                <li>• Use rich formatting to make it easy to read</li>
              </ul>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
