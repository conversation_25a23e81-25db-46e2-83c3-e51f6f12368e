"use client";

import { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { 
  X, 
  Heart, 
  Share2, 
  ShoppingCart,
  Eye,
  Star,
  Truck,
  Shield,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { ProductFormData } from "./ProductForm";

interface ProductPreviewProps {
  data: ProductFormData;
  isVisible: boolean;
  isModal?: boolean;
  onClose?: () => void;
}

export function ProductPreview({ 
  data, 
  isVisible, 
  isModal = false, 
  onClose 
}: ProductPreviewProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  if (!isVisible) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getImageUrl = (file: File) => {
    return URL.createObjectURL(file);
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === data.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? data.images.length - 1 : prev - 1
    );
  };

  const PreviewContent = () => (
    <div className="space-y-6">
      {/* Preview Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Eye className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Customer Preview
          </h3>
        </div>
        <Badge variant="outline" className="text-xs">
          Live Preview
        </Badge>
      </div>

      {/* Product Images */}
      {data.images.length > 0 ? (
        <div className="space-y-4">
          <div className="relative aspect-square bg-neutral-100 dark:bg-neutral-800 rounded-2xl overflow-hidden">
            <img
              src={getImageUrl(data.images[currentImageIndex] || ("" as any))}
              alt={data.title || "Product preview"}
              className="w-full h-full object-cover"
            />
            
            {data.images.length > 1 && (
              <>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={prevImage}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 p-0 rounded-full"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={nextImage}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 p-0 rounded-full"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
                
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                  {data.images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-2 h-2 rounded-full transition-all duration-200 ${
                        index === currentImageIndex 
                          ? 'bg-white' 
                          : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              </>
            )}
          </div>

          {data.images.length > 1 && (
            <div className="flex space-x-2 overflow-x-auto">
              {data.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                    index === currentImageIndex 
                      ? 'border-black dark:border-white' 
                      : 'border-neutral-200 dark:border-neutral-700'
                  }`}
                >
                  <img
                    src={getImageUrl(image)}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="aspect-square bg-neutral-100 dark:bg-neutral-800 rounded-2xl flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-neutral-200 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-3">
              <Eye className="w-8 h-8 text-neutral-400" />
            </div>
            <p className="text-neutral-500 dark:text-neutral-400">
              No images uploaded
            </p>
          </div>
        </div>
      )}

      {/* Product Info */}
      <div className="space-y-4">
        <div>
          <h1 className="text-2xl font-bold text-black dark:text-white">
            {data.title || "Product Title"}
          </h1>
          {data.brand && (
            <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-1">
              {data.brand}
            </p>
          )}
        </div>

        <div className="flex items-center space-x-4">
          <p className="text-3xl font-bold text-black dark:text-white">
            {data.price > 0 ? formatCurrency(data.price) : "$0"}
          </p>
          
          {data.condition && (
            <Badge variant="outline" className="text-sm">
              {data.condition}
            </Badge>
          )}
          
          {data.category && (
            <Badge variant="secondary" className="text-sm">
              {data.category}
            </Badge>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <Button className="flex-1 bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 h-12">
            <ShoppingCart className="w-5 h-5 mr-2" />
            Add to Cart
          </Button>
          
          <Button variant="outline" size="sm" className="h-12 w-12 p-0">
            <Heart className="w-5 h-5" />
          </Button>
          
          <Button variant="outline" size="sm" className="h-12 w-12 p-0">
            <Share2 className="w-5 h-5" />
          </Button>
        </div>

        {/* Delivery Info */}
        {data.estimatedDeliveryDays > 0 && (
          <div className="flex items-center space-x-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <Truck className="w-4 h-4 text-green-600 dark:text-green-400" />
            <span className="text-sm text-green-800 dark:text-green-400">
              Estimated delivery: {data.estimatedDeliveryDays} day{data.estimatedDeliveryDays > 1 ? 's' : ''}
            </span>
          </div>
        )}

        {/* Trust Indicators */}
        <div className="flex items-center space-x-4 text-sm text-neutral-600 dark:text-neutral-400">
          <div className="flex items-center space-x-1">
            <Shield className="w-4 h-4" />
            <span>Authenticity Guaranteed</span>
          </div>
          <div className="flex items-center space-x-1">
            <Star className="w-4 h-4" />
            <span>Verified Seller</span>
          </div>
        </div>

        {/* Description */}
        {data.description && (
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-black dark:text-white">
              Description
            </h3>
            <div 
              className="text-neutral-700 dark:text-neutral-300 prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: data.description }}
            />
          </div>
        )}
      </div>
    </div>
  );

  if (isModal) {
    return (
      <Dialog open={isVisible} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Product Preview</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="p-6">
            <PreviewContent />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Card className="p-6 bg-white dark:bg-neutral-900 rounded-3xl shadow-xl sticky top-8">
      <PreviewContent />
    </Card>
  );
}
