"use client";

import { useState, useRef, useCallback } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  Camera,
  Move,
  Star,
  AlertCircle,
  Info,
  Crop
} from "lucide-react";

interface ImageUploadSectionProps {
  images: File[];
  onImagesChange: (images: File[]) => void;
  error?: string;
  maxImages?: number;
}

export function ImageUploadSection({ 
  images, 
  onImagesChange, 
  error,
  maxImages = 10 
}: ImageUploadSectionProps) {
  const [dragOver, setDragOver] = useState(false);
  const [primaryImageIndex, setPrimaryImageIndex] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFileSelection(files);
  }, [images]);

  const handleFileSelection = (newFiles: File[]) => {
    const imageFiles = newFiles.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length === 0) {
      alert('Please select only image files.');
      return;
    }

    const totalImages = images.length + imageFiles.length;
    if (totalImages > maxImages) {
      alert(`You can only upload up to ${maxImages} images.`);
      return;
    }

    // Check file sizes (max 10MB per image)
    const oversizedFiles = imageFiles.filter(file => file.size > 10 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      alert('Some files are too large. Please keep images under 10MB.');
      return;
    }

    onImagesChange([...images, ...imageFiles]);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      handleFileSelection(files);
    }
  };

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
    
    // Adjust primary image index if necessary
    if (primaryImageIndex >= newImages.length) {
      setPrimaryImageIndex(Math.max(0, newImages.length - 1));
    }
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage || ("" as any));
    onImagesChange(newImages);
    
    // Update primary image index
    if (primaryImageIndex === fromIndex) {
      setPrimaryImageIndex(toIndex);
    } else if (fromIndex < primaryImageIndex && toIndex >= primaryImageIndex) {
      setPrimaryImageIndex(primaryImageIndex - 1);
    } else if (fromIndex > primaryImageIndex && toIndex <= primaryImageIndex) {
      setPrimaryImageIndex(primaryImageIndex + 1);
    }
  };

  const setPrimaryImage = (index: number) => {
    setPrimaryImageIndex(index);
  };

  const getImageUrl = (file: File) => {
    return URL.createObjectURL(file);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center">
          <Camera className="w-5 h-5 text-purple-600 dark:text-purple-400" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-black dark:text-white">
            Product Images
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400">
            Upload high-quality images of your product
          </p>
        </div>
      </div>

      {/* Upload Area */}
      {images.length < maxImages && (
        <Card className="p-8 border-2 border-dashed transition-all duration-200 cursor-pointer hover:border-neutral-400 dark:hover:border-neutral-600"
          style={{
            borderColor: dragOver 
              ? '#3b82f6' 
              : error 
              ? '#ef4444' 
              : '#d1d5db'
          }}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
          
          <div className="text-center space-y-4">
            <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
              dragOver 
                ? 'bg-blue-100 dark:bg-blue-900' 
                : 'bg-neutral-100 dark:bg-neutral-800'
            }`}>
              <Upload className={`w-8 h-8 ${
                dragOver 
                  ? 'text-blue-600 dark:text-blue-400' 
                  : 'text-neutral-400'
              }`} />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-black dark:text-white mb-2">
                {dragOver ? 'Drop images here' : 'Upload product images'}
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                Drag and drop images here, or click to browse
              </p>
              <div className="flex justify-center">
                <Button variant="outline" className="pointer-events-none">
                  <ImageIcon className="w-4 h-4 mr-2" />
                  Choose Images
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Error Message */}
      {error && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
          <p className="text-sm text-red-800 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* Image Guidelines */}
      <div className="flex items-start space-x-2 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
        <div className="text-sm text-blue-800 dark:text-blue-400">
          <p className="font-medium mb-2">Image Guidelines:</p>
          <ul className="space-y-1 text-xs">
            <li>• Upload up to {maxImages} high-quality images</li>
            <li>• Use JPG, PNG, or WebP format</li>
            <li>• Maximum file size: 10MB per image</li>
            <li>• Recommended resolution: 1200x1200px or higher</li>
            <li>• First image will be used as the main product image</li>
          </ul>
        </div>
      </div>

      {/* Uploaded Images */}
      {images.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-black dark:text-white">
              Uploaded Images ({images.length}/{maxImages})
            </h3>
            <Badge variant="outline">
              {primaryImageIndex + 1} of {images.length} is primary
            </Badge>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((file, index) => (
              <Card key={index} className="relative group overflow-hidden">
                <div className="aspect-square relative">
                  <img
                    src={getImageUrl(file)}
                    alt={`Product image ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Primary Image Badge */}
                  {index === primaryImageIndex && (
                    <div className="absolute top-2 left-2">
                      <Badge className="bg-yellow-500 text-white">
                        <Star className="w-3 h-3 mr-1" />
                        Primary
                      </Badge>
                    </div>
                  )}

                  {/* Image Controls */}
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center space-x-2">
                    {index !== primaryImageIndex && (
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => setPrimaryImage(index)}
                        className="h-8 px-3"
                      >
                        <Star className="w-3 h-3 mr-1" />
                        Primary
                      </Button>
                    )}
                    
                    <Button
                      size="sm"
                      variant="secondary"
                      className="h-8 px-3"
                    >
                      <Crop className="w-3 h-3 mr-1" />
                      Edit
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => removeImage(index)}
                      className="h-8 w-8 p-0"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>

                  {/* Move Buttons */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex flex-col space-y-1">
                    {index > 0 && (
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => moveImage(index, index - 1)}
                        className="h-6 w-6 p-0"
                      >
                        ←
                      </Button>
                    )}
                    {index < images.length - 1 && (
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => moveImage(index, index + 1)}
                        className="h-6 w-6 p-0"
                      >
                        →
                      </Button>
                    )}
                  </div>
                </div>

                {/* Image Info */}
                <div className="p-3">
                  <p className="text-sm font-medium text-black dark:text-white truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-neutral-500">
                    {formatFileSize(file.size)}
                  </p>
                </div>
              </Card>
            ))}
          </div>

          {/* Reorder Instructions */}
          <div className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400">
            <Move className="w-4 h-4" />
            <span>Hover over images to reorder, set as primary, or remove</span>
          </div>
        </div>
      )}
    </div>
  );
}
