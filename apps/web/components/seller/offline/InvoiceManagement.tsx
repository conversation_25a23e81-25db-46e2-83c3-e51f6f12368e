"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { Card } from "@repo/ui/components/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { 
  FileText, 
  Search, 
  Filter,
  MoreHorizontal,
  Download,
  Mail,
  Eye,
  CheckCircle,
  Clock,
  AlertTriangle,
  Calendar,
  DollarSign
} from "lucide-react";

interface Invoice {
  id: string;
  number: string;
  date: string;
  dueDate: string;
  status: "draft" | "sent" | "paid" | "overdue";
  clientName: string;
  clientEmail: string;
  productTitle: string;
  amount: number;
  paymentMethod: string;
}

interface InvoiceManagementProps {
  invoices?: Invoice[];
}

const MOCK_INVOICES: Invoice[] = [
  {
    id: "1",
    number: "HV-001234",
    date: "2024-01-15",
    dueDate: "2024-02-14",
    status: "paid",
    clientName: "Sarah Johnson",
    clientEmail: "<EMAIL>",
    productTitle: "Hermès Birkin 30 Black Togo",
    amount: 12500,
    paymentMethod: "Bank Transfer"
  },
  {
    id: "2",
    number: "HV-001235",
    date: "2024-01-18",
    dueDate: "2024-02-17",
    status: "sent",
    clientName: "Michael Chen",
    clientEmail: "<EMAIL>",
    productTitle: "Chanel Classic Flap Medium",
    amount: 8900,
    paymentMethod: "Credit Card"
  },
  {
    id: "3",
    number: "HV-001236",
    date: "2024-01-10",
    dueDate: "2024-02-09",
    status: "overdue",
    clientName: "Emma Wilson",
    clientEmail: "<EMAIL>",
    productTitle: "Louis Vuitton Neverfull MM",
    amount: 1800,
    paymentMethod: "Cash"
  },
  {
    id: "4",
    number: "HV-001237",
    date: "2024-01-20",
    dueDate: "2024-02-19",
    status: "draft",
    clientName: "David Rodriguez",
    clientEmail: "<EMAIL>",
    productTitle: "Rolex Submariner",
    amount: 15000,
    paymentMethod: "Check"
  }
];

const STATUS_COLORS = {
  draft: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  sent: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  paid: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  overdue: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

const STATUS_ICONS = {
  draft: FileText,
  sent: Mail,
  paid: CheckCircle,
  overdue: AlertTriangle,
};

export function InvoiceManagement({ invoices = MOCK_INVOICES }: InvoiceManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.productTitle.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || invoice.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const sortedInvoices = [...filteredInvoices].sort((a, b) => {
    let aValue: any = a[sortBy as keyof Invoice];
    let bValue: any = b[sortBy as keyof Invoice];
    
    if (sortBy === "date" || sortBy === "dueDate") {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }
    
    if (sortOrder === "asc") {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getStatusIcon = (status: string) => {
    const Icon = STATUS_ICONS[status as keyof typeof STATUS_ICONS];
    return Icon ? <Icon className="w-4 h-4" /> : null;
  };

  const handleResendInvoice = (invoice: Invoice) => {
    console.log("Resending invoice:", invoice.number);
    // Implement resend functionality
  };

  const handleMarkAsPaid = (invoice: Invoice) => {
    console.log("Marking as paid:", invoice.number);
    // Implement mark as paid functionality
  };

  const handleDownloadPDF = (invoice: Invoice) => {
    console.log("Downloading PDF:", invoice.number);
    // Implement PDF download functionality
  };

  const handleViewInvoice = (invoice: Invoice) => {
    console.log("Viewing invoice:", invoice.number);
    // Implement view functionality
  };

  const getStats = () => {
    const total = invoices.length;
    const paid = invoices.filter(i => i.status === "paid").length;
    const overdue = invoices.filter(i => i.status === "overdue").length;
    const totalAmount = invoices.reduce((sum, i) => sum + i.amount, 0);
    const paidAmount = invoices.filter(i => i.status === "paid").reduce((sum, i) => sum + i.amount, 0);
    
    return { total, paid, overdue, totalAmount, paidAmount };
  };

  const stats = getStats();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-black dark:text-white">
          Invoice Management
        </h1>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-1">
          Track and manage your offline sales invoices
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Total Invoices
              </p>
              <p className="text-3xl font-bold text-black dark:text-white mt-2">
                {stats.total}
              </p>
            </div>
            <FileText className="w-8 h-8 text-neutral-400" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Paid Invoices
              </p>
              <p className="text-3xl font-bold text-green-600 dark:text-green-400 mt-2">
                {stats.paid}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Overdue
              </p>
              <p className="text-3xl font-bold text-red-600 dark:text-red-400 mt-2">
                {stats.overdue}
              </p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Total Revenue
              </p>
              <p className="text-2xl font-bold text-black dark:text-white mt-2">
                {formatCurrency(stats.paidAmount)}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-neutral-400" />
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
          <Input
            placeholder="Search invoices..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-12 rounded-xl"
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48 h-12 rounded-xl">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>

        <Select 
          value={`${sortBy}-${sortOrder}`} 
          onValueChange={(value) => {
            const [field, order] = value.split("-");
            setSortBy(field || "date");
            setSortOrder(order as "asc" | "desc");
          }}
        >
          <SelectTrigger className="w-48 h-12 rounded-xl">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="date-desc">Date (Newest)</SelectItem>
            <SelectItem value="date-asc">Date (Oldest)</SelectItem>
            <SelectItem value="amount-desc">Amount (High to Low)</SelectItem>
            <SelectItem value="amount-asc">Amount (Low to High)</SelectItem>
            <SelectItem value="dueDate-asc">Due Date (Soonest)</SelectItem>
            <SelectItem value="dueDate-desc">Due Date (Latest)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Invoices Table */}
      <Card className="overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-neutral-50 dark:bg-neutral-900">
              <TableHead>Invoice</TableHead>
              <TableHead>Client</TableHead>
              <TableHead>Product</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead className="w-12">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedInvoices.map((invoice) => (
              <TableRow key={invoice.id} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                <TableCell>
                  <div>
                    <p className="font-semibold text-black dark:text-white font-mono">
                      {invoice.number}
                    </p>
                    <p className="text-sm text-neutral-500">
                      {formatDate(invoice.date)}
                    </p>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div>
                    <p className="font-medium text-black dark:text-white">
                      {invoice.clientName}
                    </p>
                    <p className="text-sm text-neutral-500">
                      {invoice.clientEmail}
                    </p>
                  </div>
                </TableCell>
                
                <TableCell>
                  <p className="font-medium text-black dark:text-white line-clamp-2">
                    {invoice.productTitle}
                  </p>
                  <p className="text-sm text-neutral-500">
                    {invoice.paymentMethod}
                  </p>
                </TableCell>
                
                <TableCell>
                  <p className="font-bold text-black dark:text-white">
                    {formatCurrency(invoice.amount)}
                  </p>
                </TableCell>
                
                <TableCell>
                  <Badge className={`${STATUS_COLORS[invoice.status]} flex items-center space-x-1`}>
                    {getStatusIcon(invoice.status)}
                    <span className="capitalize">{invoice.status}</span>
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-neutral-400" />
                    <span className="text-black dark:text-white">
                      {formatDate(invoice.dueDate)}
                    </span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewInvoice(invoice)}>
                        <Eye className="w-4 h-4 mr-2" />
                        View Invoice
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem onClick={() => handleDownloadPDF(invoice)}>
                        <Download className="w-4 h-4 mr-2" />
                        Download PDF
                      </DropdownMenuItem>
                      
                      {(invoice.status === "draft" || invoice.status === "overdue") && (
                        <DropdownMenuItem onClick={() => handleResendInvoice(invoice)}>
                          <Mail className="w-4 h-4 mr-2" />
                          {invoice.status === "draft" ? "Send Invoice" : "Resend Invoice"}
                        </DropdownMenuItem>
                      )}
                      
                      {invoice.status !== "paid" && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleMarkAsPaid(invoice)}>
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Mark as Paid
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {/* Empty State */}
      {sortedInvoices.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
            No invoices found
          </h3>
          <p className="text-neutral-500 dark:text-neutral-500">
            {searchTerm || statusFilter !== "all" 
              ? "Try adjusting your search or filters" 
              : "Your invoices will appear here when you make offline sales"
            }
          </p>
        </div>
      )}
    </div>
  );
}
