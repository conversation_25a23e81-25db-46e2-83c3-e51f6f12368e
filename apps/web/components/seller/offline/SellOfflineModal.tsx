"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Card } from "@repo/ui/components/card";
import { 
  ShoppingCart, 
  AlertTriangle, 
  CheckCircle,
  FileText,
  Mail,
  Download,
  X
} from "lucide-react";
import { ClientInfoForm } from "./ClientInfoForm";
import { InvoiceGenerator } from "./InvoiceGenerator";

interface Product {
  id: string;
  title: string;
  brand: string;
  price: number;
  category: string;
  condition: string;
  images: string[];
}

interface ClientInfo {
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  salePrice: number;
  paymentMethod: string;
}

interface SellOfflineModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
  onSaleComplete: (saleData: { product: Product; client: ClientInfo; invoiceId: string }) => void;
}

export function SellOfflineModal({ 
  isOpen, 
  onClose, 
  product, 
  onSaleComplete 
}: SellOfflineModalProps) {
  const [step, setStep] = useState<"warning" | "client-info" | "invoice" | "complete">("warning");
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const [invoiceId, setInvoiceId] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState(false);

  const handleConfirmOfflineSale = () => {
    setStep("client-info");
  };

  const handleClientInfoSubmit = (data: ClientInfo) => {
    setClientInfo(data);
    setStep("invoice");
  };

  const handleInvoiceGenerated = (generatedInvoiceId: string) => {
    setInvoiceId(generatedInvoiceId);
    setStep("complete");
  };

  const handleSaleComplete = async () => {
    if (!clientInfo) return;

    setIsProcessing(true);
    try {
      // Remove product from marketplace
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      onSaleComplete({
        product,
        client: clientInfo,
        invoiceId
      });
      
      onClose();
      resetModal();
    } catch (error) {
      console.error("Failed to complete sale:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const resetModal = () => {
    setStep("warning");
    setClientInfo(null);
    setInvoiceId("");
    setIsProcessing(false);
  };

  const handleClose = () => {
    onClose();
    resetModal();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const renderWarningStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertTriangle className="w-8 h-8 text-orange-600 dark:text-orange-400" />
        </div>
        <h3 className="text-xl font-bold text-black dark:text-white mb-2">
          Sell Product Offline
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400">
          This will remove the product from the marketplace and mark it as sold offline
        </p>
      </div>

      <Card className="p-6 bg-neutral-50 dark:bg-neutral-800">
        <div className="flex items-start space-x-4">
          <img
            src={product.images[0] || "/api/placeholder/100/100"}
            alt={product.title}
            className="w-20 h-20 object-cover rounded-lg"
          />
          <div className="flex-1">
            <h4 className="font-semibold text-black dark:text-white">
              {product.title}
            </h4>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              {product.brand} • {product.condition}
            </p>
            <div className="flex items-center space-x-2 mt-2">
              <Badge variant="outline">{product.category}</Badge>
              <span className="text-lg font-bold text-black dark:text-white">
                {formatCurrency(product.price)}
              </span>
            </div>
          </div>
        </div>
      </Card>

      <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-5 h-5 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-orange-900 dark:text-orange-300 mb-2">
              Important Notice
            </h4>
            <ul className="text-sm text-orange-800 dark:text-orange-400 space-y-1">
              <li>• Product will be removed from the marketplace immediately</li>
              <li>• This action cannot be undone</li>
              <li>• You'll need to provide client information for the invoice</li>
              <li>• An invoice will be generated for your records</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button variant="outline" onClick={handleClose}>
          Cancel
        </Button>
        <Button 
          onClick={handleConfirmOfflineSale}
          className="bg-orange-600 text-white hover:bg-orange-700"
        >
          <ShoppingCart className="w-4 h-4 mr-2" />
          Continue with Offline Sale
        </Button>
      </div>
    </div>
  );

  const renderCompleteStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
        <h3 className="text-xl font-bold text-black dark:text-white mb-2">
          Sale Completed Successfully!
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400">
          Product has been sold offline and invoice has been generated
        </p>
      </div>

      <Card className="p-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-medium text-green-900 dark:text-green-300">
              Invoice Number:
            </span>
            <span className="font-mono text-green-900 dark:text-green-300">
              {invoiceId}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="font-medium text-green-900 dark:text-green-300">
              Client:
            </span>
            <span className="text-green-900 dark:text-green-300">
              {clientInfo?.name}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="font-medium text-green-900 dark:text-green-300">
              Sale Amount:
            </span>
            <span className="text-lg font-bold text-green-900 dark:text-green-300">
              {clientInfo && formatCurrency(clientInfo.salePrice)}
            </span>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <Button variant="outline" className="h-12">
          <FileText className="w-4 h-4 mr-2" />
          View Invoice
        </Button>
        
        <Button variant="outline" className="h-12">
          <Download className="w-4 h-4 mr-2" />
          Download PDF
        </Button>
        
        <Button variant="outline" className="h-12">
          <Mail className="w-4 h-4 mr-2" />
          Email to Client
        </Button>
        
        <Button 
          onClick={handleSaleComplete}
          disabled={isProcessing}
          className="bg-green-600 text-white hover:bg-green-700 h-12"
        >
          {isProcessing ? "Processing..." : "Complete Sale"}
        </Button>
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>
              {step === "warning" && "Sell Product Offline"}
              {step === "client-info" && "Client Information"}
              {step === "invoice" && "Generate Invoice"}
              {step === "complete" && "Sale Complete"}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="p-6">
          {step === "warning" && renderWarningStep()}
          
          {step === "client-info" && (
            <ClientInfoForm
              product={product}
              onSubmit={handleClientInfoSubmit}
              onCancel={() => setStep("warning")}
            />
          )}
          
          {step === "invoice" && clientInfo && (
            <InvoiceGenerator
              product={product}
              client={clientInfo}
              onInvoiceGenerated={handleInvoiceGenerated}
              onBack={() => setStep("client-info")}
            />
          )}
          
          {step === "complete" && renderCompleteStep()}
        </div>
      </DialogContent>
    </Dialog>
  );
}
