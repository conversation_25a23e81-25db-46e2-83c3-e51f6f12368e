"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Card } from "@repo/ui/components/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  DollarSign,
  CreditCard,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  ArrowRight
} from "lucide-react";

interface Product {
  id: string;
  title: string;
  brand: string;
  price: number;
  category: string;
  condition: string;
  images: string[];
}

interface ClientInfo {
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  salePrice: number;
  paymentMethod: string;
}

interface ClientInfoFormProps {
  product: Product;
  onSubmit: (clientInfo: ClientInfo) => void;
  onCancel: () => void;
}

const PAYMENT_METHODS = [
  "Cash",
  "Credit Card",
  "Debit Card",
  "Bank Transfer",
  "Check",
  "PayPal",
  "Venmo",
  "Zelle",
  "Other"
];

const US_STATES = [
  "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware",
  "Florida", "Georgia", "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky",
  "Louisiana", "Maine", "Maryland", "Massachusetts", "Michigan", "Minnesota", "Mississippi",
  "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire", "New Jersey", "New Mexico",
  "New York", "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", "Pennsylvania",
  "Rhode Island", "South Carolina", "South Dakota", "Tennessee", "Texas", "Utah", "Vermont",
  "Virginia", "Washington", "West Virginia", "Wisconsin", "Wyoming"
];

export function ClientInfoForm({ product, onSubmit, onCancel }: ClientInfoFormProps) {
  const [formData, setFormData] = useState<ClientInfo>({
    name: "",
    email: "",
    phone: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "United States"
    },
    salePrice: product.price,
    paymentMethod: ""
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = (field: string, value: any): string => {
    switch (field) {
      case "name":
        return value.length < 2 ? "Client name is required" : "";
      case "email":
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return !emailRegex.test(value) ? "Please enter a valid email address" : "";
      case "phone":
        const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
        return !phoneRegex.test(value) ? "Please enter a valid phone number" : "";
      case "street":
        return value.length < 5 ? "Street address is required" : "";
      case "city":
        return value.length < 2 ? "City is required" : "";
      case "state":
        return !value ? "State is required" : "";
      case "zipCode":
        const zipRegex = /^\d{5}(-\d{4})?$/;
        return !zipRegex.test(value) ? "Please enter a valid ZIP code" : "";
      case "salePrice":
        return value <= 0 ? "Sale price must be greater than 0" : "";
      case "paymentMethod":
        return !value ? "Payment method is required" : "";
      default:
        return "";
    }
  };

  const handleFieldChange = (field: string, value: any) => {
    if (field.startsWith("address.")) {
      const addressField = field.split(".")[1];
      setFormData(prev => ({
        ...prev,
        address: { ...prev.address, [addressField as string]: value }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    if (touched[field]) {
      const error = validateField(field, value);
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const handleFieldBlur = (field: string, value: any) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    const error = validateField(field, value);
    setErrors(prev => ({ ...prev, [field]: error }));
  };

  const validateForm = (): boolean => {
    const fieldsToValidate = [
      "name", "email", "phone", "street", "city", "state", "zipCode", "salePrice", "paymentMethod"
    ];
    
    const newErrors: Record<string, string> = {};
    
    fieldsToValidate.forEach(field => {
      let value: any;
      if (field === "street" || field === "city" || field === "state" || field === "zipCode") {
        value = formData.address[field as keyof typeof formData.address];
      } else {
        value = formData[field as keyof ClientInfo];
      }
      
      const error = validateField(field, value);
      if (error) newErrors[field] = error;
    });

    setErrors(newErrors);
    setTouched(fieldsToValidate.reduce((acc, field) => ({ ...acc, [field]: true }), {}));
    
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const getFieldStatus = (field: string) => {
    let value: any;
    if (field === "street" || field === "city" || field === "state" || field === "zipCode") {
      value = formData.address[field as keyof typeof formData.address];
    } else {
      value = formData[field as keyof ClientInfo];
    }

    const error = errors[field];
    const isTouched = touched[field];
    
    if (!isTouched) return "default";
    if (error) return "error";
    if (value) return "success";
    return "default";
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Product Summary */}
      <Card className="p-4 bg-neutral-50 dark:bg-neutral-800">
        <div className="flex items-center space-x-3">
          <img
            src={product.images[0] || "/api/placeholder/60/60"}
            alt={product.title}
            className="w-12 h-12 object-cover rounded-lg"
          />
          <div>
            <h4 className="font-semibold text-black dark:text-white">
              {product.title}
            </h4>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Listed at {formatCurrency(product.price)}
            </p>
          </div>
        </div>
      </Card>

      {/* Client Information */}
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Client Information
          </h3>
        </div>

        {/* Name and Email */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium text-black dark:text-white">
              Client Name *
            </Label>
            <div className="relative">
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleFieldChange("name", e.target.value)}
                onBlur={(e) => handleFieldBlur("name", e.target.value)}
                placeholder="Enter client's full name"
                className={`pl-10 h-12 ${
                  getFieldStatus("name") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("name") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : ""
                }`}
              />
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" />
              {getFieldStatus("name") === "success" && (
                <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500" />
              )}
            </div>
            {errors.name && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {errors.name}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium text-black dark:text-white">
              Email Address *
            </Label>
            <div className="relative">
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleFieldChange("email", e.target.value)}
                onBlur={(e) => handleFieldBlur("email", e.target.value)}
                placeholder="<EMAIL>"
                className={`pl-10 h-12 ${
                  getFieldStatus("email") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("email") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : ""
                }`}
              />
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" />
              {getFieldStatus("email") === "success" && (
                <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500" />
              )}
            </div>
            {errors.email && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {errors.email}
              </p>
            )}
          </div>
        </div>

        {/* Phone */}
        <div className="space-y-2">
          <Label htmlFor="phone" className="text-sm font-medium text-black dark:text-white">
            Phone Number
          </Label>
          <div className="relative">
            <Input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={(e) => handleFieldChange("phone", e.target.value)}
              onBlur={(e) => handleFieldBlur("phone", e.target.value)}
              placeholder="(*************"
              className={`pl-10 h-12 ${
                getFieldStatus("phone") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("phone") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : ""
              }`}
            />
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" />
            {getFieldStatus("phone") === "success" && (
              <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500" />
            )}
          </div>
          {errors.phone && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-3 h-3 mr-1" />
              {errors.phone}
            </p>
          )}
        </div>
      </div>

      {/* Address Information */}
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <MapPin className="w-5 h-5 text-green-600 dark:text-green-400" />
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Billing Address
          </h3>
        </div>

        {/* Street Address */}
        <div className="space-y-2">
          <Label htmlFor="street" className="text-sm font-medium text-black dark:text-white">
            Street Address *
          </Label>
          <Input
            id="street"
            value={formData.address.street}
            onChange={(e) => handleFieldChange("address.street", e.target.value)}
            onBlur={(e) => handleFieldBlur("street", e.target.value)}
            placeholder="123 Main Street"
            className={`h-12 ${
              getFieldStatus("street") === "error"
                ? "border-red-300 focus:border-red-500"
                : getFieldStatus("street") === "success"
                ? "border-green-300 focus:border-green-500"
                : ""
            }`}
          />
          {errors.street && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-3 h-3 mr-1" />
              {errors.street}
            </p>
          )}
        </div>

        {/* City, State, ZIP */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city" className="text-sm font-medium text-black dark:text-white">
              City *
            </Label>
            <Input
              id="city"
              value={formData.address.city}
              onChange={(e) => handleFieldChange("address.city", e.target.value)}
              onBlur={(e) => handleFieldBlur("city", e.target.value)}
              placeholder="New York"
              className={`h-12 ${
                getFieldStatus("city") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("city") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : ""
              }`}
            />
            {errors.city && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {errors.city}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium text-black dark:text-white">
              State *
            </Label>
            <Select
              value={formData.address.state}
              onValueChange={(value) => handleFieldChange("address.state", value)}
            >
              <SelectTrigger className={`h-12 ${
                getFieldStatus("state") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("state") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : ""
              }`}>
                <SelectValue placeholder="Select state" />
              </SelectTrigger>
              <SelectContent>
                {US_STATES.map((state) => (
                  <SelectItem key={state} value={state}>
                    {state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.state && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {errors.state}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="zipCode" className="text-sm font-medium text-black dark:text-white">
              ZIP Code *
            </Label>
            <Input
              id="zipCode"
              value={formData.address.zipCode}
              onChange={(e) => handleFieldChange("address.zipCode", e.target.value)}
              onBlur={(e) => handleFieldBlur("zipCode", e.target.value)}
              placeholder="10001"
              className={`h-12 ${
                getFieldStatus("zipCode") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("zipCode") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : ""
              }`}
            />
            {errors.zipCode && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {errors.zipCode}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Sale Information */}
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <DollarSign className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Sale Details
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="salePrice" className="text-sm font-medium text-black dark:text-white">
              Sale Price *
            </Label>
            <div className="relative">
              <Input
                id="salePrice"
                type="number"
                value={formData.salePrice || ""}
                onChange={(e) => handleFieldChange("salePrice", parseFloat(e.target.value) || 0)}
                onBlur={(e) => handleFieldBlur("salePrice", parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                className={`pl-10 h-12 ${
                  getFieldStatus("salePrice") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("salePrice") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : ""
                }`}
                min="0"
                step="0.01"
              />
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" />
              {getFieldStatus("salePrice") === "success" && (
                <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500" />
              )}
            </div>
            {errors.salePrice && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {errors.salePrice}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium text-black dark:text-white">
              Payment Method *
            </Label>
            <Select
              value={formData.paymentMethod}
              onValueChange={(value) => handleFieldChange("paymentMethod", value)}
            >
              <SelectTrigger className={`h-12 ${
                getFieldStatus("paymentMethod") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("paymentMethod") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : ""
              }`}>
                <div className="flex items-center">
                  <CreditCard className="w-4 h-4 mr-2 text-neutral-400" />
                  <SelectValue placeholder="Select payment method" />
                </div>
              </SelectTrigger>
              <SelectContent>
                {PAYMENT_METHODS.map((method) => (
                  <SelectItem key={method} value={method}>
                    {method}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.paymentMethod && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {errors.paymentMethod}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-between pt-6">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="h-12 px-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        
        <Button
          type="submit"
          className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 h-12 px-6"
        >
          Generate Invoice
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </form>
  );
}
