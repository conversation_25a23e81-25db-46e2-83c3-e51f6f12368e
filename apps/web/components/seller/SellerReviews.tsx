"use client";

import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Textarea } from "@repo/ui/components/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Star, MessageSquare, CheckCircle, Package, Calendar, User } from "lucide-react";
import Image from "next/image";
import { useAuth } from "@/hooks/useBetterAuth";

interface SellerReviewsProps {
  sellerId: Id<"users">;
  sellerName: string;
  currentRating?: number;
  reviewCount?: number;
}

interface ReviewFormData {
  orderId: Id<"orders">;
  rating: number;
  review: string;
}

export function SellerReviews({ sellerId, sellerName, currentRating = 0, reviewCount = 0 }: SellerReviewsProps) {
  const { user } = useAuth();
  const [selectedRating, setSelectedRating] = useState(5);
  const [reviewText, setReviewText] = useState("");
  const [selectedOrder, setSelectedOrder] = useState<Id<"orders"> | null>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [ratingFilter, setRatingFilter] = useState<number | null>(null);

  const ITEMS_PER_PAGE = 10;

  // Get seller reviews
  const reviewsData = useQuery(api.sellerReviews.getSellerReviews, {
    sellerId,
    limit: ITEMS_PER_PAGE,
    offset: currentPage * ITEMS_PER_PAGE,
    rating: ratingFilter || undefined,
    verifiedOnly: true,
  });

  // Get user's reviewable orders for this seller
  const reviewableOrders = useQuery(api.sellerReviews.getReviewableOrders, {
    sellerId: user ? sellerId : undefined,
  });

  // Add review mutation
  const addReview = useMutation(api.sellerReviews.addSellerReview);

  const reviews = reviewsData?.reviews || [];
  const ratingStats = reviewsData?.ratingStats;
  const hasMore = reviewsData?.pagination?.hasMore || false;

  const handleRatingSelect = (rating: number) => {
    setSelectedRating(rating);
  };

  const handleSubmitReview = async () => {
    if (!selectedOrder || !reviewText.trim()) return;

    try {
      await addReview({
        sellerId,
        orderId: selectedOrder,
        rating: selectedRating,
        review: reviewText.trim(),
      });

      // Reset form and close dialog
      setSelectedOrder(null);
      setReviewText("");
      setSelectedRating(5);
      setIsReviewDialogOpen(false);

      // Refresh reviews
      // The query will automatically update due to Convex reactivity
    } catch (error) {
      console.error("Failed to submit review:", error);
      // You could add toast notification here
    }
  };

  const canReview = user && reviewableOrders?.orders && reviewableOrders.orders.length > 0;

  const renderStars = (rating: number, interactive = false, size: "sm" | "md" | "lg" = "md") => {
    const sizeClasses = {
      sm: "w-3 h-3",
      md: "w-4 h-4",
      lg: "w-5 h-5",
    };

    return (
      <div className="flex items-center space-x-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? "text-yellow-500 fill-current"
                : "text-gray-300 dark:text-gray-600"
            } ${interactive ? "cursor-pointer hover:scale-110 transition-transform" : ""}`}
            onClick={() => interactive && handleRatingSelect(star)}
          />
        ))}
      </div>
    );
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getRatingLabel = (rating: number) => {
    if (rating >= 4.5) return "Excellent";
    if (rating >= 4.0) return "Very Good";
    if (rating >= 3.5) return "Good";
    if (rating >= 3.0) return "Average";
    if (rating >= 2.5) return "Below Average";
    return "Poor";
  };

  return (
    <div className="space-y-6">
      {/* Reviews Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Customer Reviews</h3>
          </div>
          {currentRating > 0 && (
            <div className="flex items-center space-x-2">
              {renderStars(currentRating, false, "md")}
              <span className="text-sm font-medium">
                {currentRating.toFixed(1)} ({reviewCount} reviews)
              </span>
              <Badge variant="secondary" className="text-xs">
                {getRatingLabel(currentRating)}
              </Badge>
            </div>
          )}
        </div>

        {canReview && (
          <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <MessageSquare className="w-4 h-4 mr-2" />
                Write a Review
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Review {sellerName}</DialogTitle>
              </DialogHeader>
              
              <div className="space-y-6">
                {/* Order Selection */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Select Order to Review</label>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {reviewableOrders?.orders?.map((order) => (
                      <div
                        key={order._id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedOrder === order._id
                            ? "border-primary bg-primary/5"
                            : "border-border hover:border-primary/50"
                        }`}
                        onClick={() => setSelectedOrder(order._id)}
                      >
                        <div className="flex items-center space-x-3">
                          {order.product?.images?.[0] && (
                            <div className="w-12 h-12 rounded-lg overflow-hidden bg-muted">
                              <Image
                                src={order.product.images[0]}
                                alt={order.product.title}
                                width={48}
                                height={48}
                                className="w-full h-full object-cover"
                              />
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-sm truncate">
                              {order.product?.title || "Product"}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Order #{order.orderNumber} • {formatDate(order.deliveredDate || 0)}
                            </p>
                          </div>
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Rating Selection */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Rating</label>
                  <div className="flex items-center space-x-4">
                    {renderStars(selectedRating, true, "lg")}
                    <span className="text-sm text-muted-foreground">
                      {selectedRating} out of 5 stars
                    </span>
                  </div>
                </div>

                {/* Review Text */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Review</label>
                  <Textarea
                    placeholder="Share your experience with this seller... (minimum 10 characters)"
                    value={reviewText}
                    onChange={(e) => setReviewText(e.target.value)}
                    rows={4}
                    maxLength={1000}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Minimum 10 characters</span>
                    <span>{reviewText.length}/1000</span>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setIsReviewDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSubmitReview}
                    disabled={
                      !selectedOrder ||
                      reviewText.trim().length < 10 ||
                      reviewText.trim().length > 1000
                    }
                  >
                    Submit Review
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Rating Distribution */}
      {ratingStats && ratingStats.total > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Rating Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[5, 4, 3, 2, 1].map((rating) => {
                const count = ratingStats.distribution[rating as keyof typeof ratingStats.distribution] || 0;
                const percentage = ratingStats.total > 0 ? (count / ratingStats.total) * 100 : 0;
                
                return (
                  <div key={rating} className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1 w-16">
                      <span className="text-sm font-medium">{rating}</span>
                      <Star className="w-3 h-3 text-yellow-500 fill-current" />
                    </div>
                    <div className="flex-1 bg-muted rounded-full h-2">
                      <div
                        className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-muted-foreground w-12 text-right">
                      {count}
                    </span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      {reviews.length > 0 ? (
        <div className="space-y-4">
          {reviews.map((review) => (
            <Card key={review._id}>
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  {/* User Avatar */}
                  <div className="flex-shrink-0">
                    {review.user?.profileImage ? (
                      <Image
                        src={review.user.profileImage}
                        alt={review.user.name}
                        width={40}
                        height={40}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                        <User className="w-5 h-5 text-muted-foreground" />
                      </div>
                    )}
                  </div>

                  {/* Review Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="font-medium text-sm">{review.user?.name || "Anonymous"}</span>
                      <Badge variant="secondary" className="text-xs">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Verified Purchase
                      </Badge>
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      {renderStars(review.rating, false, "sm")}
                      <span className="text-sm text-muted-foreground">
                        {formatDate(review.reviewDate)}
                      </span>
                    </div>

                    <p className="text-sm text-foreground mb-3">{review.review}</p>

                    {/* Product Info */}
                    {review.product && (
                      <div className="flex items-center space-x-2 p-2 bg-muted rounded-lg">
                        <Package className="w-4 h-4 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          Purchased: {review.product.title}
                          {review.product.brand && ` by ${review.product.brand}`}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Pagination */}
          {hasMore && (
            <div className="flex justify-center pt-4">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => prev + 1)}
              >
                Load More Reviews
              </Button>
            </div>
          )}
        </div>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h4 className="text-lg font-medium mb-2">No Reviews Yet</h4>
            <p className="text-muted-foreground mb-4">
              This seller doesn't have any reviews yet. Be the first to share your experience!
            </p>
            {canReview && (
              <Button onClick={() => setIsReviewDialogOpen(true)}>
                Write the First Review
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Rating Filter */}
      {reviews.length > 0 && (
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">Filter by rating:</span>
          <Button
            variant={ratingFilter === null ? "default" : "outline"}
            size="sm"
            onClick={() => setRatingFilter(null)}
          >
            All
          </Button>
          {[5, 4, 3, 2, 1].map((rating) => (
            <Button
              key={rating}
              variant={ratingFilter === rating ? "default" : "outline"}
              size="sm"
              onClick={() => setRatingFilter(ratingFilter === rating ? null : rating)}
            >
              {rating}+
            </Button>
          ))}
        </div>
      )}
    </div>
  );
}
