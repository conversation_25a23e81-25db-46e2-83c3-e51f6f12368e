"use client";

import { useState, useRef } from "react";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Badge } from "@repo/ui/components/badge";
import { Progress } from "@repo/ui/components/progress";
import { 
  Upload, 
  Download, 
  FileSpreadsheet, 
  CheckCircle, 
  AlertCircle, 
  X,
  Info
} from "lucide-react";

interface ProductData {
  title: string;
  description?: string;
  price: number;
  category: "clothing" | "sneakers" | "collectibles" | "accessories" | "handbags" | "jewelry" | "watches" | "sunglasses";
  brand: string;
  condition: "new" | "like_new" | "excellent" | "very_good" | "good" | "fair";
  size?: string;
  color?: string;
  material?: string;
  year?: number;
  originalPrice?: number;
  sku?: string;
  ownershipType?: "owned" | "consigned";
  sourceInfo?: {
    source: string;
    costPaid?: number;
    paymentMethod?: string;
    purchaseDate?: number;
    receipt?: string;
  };
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  shippingCost?: number;
  tags?: string[];
}

interface ValidationError {
  row: number;
  field: string;
  message: string;
}

interface FieldMapping {
  title: string;
  description: string;
  price: string;
  category: string;
  brand: string;
  condition: string;
  size: string;
  color: string;
  material: string;
  year: string;
  originalPrice: string;
  sku: string;
  ownershipType: string;
  source: string;
  costPaid: string;
  paymentMethod: string;
  tags: string;
  weight: string;
  shippingCost: string;
  length: string;
  width: string;
  height: string;
}

interface BulkUploadModalProps {
  onSuccess?: () => void;
}

export function BulkUploadModal({ onSuccess }: BulkUploadModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [products, setProducts] = useState<ProductData[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [hasValidData, setHasValidData] = useState(false);
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [showFieldMapping, setShowFieldMapping] = useState(false);
  const [fieldMapping, setFieldMapping] = useState<FieldMapping>({
    title: '',
    description: '',
    price: '',
    category: '',
    brand: '',
    condition: '',
    size: '',
    color: '',
    material: '',
    year: '',
    originalPrice: '',
    sku: '',
    ownershipType: '',
    source: '',
    costPaid: '',
    paymentMethod: '',
    tags: '',
    weight: '',
    shippingCost: '',
    length: '',
    width: '',
    height: ''
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const bulkCreateProducts = useMutation(api.productManagement.bulkCreateProducts);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      let text: string;
      
      // Handle different file types
      if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        toast.error('Please export your Excel file as CSV format before uploading. Excel files are not directly supported.');
        return;
      } else if (file.name.endsWith('.csv') || file.name.endsWith('.txt')) {
        text = await file.text();
      } else {
        toast.error('Please upload a CSV or TXT file');
        return;
      }

      const lines = text.split('\n');
      if (lines.length < 2) {
        toast.error('CSV file must have at least a header row and one data row');
        return;
      }

      const headers = lines[0]?.split(',').map(h => h.trim().replace(/"/g, '')) || [];
      setCsvHeaders(headers);
      
      // Auto-map common field names
      const autoMapping: FieldMapping = {
        title: '',
        description: '',
        price: '',
        category: '',
        brand: '',
        condition: '',
        size: '',
        color: '',
        material: '',
        year: '',
        originalPrice: '',
        sku: '',
        ownershipType: '',
        source: '',
        costPaid: '',
        paymentMethod: '',
        tags: '',
        weight: '',
        shippingCost: '',
        length: '',
        width: '',
        height: ''
      };

      headers.forEach(header => {
        const lowerHeader = header.toLowerCase();
        if (lowerHeader.includes('title') || lowerHeader.includes('name')) autoMapping.title = header;
        if (lowerHeader.includes('desc')) autoMapping.description = header;
        if (lowerHeader.includes('price') || lowerHeader.includes('cost')) autoMapping.price = header;
        if (lowerHeader.includes('category') || lowerHeader.includes('cat')) autoMapping.category = header;
        if (lowerHeader.includes('brand')) autoMapping.brand = header;
        if (lowerHeader.includes('condition') || lowerHeader.includes('cond')) autoMapping.condition = header;
        if (lowerHeader.includes('size')) autoMapping.size = header;
        if (lowerHeader.includes('color') || lowerHeader.includes('colour')) autoMapping.color = header;
        if (lowerHeader.includes('material')) autoMapping.material = header;
        if (lowerHeader.includes('year')) autoMapping.year = header;
        if (lowerHeader.includes('original') && lowerHeader.includes('price')) autoMapping.originalPrice = header;
        if (lowerHeader.includes('sku') || lowerHeader.includes('id')) autoMapping.sku = header;
        if (lowerHeader.includes('ownership') || lowerHeader.includes('type')) autoMapping.ownershipType = header;
        if (lowerHeader.includes('source')) autoMapping.source = header;
        if (lowerHeader.includes('cost') && lowerHeader.includes('paid')) autoMapping.costPaid = header;
        if (lowerHeader.includes('payment') || lowerHeader.includes('method')) autoMapping.paymentMethod = header;
        if (lowerHeader.includes('tags') || lowerHeader.includes('tag')) autoMapping.tags = header;
        if (lowerHeader.includes('weight')) autoMapping.weight = header;
        if (lowerHeader.includes('shipping')) autoMapping.shippingCost = header;
        if (lowerHeader.includes('length')) autoMapping.length = header;
        if (lowerHeader.includes('width')) autoMapping.width = header;
        if (lowerHeader.includes('height')) autoMapping.height = header;
      });

      setFieldMapping(autoMapping);
      setShowFieldMapping(true);

    } catch (error) {
      console.error('Error reading file:', error);
      toast.error('Failed to read file. Please check the format and try again.');
    }
  };

  const handleUpload = async () => {
    if (!hasValidData || products.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const result = await bulkCreateProducts({ products });
      
      if (result.success) {
        toast.success(result.message);
        setIsOpen(false);
        setProducts([]);
        setValidationErrors([]);
        setHasValidData(false);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        // Call the success callback to refresh inventory
        onSuccess?.();
      } else {
        toast.error('Failed to create products');
      }
    } catch (error: any) {
      toast.error(`Upload failed: ${error.message}`);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const downloadTemplate = () => {
    const template = `title,description,price,category,brand,condition,size,color,material,year,originalPrice,sku,ownershipType,source,costPaid,paymentMethod,tags,weight,shippingCost,length,width,height
"Chanel Classic Flap Bag","Authentic Chanel Classic Flap Bag in black caviar leather with gold hardware",8500,handbags,Chanel,excellent,Medium,Black,Caviar Leather,2022,9500,CH-CF-BLK-001,owned,Boutique Purchase,7500,Credit Card,"chanel;classic;flap;black;caviar;luxury;handbag",0.8,50,25,8,16
"Rolex Submariner Date","Stainless steel Rolex Submariner with date function and black dial",12000,watches,Rolex,like_new,40mm,Black,Stainless Steel,2021,13500,ROLEX-SUB-001,owned,Authorized Dealer,11000,Credit Card,"rolex;submariner;stainless;steel;luxury;watch",0.15,25,40,40,12
"Supreme Box Logo Hoodie","Classic Supreme box logo hoodie in black",800,clothing,Supreme,good,L,Black,Cotton,2020,1200,SUP-BOGO-001,owned,Resale Shop,600,Cash,"supreme;box;logo;hoodie;streetwear",0.5,15,28,22,2`;
    
    const blob = new Blob([template], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'inventory_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const parseProductsWithMapping = async () => {
    if (!csvHeaders.length) return;

    try {
      const file = fileInputRef.current?.files?.[0];
      if (!file) return;

      const text = await file.text();
      const lines = text.split('\n');
      
      const parsedProducts: ProductData[] = [];
      const errors: ValidationError[] = [];

      for (let i = 1; i < lines.length; i++) {
        const line = lines[i]?.trim();
        if (!line) continue;

        // Handle quoted values properly
        const values: string[] = [];
        let currentValue = '';
        let inQuotes = false;
        
        for (let j = 0; j < line.length; j++) {
          const char = line[j];
          if (char === '"') {
            inQuotes = !inQuotes;
          } else if (char === ',' && !inQuotes) {
            values.push(currentValue.trim());
            currentValue = '';
          } else {
            currentValue += char;
          }
        }
        values.push(currentValue.trim());

        const product: any = {};

        // Map CSV columns to product fields using field mapping
        Object.entries(fieldMapping).forEach(([field, csvHeader]) => {
          if (!csvHeader || csvHeader === "unmapped") return;
          
          const headerIndex = csvHeaders.indexOf(csvHeader);
          if (headerIndex === -1) return;
          
          const value = values[headerIndex] || '';
          const cleanValue = value.replace(/"/g, '').trim();
          
          switch (field) {
            case 'title':
            case 'description':
            case 'brand':
            case 'size':
            case 'color':
            case 'material':
            case 'sku':
              product[field] = cleanValue;
              break;
            case 'price':
            case 'originalPrice':
            case 'year':
            case 'weight':
            case 'shippingCost':
              const numValue = parseFloat(cleanValue);
              if (!isNaN(numValue)) {
                product[field] = numValue;
              }
              break;
            case 'category':
              const category = cleanValue.toLowerCase();
              if (['clothing', 'sneakers', 'collectibles', 'accessories', 'handbags', 'jewelry', 'watches', 'sunglasses'].includes(category)) {
                product.category = category as ProductData['category'];
              } else {
                errors.push({ row: i + 1, field: 'category', message: `Invalid category: ${cleanValue}. Must be one of: clothing, sneakers, collectibles, accessories, handbags, jewelry, watches, sunglasses` });
              }
              break;
            case 'condition':
              const condition = cleanValue.toLowerCase();
              if (['new', 'like_new', 'excellent', 'very_good', 'good', 'fair'].includes(condition)) {
                product.condition = condition as ProductData['condition'];
              } else {
                errors.push({ row: i + 1, field: 'condition', message: `Invalid condition: ${cleanValue}. Must be one of: new, like_new, excellent, very_good, good, fair` });
              }
              break;
            case 'ownershipType':
              const ownershipType = cleanValue.toLowerCase();
              if (['owned', 'consigned'].includes(ownershipType)) {
                product.ownershipType = ownershipType as ProductData['ownershipType'];
              } else {
                errors.push({ row: i + 1, field: 'ownershipType', message: `Invalid ownership type: ${cleanValue}. Must be one of: owned, consigned` });
              }
              break;
            case 'tags':
              product.tags = cleanValue ? cleanValue.split(';').map(tag => tag.trim()).filter(tag => tag.length > 0) : [];
              break;
            case 'source':
              if (!product.sourceInfo) product.sourceInfo = {};
              (product.sourceInfo as any).source = cleanValue;
              break;
            case 'costPaid':
              const costValue = parseFloat(cleanValue);
              if (!isNaN(costValue)) {
                if (!product.sourceInfo) product.sourceInfo = {};
                (product.sourceInfo as any).costPaid = costValue;
              }
              break;
            case 'paymentMethod':
              if (!product.sourceInfo) product.sourceInfo = {};
              (product.sourceInfo as any).paymentMethod = cleanValue;
              break;
            case 'length':
            case 'width':
            case 'height':
              if (!product.dimensions) product.dimensions = {};
              const dimValue = parseFloat(cleanValue);
              if (!isNaN(dimValue)) {
                (product.dimensions as any)[field] = dimValue;
              }
              break;
          }
        });

        // Validate required fields
        if (!product.title || product.title.length < 3) {
          errors.push({ row: i + 1, field: 'title', message: 'Title must be at least 3 characters' });
        }
        if (!product.price || product.price <= 0) {
          errors.push({ row: i + 1, field: 'price', message: 'Price must be greater than 0' });
        }
        if (!product.category) {
          errors.push({ row: i + 1, field: 'category', message: 'Category is required' });
        }
        if (!product.brand) {
          errors.push({ row: i + 1, field: 'brand', message: 'Brand is required' });
        }
        if (!product.condition) {
          errors.push({ row: i + 1, field: 'condition', message: 'Condition is required' });
        }

        // Set defaults
        if (!product.ownershipType) product.ownershipType = 'owned';
        if (!product.description) product.description = `${product.brand} ${product.title}`;

        parsedProducts.push(product as ProductData);
      }

      setProducts(parsedProducts);
      setValidationErrors(errors);
      setHasValidData(parsedProducts.length > 0 && errors.length === 0);
      setShowFieldMapping(false);

      if (errors.length > 0) {
        toast.error(`Found ${errors.length} validation errors. Please fix them before uploading.`);
      } else {
        toast.success(`Successfully parsed ${parsedProducts.length} products from CSV`);
      }

    } catch (error) {
      console.error('Error parsing products:', error);
      toast.error('Failed to parse products. Please check your field mapping.');
    }
  };

  const clearData = () => {
    setProducts([]);
    setValidationErrors([]);
    setHasValidData(false);
    setCsvHeaders([]);
    setShowFieldMapping(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="rounded-xl font-light">
          <Upload className="w-4 h-4 mr-2" />
          BULK UPLOAD
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="w-5 h-5" />
            Bulk Upload Inventory
          </DialogTitle>
          <DialogDescription>
            Upload a CSV file to create multiple products at once. Products will be created as drafts without images.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Download */}
          <div className="bg-primary/5 rounded-xl p-4 border border-border">
            <div className="flex items-center gap-2 mb-3">
              <Info className="w-4 h-4 text-primary" />
              <h4 className="font-medium">CSV Template</h4>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              Download our CSV template to ensure proper formatting. Required fields: title, price, category, brand, condition.
            </p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={downloadTemplate}
              className="rounded-xl font-light"
            >
              <Download className="w-4 h-4 mr-2" />
              Download Template
            </Button>
          </div>

          {/* File Upload */}
          <div className="space-y-4">
            <Label htmlFor="csv-file">Upload CSV File</Label>
            <Input
              id="csv-file"
              ref={fileInputRef}
              type="file"
              accept=".csv,.txt"
              onChange={handleFileUpload}
              className="rounded-xl"
              disabled={isUploading}
              placeholder="Select a CSV file to upload"
            />
            <p className="text-xs text-muted-foreground">
              Supported formats: CSV, TXT. Maximum file size: 10MB
            </p>
          </div>

          {/* Field Mapping */}
          {showFieldMapping && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Map CSV Fields to Product Fields</h4>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={parseProductsWithMapping}
                  className="rounded-xl font-light"
                >
                  Parse Products
                </Button>
              </div>
              
              <div className="grid grid-cols-2 gap-4 max-h-80 overflow-y-auto border border-border rounded-xl p-4">
                {Object.entries(fieldMapping).map(([field, csvHeader]) => (
                  <div key={field} className="space-y-2">
                    <Label className="text-sm font-medium capitalize">
                      {field.replace(/([A-Z])/g, ' $1').trim()}
                      {['title', 'price', 'category', 'brand', 'condition'].includes(field) && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </Label>
                    <Select 
                      value={csvHeader || "unmapped"} 
                      onValueChange={(value) => setFieldMapping(prev => ({ ...prev, [field]: value === "unmapped" ? "" : value }))}
                    >
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="Select CSV column" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="unmapped">-- Not Used --</SelectItem>
                        {csvHeaders.map(header => (
                          <SelectItem key={header} value={header}>
                            {header}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                ))}
              </div>
              
              <div className="bg-primary/5 rounded-xl p-3 border border-border">
                <div className="flex items-center gap-2 mb-2">
                  <Info className="w-4 h-4 text-primary" />
                  <span className="text-sm font-medium">Field Mapping Tips</span>
                </div>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Required fields are marked with <span className="text-red-500">*</span></li>
                  <li>• Map CSV columns to the appropriate product fields</li>
                  <li>• Leave unused fields as "-- Not Used --"</li>
                  <li>• Click "Parse Products" when ready to continue</li>
                </ul>
              </div>
            </div>
          )}

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="bg-destructive/5 border border-destructive/20 rounded-xl p-4">
              <div className="flex items-center gap-2 mb-3">
                <AlertCircle className="w-4 h-4 text-destructive" />
                <h4 className="font-medium text-destructive">Validation Errors</h4>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {validationErrors.map((error, index) => (
                  <div key={index} className="text-sm text-destructive">
                    <span className="font-medium">Row {error.row}:</span> {error.field} - {error.message}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Products Preview */}
          {products.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Products to Upload ({products.length})</h4>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={clearData}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear
                </Button>
              </div>
              <div className="max-h-60 overflow-y-auto border border-border rounded-xl">
                <div className="grid grid-cols-1 gap-2 p-4">
                  {products.slice(0, 10).map((product, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-primary/5 rounded-lg border border-border">
                      <div className="flex-1">
                        <div className="font-medium text-sm">{product.title}</div>
                        <div className="text-xs text-muted-foreground">
                          {product.brand} • {product.category} • ${product.price}
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {product.condition}
                      </Badge>
                    </div>
                  ))}
                  {products.length > 10 && (
                    <div className="text-center text-sm text-muted-foreground py-2">
                      ... and {products.length - 10} more products
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Upload Progress */}
          {isUploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Uploading products...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => setIsOpen(false)}
            disabled={isUploading}
            className="rounded-xl font-light"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleUpload}
            disabled={!hasValidData || isUploading}
            className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light"
          >
            {isUploading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="w-4 h-4 mr-2" />
                Upload {products.length} Products
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
