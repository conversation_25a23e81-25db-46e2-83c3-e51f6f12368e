"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Textarea } from "@repo/ui/components/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Star, MessageSquare, Edit, Trash2, Package, Calendar, User } from "lucide-react";
import Image from "next/image";
import { useAuth } from "@/hooks/useBetterAuth";

export function UserReviewManagement() {
  const { user } = useAuth();
  const [editingReview, setEditingReview] = useState<{
    id: Id<"reviews">;
    rating: number;
    review: string;
  } | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Get user's reviewable orders
  const reviewableOrders = useQuery(api.sellerReviews.getReviewableOrders, {
    sellerId: user?._id as Id<"users">,
  });

  // Get user's existing reviews
  const userReviews = useQuery(api.sellerReviews.getSellerReviews, {
    sellerId: user?._id as Id<"users">,
    limit: 50,
    verifiedOnly: true,
  });

  // Mutations
  const updateReview = useMutation(api.sellerReviews.updateSellerReview);
  const deleteReview = useMutation(api.sellerReviews.deleteSellerReview);

  const handleEditReview = async () => {
    if (!editingReview) return;

    try {
      await updateReview({
        reviewId: editingReview.id,
        rating: editingReview.rating,
        review: editingReview.review,
      });

      setEditingReview(null);
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Failed to update review:", error);
    }
  };

  const handleDeleteReview = async (reviewId: Id<"reviews">) => {
    if (!confirm("Are you sure you want to delete this review?")) return;

    try {
      await deleteReview({ reviewId });
    } catch (error) {
      console.error("Failed to delete review:", error);
    }
  };

  const renderStars = (rating: number, interactive = false, size: "sm" | "md" | "lg" = "md") => {
    const sizeClasses = {
      sm: "w-3 h-3",
      md: "w-4 h-4",
      lg: "w-5 h-5",
    };

    return (
      <div className="flex items-center space-x-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? "text-yellow-500 fill-current"
                : "text-gray-300 dark:text-gray-600"
            } ${interactive ? "cursor-pointer hover:scale-110 transition-transform" : ""}`}
            onClick={() => interactive && setEditingReview(prev => prev ? { ...prev, rating: star } : null)}
          />
        ))}
      </div>
    );
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h4 className="text-lg font-medium mb-2">Sign In Required</h4>
          <p className="text-muted-foreground">
            Please sign in to manage your reviews.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-muted-foreground" />
          <h3 className="text-lg font-semibold">My Reviews</h3>
        </div>
      </div>

      {/* Reviewable Orders */}
      {reviewableOrders?.orders && reviewableOrders.orders.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Orders Ready for Review</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {reviewableOrders.orders.map((order) => (
                <div key={order._id} className="flex items-center space-x-3 p-3 border rounded-lg">
                  {order.product?.images?.[0] && (
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-muted">
                      <Image
                        src={order.product.images[0]}
                        alt={order.product.title}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm truncate">
                      {order.product?.title || "Product"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Order #{order.orderNumber} • {formatDate(order.deliveredDate || 0)}
                    </p>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    <Package className="w-3 h-3 mr-1" />
                    Ready to Review
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Existing Reviews */}
      {userReviews?.reviews && userReviews.reviews.length > 0 ? (
        <div className="space-y-4">
          {userReviews.reviews.map((review) => (
            <Card key={review._id}>
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  {/* Product Info */}
                  <div className="flex-shrink-0">
                    {review.product?.images?.[0] ? (
                      <Image
                        src={review.product.images[0]}
                        alt={review.product.title}
                        width={60}
                        height={60}
                        className="w-15 h-15 rounded-lg object-cover"
                      />
                    ) : (
                      <div className="w-15 h-15 rounded-lg bg-muted flex items-center justify-center">
                        <Package className="w-6 h-6 text-muted-foreground" />
                      </div>
                    )}
                  </div>

                  {/* Review Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">{review.product?.title || "Product"}</span>
                        {review.product?.brand && (
                          <span className="text-xs text-muted-foreground">by {review.product.brand}</span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setEditingReview({
                                id: review._id,
                                rating: review.rating,
                                review: review.review,
                              })}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Edit Review</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <label className="text-sm font-medium">Rating</label>
                                <div className="flex items-center space-x-4 mt-2">
                                  {renderStars(editingReview?.rating || 5, true, "lg")}
                                  <span className="text-sm text-muted-foreground">
                                    {editingReview?.rating || 5} out of 5 stars
                                  </span>
                                </div>
                              </div>
                              <div>
                                <label className="text-sm font-medium">Review</label>
                                <Textarea
                                  value={editingReview?.review || ""}
                                  onChange={(e) => setEditingReview(prev => prev ? { ...prev, review: e.target.value } : null)}
                                  rows={4}
                                  maxLength={1000}
                                />
                                <div className="text-xs text-muted-foreground mt-1">
                                  {editingReview?.review?.length || 0}/1000 characters
                                </div>
                              </div>
                              <div className="flex justify-end space-x-3">
                                <Button
                                  variant="outline"
                                  onClick={() => setIsEditDialogOpen(false)}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  onClick={handleEditReview}
                                  disabled={
                                    !editingReview?.review ||
                                    editingReview.review.trim().length < 10 ||
                                    editingReview.review.trim().length > 1000
                                  }
                                >
                                  Update Review
                                </Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteReview(review._id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      {renderStars(review.rating, false, "sm")}
                      <span className="text-sm text-muted-foreground">
                        {formatDate(review.reviewDate)}
                      </span>
                    </div>

                    <p className="text-sm text-foreground">{review.review}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h4 className="text-lg font-medium mb-2">No Reviews Yet</h4>
            <p className="text-muted-foreground mb-4">
              You haven't written any reviews yet. Start reviewing sellers after your purchases are delivered!
            </p>
          </CardContent>
        </Card>
      )}

      {/* Summary Stats */}
      {userReviews?.ratingStats && userReviews.ratingStats.total > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Review Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-semibold">{userReviews.ratingStats.total}</div>
                <div className="text-sm text-muted-foreground">Total Reviews</div>
              </div>
              <div>
                <div className="text-2xl font-semibold">{userReviews.ratingStats.average.toFixed(1)}</div>
                <div className="text-sm text-muted-foreground">Average Rating</div>
              </div>
              <div>
                <div className="text-2xl font-semibold">{reviewableOrders?.total || 0}</div>
                <div className="text-sm text-muted-foreground">Ready to Review</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
