"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Label } from "@repo/ui/components/label";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  FileCheck, 
  Upload, 
  X, 
  ArrowRight, 
  ArrowLeft, 
  CheckCircle, 
  AlertCircle,
  FileText,
  Image,
  Shield
} from "lucide-react";

interface ApplicationData {
  idDocument?: File;
  businessDocuments: File[];
}

interface VerificationStepProps {
  data: ApplicationData;
  onUpdate: (data: Partial<ApplicationData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

const ACCEPTED_ID_TYPES = [
  "Driver's License",
  "Passport",
  "State ID Card",
  "Government-issued ID"
];

const ACCEPTED_BUSINESS_DOCS = [
  "Business License",
  "Articles of Incorporation",
  "Operating Agreement",
  "Tax Registration Certificate",
  "Bank Statement",
  "Utility Bill (for address verification)"
];

export function VerificationStep({ 
  data, 
  onUpdate, 
  onNext, 
  onPrev 
}: VerificationStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [dragOver, setDragOver] = useState<string | null>(null);
  const idFileInputRef = useRef<HTMLInputElement>(null);
  const businessFileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File, type: "id" | "business") => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      "image/jpeg",
      "image/png", 
      "image/webp",
      "application/pdf"
    ];

    if (!allowedTypes.includes(file.type)) {
      return "Please upload a valid image (JPEG, PNG, WebP) or PDF file";
    }

    if (file.size > maxSize) {
      return "File size must be less than 10MB";
    }

    return "";
  };

  const handleIdFileUpload = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    const error = validateFile(file as File, "id");
    
    if (error) {
      setErrors(prev => ({ ...prev, idDocument: error }));
      return;
    }
    
    setErrors(prev => ({ ...prev, idDocument: "" }));
    onUpdate({ idDocument: file as any });
  };

  const handleBusinessFileUpload = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const newFiles = Array.from(files);
    const validFiles: File[] = [];
    const fileErrors: string[] = [];
    
    newFiles.forEach(file => {
      const error = validateFile(file, "business");
      if (error) {
        fileErrors.push(`${file.name}: ${error}`);
      } else {
        validFiles.push(file);
      }
    });
    
    if (fileErrors.length > 0) {
      setErrors(prev => ({ ...prev, businessDocuments: fileErrors.join(", ") }));
      return;
    }
    
    setErrors(prev => ({ ...prev, businessDocuments: "" }));
    onUpdate({ businessDocuments: [...data.businessDocuments, ...validFiles] as any });
  };

  const removeBusinessDocument = (index: number) => {
    const updatedDocs = data.businessDocuments.filter((_: File, i: number) => i !== index);
    onUpdate({ businessDocuments: updatedDocs });
  };

  const handleDragOver = (e: React.DragEvent, type: string) => {
    e.preventDefault();
    setDragOver(type);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(null);
  };

  const handleDrop = (e: React.DragEvent, type: "id" | "business") => {
    e.preventDefault();
    setDragOver(null);
    
    if (type === "id") {
      handleIdFileUpload(e.dataTransfer.files);
    } else {
      handleBusinessFileUpload(e.dataTransfer.files);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!data.idDocument) {
      newErrors.idDocument = "Please upload a valid ID document";
    }
    
    if (data.businessDocuments.length === 0) {
      newErrors.businessDocuments = "Please upload at least one business document";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className="p-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900 dark:to-orange-800 rounded-2xl flex items-center justify-center mx-auto">
          <FileCheck className="w-8 h-8 text-orange-600 dark:text-orange-400" />
        </div>
        <div>
          <h2 className="text-3xl font-bold text-black dark:text-white mb-2">
            Identity Verification
          </h2>
          <p className="text-lg text-neutral-600 dark:text-neutral-400">
            Upload your documents for identity and business verification
          </p>
        </div>
      </div>

      {/* Form Fields */}
      <div className="space-y-8 max-w-3xl mx-auto">
        {/* ID Document Upload */}
        <div className="space-y-4">
          <div>
            <Label className="text-base font-semibold text-black dark:text-white">
              Government-Issued ID *
            </Label>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
              Upload a clear photo of your government-issued identification
            </p>
          </div>

          <div
            className={`border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-200 cursor-pointer ${
              dragOver === "id"
                ? "border-blue-400 bg-blue-50 dark:bg-blue-900/20"
                : data.idDocument
                ? "border-green-400 bg-green-50 dark:bg-green-900/20"
                : errors.idDocument
                ? "border-red-400 bg-red-50 dark:bg-red-900/20"
                : "border-neutral-300 hover:border-neutral-400 hover:bg-neutral-50 dark:hover:bg-neutral-800"
            }`}
            onDragOver={(e) => handleDragOver(e, "id")}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, "id")}
            onClick={() => idFileInputRef.current?.click()}
          >
            <input
              ref={idFileInputRef}
              type="file"
              accept="image/*,.pdf"
              onChange={(e) => handleIdFileUpload(e.target.files)}
              className="hidden"
            />
            
            {data.idDocument ? (
              <div className="space-y-3">
                <CheckCircle className="w-12 h-12 text-green-600 mx-auto" />
                <div>
                  <p className="font-semibold text-green-800 dark:text-green-300">
                    {data.idDocument.name}
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <Upload className="w-12 h-12 text-neutral-400 mx-auto" />
                <div>
                  <p className="font-semibold text-black dark:text-white">
                    Drop your ID here or click to browse
                  </p>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400">
                    Supports: JPEG, PNG, WebP, PDF (max 10MB)
                  </p>
                </div>
              </div>
            )}
          </div>

          {errors.idDocument && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.idDocument}
            </p>
          )}

          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {ACCEPTED_ID_TYPES.map((type) => (
              <Badge key={type} variant="outline" className="text-xs py-1">
                {type}
              </Badge>
            ))}
          </div>
        </div>

        {/* Business Documents Upload */}
        <div className="space-y-4">
          <div>
            <Label className="text-base font-semibold text-black dark:text-white">
              Business Documents *
            </Label>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
              Upload documents that verify your business (at least one required)
            </p>
          </div>

          <div
            className={`border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-200 cursor-pointer ${
              dragOver === "business"
                ? "border-blue-400 bg-blue-50 dark:bg-blue-900/20"
                : data.businessDocuments.length > 0
                ? "border-green-400 bg-green-50 dark:bg-green-900/20"
                : errors.businessDocuments
                ? "border-red-400 bg-red-50 dark:bg-red-900/20"
                : "border-neutral-300 hover:border-neutral-400 hover:bg-neutral-50 dark:hover:bg-neutral-800"
            }`}
            onDragOver={(e) => handleDragOver(e, "business")}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, "business")}
            onClick={() => businessFileInputRef.current?.click()}
          >
            <input
              ref={businessFileInputRef}
              type="file"
              accept="image/*,.pdf"
              multiple
              onChange={(e) => handleBusinessFileUpload(e.target.files)}
              className="hidden"
            />
            
            <div className="space-y-3">
              <Upload className="w-12 h-12 text-neutral-400 mx-auto" />
              <div>
                <p className="font-semibold text-black dark:text-white">
                  Drop business documents here or click to browse
                </p>
                <p className="text-sm text-neutral-500 dark:text-neutral-400">
                  Multiple files supported • JPEG, PNG, WebP, PDF (max 10MB each)
                </p>
              </div>
            </div>
          </div>

          {/* Uploaded Business Documents */}
          {data.businessDocuments.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-semibold text-black dark:text-white">
                Uploaded Documents ({data.businessDocuments.length})
              </h4>
              <div className="space-y-2">
                {data.businessDocuments.map((file: File, index: number) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl"
                  >
                    <div className="flex items-center space-x-3">
                      {file.type.startsWith("image/") ? (
                        <Image className="w-5 h-5 text-green-600 dark:text-green-400" />
                      ) : (
                        <FileText className="w-5 h-5 text-green-600 dark:text-green-400" />
                      )}
                      <div>
                        <p className="font-medium text-green-800 dark:text-green-300 text-sm">
                          {file.name}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeBusinessDocument(index);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-100 dark:hover:bg-red-900/20"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {errors.businessDocuments && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.businessDocuments}
            </p>
          )}

          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {ACCEPTED_BUSINESS_DOCS.map((doc) => (
              <Badge key={doc} variant="outline" className="text-xs py-1">
                {doc}
              </Badge>
            ))}
          </div>
        </div>

        {/* Security Notice */}
        <Card className="bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800 p-6">
          <div className="flex items-start space-x-4">
            <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-xl flex items-center justify-center flex-shrink-0">
              <Shield className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900 dark:text-orange-300 mb-2">
                Document Security & Privacy
              </h3>
              <ul className="text-sm text-orange-800 dark:text-orange-400 space-y-1">
                <li>• All documents are encrypted and stored securely</li>
                <li>• Documents are only used for verification purposes</li>
                <li>• We never share your documents with third parties</li>
                <li>• Documents are automatically deleted after verification</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6 max-w-3xl mx-auto">
        <Button
          onClick={onPrev}
          variant="outline"
          className="h-14 px-8 text-lg font-semibold rounded-xl border-2"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Previous
        </Button>
        
        <Button
          onClick={handleNext}
          className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 h-14 px-8 text-lg font-semibold rounded-xl"
        >
          Continue
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>
      </div>
    </div>
  );
}
