"use client";

import { useState, useMemo, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { FilterSidebar } from "../marketplace/FilterSidebar";
import { ProductGrid } from "../marketplace/ProductGrid";
import { MarketplaceHeader } from "../marketplace/MarketplaceHeader";
import { LandingHeader } from "../landing/LandingHeader";
import { AuthModal } from "../auth/auth-modal";
import { SellerProfileHeader } from "./SellerProfileHeader";
import type { FilterState } from "../marketplace/MarketplaceContent";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useBetterAuth";

interface PaginationInfo {
  hasMore: boolean;
  total: number;
  page: number;
  totalPages: number;
  nextCursor: string | null;
}

const initialFilters: FilterState = {
  categories: [],
  priceRange: [null, null],
  brands: [],
  conditions: [],
  searchQuery: "",
};

interface SellerProfileContentProps {
  sellerId: string;
}

export function SellerProfileContent({ sellerId }: SellerProfileContentProps) {
  const [filters, setFilters] = useState<FilterState>(initialFilters);
  const [sortBy, setSortBy] = useState<"newest" | "price_low" | "price_high" | "popular">("newest");
  const [currentPage, setCurrentPage] = useState(0);
  const [allProducts, setAllProducts] = useState<any[]>([]);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<"login" | "signup">("signup");
  const ITEMS_PER_PAGE = 24;
  const pathname = usePathname();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  // Validate that sellerId is a valid user ID format
  // Convex user IDs are typically 25 characters long and contain alphanumeric characters
  const isValidUserId = sellerId && 
    sellerId.length > 0 && 
    sellerId.length === 25 && 
    /^[a-zA-Z0-9]+$/.test(sellerId) &&
    sellerId !== "inventory" && // Prevent common invalid IDs
    sellerId !== "dashboard" &&
    sellerId !== "products" &&
    sellerId !== "sales" &&
    sellerId !== "reports" &&
    sellerId !== "messages" &&
    sellerId !== "invoices";
  
  // Fetch seller profile
  const sellerProfile = useQuery(
    api.sellerQueries.getPublicSellerProfile, 
    isValidUserId ? { sellerId: sellerId as Id<"users"> } : "skip"
  );

  // Fetch seller's products with filters and pagination
  const productsResult = useQuery(
    api.sellerQueries.getSellerMarketplaceProducts, 
    isValidUserId ? {
      sellerId: sellerId as Id<"users">,
    category: filters.categories.length === 1 ? filters.categories[0] : undefined,
    minPrice: filters.priceRange[0] !== null ? filters.priceRange[0] : undefined,
    maxPrice: filters.priceRange[1] !== null ? filters.priceRange[1] : undefined,
    brands: filters.brands.length > 0 ? filters.brands : undefined,
    conditions: filters.conditions.length > 0 ? filters.conditions : undefined,
    searchQuery: filters.searchQuery || undefined,
      sortBy,
      limit: ITEMS_PER_PAGE,
      offset: currentPage * ITEMS_PER_PAGE,
    } : "skip"
  );

  // Get filter options (use marketplace filter options)
  const filterOptions = useQuery(api.productQueries.getAggregatedFilterOptions);

  // LandingHeader handlers
  const handleGetAccess = () => {
    setAuthModalMode("signup");
    setIsAuthModalOpen(true);
  };

  const handleSignIn = () => {
    setAuthModalMode("login");
    setIsAuthModalOpen(true);
  };

  const handleSellerApplication = () => {
    setAuthModalMode("signup");
    setIsAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
  };

  // Update allProducts when new results come in
  useEffect(() => {
    if (productsResult?.products) {
      if (currentPage === 0) {
        setAllProducts(productsResult.products);
      } else {
        setAllProducts(prev => [...prev, ...productsResult.products]);
      }
    }
  }, [productsResult?.products, currentPage]);

  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(0);
    setAllProducts([]);
  };

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }));
    setCurrentPage(0);
    setAllProducts([]);
  };

  const clearFilters = () => {
    setFilters(initialFilters);
    setCurrentPage(0);
    setAllProducts([]);
  };

  const handleSortChange = (newSort: "newest" | "price_low" | "price_high" | "popular") => {
    setSortBy(newSort);
    setCurrentPage(0);
    setAllProducts([]);
  };

  const handleLoadMore = () => {
    if (productsResult?.pagination?.hasMore) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const activeFilterCount = useMemo(() => {
    return (
      filters.categories.length +
      filters.brands.length +
      filters.conditions.length +
      (filters.searchQuery ? 1 : 0) +
      (filters.priceRange[0] !== null || filters.priceRange[1] !== null ? 1 : 0)
    );
  }, [filters]);

  if (sellerProfile === undefined) {
    return (
      <div className="bg-card min-h-screen">
        {isAuthenticated ? (
          <MarketplaceHeader onSearch={handleSearch} />
        ) : (
          <LandingHeader
            onGetAccess={handleGetAccess}
            onSignIn={handleSignIn}
            onSellerApplication={handleSellerApplication}
          />
        )}
        <div className="container mx-auto px-6 py-12">
          <div className="animate-pulse">
            <div className="h-32 bg-muted rounded-xl mb-6"></div>
            <div className="h-8 bg-muted rounded mb-4 w-1/3"></div>
            <div className="h-4 bg-muted rounded mb-2 w-1/2"></div>
            <div className="h-4 bg-muted rounded mb-8 w-1/4"></div>
          </div>
        </div>
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={closeAuthModal}
          defaultMode={authModalMode}
        />
      </div>
    );
  }

  if (!isValidUserId) {
    return (
      <div className="bg-card min-h-screen">
        {isAuthenticated ? (
          <MarketplaceHeader onSearch={handleSearch} />
        ) : (
          <LandingHeader
            onGetAccess={handleGetAccess}
            onSignIn={handleSignIn}
            onSellerApplication={handleSellerApplication}
          />
        )}
        <div className="container mx-auto px-6 py-12 text-center">
          <h1 className="text-2xl font-medium text-foreground mb-4">Invalid Seller ID</h1>
          <p className="text-muted-foreground mb-4">
            The seller ID "{sellerId}" is not valid. Seller IDs must be valid Convex user identifiers.
          </p>
          <p className="text-muted-foreground mb-6">
            If you're looking for your seller dashboard, try{" "}
            <a href="/seller/dashboard" className="text-primary hover:underline">/seller/dashboard</a> instead.
          </p>
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Common seller routes:</p>
            <div className="flex flex-wrap justify-center gap-2">
              <a href="/seller/dashboard" className="text-primary hover:underline">Dashboard</a>
              <a href="/seller/products" className="text-primary hover:underline">Products</a>
              <a href="/seller/sales" className="text-primary hover:underline">Sales</a>
              <a href="/seller/reports" className="text-primary hover:underline">Reports</a>
            </div>
          </div>
        </div>
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={closeAuthModal}
          defaultMode={authModalMode}
        />
      </div>
    );
  }

  if (!sellerProfile) {
    return (
      <div className="bg-card min-h-screen">
        {isAuthenticated ? (
          <MarketplaceHeader onSearch={handleSearch} />
        ) : (
          <LandingHeader
            onGetAccess={handleGetAccess}
            onSignIn={handleSignIn}
            onSellerApplication={handleSellerApplication}
          />
        )}
        <div className="container mx-auto px-6 py-12 text-center">
          <h1 className="text-2xl font-medium text-foreground mb-4">Seller Not Found</h1>
          <p className="text-muted-foreground">The seller you're looking for doesn't exist or has been deactivated.</p>
        </div>
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={closeAuthModal}
          defaultMode={authModalMode}
        />
      </div>
    );
  }

  return (
    <div className="bg-card min-h-screen">
      {isAuthenticated ? (
        <MarketplaceHeader onSearch={handleSearch} />
      ) : (
        <LandingHeader
          onGetAccess={handleGetAccess}
          onSignIn={handleSignIn}
          onSellerApplication={handleSellerApplication}
        />
      )}
      
      <SellerProfileHeader sellerId={sellerId} pathname={pathname} />

      {/* Products Section */}
      <div className="container mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* Filter Sidebar - Desktop */}
          <div className="hidden md:block w-72 flex-shrink-0">
            <FilterSidebar
              filters={filters}
              filterOptions={filterOptions}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
              activeFilterCount={activeFilterCount}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Mobile Filter Button */}
            <div className="md:hidden mb-6">
              <FilterSidebar
                filters={filters}
                filterOptions={filterOptions}
                onFilterChange={handleFilterChange}
                onClearFilters={clearFilters}
                activeFilterCount={activeFilterCount}
              />
            </div>

            {/* Products Grid */}
            <div className="mb-6">
              <h2 className="text-2xl font-light text-foreground mb-4">
                {sellerProfile.businessName || sellerProfile.name}'s Collection
              </h2>
              <p className="text-muted-foreground">
                Browse {productsResult?.pagination?.total || allProducts.length} luxury items from this seller
              </p>
            </div>
            
            <ProductGrid
              products={allProducts}
              sortBy={sortBy}
              onSortChange={handleSortChange}
              isLoading={productsResult === undefined}
              activeFilterCount={activeFilterCount}
              pagination={productsResult?.pagination as PaginationInfo}
              onPageChange={handleLoadMore}
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        defaultMode={authModalMode}
      />
    </div>
  );
}
