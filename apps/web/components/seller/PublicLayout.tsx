"use client"

import { <PERSON>actNode } from "react"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"

interface PublicLayoutProps {
  children: ReactNode
  showBackButton?: boolean
  backButtonText?: string
  backButtonHref?: string
}

export function PublicLayout({ 
  children, 
  showBackButton = true,
  backButtonText = "Back to Marketplace",
  backButtonHref = "/marketplace"
}: PublicLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {children}
    </div>
  )
}
