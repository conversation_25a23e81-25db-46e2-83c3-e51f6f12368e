"use client";

import { <PERSON>, <PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Heart, Share, ShoppingCart } from "lucide-react";
import Image from "next/image";

interface ProductPreviewProps {
  data: {
    title: string;
    description: string;
    price: number;
    originalPrice?: number;
    brand: string;
    condition: string;
    images: string[];
    size?: string;
    color?: string;
    material?: string;
    estimatedDeliveryDays?: number;
  };
}

export function ProductPreview({ data }: ProductPreviewProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case "new":
        return "default";
      case "like_new":
        return "secondary";
      case "excellent":
        return "outline";
      case "good":
        return "outline";
      case "fair":
        return "destructive";
      default:
        return "outline";
    }
  };

  const renderDescription = (text: string) => {
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" class="text-blue-600 underline">$1</a>')
      .replace(/\n• /g, '<br/>• ')
      .replace(/\n\d+\. /g, '<br/>1. ')
      .replace(/\n/g, '<br/>');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>Product Preview</span>
          <Badge variant="secondary">Customer View</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Images */}
          <div className="space-y-4">
            {data.images.length > 0 ? (
              <>
                <div className="aspect-square relative rounded-lg overflow-hidden bg-neutral-100 dark:bg-neutral-800">
                  <Image
                    src={data.images[0] || ""}
                    alt={data.title}
                    fill
                    className="object-cover"
                  />
                </div>
                
                {data.images.length > 1 && (
                  <div className="grid grid-cols-4 gap-2">
                    {data.images.slice(1, 5).map((image, index) => (
                      <div key={index} className="aspect-square relative rounded-md overflow-hidden bg-neutral-100 dark:bg-neutral-800">
                        <Image
                          src={image}
                          alt={`${data.title} ${index + 2}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </>
            ) : (
              <div className="aspect-square rounded-lg bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-neutral-400 mb-2">📷</div>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">No images uploaded</p>
                </div>
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
                {data.title || "Product Title"}
              </h1>
              <p className="text-lg text-neutral-600 dark:text-neutral-400">
                {data.brand || "Brand Name"}
              </p>
            </div>

            <div className="flex items-center gap-4">
              <div className="text-3xl font-bold text-neutral-900 dark:text-neutral-100">
                {formatCurrency(data.price || 0)}
              </div>
              {data.originalPrice && data.originalPrice > (data.price || 0) && (
                <div className="text-lg text-neutral-500 line-through">
                  {formatCurrency(data.originalPrice)}
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Badge variant={getConditionColor(data.condition)}>
                {data.condition?.replace('_', ' ').toUpperCase() || "CONDITION"}
              </Badge>
              {data.estimatedDeliveryDays && (
                <Badge variant="outline">
                  {data.estimatedDeliveryDays} day delivery
                </Badge>
              )}
            </div>

            {/* Product attributes */}
            {(data.size || data.color || data.material) && (
              <div className="space-y-2">
                {data.size && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Size:</span>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">{data.size}</span>
                  </div>
                )}
                {data.color && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Color:</span>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">{data.color}</span>
                  </div>
                )}
                {data.material && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Material:</span>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">{data.material}</span>
                  </div>
                )}
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-3">
              <Button className="flex-1">
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add to Cart
              </Button>
              <Button variant="outline" size="icon">
                <Heart className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Share className="h-4 w-4" />
              </Button>
            </div>

            {/* Description */}
            {data.description && (
              <div>
                <h3 className="font-semibold mb-2">Description</h3>
                <div 
                  className="text-sm text-neutral-600 dark:text-neutral-400 prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{ __html: renderDescription(data.description) }}
                />
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}