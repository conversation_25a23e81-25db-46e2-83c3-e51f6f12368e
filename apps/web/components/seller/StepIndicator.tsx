"use client";

import { Check } from "lucide-react";
import { cn } from "@repo/ui/lib/utils";

interface Step {
  id: number;
  title: string;
  description: string;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
}

export function StepIndicator({ steps, currentStep }: StepIndicatorProps) {
  return (
    <div className="flex items-center justify-between">
      {steps.map((step, index) => {
        const isCompleted = step.id < currentStep;
        const isCurrent = step.id === currentStep;
        const isUpcoming = step.id > currentStep;

        return (
          <div key={step.id} className="flex items-center">
            {/* Step Circle */}
            <div className="flex flex-col items-center">
              <div
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200",
                  {
                    "bg-green-600 text-white": isCompleted,
                    "bg-blue-600 text-white ring-4 ring-blue-100 dark:ring-blue-900": isCurrent,
                    "bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400": isUpcoming,
                  }
                )}
              >
                {isCompleted ? (
                  <Check className="w-5 h-5" />
                ) : (
                  step.id
                )}
              </div>
              
              {/* Step Info */}
              <div className="mt-2 text-center">
                <p
                  className={cn(
                    "text-xs font-medium",
                    {
                      "text-green-600 dark:text-green-400": isCompleted,
                      "text-blue-600 dark:text-blue-400": isCurrent,
                      "text-neutral-500 dark:text-neutral-400": isUpcoming,
                    }
                  )}
                >
                  {step.title}
                </p>
                <p className="text-xs text-neutral-400 dark:text-neutral-500 hidden sm:block">
                  {step.description}
                </p>
              </div>
            </div>

            {/* Connector Line */}
            {index < steps.length - 1 && (
              <div
                className={cn(
                  "flex-1 h-0.5 mx-4 transition-all duration-200",
                  {
                    "bg-green-600": step.id < currentStep,
                    "bg-neutral-200 dark:bg-neutral-700": step.id >= currentStep,
                  }
                )}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}