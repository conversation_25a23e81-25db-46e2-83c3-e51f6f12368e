"use client";

import React, { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Textarea } from "@repo/ui/components/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Star, Package, User, MessageSquare } from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";

interface ReviewFormProps {
  order: {
    _id: Id<"orders">;
    orderNumber: string;
    orderDate: number;
    deliveredDate: number | undefined;
    totalAmount: number;
    product: {
      _id: Id<"products">;
      title: string;
      brand: string;
      images: Id<"_storage">[];
    } | null;
    seller: {
      _id: Id<"users">;
      name: string;
      businessName?: string;
    } | null;
    hasProductReview: boolean;
    hasSellerReview: boolean;
  };
  onReviewSubmitted: () => void;
}

export function ReviewForm({ order, onReviewSubmitted }: ReviewFormProps) {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"product" | "seller">("product");
  
  // Product review state
  const [productRating, setProductRating] = useState(5);
  const [productReview, setProductReview] = useState("");
  
  // Seller review state
  const [sellerRating, setSellerRating] = useState(5);
  const [sellerReview, setSellerReview] = useState("");

  // Mutations
  const addProductReview = useMutation(api.sellerReviews.addProductReview);
  const addSellerReview = useMutation(api.sellerReviews.addSellerReview);

  const handleProductReviewSubmit = async () => {
    if (!order.product || !productReview.trim()) return;

    try {
      await addProductReview({
        productId: order.product._id,
        orderId: order._id,
        rating: productRating,
        review: productReview.trim(),
      });

      setProductReview("");
      setProductRating(5);
      onReviewSubmitted();
    } catch (error) {
      console.error("Failed to submit product review:", error);
    }
  };

  const handleSellerReviewSubmit = async () => {
    if (!order.seller || !sellerReview.trim()) return;

    try {
      await addSellerReview({
        sellerId: order.seller._id,
        orderId: order._id,
        rating: sellerRating,
        review: sellerReview.trim(),
      });

      setSellerReview("");
      setSellerRating(5);
      onReviewSubmitted();
    } catch (error) {
      console.error("Failed to submit seller review:", error);
    }
  };

  const renderStars = (rating: number, onRatingChange: (rating: number) => void, size: "sm" | "md" = "md") => {
    const sizeClasses = {
      sm: "w-4 h-4",
      md: "w-6 h-6",
    };

    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onRatingChange(star)}
            className="focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded"
          >
            <Star
              className={`${sizeClasses[size]} ${
                star <= rating
                  ? "text-yellow-500 fill-current"
                  : "text-gray-300 dark:text-gray-600"
              } hover:text-yellow-400 transition-colors`}
            />
          </button>
        ))}
      </div>
    );
  };

  const canReviewProduct = !order.hasProductReview && order.product;
  const canReviewSeller = !order.hasSellerReview && order.seller;
  const canReviewAnything = canReviewProduct || canReviewSeller;

  if (!canReviewAnything) {
    return (
      <Card className="bg-muted/50">
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <MessageSquare className="w-8 h-8 mx-auto mb-2" />
            <p>You have already reviewed this order</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <MessageSquare className="w-4 h-4 mr-2" />
          Leave Review
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Review Your Purchase</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Order #{order.orderNumber}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {order.product && (
                <div className="flex items-center space-x-3">
                  <Package className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{order.product.title}</p>
                    <p className="text-sm text-muted-foreground">{order.product.brand}</p>
                  </div>
                </div>
              )}
              {order.seller && (
                <div className="flex items-center space-x-3">
                  <User className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{order.seller.businessName || order.seller.name}</p>
                    <p className="text-sm text-muted-foreground">Seller</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Review Tabs */}
          <div className="flex space-x-1 bg-muted p-1 rounded-lg">
            {canReviewProduct && (
              <button
                onClick={() => setActiveTab("product")}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "product"
                    ? "bg-background text-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground"
                }`}
              >
                Product Review
              </button>
            )}
            {canReviewSeller && (
              <button
                onClick={() => setActiveTab("seller")}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "seller"
                    ? "bg-background text-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground"
                }`}
              >
                Seller Review
              </button>
            )}
          </div>

          {/* Product Review Form */}
          {activeTab === "product" && canReviewProduct && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Package className="w-5 h-5 mr-2" />
                  Review Product
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Rating</label>
                  {renderStars(productRating, setProductRating, "md")}
                </div>
                <div>
                  <label htmlFor="product-review" className="block text-sm font-medium mb-2">
                    Review (10-1000 characters)
                  </label>
                  <Textarea
                    id="product-review"
                    placeholder="Share your experience with this product..."
                    value={productReview}
                    onChange={(e) => setProductReview(e.target.value)}
                    rows={4}
                    maxLength={1000}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {productReview.length}/1000 characters
                  </p>
                </div>
                <Button 
                  onClick={handleProductReviewSubmit}
                  disabled={productReview.trim().length < 10}
                  className="w-full"
                >
                  Submit Product Review
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Seller Review Form */}
          {activeTab === "seller" && canReviewSeller && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Review Seller
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Rating</label>
                  {renderStars(sellerRating, setSellerRating, "md")}
                </div>
                <div>
                  <label htmlFor="seller-review" className="block text-sm font-medium mb-2">
                    Review (10-1000 characters)
                  </label>
                  <Textarea
                    id="seller-review"
                    placeholder="Share your experience with this seller..."
                    value={sellerReview}
                    onChange={(e) => setSellerReview(e.target.value)}
                    rows={4}
                    maxLength={1000}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {sellerReview.length}/1000 characters
                  </p>
                </div>
                <Button 
                  onClick={handleSellerReviewSubmit}
                  disabled={sellerReview.trim().length < 10}
                  className="w-full"
                >
                  Submit Seller Review
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Review Status */}
          <div className="text-sm text-muted-foreground space-y-1">
            {order.hasProductReview && (
              <p className="flex items-center">
                <Package className="w-4 h-4 mr-2" />
                ✓ Product reviewed
              </p>
            )}
            {order.hasSellerReview && (
              <p className="flex items-center">
                <User className="w-4 h-4 mr-2" />
                ✓ Seller reviewed
              </p>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
