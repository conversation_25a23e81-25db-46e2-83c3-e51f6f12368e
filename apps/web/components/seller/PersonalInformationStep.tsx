"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { User, Mail, Phone, Calendar, ArrowRight, CheckCircle, AlertCircle } from "lucide-react";
import { ApplicationData } from "./SellerApplicationForm";

interface ApplicationData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface PersonalInformationStepProps {
  data: ApplicationData;
  onUpdate: (data: Partial<ApplicationData>) => void;
  onNext: () => void;
}

export function PersonalInformationStep({ data, onUpdate, onNext }: PersonalInformationStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = (field: string, value: string) => {
    switch (field) {
      case "firstName":
        return value.length < 2 ? "First name must be at least 2 characters" : "";
      case "lastName":
        return value.length < 2 ? "Last name must be at least 2 characters" : "";
      case "email":
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return !emailRegex.test(value) ? "Please enter a valid email address" : "";
      case "phone":
        const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
        return !phoneRegex.test(value) ? "Please enter a valid phone number" : "";
      default:
        return "";
    }
  };

  const handleFieldChange = (field: string, value: string) => {
    onUpdate({ [field]: value });
    
    if (touched[field]) {
      const error = validateField(field, value);
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const handleFieldBlur = (field: string, value: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    const error = validateField(field, value);
    setErrors(prev => ({ ...prev, [field]: error }));
  };

  const validateForm = () => {
    const fields = ["firstName", "lastName", "email", "phone"];
    const newErrors: Record<string, string> = {};
    
    fields.forEach(field => {
      const value = data[field as keyof ApplicationData] as string;
      const error = validateField(field, value);
      if (error) newErrors[field] = error;
    });
    
    setErrors(newErrors);
    setTouched(fields.reduce((acc, field) => ({ ...acc, [field]: true }), {}));
    
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  const getFieldStatus = (field: string) => {
    const value = data[field as keyof ApplicationData] as string;
    const error = errors[field];
    const isTouched = touched[field];
    
    if (!isTouched) return "default";
    if (error) return "error";
    if (value) return "success";
    return "default";
  };

  return (
    <div className="p-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 rounded-2xl flex items-center justify-center mx-auto">
          <User className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h2 className="text-3xl font-bold text-black dark:text-white mb-2">
            Personal Information
          </h2>
          <p className="text-lg text-neutral-600 dark:text-neutral-400">
            Let's start with your basic contact information
          </p>
        </div>
      </div>

      {/* Form Fields */}
      <div className="space-y-6 max-w-2xl mx-auto">
        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-base font-semibold text-black dark:text-white">
              First Name *
            </Label>
            <div className="relative">
              <Input
                id="firstName"
                type="text"
                value={data.firstName}
                onChange={(e) => handleFieldChange("firstName", e.target.value)}
                onBlur={(e) => handleFieldBlur("firstName", e.target.value)}
                placeholder="Enter your first name"
                className={`pl-12 h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                  getFieldStatus("firstName") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("firstName") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : "border-neutral-300 focus:border-black dark:focus:border-white"
                }`}
              />
              <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
              {getFieldStatus("firstName") === "success" && (
                <CheckCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
              )}
              {getFieldStatus("firstName") === "error" && (
                <AlertCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
              )}
            </div>
            {errors.firstName && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.firstName}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-base font-semibold text-black dark:text-white">
              Last Name *
            </Label>
            <div className="relative">
              <Input
                id="lastName"
                type="text"
                value={data.lastName}
                onChange={(e) => handleFieldChange("lastName", e.target.value)}
                onBlur={(e) => handleFieldBlur("lastName", e.target.value)}
                placeholder="Enter your last name"
                className={`pl-12 h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                  getFieldStatus("lastName") === "error"
                    ? "border-red-300 focus:border-red-500"
                    : getFieldStatus("lastName") === "success"
                    ? "border-green-300 focus:border-green-500"
                    : "border-neutral-300 focus:border-black dark:focus:border-white"
                }`}
              />
              <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
              {getFieldStatus("lastName") === "success" && (
                <CheckCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
              )}
              {getFieldStatus("lastName") === "error" && (
                <AlertCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
              )}
            </div>
            {errors.lastName && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.lastName}
              </p>
            )}
          </div>
        </div>

        {/* Email Field */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-base font-semibold text-black dark:text-white">
            Email Address *
          </Label>
          <div className="relative">
            <Input
              id="email"
              type="email"
              value={data.email}
              onChange={(e) => handleFieldChange("email", e.target.value)}
              onBlur={(e) => handleFieldBlur("email", e.target.value)}
              placeholder="Enter your email address"
              className={`pl-12 h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                getFieldStatus("email") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("email") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : "border-neutral-300 focus:border-black dark:focus:border-white"
              }`}
            />
            <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
            {getFieldStatus("email") === "success" && (
              <CheckCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
            )}
            {getFieldStatus("email") === "error" && (
              <AlertCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
            )}
          </div>
          {errors.email && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.email}
            </p>
          )}
        </div>

        {/* Phone Field */}
        <div className="space-y-2">
          <Label htmlFor="phone" className="text-base font-semibold text-black dark:text-white">
            Phone Number *
          </Label>
          <div className="relative">
            <Input
              id="phone"
              type="tel"
              value={data.phone}
              onChange={(e) => handleFieldChange("phone", e.target.value)}
              onBlur={(e) => handleFieldBlur("phone", e.target.value)}
              placeholder="Enter your phone number"
              className={`pl-12 h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                getFieldStatus("phone") === "error"
                  ? "border-red-300 focus:border-red-500"
                  : getFieldStatus("phone") === "success"
                  ? "border-green-300 focus:border-green-500"
                  : "border-neutral-300 focus:border-black dark:focus:border-white"
              }`}
            />
            <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
            {getFieldStatus("phone") === "success" && (
              <CheckCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
            )}
            {getFieldStatus("phone") === "error" && (
              <AlertCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
            )}
          </div>
          {errors.phone && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.phone}
            </p>
          )}
          <p className="text-sm text-neutral-500 dark:text-neutral-400">
            We'll use this for important account notifications
          </p>
        </div>
      </div>

      {/* Trust Indicators */}
      <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 p-6 max-w-2xl mx-auto">
        <div className="flex items-start space-x-4">
          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center flex-shrink-0">
            <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">
              Your Information is Secure
            </h3>
            <p className="text-sm text-blue-800 dark:text-blue-400">
              All personal information is encrypted and stored securely. We never share your data with third parties without your consent.
            </p>
          </div>
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end pt-6 max-w-2xl mx-auto">
        <Button
          onClick={handleNext}
          className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 h-14 px-8 text-lg font-semibold rounded-xl"
        >
          Continue
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>
      </div>
    </div>
  );
}
