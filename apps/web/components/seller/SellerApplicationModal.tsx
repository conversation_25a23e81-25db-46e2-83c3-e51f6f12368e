"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Badge } from "@repo/ui/components/badge";
import { Clock, CheckCircle, XCircle, AlertCircle, FileText, X } from "lucide-react";

const applicationSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  company: z.string().min(2, "Company name is required"),
  productSpecialty: z.string().min(2, "Product specialty is required"),
  numberOfProducts: z.string().min(1, "Number of products is required"),
  link: z.string().url("Please enter a valid URL"),
  referral: z.string().optional(),
});

type ApplicationFormData = z.infer<typeof applicationSchema>;

const statusConfig = {
  pending: {
    label: "Pending Review",
    color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    icon: Clock,
    description: "Your application is being reviewed by our team."
  },
  under_review: {
    label: "Under Review",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    icon: FileText,
    description: "We're currently reviewing your application details."
  },
  approved: {
    label: "Approved",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    icon: CheckCircle,
    description: "Congratulations! Your application has been approved."
  },
  rejected: {
    label: "Rejected",
    color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    icon: XCircle,
    description: "Your application was not approved at this time."
  },
  requires_info: {
    label: "More Information Needed",
    color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    icon: AlertCircle,
    description: "We need additional information to process your application."
  }
};

interface SellerApplicationModalProps {
  children: React.ReactNode;
  userApplication?: any;
  onOpenChange?: (open: boolean) => void;
  open?: boolean;
}

export function SellerApplicationModal({ children, userApplication, onOpenChange, open }: SellerApplicationModalProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const isOpen = open !== undefined ? open : internalOpen;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  const { user, isLoading } = useAuth();
  const submitApplication = useMutation(api.sellerApplicationsSimple.submitApplication);
  
  // Check if user already has an application
  const existingApplication = useQuery(
    api.sellerApplicationsSimple.getUserApplication,
    user?.email ? { email: user.email } : "skip"
  );

  const form = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationSchema),
  });

  // Pre-fill form with user data when available
  useEffect(() => {
    if (user && isOpen) {
      // Parse full name if available
      const nameParts = user.name?.split(' ') || [];
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      // Set the form values
      form.setValue('firstName', firstName);
      // Only set lastName if it's not empty, otherwise leave it for user to fill
      if (lastName) {
        form.setValue('lastName', lastName);
      }
      form.setValue('email', user.email || '');
    }
  }, [user, form, isOpen]);

  const { handleSubmit, formState: { errors }, reset } = form;

  const onSubmit = async (data: ApplicationFormData) => {
    setIsSubmitting(true);
    
    try {
      // Transform form data to match the simpler sellerApplications schema
      const applicationData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: "******-000-0000", // Valid placeholder phone number
        dateOfBirth: "N/A", // Required field
        businessName: data.company,
        businessType: "individual" as const,
        taxId: "N/A",
        businessAddress: "N/A",
        businessCity: "N/A",
        businessState: "N/A",
        businessZip: "N/A",
        businessCountry: "N/A",
        yearsExperience: "1",
        previousPlatforms: [],
        monthlyVolume: "1-10",
        specialties: [data.productSpecialty],
        termsAccepted: true,
        privacyAccepted: true,
      };

      // Submit to Convex backend
      await submitApplication(applicationData);
      
      toast.success("Application submitted successfully!", {
        description: "We'll review your application and get back to you soon.",
      });
      
      setIsSubmitted(true);
      reset();
    } catch (error: any) {
      console.error("Submission error:", error);
      toast.error("Failed to submit application", {
        description: error?.message || "Please try again or contact support.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (open !== undefined) {
      // Controlled mode - call external onOpenChange
      onOpenChange?.(open);
    } else {
      // Uncontrolled mode - use internal state
      setInternalOpen(open);
    }
    
    if (open) {
      // Close dropdown menu when modal opens (for dropdown usage)
      onOpenChange?.(false);
    }
    if (!open) {
      // Reset form and states when closing
      setIsSubmitted(false);
      reset();
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground font-light">Loading...</p>
          </div>
        </div>
      );
    }

    if (!user) {
      return (
        <div className="text-center min-h-[400px] flex items-center justify-center">
          <div>
            <h2 className="text-xl font-semibold mb-4 text-foreground">Authentication Required</h2>
            <p className="text-muted-foreground mb-6 font-light">
              Please sign in to submit a seller application.
            </p>
            <Button asChild className="rounded-xl font-medium transition-all duration-300 hover:ring-2 hover:ring-primary/20">
              <a href="/login">Sign In</a>
            </Button>
          </div>
        </div>
      );
    }

    // If user already has an application, show status instead of form
    if (existingApplication !== undefined && existingApplication) {
      const application = existingApplication;
      const status = statusConfig[application.status];
      const StatusIcon = status.icon;

      return (
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <StatusIcon className="w-5 h-5" />
            <h3 className="text-xl font-light tracking-wide text-foreground">Application Status</h3>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge className={`${status.color} font-medium`}>
              {status.label}
            </Badge>
            <span className="text-sm text-muted-foreground">
              Submitted on {new Date(application.submittedAt).toLocaleDateString()}
            </span>
          </div>
          
          <p className="text-foreground font-light">
            {status.description}
          </p>

          {application.status === "rejected" && application.rejectionReason && (
            <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">Rejection Reason:</h4>
              <p className="text-red-700 dark:text-red-300 text-sm">{application.rejectionReason}</p>
            </div>
          )}

          {application.status === "requires_info" && application.notes && (
            <div className="bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
              <h4 className="font-medium text-orange-800 dark:text-orange-200 mb-2">Additional Information Needed:</h4>
              <p className="text-orange-700 dark:text-orange-300 text-sm">{application.notes}</p>
            </div>
          )}

          {application.status === "approved" && (
            <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Next Steps:</h4>
              <p className="text-green-700 dark:text-green-300 text-sm">
                You can now access your seller dashboard and start listing products. 
                Check your email for login credentials and setup instructions.
              </p>
            </div>
          )}

          <div className="pt-4 border-t border-border">
            <h4 className="font-medium text-foreground mb-3">Application Details:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Business Name:</span>
                <p className="font-medium">{application.businessName}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Business Type:</span>
                <p className="font-medium capitalize">{application.businessType}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Specialties:</span>
                <p className="font-medium">{application.specialties.join(", ")}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Monthly Volume:</span>
                <p className="font-medium">{application.monthlyVolume}</p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (isSubmitted) {
      return (
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-semibold text-foreground">Application Submitted!</h2>
          <p className="text-muted-foreground font-light">
            Thank you for your application. We'll review it and get back to you soon.
          </p>
          <Button 
            onClick={() => handleOpenChange(false)}
            className="rounded-xl font-medium transition-all duration-300 hover:ring-2 hover:ring-primary/20"
          >
            Close
          </Button>
        </div>
      );
    }

    return (
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-sm font-medium text-foreground">First Name</Label>
            <Input
              id="firstName"
              {...form.register("firstName")}
              placeholder="Enter your first name"
              className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
            />
            {errors.firstName && (
              <p className="text-sm text-red-600 font-medium">{errors.firstName.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-sm font-medium text-foreground">Last Name</Label>
            <Input
              id="lastName"
              {...form.register("lastName")}
              placeholder="Enter your last name"
              className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
            />
            {errors.lastName && (
              <p className="text-sm text-red-600 font-medium">{errors.lastName.message}</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium text-foreground">Email</Label>
          <Input
            id="email"
            type="email"
            {...form.register("email")}
            placeholder="Enter your email address"
            className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
          />
          {errors.email && (
            <p className="text-sm text-red-600 font-medium">{errors.email.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="company" className="text-sm font-medium text-foreground">Company</Label>
          <Input
            id="company"
            {...form.register("company")}
            placeholder="Enter your company name"
            className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
          />
          {errors.company && (
            <p className="text-sm text-red-600 font-medium">{errors.company.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="productSpecialty" className="text-sm font-medium text-foreground">Product Specialty</Label>
          <Input
            id="productSpecialty"
            {...form.register("productSpecialty")}
            placeholder="e.g., Luxury watches, Designer bags, Sneakers"
            className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
          />
          {errors.productSpecialty && (
            <p className="text-sm text-red-600 font-medium">{errors.productSpecialty.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="numberOfProducts" className="text-sm font-medium text-foreground">Number of Products</Label>
          <Select onValueChange={(value) => form.setValue("numberOfProducts", value)}>
            <SelectTrigger className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-primary transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4 placeholder:text-muted">
              <SelectValue placeholder="Select number of products" />
            </SelectTrigger>
            <SelectContent className="bg-card border-border rounded-xl shadow-xl">
              <SelectItem value="1-10" className="rounded-lg hover:bg-muted/20">1-10 products</SelectItem>
              <SelectItem value="11-50" className="rounded-lg hover:bg-muted/20">11-50 products</SelectItem>
              <SelectItem value="51-100" className="rounded-lg hover:bg-muted/20">51-100 products</SelectItem>
              <SelectItem value="100+" className="rounded-lg hover:bg-muted/20">100+ products</SelectItem>
            </SelectContent>
          </Select>
          {errors.numberOfProducts && (
            <p className="text-sm text-red-600 font-medium">{errors.numberOfProducts.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="link" className="text-sm font-medium text-foreground">Website/Store Link</Label>
          <Input
            id="link"
            type="url"
            {...form.register("link")}
            placeholder="https://your-store.com"
            className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
          />
          {errors.link && (
            <p className="text-sm text-red-600 font-medium">{errors.link.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="referral" className="text-sm font-medium text-foreground">How did you hear about us?</Label>
          <Input
            id="referral"
            {...form.register("referral")}
            placeholder="e.g., Social media, Friend, Search engine, etc."
            className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
          />
        </div>

        <Button 
          type="submit" 
          className="w-full h-12 rounded-xl font-medium text-sm transition-all duration-300 hover:ring-2 hover:ring-primary/20"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : "Submit Application"}
        </Button>
      </form>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-light tracking-wide text-foreground">
            {existingApplication ? "Application Status" : "Seller Application"}
          </DialogTitle>
        </DialogHeader>
        <div className="pt-4">
          {renderContent()}
        </div>
      </DialogContent>
    </Dialog>
  );
}
