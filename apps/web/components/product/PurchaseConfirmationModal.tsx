"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { Badge } from "@repo/ui/components/badge";
import { 
  CreditCard, 
  Lock, 
  Truck, 
  ArrowRight, 
  CheckCircle,
  Shield,
  Calendar
} from "lucide-react";
import Image from "next/image";
import { Id } from "@repo/backend/convex/_generated/dataModel";

interface Product {
  _id: string;
  title: string;
  brand: string;
  price: number;
  images: string[];
  condition: string;
}

interface PurchaseConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
  quantity: number;
}

export function PurchaseConfirmationModal({
  isOpen,
  onClose,
  product,
  quantity,
}: PurchaseConfirmationModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("card");
  const [orderId, setOrderId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    cardName: "",
    billingAddress: {
      name: "",
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "US",
    }
  });

  // Convex mutations
  const createOrder = useMutation(api.orderManagement.createOrder);
  const confirmPayment = useMutation(api.orderManagement.confirmPayment);

  // Form validation
  const isFormValid = () => {
    if (paymentMethod === "card") {
      return (
        formData.cardNumber.length >= 13 &&
        formData.expiryDate.length >= 4 &&
        formData.cvv.length >= 3 &&
        formData.cardName.length > 0 &&
        formData.billingAddress.name.length > 0 &&
        formData.billingAddress.street.length > 0 &&
        formData.billingAddress.city.length > 0 &&
        formData.billingAddress.state.length > 0 &&
        formData.billingAddress.zipCode.length > 0
      );
    }
    return true; // PayPal doesn't need form validation
  };

  const totalPrice = product.price * quantity;
  const estimatedTax = totalPrice * 0.08;
  const shippingCost = totalPrice > 500 ? 0 : 25;
  const finalTotal = totalPrice + estimatedTax + shippingCost;

  const estimatedDelivery = new Date();
  estimatedDelivery.setDate(estimatedDelivery.getDate() + 3);

  const handlePurchase = async () => {
    setIsProcessing(true);
    
    try {
      // Step 1: Create the order
      const orderResult = await createOrder({
        productId: product._id as Id<"products">,
        shippingAddress: {
          name: formData.billingAddress.name,
          street: formData.billingAddress.street,
          city: formData.billingAddress.city,
          state: formData.billingAddress.state,
          zipCode: formData.billingAddress.zipCode,
          country: formData.billingAddress.country,
        },
        paymentMethod: paymentMethod,
        notes: `Purchase via ${paymentMethod}`,
      });

      if (orderResult.success && orderResult.orderId) {
        setOrderId(orderResult.orderId);
        
        // Step 2: Simulate payment processing (in real app, this would be Stripe/PayPal)
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Step 3: Confirm payment and create verified purchase
        const paymentResult = await confirmPayment({
          orderId: orderResult.orderId,
          paymentIntentId: `test_payment_${Date.now()}`, // Mock payment intent ID
          paymentMethod: paymentMethod,
          billingAddress: formData.billingAddress,
        });

        if (paymentResult.success) {
          setIsComplete(true);
        } else {
          throw new Error("Payment confirmation failed");
        }
      } else {
        throw new Error("Order creation failed");
      }
    } catch (error) {
      console.error("Purchase error:", error);
      // In a real app, you'd show an error message to the user
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    setIsComplete(false);
    setIsProcessing(false);
    onClose();
  };

  if (isComplete) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md mx-auto bg-white dark:bg-neutral-900 rounded-3xl border-0 shadow-2xl">
          <div className="text-center space-y-6 p-6">
            <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
            </div>
            
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-black dark:text-white">
                Purchase Successful!
              </h2>
              <p className="text-neutral-600 dark:text-neutral-400">
                Your order has been confirmed and verified purchase created. You can now leave a review!
              </p>
            </div>

            <div className="bg-neutral-50 dark:bg-neutral-800 rounded-2xl p-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-neutral-200 dark:bg-neutral-700 rounded-lg overflow-hidden">
                  {product.images[0] && (
                    <Image
                      src={product.images[0]}
                      alt={product.title}
                      width={48}
                      height={48}
                      className="object-cover"
                    />
                  )}
                </div>
                <div className="flex-1 text-left">
                  <p className="font-semibold text-black dark:text-white text-sm">
                    {product.title}
                  </p>
                  <p className="text-neutral-600 dark:text-neutral-400 text-xs">
                    Qty: {quantity} • ${finalTotal.toFixed(2)}
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Button onClick={handleClose} className="w-full bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 rounded-xl h-12">
                Continue Shopping
              </Button>
              <Button variant="outline" className="w-full rounded-xl h-12">
                Track Order
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl mx-auto bg-white dark:bg-neutral-900 rounded-3xl border-0 shadow-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-2xl font-bold text-black dark:text-white">
            Complete Your Purchase
          </DialogTitle>
          <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-2">
            🧪 Test Mode: This will create a verified purchase for testing the review system
          </p>
        </DialogHeader>

        <div className="p-6 space-y-8">
          {/* Order Summary */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-black dark:text-white">Order Summary</h3>
            <div className="bg-neutral-50 dark:bg-neutral-800 rounded-2xl p-4">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-neutral-200 dark:bg-neutral-700 rounded-xl overflow-hidden">
                  {product.images[0] && (
                    <Image
                      src={product.images[0]}
                      alt={product.title}
                      width={64}
                      height={64}
                      className="object-cover"
                    />
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-black dark:text-white">{product.title}</h4>
                  <p className="text-neutral-600 dark:text-neutral-400">{product.brand}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {product.condition}
                    </Badge>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">
                      Qty: {quantity}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xl font-bold text-black dark:text-white">
                    ${totalPrice.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            {/* Price Breakdown */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-neutral-600 dark:text-neutral-400">Subtotal</span>
                <span className="font-medium text-black dark:text-white">${totalPrice.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-600 dark:text-neutral-400">Tax</span>
                <span className="font-medium text-black dark:text-white">${estimatedTax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-600 dark:text-neutral-400">Shipping</span>
                <span className="font-medium text-black dark:text-white">
                  {shippingCost === 0 ? "Free" : `$${shippingCost}`}
                </span>
              </div>
              <Separator />
              <div className="flex justify-between text-xl font-bold">
                <span className="text-black dark:text-white">Total</span>
                <span className="text-black dark:text-white">${finalTotal.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Payment Method */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-black dark:text-white">Payment Method</h3>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => setPaymentMethod("card")}
                className={`p-4 rounded-xl border-2 transition-colors ${
                  paymentMethod === "card"
                    ? "border-black dark:border-white bg-neutral-50 dark:bg-neutral-800"
                    : "border-neutral-200 dark:border-neutral-700"
                }`}
              >
                <CreditCard className="w-6 h-6 mx-auto mb-2" />
                <span className="text-sm font-medium">Credit Card</span>
              </button>
              <button
                onClick={() => setPaymentMethod("paypal")}
                className={`p-4 rounded-xl border-2 transition-colors ${
                  paymentMethod === "paypal"
                    ? "border-black dark:border-white bg-neutral-50 dark:bg-neutral-800"
                    : "border-neutral-200 dark:border-neutral-700"
                }`}
              >
                <div className="w-6 h-6 mx-auto mb-2 bg-blue-600 rounded"></div>
                <span className="text-sm font-medium">PayPal</span>
              </button>
            </div>
          </div>

          {/* Payment Form */}
          {paymentMethod === "card" && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cardNumber">Card Number</Label>
                  <Input
                    id="cardNumber"
                    placeholder="1234 5678 9012 3456"
                    className="rounded-xl"
                    value={formData.cardNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, cardNumber: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="expiryDate">Expiry Date</Label>
                  <Input
                    id="expiryDate"
                    placeholder="MM/YY"
                    className="rounded-xl"
                    value={formData.expiryDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, expiryDate: e.target.value }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cvv">CVV</Label>
                  <Input
                    id="cvv"
                    placeholder="123"
                    className="rounded-xl"
                    value={formData.cvv}
                    onChange={(e) => setFormData(prev => ({ ...prev, cvv: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cardName">Name on Card</Label>
                  <Input
                    id="cardName"
                    placeholder="John Doe"
                    className="rounded-xl"
                    value={formData.cardName}
                    onChange={(e) => setFormData(prev => ({ ...prev, cardName: e.target.value }))}
                  />
                </div>
              </div>
              
              {/* Billing Address */}
              <div className="space-y-4">
                <h4 className="font-semibold text-black dark:text-white">Billing Address</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="billingName">Full Name</Label>
                    <Input
                      id="billingName"
                      placeholder="John Doe"
                      className="rounded-xl"
                      value={formData.billingAddress.name}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        billingAddress: { ...prev.billingAddress, name: e.target.value }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="billingStreet">Street Address</Label>
                    <Input
                      id="billingStreet"
                      placeholder="123 Main St"
                      className="rounded-xl"
                      value={formData.billingAddress.street}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        billingAddress: { ...prev.billingAddress, street: e.target.value }
                      }))}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="billingCity">City</Label>
                    <Input
                      id="billingCity"
                      placeholder="New York"
                      className="rounded-xl"
                      value={formData.billingAddress.city}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        billingAddress: { ...prev.billingAddress, city: e.target.value }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="billingState">State</Label>
                    <Input
                      id="billingState"
                      placeholder="NY"
                      className="rounded-xl"
                      value={formData.billingAddress.state}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        billingAddress: { ...prev.billingAddress, state: e.target.value }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="billingZip">ZIP Code</Label>
                    <Input
                      id="billingZip"
                      placeholder="10001"
                      className="rounded-xl"
                      value={formData.billingAddress.zipCode}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        billingAddress: { ...prev.billingAddress, zipCode: e.target.value }
                      }))}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Delivery Info */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-2xl p-4">
            <div className="flex items-center space-x-3">
              <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="font-semibold text-blue-800 dark:text-blue-300">
                  Estimated Delivery: {estimatedDelivery.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Insured shipping with tracking included
                </p>
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="flex items-center space-x-3 text-sm text-neutral-600 dark:text-neutral-400">
            <Lock className="w-4 h-4" />
            <span>Your payment information is encrypted and secure</span>
          </div>

          {/* Form Validation Notice */}
          {paymentMethod === "card" && !isFormValid() && (
            <div className="bg-amber-50 dark:bg-amber-900/20 rounded-2xl p-4 border border-amber-200 dark:border-amber-700">
              <div className="flex items-center space-x-3 text-sm text-amber-800 dark:text-amber-300">
                <Shield className="w-4 h-4" />
                <span>Please complete all required fields to proceed with your purchase</span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={handlePurchase}
              disabled={isProcessing || !isFormValid()}
              className="w-full h-14 text-lg font-bold bg-black hover:bg-neutral-800 dark:bg-white dark:hover:bg-neutral-200 dark:text-black rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? (
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                <>
                  <Shield className="w-6 h-6 mr-3" />
                  Complete Purchase - ${finalTotal.toFixed(2)}
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={handleClose}
              className="w-full h-12 rounded-2xl"
              disabled={isProcessing}
            >
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
