"use client";

import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Star, User, MessageSquare, Package } from "lucide-react";
import { ReviewForm } from "@/components/seller/ReviewForm";
import { useAuth } from "@/hooks/useBetterAuth";

interface ProductReviewsProps {
  productId: Id<"products">;
  productTitle: string;
  productBrand: string;
}

export function ProductReviews({ productId, productTitle, productBrand }: ProductReviewsProps) {
  const { user } = useAuth();
  const [currentPage, setCurrentPage] = useState(0);
  const [ratingFilter, setRatingFilter] = useState<number | null>(null);

  const ITEMS_PER_PAGE = 10;

  // Get product reviews
  const reviewsData = useQuery(api.sellerReviews.getProductReviews, {
    productId,
    limit: ITEMS_PER_PAGE,
    offset: currentPage * ITEMS_PER_PAGE,
    rating: ratingFilter || undefined,
    verifiedOnly: true,
  });

  // Get user's reviewable orders for this product
  const reviewableOrders = useQuery(api.sellerReviews.getReviewableOrders, {
    productId,
  });

  const reviews = reviewsData?.reviews || [];
  const ratingStats = reviewsData?.ratingStats;
  const hasMore = reviewsData?.pagination?.hasMore || false;

  const renderStars = (rating: number, size: "sm" | "md" | "lg" = "md") => {
    const sizeClasses = {
      sm: "w-3 h-3",
      md: "w-4 h-4",
      lg: "w-5 h-5",
    };

    return (
      <div className="flex items-center space-x-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? "text-yellow-500 fill-current"
                : "text-gray-300 dark:text-gray-600"
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const canReview = user && reviewableOrders?.orders && reviewableOrders.orders.length > 0;

  return (
    <div className="space-y-6">
      {/* Reviews Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Customer Reviews</h2>
          {ratingStats && (
            <div className="flex items-center space-x-4 mt-2">
              <div className="flex items-center space-x-2">
                <span className="text-3xl font-bold">{ratingStats.average}</span>
                <div className="flex flex-col">
                  {renderStars(ratingStats.average, "md")}
                  <span className="text-sm text-muted-foreground">
                    {ratingStats.total} verified reviews
                  </span>
                </div>
              </div>
              
              {/* Rating Distribution */}
              <div className="hidden md:flex space-x-2">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center space-x-1">
                    <span className="text-xs text-muted-foreground">{rating}★</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-500 h-2 rounded-full"
                        style={{
                          width: `${ratingStats.total > 0 ? (ratingStats.distribution[rating as keyof typeof ratingStats.distribution] / ratingStats.total) * 100 : 0}%`
                        }}
                      />
                    </div>
                    <span className="text-xs text-muted-foreground w-8 text-right">
                      {ratingStats.distribution[rating as keyof typeof ratingStats.distribution]}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Filter by Rating */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Filter by:</span>
          <Button
            variant={ratingFilter === null ? "default" : "outline"}
            size="sm"
            onClick={() => setRatingFilter(null)}
          >
            All
          </Button>
          {[5, 4, 3, 2, 1].map((rating) => (
            <Button
              key={rating}
              variant={ratingFilter === rating ? "default" : "outline"}
              size="sm"
              onClick={() => setRatingFilter(rating)}
            >
              {rating}★
            </Button>
          ))}
        </div>
      </div>

      {/* Review Button */}
      {canReview && (
        <Card className="border-l-4 border-l-primary">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium mb-1">Have you purchased this product?</h3>
                <p className="text-sm text-muted-foreground">
                  Share your experience with other customers
                </p>
              </div>
              {reviewableOrders?.orders && reviewableOrders.orders.length > 0 && reviewableOrders.orders[0] && (
                <ReviewForm 
                  order={reviewableOrders.orders[0]} 
                  onReviewSubmitted={() => {
                    // The query will automatically update due to Convex reactivity
                  }} 
                />
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.length > 0 ? (
          reviews.map((review) => (
            <Card key={review._id} className="bg-background/50">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                      <User className="w-5 h-5 text-muted-foreground" />
                    </div>
                    <div>
                      <div className="font-medium">{review.user?.name || "User"}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(review.reviewDate)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {renderStars(review.rating, "sm")}
                    <span className="text-sm font-medium ml-2">
                      {review.rating}.0
                    </span>
                  </div>
                </div>
                
                {review.review && (
                  <p className="text-foreground mb-3">{review.review}</p>
                )}
                
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Package className="w-4 h-4" />
                    <span>Verified Purchase</span>
                  </div>
                  {review.orderNumber && (
                    <span>Order #{review.orderNumber}</span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card className="bg-background/50">
            <CardContent className="p-12 text-center">
              <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Reviews Yet</h3>
              <p className="text-muted-foreground mb-4">
                Be the first to review this product.
              </p>
                             {canReview && reviewableOrders?.orders && reviewableOrders.orders.length > 0 && reviewableOrders.orders[0] && (
                 <ReviewForm 
                   order={reviewableOrders.orders[0]} 
                   onReviewSubmitted={() => {
                     // The query will automatically update due to Convex reactivity
                   }} 
                 />
               )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Pagination */}
      {hasMore && (
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Load More Reviews
          </Button>
        </div>
      )}
    </div>
  );
}
