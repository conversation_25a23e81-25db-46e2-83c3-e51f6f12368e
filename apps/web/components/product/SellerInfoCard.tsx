"use client";

import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { Star, Shield, MessageCircle, Store, Calendar } from "lucide-react";
import { ContactSellerModal } from "@/components/marketplace/ContactSellerModal";
import Link from "next/link";

interface Seller {
  _id: string;
  name: string;
  userType: string;
  avatar?: string;
  rating?: number;
  reviewCount?: number;
  totalSales?: number;
  memberSince?: number;
  isVerified?: boolean;
  responseTime?: string;
  location?: string;
  businessName?: string;
  verificationStatus?: string;
}

interface Product {
  _id: string;
  title: string;
  price: number;
  images: string[];
  brand: string;
}

interface SellerInfoCardProps {
  seller: Seller;
  product?: Product;
}

export function SellerInfoCard({ seller, product }: SellerInfoCardProps) {
  const rating = seller.rating || 0;
  const totalSales = seller.totalSales || 0;
  const memberSince = seller.memberSince || Date.now();
  const responseTime = seller.responseTime || "Response time not specified";
  const location = seller.location || "Location not specified";

  const formatMemberSince = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <div key="half" className="relative">
          <Star className="w-4 h-4 text-muted-foreground/50" />
          <div className="absolute inset-0 overflow-hidden w-1/2">
            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
          </div>
        </div>
      );
    }

    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="w-4 h-4 text-muted-foreground/50" />
      );
    }

    return stars;
  };

  return (
    <div className="bg-card border-0 rounded-xl p-8 shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <Avatar className="w-16 h-16 ring-2 ring-border">
              <AvatarImage src={seller.avatar} alt={seller.name} />
              <AvatarFallback className="bg-gradient-to-br from-muted/50 to-muted text-lg font-medium">
                {getInitials(seller.name)}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <div>
                  <h3 className="text-xl font-medium text-foreground">
                    {seller.businessName || seller.name}
                  </h3>
                  {seller.businessName && (
                    <p className="text-sm text-muted-foreground font-light">
                      {seller.name}
                    </p>
                  )}
                </div>
                {seller.isVerified && (
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1">
                    <Shield className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
                {seller.verificationStatus === "pending" && (
                  <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 px-2 py-1">
                    Pending Verification
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-1">
                {rating > 0 ? (
                  <Link
                    href={`/seller/${seller._id}/reviews`}
                    className="flex items-center space-x-1 hover:opacity-80 transition-opacity cursor-pointer"
                  >
                    {renderStars(rating)}
                    <span className="text-sm font-light text-muted-foreground ml-2">
                      {rating.toFixed(1)} ({seller.reviewCount || 0} reviews)
                    </span>
                  </Link>
                ) : (
                  <span className="text-sm text-muted-foreground font-light">
                    No ratings yet
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-muted rounded-2xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Store className="w-5 h-5 text-muted-foreground" />
            </div>
            <p className="text-2xl font-light text-foreground">{totalSales}</p>
            <p className="text-sm text-muted-foreground font-light">Total Sales</p>
          </div>
          
          <div className="bg-muted rounded-2xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Calendar className="w-5 h-5 text-muted-foreground" />
            </div>
            <p className="text-lg font-light text-foreground">
              {formatMemberSince(memberSince)}
            </p>
            <p className="text-sm text-muted-foreground font-light">Member Since</p>
          </div>
        </div>

        {/* Additional Info */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground font-light">Response time</span>
            <span className="font-medium text-foreground">{responseTime}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground font-light">Location</span>
            <span className="font-medium text-foreground">{location}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground font-light">Seller type</span>
            <Badge variant="outline" className="font-light">
              {seller.userType === 'seller' ? 'Professional' : 'Individual'}
            </Badge>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-3 pt-4 border-t border-border">
          <ContactSellerModal seller={seller} product={product}>
            <Button className="w-full bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-300 rounded-xl h-12 font-light">
              <MessageCircle className="w-5 h-5 mr-2" />
              Contact Seller
            </Button>
          </ContactSellerModal>
          <Link href={`/seller/${seller._id}`} className="w-full" passHref>
            <Button
              variant="outline"
              className="w-full rounded-xl h-12 font-light transition-colors duration-300"
            >
              <Store className="w-5 h-5 mr-2" />
              View All Items
            </Button>
          </Link>
        </div>

        {/* Trust Indicators */}
        {seller.isVerified && (
          <div className="bg-green-50 dark:bg-green-900/20 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="w-5 h-5 text-green-600 dark:text-green-400" />
              <span className="font-semibold text-green-800 dark:text-green-300">Verified Seller</span>
            </div>
            <p className="text-sm text-green-700 dark:text-green-400">
              This seller has been verified by HauteVault and meets our quality standards.
            </p>
          </div>
        )}
        {seller.verificationStatus === "pending" && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              <span className="font-semibold text-yellow-800 dark:text-yellow-300">Verification Pending</span>
            </div>
            <p className="text-sm text-yellow-700 dark:text-yellow-400">
              This seller's verification is currently under review by HauteVault.
            </p>
          </div>
        )}
        {!seller.isVerified && seller.verificationStatus !== "pending" && (
          <div className="bg-neutral-50 dark:bg-neutral-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
              <span className="font-semibold text-neutral-800 dark:text-neutral-300">New Seller</span>
            </div>
            <p className="text-sm text-neutral-700 dark:text-neutral-400">
              This seller is new to HauteVault. Exercise standard caution when purchasing.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
