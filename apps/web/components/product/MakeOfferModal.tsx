"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { 
  DollarSign, 
  MessageSquare, 
  Clock, 
  AlertCircle,
  CheckCircle2
} from "lucide-react";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { toast } from "sonner";

interface Product {
  _id: Id<"products">;
  title: string;
  brand: string;
  price: number;
  condition: string;
}

interface MakeOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
  existingOffer?: {
    offerAmount: number;
    message: string;
    status: string;
    counterOffer?: number;
    counterMessage?: string;
  };
}

export function MakeOfferModal({ isOpen, onClose, product, existingOffer }: MakeOfferModalProps) {
  const [offerAmount, setOfferAmount] = useState(existingOffer?.offerAmount?.toString() || "");
  const [message, setMessage] = useState(existingOffer?.message || "");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const createOffer = useMutation(api.offerManagement.createOffer);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!offerAmount || parseFloat(offerAmount) <= 0) {
      toast.error("Please enter a valid offer amount");
      return;
    }

    const amount = parseFloat(offerAmount);
    if (amount > product.price * 1.5) {
      toast.error("Offer amount cannot exceed 150% of listing price");
      return;
    }

    setIsSubmitting(true);

    try {
      await createOffer({
        productId: product._id as any,
        offerAmount: amount,
        message: message.trim() || undefined,
      });

      toast.success("Offer submitted successfully!");
      setOfferAmount("");
      setMessage("");
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to submit offer");
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const maxOffer = product.price * 1.5;
  const minOffer = Math.max(1, product.price * 0.5); // At least $1 or 50% of listing price

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-primary" />
            {existingOffer ? "Update Offer" : "Make an Offer"}
          </DialogTitle>
          <DialogDescription>
            {existingOffer 
              ? `Update your offer for ${product.title} by ${product.brand}`
              : `Submit your offer for ${product.title} by ${product.brand}`
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Product Info */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">Listing Price</span>
              <span className="text-lg font-semibold text-foreground">
                {formatCurrency(product.price)}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">Condition</span>
              <Badge variant="outline" className="text-xs">
                {product.condition.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>
          </div>

          {/* Offer Amount */}
          <div className="space-y-3">
            <Label htmlFor="offerAmount" className="text-sm font-medium">
              Your Offer Amount
            </Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                id="offerAmount"
                type="number"
                step="0.01"
                min={minOffer}
                max={maxOffer}
                value={offerAmount}
                onChange={(e) => setOfferAmount(e.target.value)}
                placeholder="Enter your offer"
                className="pl-10"
                required
              />
            </div>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Min: {formatCurrency(minOffer)}</span>
              <span>Max: {formatCurrency(maxOffer)}</span>
            </div>
          </div>

          {/* Message */}
          <div className="space-y-3">
            <Label htmlFor="message" className="text-sm font-medium">
              Message to Seller (Optional)
            </Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Tell the seller why you're interested in this item..."
              className="min-h-[80px] resize-none"
              maxLength={500}
            />
            <div className="text-xs text-muted-foreground text-right">
              {message.length}/500 characters
            </div>
          </div>

          <Separator />

          {/* Offer Guidelines */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-foreground">Offer Guidelines</h4>
            <div className="space-y-2 text-xs text-muted-foreground">
              <div className="flex items-start gap-2">
                <Clock className="w-3 h-3 mt-0.5 flex-shrink-0" />
                <span>Offers expire in 7 days</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle2 className="w-3 h-3 mt-0.5 flex-shrink-0" />
                <span>Seller can accept, decline, or counter your offer</span>
              </div>
              <div className="flex items-start gap-2">
                <AlertCircle className="w-3 h-3 mt-0.5 flex-shrink-0" />
                <span>You can only have one active offer per product</span>
              </div>
              {existingOffer && (
                <div className="flex items-start gap-2">
                  <MessageSquare className="w-3 h-3 mt-0.5 flex-shrink-0" />
                  <span>Updating your offer will replace the previous one</span>
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !offerAmount || parseFloat(offerAmount) < minOffer}
              className="min-w-[120px]"
            >
              {isSubmitting ? "Submitting..." : (existingOffer ? "Update Offer" : "Submit Offer")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
