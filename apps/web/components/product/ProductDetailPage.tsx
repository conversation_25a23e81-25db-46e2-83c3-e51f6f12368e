"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import Link from "next/link";
import { Button } from "@repo/ui/components/button";
import { ArrowLeft, Heart, Share2, Eye, Star } from "lucide-react";
import { ProductImageGallery } from "./ProductImageGallery";
import { useFavorite } from "@/hooks/useFavorites";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { ProductLayout } from "@/components/layouts/ProductLayout";

import { SellerInfoCard } from "./SellerInfoCard";
import { PurchaseActions } from "./PurchaseActions";
import { PurchaseConfirmationModal } from "./PurchaseConfirmationModal";
import { ShareProductModal } from "./ShareProductModal";
import { Badge } from "@repo/ui/components/badge";

interface Product {
  _id: Id<"products">;
  title: string;
  brand: string;
  price: number;
  originalPrice?: number;
  description: string;
  category: string;
  condition: string;
  images: string[];
  size?: string;
  color?: string;
  material?: string;
  year?: number;
  tags: string[];
  views: number;
  favorites: number;
  publishedAt?: number;
  _creationTime: number;
  seller: {
    _id: string;
    name: string;
    userType: string;
  } | null;
}

interface Seller {
  _id: string;
  name: string;
  userType: string;
  avatar?: string;
  rating?: number;
  reviewCount?: number;
  totalSales?: number;
  memberSince?: number;
  isVerified?: boolean;
  responseTime?: string;
  location?: string;
  businessName?: string;
  verificationStatus?: string;
}

interface ProductDetailPageProps {
  product: Product;
  seller: Seller;
}

const CONDITION_LABELS = {
  new: "New",
  like_new: "Like New",
  excellent: "Excellent",
  very_good: "Very Good",
  good: "Good",
  fair: "Fair",
};

const CONDITION_COLORS = {
  new: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  like_new: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  excellent: "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300",
  very_good: "bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-300",
  good: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  fair: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
};

export function ProductDetailPage({ product, seller }: ProductDetailPageProps) {
  const router = useRouter();
  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [quantity, setQuantity] = useState(1);
  
  const { isFavorited, toggleFavorite, favoritesCount } = useFavorite(product._id);

  // Fetch related products
  const relatedProducts = useQuery(api.products.getRelatedProducts, {
    productId: product._id as any,
    limit: 4,
  });

  const handlePurchase = () => {
    setIsPurchaseModalOpen(true);
  };

  const handleShare = () => {
    setIsShareModalOpen(true);
  };

  const handleFavorite = async () => {
    await toggleFavorite();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <ProductLayout>
      <div className="container mx-auto px-6 py-12">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-3 text-sm text-muted-foreground mb-8">
          <Link 
            href="/marketplace" 
            className="hover:text-foreground transition-colors font-light"
          >
            Marketplace
          </Link>
          <span className="text-muted-foreground/50">/</span>
          <span className="capitalize hover:text-foreground transition-colors cursor-pointer font-light">
            {product.category}
          </span>
          <span className="text-muted-foreground/50">/</span>
          <span className="text-foreground font-medium truncate">
            {product.title}
          </span>
        </div>


        {/* Main Content */}
        <div className="grid grid-cols-1 xl:grid-cols-12 gap-12">
          {/* Left Column - Image Gallery */}
          <div className="xl:col-span-7">
            <ProductImageGallery 
              images={product.images} 
              title={product.title} 
            />
          </div>

          {/* Right Column - Product Info and Purchase */}
          <div className="xl:col-span-5">
            {/* Product Information - Non-sticky */}
            <div className="bg-card border-0 rounded-xl p-8 shadow-sm hover:shadow-md transition-shadow duration-300 mb-8">
              {/* Header */}
              <div className="mb-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h1 className="text-3xl font-light text-primary tracking-tight mb-2">
                      {product.title}
                    </h1>
                    <p className="text-xl text-muted-foreground font-light mb-3">
                      {product.brand}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleFavorite}
                      className={`h-10 w-10 rounded-full transition-colors duration-300 ${
                        isFavorited 
                          ? 'text-red-500 bg-red-50 dark:bg-red-950' 
                          : 'text-muted-foreground hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-950'
                      }`}
                    >
                      <Heart className={`w-5 h-5 ${isFavorited ? 'fill-current' : ''}`} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleShare}
                      className="h-10 w-10 rounded-full text-muted-foreground hover:text-foreground hover:bg-muted transition-colors duration-300"
                    >
                      <Share2 className="w-5 h-5" />
                    </Button>
                  </div>
                </div>

                {/* Price */}
                <div className="flex items-baseline space-x-3 mb-4">
                  <span className="text-4xl font-light text-foreground">
                    {formatCurrency(product.price)}
                  </span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <span className="text-xl text-muted-foreground line-through font-light">
                      {formatCurrency(product.originalPrice)}
                    </span>
                  )}
                </div>

                {/* Condition Badge */}
                <Badge 
                  className={`${CONDITION_COLORS[product.condition as keyof typeof CONDITION_COLORS]} font-medium`}
                >
                  {CONDITION_LABELS[product.condition as keyof typeof CONDITION_LABELS]}
                </Badge>
              </div>

              {/* Product Details */}
              <div className="space-y-4 mb-6">
                {product.size && (
                  <div className="flex justify-between py-2 border-b border-border">
                    <span className="text-muted-foreground font-light">Size</span>
                    <span className="font-medium text-foreground">{product.size}</span>
                  </div>
                )}
                {product.color && (
                  <div className="flex justify-between py-2 border-b border-border">
                    <span className="text-muted-foreground font-light">Color</span>
                    <span className="font-medium text-foreground capitalize">{product.color}</span>
                  </div>
                )}
                {product.material && (
                  <div className="flex justify-between py-2 border-b border-border">
                    <span className="text-muted-foreground font-light">Material</span>
                    <span className="font-medium text-foreground">{product.material}</span>
                  </div>
                )}
                {product.year && (
                  <div className="flex justify-between py-2 border-b border-border">
                    <span className="text-muted-foreground font-light">Year</span>
                    <span className="font-medium text-foreground">{product.year}</span>
                  </div>
                )}
              </div>

              {/* Description */}
              <div className="mb-6">
                <h3 className="font-medium text-foreground mb-3">Description</h3>
                <p className="text-muted-foreground font-light leading-relaxed">
                  {product.description}
                </p>
              </div>

              {/* Tags */}
              {product.tags && product.tags.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-medium text-foreground mb-3">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {product.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs font-light">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Stats */}
              <div className="flex items-center space-x-6 text-sm text-muted-foreground font-light">
                <div className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>{product.views} views</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Heart className="w-4 h-4" />
                  <span>{favoritesCount} favorites</span>
                </div>
                {product.publishedAt && (
                  <div>
                    <span>Listed {formatDate(product.publishedAt)}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Purchase Actions and Seller Info Container */}
            <div className="space-y-8">
            {/* Purchase Actions */}
            <PurchaseActions
              product={product}
              onPurchase={handlePurchase}
              onShare={handleShare}
            />

            {/* Seller Information */}
            <SellerInfoCard 
              seller={seller} 
              product={{
                _id: product._id,
                title: product.title,
                price: product.price,
                images: product.images,
                brand: product.brand,
              }}
            />
            </div>
          </div>
        </div>

        {/* Related Products Section */}
        {relatedProducts && relatedProducts.length > 0 && (
        <div className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-primary tracking-tight mb-4">
              You might also like
            </h2>
            <p className="text-lg text-muted-foreground font-light">
                More luxury {product.category} from our curated collection
            </p>
          </div>

            {/* Real related products */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {relatedProducts.map((relatedProduct) => (
              <Link
                  key={relatedProduct._id}
                  href={`/marketplace/product/${relatedProduct._id}`}
                  className="bg-card rounded-xl overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1 block group"
                >
                  <div className="aspect-square overflow-hidden">
                    {relatedProduct.images.length > 0 ? (
                      <img
                        src={relatedProduct.images[0]}
                        alt={relatedProduct.title}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-muted/50 to-muted flex items-center justify-center">
                  <div className="text-center space-y-2">
                    <div className="w-16 h-16 bg-muted rounded-2xl mx-auto flex items-center justify-center">
                      <svg className="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                          <p className="text-sm text-muted-foreground font-light">No Image</p>
                        </div>
                  </div>
                    )}
                </div>
                <div className="p-6 space-y-3">
                  <div className="space-y-2">
                      <p className="font-medium text-foreground line-clamp-2">
                        {relatedProduct.title}
                      </p>
                      <p className="text-sm text-muted-foreground font-light">
                        {relatedProduct.brand}
                      </p>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-lg font-light text-foreground">
                        {formatCurrency(relatedProduct.price)}
                      </p>
                      <Badge 
                        className={`${CONDITION_COLORS[relatedProduct.condition as keyof typeof CONDITION_COLORS]} text-xs`}
                      >
                        {CONDITION_LABELS[relatedProduct.condition as keyof typeof CONDITION_LABELS]}
                      </Badge>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
        )}
      </div>

      {/* Modals */}
      <PurchaseConfirmationModal
        isOpen={isPurchaseModalOpen}
        onClose={() => setIsPurchaseModalOpen(false)}
        product={product}
        quantity={quantity}
      />

      <ShareProductModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        product={product}
      />
    </ProductLayout>
  );
}
