"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { 
  ShoppingBag, 
  Heart, 
  Share2, 
  Minus, 
  Plus, 
  CreditCard, 
  Lock,
  Truck,
  Shield,
  DollarSign
} from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { useFavorite } from "@/hooks/useFavorites";
import { useCartActions, useIsInCart } from "@/hooks/useCart";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { MakeOfferModal } from "./MakeOfferModal";

interface Product {
  _id: Id<"products">;
  title: string;
  brand: string;
  price: number;
  category: string;
  condition: string;
  status?: string;
}

interface PurchaseActionsProps {
  product: Product;
  onPurchase: () => void;
  onShare: () => void;
  className?: string;
}

export function PurchaseActions({ 
  product, 
  onPurchase, 
  onShare, 
  className 
}: PurchaseActionsProps) {
  const [quantity, setQuantity] = useState(1);
  const [showMakeOffer, setShowMakeOffer] = useState(false);
  
  const { isFavorited, toggleFavorite } = useFavorite(product._id);
  const { addToCart, removeFromCart, isLoading: isAddingToCart } = useCartActions();
  const isInCart = useIsInCart(product._id);
  
  // Check if user has already made an offer on this product
  const userOffer = useQuery(api.offerManagement.getProductOffers, { 
    productId: product._id as any 
  });
  
  const hasExistingOffer = userOffer && userOffer.length > 0;
  const existingOffer = hasExistingOffer ? userOffer[0] : null;

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= 10) {
      setQuantity(newQuantity);
    }
  };

  const handleCartAction = async () => {
    if (isInCart) {
      await removeFromCart(product._id);
    } else {
      await addToCart(product._id, quantity);
    }
  };

  const handleFavorite = async () => {
    await toggleFavorite();
  };

  const totalPrice = product.price * quantity;
  const estimatedTax = totalPrice * 0.08; // 8% tax
  const shippingCost = totalPrice > 500 ? 0 : 25; // Free shipping over $500
  const finalTotal = totalPrice + estimatedTax + shippingCost;

  return (
    <div className={cn("bg-card border-0 rounded-xl p-8 shadow-sm hover:shadow-md transition-shadow duration-300", className)}>
      <div className="space-y-8">
        {/* Quantity Selector */}
        {/* <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="text-lg font-medium text-foreground">
              Quantity
            </label>
            {isInCart && (
              <div className="text-xs text-green-600 dark:text-green-400 font-light">
                <span>✓ In Cart</span>
              </div>
            )}
          </div>
          <div className="flex items-center justify-center space-x-4 bg-muted rounded-2xl p-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(quantity - 1)}
              disabled={quantity <= 1}
              className="w-12 h-12 p-0 rounded-full border-2 border-border hover:border-foreground transition-colors duration-300 disabled:opacity-50"
            >
              <Minus className="w-5 h-5" />
            </Button>
            <span className="w-16 text-center text-2xl font-light text-foreground">
              {quantity}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(quantity + 1)}
              disabled={quantity >= 10}
              className="w-12 h-12 p-0 rounded-full border-2 border-border hover:border-foreground transition-colors duration-300 disabled:opacity-50"
            >
              <Plus className="w-5 h-5" />
            </Button>
          </div>
        </div> */}

        {/* Price Breakdown */}
        <div className="space-y-4">
          <div className="space-y-3">
            <div className="flex justify-between text-lg">
              <span className="text-muted-foreground font-light">
                Item price ({quantity}x)
              </span>
              <span className="font-medium text-foreground">
                ${totalPrice.toLocaleString()}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-muted-foreground font-light">
                Estimated tax
              </span>
              <span className="font-medium text-foreground">
                ${estimatedTax.toFixed(2)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-muted-foreground font-light">
                Shipping
              </span>
              <span className="font-medium text-foreground">
                {shippingCost === 0 ? (
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 text-xs font-light">
                    Free
                  </Badge>
                ) : (
                  `$${shippingCost}`
                )}
              </span>
            </div>
          </div>
          
          <Separator />
          
          <div className="flex justify-between text-2xl font-medium">
            <span className="text-foreground">Total</span>
            <span className="text-foreground">
              ${finalTotal.toFixed(2)}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Button
            onClick={onPurchase}
            className="w-full h-14 text-lg font-light bg-primary hover:bg-primary/90 transition-colors duration-300 rounded-xl"
          >
            <CreditCard className="w-6 h-6 mr-3" />
            Buy Now - ${finalTotal.toFixed(2)}
          </Button>
          
          <Button
            variant={isInCart ? "secondary" : "outline"}
            onClick={handleCartAction}
            disabled={isAddingToCart}
            className={cn(
              "w-full h-14 text-lg font-light rounded-xl border-2 transition-colors duration-300",
              isInCart && "bg-green-50 border-green-200 text-green-800 hover:bg-green-100 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300 dark:hover:bg-green-900/30"
            )}
          >
            <ShoppingBag className="w-6 h-6 mr-3" />
            {isAddingToCart 
              ? (isInCart ? "Removing..." : "Adding...") 
              : (isInCart ? "Remove from Cart" : "Add to Cart")
            }
          </Button>

          {/* Offer Button - Only show if product is active */}
          {(!product.status || product.status === "active") && (
            <>
              {/* Offer Info */}
              <div className="text-center text-xs text-muted-foreground mb-2">
                💡 Negotiate the price with the seller
              </div>
              
              {/* Show existing offer status if user has made an offer */}
              {hasExistingOffer && existingOffer && (
                <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="text-center text-sm">
                    <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                      Your Offer: ${existingOffer.offerAmount?.toLocaleString()}
                    </p>
                    <p className="text-xs text-blue-600 dark:text-blue-300">
                      Status: {existingOffer.status?.charAt(0).toUpperCase() + existingOffer.status?.slice(1)}
                      {existingOffer.status === "countered" && existingOffer.counterOffer && (
                        <span className="block mt-1">
                          Seller's Counter: ${existingOffer.counterOffer.toLocaleString()}
                        </span>
                      )}
                      {existingOffer.status === "pending" && existingOffer.expiresAt && (
                        <span className="block mt-1 text-xs">
                          Expires: {new Date(existingOffer.expiresAt).toLocaleDateString()}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              )}
              
              <Button
                variant="outline"
                onClick={() => setShowMakeOffer(true)}
                className="w-full h-14 text-lg font-light rounded-xl border-2 border-amber-600 text-amber-700 hover:bg-amber-50 hover:border-amber-700 transition-all duration-300 shadow-sm hover:shadow-md"
                disabled={hasExistingOffer && existingOffer?.status === "pending"}
              >
                <DollarSign className="w-6 h-6 mr-3 text-amber-600" />
                {hasExistingOffer && existingOffer?.status === "pending" 
                  ? "Offer Pending" 
                  : hasExistingOffer 
                    ? "Update Offer" 
                    : "Make an Offer"
                }
              </Button>
            </>
          )}
          
          {/* Product Status Info */}
          {product.status && product.status !== "active" && (
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">
                {product.status === "reserved" && "🚫 Product reserved - no offers accepted"}
                {product.status === "sold" && "✅ Product sold"}
                {product.status === "archived" && "📦 Product archived"}
              </p>
            </div>
          )}
          
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              onClick={handleFavorite}
              className="h-12 rounded-xl border-2 font-light transition-colors duration-300"
            >
              <Heart
                className={cn(
                  "w-5 h-5 mr-2 transition-colors",
                  isFavorited ? "fill-red-500 text-red-500" : "text-muted-foreground"
                )}
              />
              {isFavorited ? "Saved" : "Save"}
            </Button>
            
            <Button
              variant="outline"
              onClick={onShare}
              className="h-12 rounded-xl border-2 font-light transition-colors duration-300"
            >
              <Share2 className="w-5 h-5 mr-2" />
              Share
            </Button>
          </div>
        </div>

        {/* Security & Trust */}
        <div className="space-y-4 pt-4 border-t border-border">
          <div className="flex items-center space-x-3 text-sm text-muted-foreground font-light">
            <Lock className="w-4 h-4" />
            <span>Secure checkout with 256-bit SSL encryption</span>
          </div>
          
          <div className="flex items-center space-x-3 text-sm text-muted-foreground font-light">
            <Shield className="w-4 h-4" />
            <span>Authenticity guaranteed or money back</span>
          </div>
          
          <div className="flex items-center space-x-3 text-sm text-muted-foreground font-light">
            <Truck className="w-4 h-4" />
            <span>Insured shipping with tracking</span>
          </div>
        </div>

        {/* Shipping Info */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Truck className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span className="font-medium text-blue-800 dark:text-blue-300">Fast & Secure Delivery</span>
          </div>
          <p className="text-sm text-blue-700 dark:text-blue-400 font-light">
            Your item will be carefully packaged and shipped with full insurance coverage.
          </p>
        </div>
      </div>

      {/* Make Offer Modal */}
      <MakeOfferModal
        isOpen={showMakeOffer}
        onClose={() => setShowMakeOffer(false)}
        product={product}
        existingOffer={existingOffer as any}
      />
    </div>
  );
}
