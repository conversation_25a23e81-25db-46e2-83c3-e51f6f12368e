"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { 
  Copy, 
  Check, 
  Facebook, 
  Twitter, 
  Instagram, 
  MessageCircle,
  Mail,
  Link2
} from "lucide-react";
import { toast } from "sonner";

interface Product {
  _id: string;
  title: string;
  brand: string;
  price: number;
  images: string[];
}

interface ShareProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
}

export function ShareProductModal({
  isOpen,
  onClose,
  product,
}: ShareProductModalProps) {
  const [copied, setCopied] = useState(false);

  const productUrl = `${window.location.origin}/marketplace/product/${product._id}`;
  const shareText = `Check out this ${product.brand} ${product.title} for $${product.price.toLocaleString()} on HauteVault`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(productUrl);
      setCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("Failed to copy link");
    }
  };

  const shareOptions = [
    {
      name: "Facebook",
      icon: Facebook,
      color: "bg-blue-600 hover:bg-blue-700",
      action: () => {
        const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(productUrl)}`;
        window.open(url, '_blank', 'width=600,height=400');
      }
    },
    {
      name: "Twitter",
      icon: Twitter,
      color: "bg-sky-500 hover:bg-sky-600",
      action: () => {
        const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(productUrl)}`;
        window.open(url, '_blank', 'width=600,height=400');
      }
    },
    {
      name: "Instagram",
      icon: Instagram,
      color: "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",
      action: () => {
        // Instagram doesn't have direct sharing, so copy link
        handleCopyLink();
        toast.info("Link copied! Paste it in your Instagram story or post");
      }
    },
    {
      name: "WhatsApp",
      icon: MessageCircle,
      color: "bg-green-500 hover:bg-green-600",
      action: () => {
        const url = `https://wa.me/?text=${encodeURIComponent(`${shareText} ${productUrl}`)}`;
        window.open(url, '_blank');
      }
    },
    {
      name: "Email",
      icon: Mail,
      color: "bg-neutral-600 hover:bg-neutral-700",
      action: () => {
        const subject = encodeURIComponent(`Check out this luxury item on HauteVault`);
        const body = encodeURIComponent(`${shareText}\n\n${productUrl}`);
        window.open(`mailto:?subject=${subject}&body=${body}`);
      }
    },
    {
      name: "Copy Link",
      icon: copied ? Check : Copy,
      color: copied ? "bg-green-600" : "bg-black hover:bg-neutral-800 dark:bg-white dark:hover:bg-neutral-200",
      textColor: copied ? "text-white" : "text-white dark:text-black",
      action: handleCopyLink
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto bg-white dark:bg-neutral-900 rounded-3xl border-0 shadow-2xl">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-2xl font-bold text-black dark:text-white text-center">
            Share this item
          </DialogTitle>
        </DialogHeader>

        <div className="p-6 space-y-6">
          {/* Product Preview */}
          <div className="bg-neutral-50 dark:bg-neutral-800 rounded-2xl p-4">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-neutral-200 dark:bg-neutral-700 rounded-xl overflow-hidden">
                {product.images[0] && (
                  <img
                    src={product.images[0]}
                    alt={product.title}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-black dark:text-white text-sm">
                  {product.title}
                </h4>
                <p className="text-neutral-600 dark:text-neutral-400 text-sm">
                  {product.brand}
                </p>
                <p className="text-lg font-bold text-black dark:text-white">
                  ${product.price.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          {/* Share Options */}
          <div className="grid grid-cols-3 gap-3">
            {shareOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <button
                  key={option.name}
                  onClick={option.action}
                  className={`${option.color} ${option.textColor || 'text-white'} p-4 rounded-2xl transition-all duration-200 hover:scale-105 flex flex-col items-center space-y-2`}
                >
                  <IconComponent className="w-6 h-6" />
                  <span className="text-xs font-medium">{option.name}</span>
                </button>
              );
            })}
          </div>

          {/* Copy Link Section */}
          <div className="space-y-3">
            <label className="text-sm font-medium text-black dark:text-white">
              Product Link
            </label>
            <div className="flex space-x-2">
              <Input
                value={productUrl}
                readOnly
                className="flex-1 bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 rounded-xl"
              />
              <Button
                onClick={handleCopyLink}
                variant="outline"
                className="px-4 rounded-xl"
              >
                {copied ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>

          {/* QR Code Option */}
          <div className="text-center">
            <Button
              variant="outline"
              className="w-full rounded-xl h-12"
              onClick={() => {
                // TODO: Implement QR code generation
                toast.info("QR code feature coming soon!");
              }}
            >
              <Link2 className="w-5 h-5 mr-2" />
              Generate QR Code
            </Button>
          </div>

          {/* Close Button */}
          <Button
            onClick={onClose}
            variant="outline"
            className="w-full rounded-xl h-12"
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
