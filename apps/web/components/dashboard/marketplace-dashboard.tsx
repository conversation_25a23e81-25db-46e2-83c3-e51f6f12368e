"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { 
  Package, 
  DollarSign, 
  TrendingUp, 
  ShoppingBag,
  Clock,
  CheckCircle2,
  XCircle,
  TrendingDown
} from "lucide-react";
import Link from "next/link";

interface MarketplaceDashboardProps {
  user: any;
}

export function MarketplaceDashboard({ user }: MarketplaceDashboardProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-background/95 backdrop-blur">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold text-foreground">HauteVault Marketplace</h1>
            <Badge className="bg-primary text-primary-foreground">Active Subscriber</Badge>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-muted-foreground">Welcome, {user.name}</span>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* Quick Actions */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Link href="/marketplace">
            <Card className="bg-card border-0 shadow-sm hover:shadow-md transition-shadow duration-300 cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Browse Products</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">Explore</div>
                <p className="text-xs text-muted-foreground">Discover luxury items</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/offers">
            <Card className="bg-card border-0 shadow-sm hover:shadow-md transition-shadow duration-300 cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">My Offers</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">Track</div>
                <p className="text-xs text-muted-foreground">Manage your offers</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/cart">
            <Card className="bg-card border-0 shadow-sm hover:shadow-md transition-shadow duration-300 cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Shopping Cart</CardTitle>
                <ShoppingBag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">Cart</div>
                <p className="text-xs text-muted-foreground">View your items</p>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Recent Activity */}
        <Card className="bg-card border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-foreground">Recent Activity</CardTitle>
            <CardDescription className="text-muted-foreground">
              Your latest marketplace interactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border border-border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="font-medium text-foreground">Offer Submitted</h3>
                    <p className="text-sm text-muted-foreground">Hermès Birkin 35 - $12,500</p>
                  </div>
                </div>
                <Badge variant="outline" className="text-amber-600 border-amber-600">
                  <Clock className="w-3 h-3 mr-1" />
                  Pending
                </Badge>
              </div>

              <div className="flex items-center justify-between p-4 border border-border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="font-medium text-foreground">Counter Offer Received</h3>
                    <p className="text-sm text-muted-foreground">Chanel Classic Flap - $9,200</p>
                  </div>
                </div>
                <Badge variant="outline" className="text-blue-600 border-blue-600">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  Countered
                </Badge>
              </div>

              <div className="flex items-center justify-between p-4 border border-border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                    <CheckCircle2 className="w-6 h-6 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="font-medium text-foreground">Offer Accepted</h3>
                    <p className="text-sm text-muted-foreground">Rolex Submariner - $15,800</p>
                  </div>
                </div>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  <CheckCircle2 className="w-3 h-3 mr-1" />
                  Accepted
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}