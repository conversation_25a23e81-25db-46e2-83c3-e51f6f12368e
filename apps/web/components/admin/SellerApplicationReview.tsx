"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Textarea } from "@repo/ui/components/textarea";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Building, 
  Mail,
  Phone,
  MapPin
} from "lucide-react";
import { toast } from "sonner";

interface SellerApplicationReviewProps {
  application: any;
  isOpen: boolean;
  onClose: () => void;
  onStatusUpdate: () => void;
}

export function SellerApplicationReview({
  application,
  isOpen,
  onClose,
  onStatusUpdate,
}: SellerApplicationReviewProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [adminNotes, setAdminNotes] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");
  const [commissionRate, setCommissionRate] = useState("5");

  const updateApplicationStatus = useMutation(api.sellerApplicationsSimple.updateApplicationStatus);

  const handleStatusUpdate = async (status: "approved" | "rejected" | "under_review") => {
    if (!application) return;

    setIsProcessing(true);
    try {
      await updateApplicationStatus({
        applicationId: application._id,
        status,
        notes: adminNotes || undefined,
        rejectionReason: status === "rejected" ? rejectionReason : undefined,
      });

      toast.success(`Application ${status} successfully`);
      onStatusUpdate();
      onClose();
    } catch (error) {
      console.error("Error updating application:", error);
      toast.error(`Failed to ${status} application`);
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Approved</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      case "under_review":
        return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">Under Review</Badge>;
      case "pending":
        return <Badge variant="secondary">Pending</Badge>;
      case "requires_info":
        return <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">Requires Info</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (!application) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Building className="w-5 h-5" />
            {application.businessName}
            {getStatusBadge(application.status)}
          </DialogTitle>
          <DialogDescription>
            Review seller application details and update status
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Applicant Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Contact Information
              </h3>
              <div className="space-y-2">
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Name</Label>
                  <p className="font-medium">{application.firstName} {application.lastName}</p>
                </div>
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Email</Label>
                  <p>{application.email}</p>
                </div>
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Phone</Label>
                  <p>{application.phone}</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Building className="w-4 h-4" />
                Business Information
              </h3>
              <div className="space-y-2">
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Business Name</Label>
                  <p className="font-medium">{application.businessName}</p>
                </div>
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Business Type</Label>
                  <p className="capitalize">{application.businessType}</p>
                </div>
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Tax ID</Label>
                  <p>{application.taxId || "Not provided"}</p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Business Address */}
          <div>
            <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
              <MapPin className="w-4 h-4" />
              Business Address
            </h3>
            <div className="bg-neutral-50 dark:bg-neutral-900 p-4 rounded-lg">
              <p>{application.businessAddress}</p>
              <p>{application.businessCity}, {application.businessState} {application.businessZip}</p>
              <p>{application.businessCountry}</p>
            </div>
          </div>

          <Separator />

          {/* Business Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Business Details</h3>
              <div className="space-y-2">
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Monthly Volume</Label>
                  <p>{application.monthlyVolume || "Not specified"}</p>
                </div>
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Years Experience</Label>
                  <p>{application.yearsExperience || "Not specified"}</p>
                </div>
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Previous Platforms</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {application.previousPlatforms?.map((platform: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {platform}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <Label className="text-sm text-neutral-600 dark:text-neutral-400">Specialties</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {application.specialties?.map((specialty: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>

          </div>

          <Separator />

          {/* Admin Actions */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Administrative Actions</h3>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="adminNotes">Admin Notes</Label>
                <Textarea
                  id="adminNotes"
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add notes about this application..."
                  className="mt-1"
                />
              </div>

              {application.verificationStatus !== "approved" && (
                <div>
                  <Label htmlFor="commissionRate">Commission Rate (%)</Label>
                  <Input
                    id="commissionRate"
                    type="number"
                    value={commissionRate}
                    onChange={(e) => setCommissionRate(e.target.value)}
                    min="0"
                    max="100"
                    step="0.1"
                    className="mt-1 w-32"
                  />
                </div>
              )}

              <div>
                <Label htmlFor="rejectionReason">Rejection Reason (if rejecting)</Label>
                <Textarea
                  id="rejectionReason"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Provide reason for rejection..."
                  className="mt-1"
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          
          {application.status === "pending" && (
            <Button
              variant="outline"
              onClick={() => handleStatusUpdate("under_review")}
              disabled={isProcessing}
            >
              <Clock className="w-4 h-4 mr-2" />
              Mark Under Review
            </Button>
          )}
          
          {application.status !== "rejected" && (
            <Button
              variant="destructive"
              onClick={() => handleStatusUpdate("rejected")}
              disabled={isProcessing || !rejectionReason.trim()}
            >
              <XCircle className="w-4 h-4 mr-2" />
              Reject
            </Button>
          )}
          
          {application.status !== "approved" && (
            <Button
              onClick={() => handleStatusUpdate("approved")}
              disabled={isProcessing}
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Approve
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}