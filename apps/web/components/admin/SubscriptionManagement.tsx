"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { 
  Search, 
  CreditCard,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  RefreshCw,
  Eye,
  Crown,
  Star,
  Zap
} from "lucide-react";
import { toast } from "sonner";

interface SubscriptionManagementProps {
  activeTab?: "overview" | "expiring" | "analytics";
}

export function SubscriptionManagement({ activeTab = "overview" }: SubscriptionManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [planFilter, setPlanFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d" | "1y">("30d");

  // Queries
  const subscriptionMetrics = useQuery(api.subscriptionQueries.getSubscriptionMetrics, {
    timeRange,
    includeChurnAnalysis: true
  });

  const expiringSubscriptions = useQuery(api.subscriptionQueries.getExpiringSubscriptions, {
    daysAhead: 30,
    includeGracePeriod: true,
    limit: 100
  });

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "trial": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "inactive": return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      case "expired": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case "basic": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "premium": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case "enterprise": return "bg-gold-100 text-gold-800 dark:bg-gold-900 dark:text-gold-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case "basic": return Star;
      case "premium": return Crown;
      case "enterprise": return Zap;
      default: return CreditCard;
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "critical": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "high": return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "low": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  if (!subscriptionMetrics) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600 dark:text-gray-400">Loading subscription data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-black dark:text-white">
            Subscription Management
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-1">
            Monitor subscriptions, revenue, and user engagement
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 days</SelectItem>
              <SelectItem value="30d">30 days</SelectItem>
              <SelectItem value="90d">90 days</SelectItem>
              <SelectItem value="1y">1 year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              Active Subscriptions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {subscriptionMetrics.overview.activeSubscriptions || 0}
            </div>
            <Badge className="mt-1">
              {subscriptionMetrics.overview.trialUsers || 0} trials
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Monthly Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(subscriptionMetrics.overview.subscriptionRate || 0)}
            </div>
            <Badge variant="secondary" className="mt-1">
              MRR
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Growth Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {Math.round((subscriptionMetrics.overview.subscriptionRate || 0) * 100) / 100}%
            </div>
            <Badge variant="secondary" className="mt-1">
              Trial conversion
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
              <AlertTriangle className="w-4 h-4" />
              Expiring Soon
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {subscriptionMetrics.expiring.next30Days || 0}
            </div>
            <Badge variant="secondary" className="mt-1">
              Next 7 days
            </Badge>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue={activeTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="expiring">
            Expiring Subscriptions
            {(subscriptionMetrics.expiring.next7Days || 0) > 0 && (
              <Badge variant="destructive" className="ml-2">
                {subscriptionMetrics.expiring.next7Days}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Plan Distribution */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-blue-600" />
                  Basic Plan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {subscriptionMetrics.planBreakdown?.basic || 0}
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {formatCurrency((subscriptionMetrics.planBreakdown?.basic || 0) * 29.99)} MRR
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crown className="w-5 h-5 text-purple-600" />
                  Premium Plan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {subscriptionMetrics.planBreakdown?.premium || 0}
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {formatCurrency((subscriptionMetrics.planBreakdown?.premium || 0) * 59.99)} MRR
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-gold-600" />
                  Enterprise Plan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {subscriptionMetrics.planBreakdown?.enterprise || 0}
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {formatCurrency((subscriptionMetrics.planBreakdown?.enterprise || 0) * 149.99)} MRR
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Growth Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Growth Metrics</CardTitle>
              <CardDescription>
                Subscription activity in the last {timeRange}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {subscriptionMetrics.overview.totalUsers || 0}
                  </div>
                  <p className="text-sm text-gray-600">New Subscriptions</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {subscriptionMetrics.overview.activeSubscriptions || 0}
                  </div>
                  <p className="text-sm text-gray-600">Upgrades</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {subscriptionMetrics.overview.trialUsers || 0}
                  </div>
                  <p className="text-sm text-gray-600">Downgrades</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {subscriptionMetrics.overview.inactiveUsers || 0}
                  </div>
                  <p className="text-sm text-gray-600">Cancellations</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="expiring" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Expiring Subscriptions</CardTitle>
              <CardDescription>
                Users with subscriptions expiring in the next 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              {expiringSubscriptions && expiringSubscriptions.summary.totalExpiring > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Plan</TableHead>
                      <TableHead>Expires</TableHead>
                      <TableHead>Urgency</TableHead>
                      <TableHead>Revenue Impact</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {expiringSubscriptions.users.map((subscription: any) => {
                      const PlanIcon = getPlanIcon(subscription.subscriptionPlan);
                      return (
                        <TableRow key={subscription.userId}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{subscription.name}</div>
                              <div className="text-sm text-gray-500">{subscription.email}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <PlanIcon className="w-4 h-4" />
                              <Badge className={getPlanColor(subscription.subscriptionPlan)}>
                                {subscription.subscriptionPlan}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {formatDate(subscription.subscriptionExpiresAt)}
                              </div>
                              <div className="text-sm text-gray-500">
                                {subscription.daysUntilExpiration} days
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getUrgencyColor(subscription.urgency)}>
                              {subscription.urgency}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {formatCurrency(subscription.monthlyValue)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button size="sm" variant="outline">
                              <Eye className="w-4 h-4 mr-2" />
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="w-12 h-12 mx-auto text-green-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    No subscriptions expiring soon
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Analytics</CardTitle>
              <CardDescription>
                Detailed revenue and growth analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-4">Revenue Breakdown</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Monthly Recurring Revenue:</span>
                      <span className="font-medium">
                        {formatCurrency(subscriptionMetrics.overview.subscriptionRate || 0)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Annual Recurring Revenue:</span>
                      <span className="font-medium">
                        {formatCurrency(subscriptionMetrics.overview.subscriptionRate || 0)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average Revenue Per User:</span>
                      <span className="font-medium">
                        {formatCurrency(
                          subscriptionMetrics.overview.activeSubscriptions > 0 
                            ? (subscriptionMetrics.overview.subscriptionRate || 0) / subscriptionMetrics.overview.activeSubscriptions
                            : 0
                        )}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-4">Conversion Metrics</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Trial to Paid Conversion:</span>
                      <span className="font-medium">
                        {Math.round((subscriptionMetrics.overview.subscriptionRate || 0) * 100) / 100}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Upgrade Rate:</span>
                      <span className="font-medium">
                        {Math.round(((subscriptionMetrics.overview.activeSubscriptions || 0) / Math.max(subscriptionMetrics.overview.activeSubscriptions || 1, 1)) * 100)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Churn Rate:</span>
                      <span className="font-medium text-red-600">
                        {subscriptionMetrics.churnAnalysis?.churnRate || 0}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

