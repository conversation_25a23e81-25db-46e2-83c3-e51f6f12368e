"use client";

import { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Switch } from "@repo/ui/components/switch";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { 
  Settings, 
  DollarSign, 
  Package,
  Plus,
  Edit,
  Trash2,
  Save,
  Crown,
  Tag,
  Percent
} from "lucide-react";

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  features: string[];
  isActive: boolean;
}

interface PlatformFee {
  id: string;
  name: string;
  type: "percentage" | "fixed";
  value: number;
  description: string;
  isActive: boolean;
}

interface Category {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  itemCount: number;
}

interface Brand {
  id: string;
  name: string;
  isWhitelisted: boolean;
  addedDate: string;
  category: string;
}

// Mock data
const MOCK_SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: "1",
    name: "Basic",
    price: 29,
    features: ["Up to 10 listings", "Basic analytics", "Email support"],
    isActive: true
  },
  {
    id: "2",
    name: "Premium",
    price: 79,
    features: ["Up to 50 listings", "Advanced analytics", "Priority support", "Featured listings"],
    isActive: true
  },
  {
    id: "3",
    name: "Enterprise",
    price: 199,
    features: ["Unlimited listings", "Custom analytics", "Dedicated support", "API access"],
    isActive: true
  }
];

const MOCK_PLATFORM_FEES: PlatformFee[] = [
  {
    id: "1",
    name: "Transaction Fee",
    type: "percentage",
    value: 5.0,
    description: "Fee charged on each successful transaction",
    isActive: true
  },
  {
    id: "2",
    name: "Listing Fee",
    type: "fixed",
    value: 2.99,
    description: "One-time fee for creating a listing",
    isActive: true
  },
  {
    id: "3",
    name: "Premium Listing Fee",
    type: "fixed",
    value: 9.99,
    description: "Fee for featured/premium listings",
    isActive: true
  }
];

const MOCK_CATEGORIES: Category[] = [
  { id: "1", name: "Handbags", description: "Luxury handbags and purses", isActive: true, itemCount: 1250 },
  { id: "2", name: "Accessories", description: "Jewelry, watches, and accessories", isActive: true, itemCount: 890 },
  { id: "3", name: "Clothing", description: "Designer clothing and apparel", isActive: true, itemCount: 2100 },
  { id: "4", name: "Shoes", description: "Designer footwear", isActive: true, itemCount: 650 }
];

const MOCK_BRANDS: Brand[] = [
  { id: "1", name: "Hermès", isWhitelisted: true, addedDate: "2024-01-01", category: "Handbags" },
  { id: "2", name: "Chanel", isWhitelisted: true, addedDate: "2024-01-01", category: "Handbags" },
  { id: "3", name: "Louis Vuitton", isWhitelisted: true, addedDate: "2024-01-01", category: "Handbags" },
  { id: "4", name: "Rolex", isWhitelisted: true, addedDate: "2024-01-01", category: "Accessories" }
];

export function PlatformSettings() {
  const [activeTab, setActiveTab] = useState<"pricing" | "fees" | "categories" | "brands">("pricing");
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>(MOCK_SUBSCRIPTION_PLANS);
  const [platformFees, setPlatformFees] = useState<PlatformFee[]>(MOCK_PLATFORM_FEES);
  const [categories, setCategories] = useState<Category[]>(MOCK_CATEGORIES);
  const [brands, setBrands] = useState<Brand[]>(MOCK_BRANDS);
  const [editDialog, setEditDialog] = useState(false);
  const [editItem, setEditItem] = useState<any>(null);
  const [newBrandName, setNewBrandName] = useState("");
  const [newBrandCategory, setNewBrandCategory] = useState("");

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleSaveSettings = () => {
    console.warn("Saving platform settings...");
    // Implementation would go here
  };

  const handleAddBrand = () => {
    if (newBrandName && newBrandCategory) {
      const newBrand: Brand = {
        id: Date.now().toString(),
        name: newBrandName,
        isWhitelisted: true,
        addedDate: new Date().toISOString(),
        category: newBrandCategory
      };
      setBrands([...brands, newBrand]);
      setNewBrandName("");
      setNewBrandCategory("");
    }
  };

  const toggleBrandWhitelist = (brandId: string) => {
    setBrands(brands.map(brand => 
      brand.id === brandId 
        ? { ...brand, isWhitelisted: !brand.isWhitelisted }
        : brand
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-light text-foreground">
            Platform Settings
          </h1>
          <p className="text-lg text-muted-foreground font-light mt-1">
            Configure platform pricing, fees, and policies
          </p>
        </div>
        
        <Button 
          onClick={handleSaveSettings}
          className="bg-primary text-primary-foreground hover:bg-primary/90"
        >
          <Save className="w-4 h-4 mr-2" />
          Save Changes
        </Button>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center space-x-1 bg-muted p-1 rounded-xl w-fit">
        <Button
          variant={activeTab === "pricing" ? "default" : "ghost"}
          onClick={() => setActiveTab("pricing")}
          className="h-10 px-6"
        >
          <DollarSign className="w-4 h-4 mr-2" />
          Subscription Pricing
        </Button>
        <Button
          variant={activeTab === "fees" ? "default" : "ghost"}
          onClick={() => setActiveTab("fees")}
          className="h-10 px-6"
        >
          <Percent className="w-4 h-4 mr-2" />
          Platform Fees
        </Button>
        <Button
          variant={activeTab === "categories" ? "default" : "ghost"}
          onClick={() => setActiveTab("categories")}
          className="h-10 px-6"
        >
          <Package className="w-4 h-4 mr-2" />
          Categories
        </Button>
        <Button
          variant={activeTab === "brands" ? "default" : "ghost"}
          onClick={() => setActiveTab("brands")}
          className="h-10 px-6"
        >
          <Crown className="w-4 h-4 mr-2" />
          Brand Whitelist
        </Button>
      </div>

      {/* Subscription Pricing Tab */}
      {activeTab === "pricing" && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {subscriptionPlans.map((plan) => (
            <Card key={plan.id} className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-light text-foreground">
                  {plan.name}
                </h3>
                <Switch checked={plan.isActive} />
              </div>
              
              <div className="mb-6">
                <span className="text-3xl font-light text-foreground">
                  {formatCurrency(plan.price)}
                </span>
                <span className="text-muted-foreground">/month</span>
              </div>
              
              <div className="space-y-3 mb-6">
                {plan.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-sm text-muted-foreground font-light">
                      {feature}
                    </span>
                  </div>
                ))}
              </div>
              
              <Button variant="outline" className="w-full">
                <Edit className="w-4 h-4 mr-2" />
                Edit Plan
              </Button>
            </Card>
          ))}
        </div>
      )}

      {/* Platform Fees Tab */}
      {activeTab === "fees" && (
        <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted">
                <TableHead>Fee Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-12">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {platformFees.map((fee) => (
                <TableRow key={fee.id} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                  <TableCell className="font-semibold text-black dark:text-white">
                    {fee.name}
                  </TableCell>
                  
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {fee.type}
                    </Badge>
                  </TableCell>
                  
                  <TableCell className="font-bold">
                    {fee.type === "percentage" ? `${fee.value}%` : formatCurrency(fee.value)}
                  </TableCell>
                  
                  <TableCell className="text-neutral-600 dark:text-neutral-400">
                    {fee.description}
                  </TableCell>
                  
                  <TableCell>
                    <Badge className={fee.isActive 
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                    }>
                      {fee.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <Edit className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {/* Categories Tab */}
      {activeTab === "categories" && (
        <Card className="overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-neutral-50 dark:bg-neutral-900">
                <TableHead>Category Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Items</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-12">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {categories.map((category) => (
                <TableRow key={category.id} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                  <TableCell className="font-semibold text-black dark:text-white">
                    {category.name}
                  </TableCell>
                  
                  <TableCell className="text-neutral-600 dark:text-neutral-400">
                    {category.description}
                  </TableCell>
                  
                  <TableCell>
                    <span className="font-semibold text-blue-600 dark:text-blue-400">
                      {category.itemCount.toLocaleString()}
                    </span>
                  </TableCell>
                  
                  <TableCell>
                    <Badge className={category.isActive 
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                    }>
                      {category.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {/* Brand Whitelist Tab */}
      {activeTab === "brands" && (
        <div className="space-y-6">
          {/* Add New Brand */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-black dark:text-white mb-4">
              Add New Brand
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="brandName">Brand Name</Label>
                <Input
                  id="brandName"
                  value={newBrandName}
                  onChange={(e) => setNewBrandName(e.target.value)}
                  placeholder="Enter brand name"
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="brandCategory">Category</Label>
                <Input
                  id="brandCategory"
                  value={newBrandCategory}
                  onChange={(e) => setNewBrandCategory(e.target.value)}
                  placeholder="Enter category"
                  className="mt-1"
                />
              </div>
              
              <div className="flex items-end">
                <Button onClick={handleAddBrand} className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Brand
                </Button>
              </div>
            </div>
          </Card>

          {/* Brand List */}
          <Card className="overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-neutral-50 dark:bg-neutral-900">
                  <TableHead>Brand Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Added Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {brands.map((brand) => (
                  <TableRow key={brand.id} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Crown className="w-4 h-4 text-yellow-500" />
                        <span className="font-semibold text-black dark:text-white">
                          {brand.name}
                        </span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge variant="outline">
                        {brand.category}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      {formatDate(brand.addedDate)}
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Switch 
                          checked={brand.isWhitelisted}
                          onCheckedChange={() => toggleBrandWhitelist(brand.id)}
                        />
                        <span className="text-sm text-neutral-600 dark:text-neutral-400">
                          {brand.isWhitelisted ? "Whitelisted" : "Not Whitelisted"}
                        </span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </div>
      )}
    </div>
  );
}
