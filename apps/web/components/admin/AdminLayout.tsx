"use client"

import { ReactNode } from "react"
import { usePathname } from "next/navigation"
import { useAuth } from "@/hooks/useBetterAuth"
import Link from "next/link"

import { AdminSidebar } from "@/components/admin/AdminSidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@repo/ui/components/breadcrumb"
import { Separator } from "@repo/ui/components/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@repo/ui/components/sidebar"

interface AdminLayoutProps {
  children: ReactNode
}

const breadcrumbMap: Record<string, { label: string; href?: string }[]> = {
  '/admin': [{ label: 'Admin Dashboard' }],
  '/admin/support-tickets': [{ label: 'Support Tickets' }],
  '/admin/cleanup': [{ label: 'Cleanup' }],
  '/admin/seller-applications': [{ label: 'Seller Applications' }],
  '/admin/users': [{ label: 'User Management' }],
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname()
  const { user, isLoading, isAuthenticated } = useAuth()

  // Show loading state
  if (isLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Redirect if not authenticated or not an admin
  if (!isAuthenticated || !user || user.userType !== "admin") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Access Restricted</h2>
          <p className="text-muted-foreground mb-6">
            You need to be logged in as an admin to access this page.
          </p>
          <Link
            href="/login"
            className="bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90"
          >
            Sign In
          </Link>
        </div>
      </div>
    )
  }

  const breadcrumbs = breadcrumbMap[pathname] || [{ label: 'Admin Dashboard' }]

  return (
    <SidebarProvider>
      <AdminSidebar />
      <SidebarInset className="max-h-[calc(100vh-16px)] flex flex-col overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b border-border bg-background">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/admin">
                    Admin Portal
                  </BreadcrumbLink>
                </BreadcrumbItem>
                {breadcrumbs.length > 1 && (
                  <BreadcrumbSeparator className="hidden md:block" />
                )}
                {breadcrumbs.map((crumb, index) => (
                  <div key={index} className="flex items-center">
                    {index > 0 && <BreadcrumbSeparator className="hidden md:block" />}
                    <BreadcrumbItem>
                      {crumb.href ? (
                        <BreadcrumbLink href={crumb.href}>
                          {crumb.label}
                        </BreadcrumbLink>
                      ) : (
                        <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                      )}
                    </BreadcrumbItem>
                  </div>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <main className="flex-1 overflow-hidden">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}

