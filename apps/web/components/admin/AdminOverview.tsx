"use client";

import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { 
  Users, 
  UserCheck, 
  DollarSign, 
  Package,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  Crown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
  Calendar,
  Shield,
  BarChart3,
  Settings
} from "lucide-react";
import { AdminUser } from "./AdminDashboard";
import { CardHeader, CardTitle, CardContent } from "@repo/ui/components/card";

// Updated interface to match our new query structure
export interface PlatformMetrics {
  totalUsers: number;
  totalConsumers: number;
  totalSellers: number;
  activeSubscriptions: number;
  platformRevenue: number;
  totalListings: number;
  activeListings: number;
  soldListings: number;
  pendingApplications: number;
  monthlyGrowth: {
    users: number;
    revenue: number;
    listings: number;
  };
  subscriptionBreakdown: {
    basic: number;
    premium: number;
    enterprise: number;
  };
  monthlyRecurringRevenue: number;
  transactionMetrics: {
    totalOrders: number;
    totalTransactionValue: number;
    averageOrderValue: number;
    platformCommissionRevenue: number;
  };
}

interface AdminOverviewProps {
  metrics: PlatformMetrics;
  adminUser: AdminUser;
}

interface RecentActivity {
  id: string;
  type: "user_signup" | "seller_application" | "subscription" | "listing" | "transaction";
  description: string;
  timestamp: string;
  status: "success" | "warning" | "error";
}

export function AdminOverview({ metrics, adminUser }: AdminOverviewProps) {
  // Fetch real activity data
  const recentActivity = useQuery(api.adminMetrics.getRecentActivity, { limit: 10 });
  const systemHealth = useQuery(api.adminMetrics.getSystemHealth);
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "user_signup":
        return Users;
      case "seller_application":
        return UserCheck;
      case "subscription":
        return CheckCircle;
      case "listing":
        return Package;
      case "transaction":
        return DollarSign;
      default:
        return Activity;
    }
  };

  const getActivityColor = (status: string) => {
    switch (status) {
      case "success":
        return "text-green-600 dark:text-green-400";
      case "warning":
        return "text-yellow-600 dark:text-yellow-400";
      case "error":
        return "text-red-600 dark:text-red-400";
      default:
        return "text-muted-foreground";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800">Success</Badge>;
      case "warning":
        return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800">Warning</Badge>;
      case "error":
        return <Badge className="bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border-red-200 dark:border-red-800">Error</Badge>;
      default:
        return <Badge variant="secondary">Info</Badge>;
    }
  };

  return (
    <div>
      <div className="container mx-auto px-6 py-6 space-y-6">
        {/* Welcome Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Admin Dashboard
            </h1>
            <p className="text-lg text-muted-foreground mt-1">
              Welcome back, {adminUser.name}. Monitor platform performance and manage operations.
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="border-primary/20 bg-primary/10 text-primary">
              <Shield className="w-4 h-4 mr-2" />
              {adminUser.role.replace('_', ' ').toUpperCase()}
            </Badge>
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Stats Cards - Matching seller dashboard exactly */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Users */}
          <Card className="bg-primary text-primary-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-light text-primary-foreground/80 uppercase tracking-wide">
                  <Users className="w-4 h-4 mr-2 inline" />
                  Total Users
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-light text-primary-foreground">
                {metrics.totalUsers.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          {/* Platform Revenue */}
          <Card className="bg-accent text-accent-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-light text-accent-foreground/80 uppercase tracking-wide">
                  <DollarSign className="w-4 h-4 mr-2 inline" />
                  Platform Revenue
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-light text-accent-foreground">
                {formatCurrency(metrics.platformRevenue)}
              </div>
            </CardContent>
          </Card>

          {/* Active Listings */}
          <Card className="bg-secondary text-secondary-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-light text-secondary-foreground/80 uppercase tracking-wide">
                  <Package className="w-4 h-4 mr-2 inline" />
                  Active Listings
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-light text-secondary-foreground">
                {metrics.activeListings.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          {/* Pending Applications */}
          <Card className="bg-muted text-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-light text-foreground/70 uppercase tracking-wide">
                  <UserCheck className="w-4 h-4 mr-2 inline" />
                  Pending Applications
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-light text-foreground">
                {metrics.pendingApplications}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Secondary Stats Row - Matching seller dashboard exactly */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Subscription Breakdown */}
          <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground">Subscription Breakdown</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-light text-foreground">Basic</span>
                <Badge variant="secondary">{metrics.subscriptionBreakdown.basic}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-light text-foreground">Premium</span>
                <Badge variant="outline" className="border-primary/20 bg-primary/10 text-primary">
                  {metrics.subscriptionBreakdown.premium}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-light text-foreground">Enterprise</span>
                <Badge className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
                  {metrics.subscriptionBreakdown.enterprise}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Transaction Metrics */}
          <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground">Transaction Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-light text-foreground">Total Orders</span>
                <span className="font-semibold">{metrics.transactionMetrics.totalOrders.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-light text-foreground">Avg Order Value</span>
                <span className="font-semibold">{formatCurrency(metrics.transactionMetrics.averageOrderValue)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-light text-foreground">Commission Revenue</span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  {formatCurrency(metrics.transactionMetrics.platformCommissionRevenue)}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* System Health */}
          <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground">System Health</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {systemHealth ? (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-light text-foreground">API Status</span>
                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800">
                      Healthy
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-light text-foreground">Database</span>
                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800">
                      Online
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-light text-foreground">Uptime</span>
                    <span className="font-semibold text-green-600 dark:text-green-400">99.9%</span>
                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center h-20">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-light text-foreground">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            {recentActivity ? (
              <div className="space-y-4">
                {recentActivity.slice(0, 5).map((activity: any) => {
                  const IconComponent = getActivityIcon(activity.type);
                  return (
                    <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <IconComponent className="w-5 h-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-foreground">{activity.description}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                      {getStatusBadge(activity.status)}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="flex items-center justify-center h-20">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
