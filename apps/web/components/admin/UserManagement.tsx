"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { Card } from "@repo/ui/components/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { 
  Search, 
  Filter,
  MoreHorizontal,
  UserX,
  UserCheck,
  Mail,
  Eye,
  Ban,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  Crown,
  Star
} from "lucide-react";
import { User } from "./AdminDashboard";

// Mock user data
const MOCK_USERS: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    type: "consumer",
    status: "active",
    subscriptionStatus: "active",
    subscriptionPlan: "premium",
    joinDate: "2024-01-15",
    lastActive: "2024-01-20",
    totalSpent: 45000
  },
  {
    id: "2",
    name: "Michael Chen",
    email: "<EMAIL>",
    type: "seller",
    status: "active",
    subscriptionStatus: "active",
    subscriptionPlan: "enterprise",
    joinDate: "2024-01-10",
    lastActive: "2024-01-20",
    totalEarned: 125000
  },
  {
    id: "3",
    name: "Emma Wilson",
    email: "<EMAIL>",
    type: "consumer",
    status: "suspended",
    subscriptionStatus: "inactive",
    subscriptionPlan: null,
    joinDate: "2024-01-05",
    lastActive: "2024-01-18",
    totalSpent: 2800
  },
  {
    id: "4",
    name: "David Rodriguez",
    email: "<EMAIL>",
    type: "seller",
    status: "pending",
    subscriptionStatus: "trial",
    subscriptionPlan: "basic",
    joinDate: "2024-01-18",
    lastActive: "2024-01-19",
    totalEarned: 0
  }
];

const STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  suspended: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  pending: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
};

const SUBSCRIPTION_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  inactive: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  trial: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  expired: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

const PLAN_COLORS = {
  basic: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  premium: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  enterprise: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
};

export function UserManagement() {
  const [users] = useState<User[]>(MOCK_USERS);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [subscriptionFilter, setSubscriptionFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState("joinDate");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === "all" || user.type === typeFilter;
    const matchesStatus = statusFilter === "all" || user.status === statusFilter;
    const matchesSubscription = subscriptionFilter === "all" || user.subscriptionStatus === subscriptionFilter;
    
    return matchesSearch && matchesType && matchesStatus && matchesSubscription;
  });

  const sortedUsers = [...filteredUsers].sort((a, b) => {
    let aValue: any = a[sortBy as keyof User];
    let bValue: any = b[sortBy as keyof User];
    
    if (sortBy === "joinDate" || sortBy === "lastActive") {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }
    
    if (sortOrder === "asc") {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleUserAction = (userId: string, action: string) => {
    console.warn(`${action} user:`, userId);
    // Implementation would go here
  };

  const getStats = () => {
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.status === "active").length;
    const activeSubscriptions = users.filter(u => u.subscriptionStatus === "active").length;
    const totalRevenue = users.reduce((sum, u) => sum + (u.totalSpent || 0), 0);
    
    return { totalUsers, activeUsers, activeSubscriptions, totalRevenue };
  };

  const stats = getStats();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-black dark:text-white">
          User Management
        </h1>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-1">
          Manage platform users and subscriptions
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Total Users
              </p>
              <p className="text-3xl font-bold text-black dark:text-white mt-2">
                {stats.totalUsers}
              </p>
            </div>
            <Users className="w-8 h-8 text-neutral-400" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Active Users
              </p>
              <p className="text-3xl font-bold text-green-600 dark:text-green-400 mt-2">
                {stats.activeUsers}
              </p>
            </div>
            <UserCheck className="w-8 h-8 text-green-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Active Subscriptions
              </p>
              <p className="text-3xl font-bold text-blue-600 dark:text-blue-400 mt-2">
                {stats.activeSubscriptions}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-blue-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Total Revenue
              </p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400 mt-2">
                {formatCurrency(stats.totalRevenue)}
              </p>
            </div>
            <Crown className="w-8 h-8 text-purple-500" />
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-12 rounded-xl"
          />
        </div>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-48 h-12 rounded-xl">
            <SelectValue placeholder="User Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="consumer">Consumers</SelectItem>
            <SelectItem value="seller">Sellers</SelectItem>
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48 h-12 rounded-xl">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="suspended">Suspended</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
          </SelectContent>
        </Select>

        <Select value={subscriptionFilter} onValueChange={setSubscriptionFilter}>
          <SelectTrigger className="w-48 h-12 rounded-xl">
            <SelectValue placeholder="Subscription" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Subscriptions</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="trial">Trial</SelectItem>
            <SelectItem value="expired">Expired</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Users Table */}
      <Card className="overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-neutral-50 dark:bg-neutral-900">
              <TableHead>User</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Subscription</TableHead>
              <TableHead>Activity</TableHead>
              <TableHead>Performance</TableHead>
              <TableHead className="w-12">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedUsers.map((user) => (
              <TableRow key={user.id} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-neutral-200 dark:bg-neutral-700 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-neutral-600 dark:text-neutral-300">
                        {user.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <p className="font-semibold text-black dark:text-white">
                        {user.name}
                      </p>
                      <p className="text-sm text-neutral-500">
                        {user.email}
                      </p>
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant="outline" className="capitalize">
                    {user.type === "seller" ? (
                      <Star className="w-3 h-3 mr-1" />
                    ) : (
                      <Users className="w-3 h-3 mr-1" />
                    )}
                    {user.type}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <Badge className={STATUS_COLORS[user.status]}>
                    {user.status === "active" ? (
                      <CheckCircle className="w-3 h-3 mr-1" />
                    ) : user.status === "suspended" ? (
                      <XCircle className="w-3 h-3 mr-1" />
                    ) : (
                      <Clock className="w-3 h-3 mr-1" />
                    )}
                    {user.status}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    <Badge className={SUBSCRIPTION_COLORS[user.subscriptionStatus]}>
                      {user.subscriptionStatus}
                    </Badge>
                    {user.subscriptionPlan && (
                      <Badge variant="outline" className={PLAN_COLORS[user.subscriptionPlan]}>
                        {user.subscriptionPlan}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm">
                    <p className="text-neutral-600 dark:text-neutral-400">
                      Joined: {formatDate(user.joinDate)}
                    </p>
                    <p className="text-neutral-500">
                      Last active: {formatDate(user.lastActive)}
                    </p>
                  </div>
                </TableCell>
                
                <TableCell>
                  {user.type === "consumer" && user.totalSpent ? (
                    <div className="text-sm">
                      <p className="font-semibold text-green-600 dark:text-green-400">
                        {formatCurrency(user.totalSpent)}
                      </p>
                      <p className="text-neutral-500">spent</p>
                    </div>
                  ) : user.type === "seller" && user.totalEarned !== undefined ? (
                    <div className="text-sm">
                      <p className="font-semibold text-purple-600 dark:text-purple-400">
                        {formatCurrency(user.totalEarned)}
                      </p>
                      <p className="text-neutral-500">earned</p>
                    </div>
                  ) : (
                    <span className="text-neutral-400">-</span>
                  )}
                </TableCell>
                
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleUserAction(user.id, "view")}>
                        <Eye className="w-4 h-4 mr-2" />
                        View Profile
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem onClick={() => handleUserAction(user.id, "email")}>
                        <Mail className="w-4 h-4 mr-2" />
                        Send Email
                      </DropdownMenuItem>
                      
                      <DropdownMenuSeparator />
                      
                      {user.status === "active" ? (
                        <DropdownMenuItem 
                          onClick={() => handleUserAction(user.id, "suspend")}
                          className="text-red-600"
                        >
                          <Ban className="w-4 h-4 mr-2" />
                          Suspend User
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem 
                          onClick={() => handleUserAction(user.id, "activate")}
                          className="text-green-600"
                        >
                          <UserCheck className="w-4 h-4 mr-2" />
                          Activate User
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {/* Empty State */}
      {sortedUsers.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
            No users found
          </h3>
          <p className="text-neutral-500 dark:text-neutral-500">
            Try adjusting your search or filters
          </p>
        </div>
      )}
    </div>
  );
}
