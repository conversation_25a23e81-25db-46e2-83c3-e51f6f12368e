"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { SellerApplicationReview } from "./SellerApplicationReview";
import { Eye, CheckCircle, XCircle, Clock } from "lucide-react";
import { toast } from "sonner";

export function SellerApplicationsTable() {
  const [selectedApplication, setSelectedApplication] = useState<any>(null);
  const [isReviewOpen, setIsReviewOpen] = useState(false);
  
  const applications = useQuery(api.sellerApplicationsSimple.getAllApplications, {});
  const updateApplicationStatus = useMutation(api.sellerApplicationsSimple.updateApplicationStatus);

  const handleQuickAction = async (applicationId: string, status: "approved" | "rejected" | "under_review") => {
    try {
      await updateApplicationStatus({
        applicationId: applicationId as any,
        status,
      });
      toast.success(`Application ${status} successfully`);
    } catch (error) {
      console.error("Error updating application:", error);
      toast.error(`Failed to ${status} application`);
    }
  };

  const openReview = (application: any) => {
    setSelectedApplication(application);
    setIsReviewOpen(true);
  };

  const closeReview = () => {
    setSelectedApplication(null);
    setIsReviewOpen(false);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Approved</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      case "under_review":
        return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">Under Review</Badge>;
      case "pending":
        return <Badge variant="secondary">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (!applications) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-neutral-600 dark:text-neutral-400">Loading applications...</div>
      </div>
    );
  }

  if (applications.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-neutral-600 dark:text-neutral-400">No applications found</p>
          <p className="text-sm text-neutral-500 dark:text-neutral-500 mt-1">
            New seller applications will appear here
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Applicant</TableHead>
            <TableHead>Business Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Applied</TableHead>
            <TableHead>Expected Volume</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {applications.map((application) => (
            <TableRow key={application._id}>
              <TableCell>
                <div>
                  <div className="font-medium">
                    {application.firstName} {application.lastName}
                  </div>
                  <div className="text-sm text-muted-foreground">{application.email}</div>
                </div>
              </TableCell>
              <TableCell>
                <div className="font-medium">{application.businessName}</div>
                <div className="text-sm text-muted-foreground capitalize">
                  {application.businessType}
                </div>
              </TableCell>
              <TableCell>
                {getStatusBadge(application.status)}
              </TableCell>
              <TableCell>
                {new Date(application.submittedAt).toLocaleDateString()}
              </TableCell>
              <TableCell>
                {application.monthlyVolume || "Not specified"}
              </TableCell>
              <TableCell>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => openReview(application)}
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    Review
                  </Button>
                  
                  {application.status === "pending" && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleQuickAction(application._id, "under_review")}
                      >
                        <Clock className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleQuickAction(application._id, "approved")}
                      >
                        <CheckCircle className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleQuickAction(application._id, "rejected")}
                      >
                        <XCircle className="w-3 h-3" />
                      </Button>
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <SellerApplicationReview
        application={selectedApplication}
        isOpen={isReviewOpen}
        onClose={closeReview}
        onStatusUpdate={() => {
          // This will trigger a refetch of the applications
          closeReview();
        }}
      />
    </>
  );
}
