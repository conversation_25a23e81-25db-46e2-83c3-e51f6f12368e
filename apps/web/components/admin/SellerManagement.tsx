"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { Card } from "@repo/ui/components/card";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { 
  Search, 
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  FileText,
  DollarSign,
  TrendingUp,
  Users,
  Package,
  Star,
  Award
} from "lucide-react";
import { SellerApplication } from "./AdminDashboard";

// Mock seller application data
const MOCK_APPLICATIONS: SellerApplication[] = [
  {
    id: "1",
    applicantName: "Luxury Boutique Co.",
    applicantEmail: "<EMAIL>",
    businessName: "Luxury Boutique Co.",
    businessType: "Retail Store",
    applicationDate: "2024-01-15",
    status: "pending",
    documents: ["business_license.pdf", "tax_id.pdf", "insurance.pdf"],
  },
  {
    id: "2",
    applicantName: "Elite Fashion House",
    applicantEmail: "<EMAIL>",
    businessName: "Elite Fashion House",
    businessType: "Fashion Retailer",
    applicationDate: "2024-01-12",
    status: "approved",
    documents: ["business_license.pdf", "tax_id.pdf"],
    reviewNotes: "Excellent credentials and strong business history."
  },
  {
    id: "3",
    applicantName: "Premium Accessories Ltd",
    applicantEmail: "<EMAIL>",
    businessName: "Premium Accessories Ltd",
    businessType: "Accessories Retailer",
    applicationDate: "2024-01-10",
    status: "rejected",
    documents: ["business_license.pdf"],
    reviewNotes: "Incomplete documentation and insufficient business history."
  }
];

// Mock seller performance data
const MOCK_SELLER_PERFORMANCE = [
  {
    id: "seller-1",
    name: "Elite Fashion House",
    email: "<EMAIL>",
    totalSales: 125000,
    itemsSold: 45,
    averageRating: 4.8,
    commissionEarned: 12500,
    joinDate: "2024-01-01",
    status: "active"
  },
  {
    id: "seller-2",
    name: "Luxury Timepieces",
    email: "<EMAIL>",
    totalSales: 89000,
    itemsSold: 12,
    averageRating: 4.9,
    commissionEarned: 8900,
    joinDate: "2023-12-15",
    status: "active"
  }
];

const STATUS_COLORS = {
  pending: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
  approved: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  rejected: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

export function SellerManagement() {
  const [applications] = useState<SellerApplication[]>(MOCK_APPLICATIONS);
  const [activeTab, setActiveTab] = useState<"applications" | "approved" | "performance">("applications");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedApplication, setSelectedApplication] = useState<SellerApplication | null>(null);
  const [reviewDialog, setReviewDialog] = useState(false);
  const [reviewNotes, setReviewNotes] = useState("");

  const filteredApplications = applications.filter(app => {
    const matchesSearch = 
      app.applicantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.applicantEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.businessName.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (activeTab === "applications") {
      return matchesSearch && app.status === "pending";
    } else if (activeTab === "approved") {
      return matchesSearch && app.status === "approved";
    }
    
    return matchesSearch;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleReviewApplication = (application: SellerApplication, action: "approve" | "reject") => {
    setSelectedApplication(application);
    setReviewDialog(true);
  };

  const submitReview = (action: "approve" | "reject") => {
    console.warn(`${action} application:`, selectedApplication?.id, reviewNotes);
    setReviewDialog(false);
    setSelectedApplication(null);
    setReviewNotes("");
  };

  const getStats = () => {
    const pendingApplications = applications.filter(a => a.status === "pending").length;
    const approvedSellers = applications.filter(a => a.status === "approved").length;
    const totalCommission = MOCK_SELLER_PERFORMANCE.reduce((sum, s) => sum + s.commissionEarned, 0);
    const totalSales = MOCK_SELLER_PERFORMANCE.reduce((sum, s) => sum + s.totalSales, 0);
    
    return { pendingApplications, approvedSellers, totalCommission, totalSales };
  };

  const stats = getStats();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-black dark:text-white">
          Seller Management
        </h1>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-1">
          Manage seller applications and performance
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Pending Applications
              </p>
              <p className="text-3xl font-bold text-orange-600 dark:text-orange-400 mt-2">
                {stats.pendingApplications}
              </p>
            </div>
            <Clock className="w-8 h-8 text-orange-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Approved Sellers
              </p>
              <p className="text-3xl font-bold text-green-600 dark:text-green-400 mt-2">
                {stats.approvedSellers}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Total Sales
              </p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400 mt-2">
                {formatCurrency(stats.totalSales)}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-blue-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Commission Earned
              </p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400 mt-2">
                {formatCurrency(stats.totalCommission)}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-purple-500" />
          </div>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center space-x-1 bg-neutral-100 dark:bg-neutral-800 p-1 rounded-xl w-fit">
        <Button
          variant={activeTab === "applications" ? "default" : "ghost"}
          onClick={() => setActiveTab("applications")}
          className="h-10 px-6"
        >
          <Clock className="w-4 h-4 mr-2" />
          Pending Applications
          {stats.pendingApplications > 0 && (
            <Badge variant="destructive" className="ml-2">
              {stats.pendingApplications}
            </Badge>
          )}
        </Button>
        <Button
          variant={activeTab === "approved" ? "default" : "ghost"}
          onClick={() => setActiveTab("approved")}
          className="h-10 px-6"
        >
          <CheckCircle className="w-4 h-4 mr-2" />
          Approved Sellers
        </Button>
        <Button
          variant={activeTab === "performance" ? "default" : "ghost"}
          onClick={() => setActiveTab("performance")}
          className="h-10 px-6"
        >
          <TrendingUp className="w-4 h-4 mr-2" />
          Performance
        </Button>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
        <Input
          placeholder="Search sellers..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 h-12 rounded-xl"
        />
      </div>

      {/* Content based on active tab */}
      {(activeTab === "applications" || activeTab === "approved") && (
        <Card className="overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-neutral-50 dark:bg-neutral-900">
                <TableHead>Applicant</TableHead>
                <TableHead>Business</TableHead>
                <TableHead>Application Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Documents</TableHead>
                <TableHead className="w-12">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredApplications.map((application) => (
                <TableRow key={application.id} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                  <TableCell>
                    <div>
                      <p className="font-semibold text-black dark:text-white">
                        {application.applicantName}
                      </p>
                      <p className="text-sm text-neutral-500">
                        {application.applicantEmail}
                      </p>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div>
                      <p className="font-medium text-black dark:text-white">
                        {application.businessName}
                      </p>
                      <p className="text-sm text-neutral-500">
                        {application.businessType}
                      </p>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    {formatDate(application.applicationDate)}
                  </TableCell>
                  
                  <TableCell>
                    <Badge className={STATUS_COLORS[application.status]}>
                      {application.status === "pending" ? (
                        <Clock className="w-3 h-3 mr-1" />
                      ) : application.status === "approved" ? (
                        <CheckCircle className="w-3 h-3 mr-1" />
                      ) : (
                        <XCircle className="w-3 h-3 mr-1" />
                      )}
                      {application.status}
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <FileText className="w-4 h-4 text-neutral-400" />
                      <span className="text-sm text-neutral-600 dark:text-neutral-400">
                        {application.documents.length} files
                      </span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem>
                          <FileText className="w-4 h-4 mr-2" />
                          View Documents
                        </DropdownMenuItem>
                        
                        {application.status === "pending" && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleReviewApplication(application, "approve")}
                              className="text-green-600"
                            >
                              <CheckCircle className="w-4 h-4 mr-2" />
                              Approve
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleReviewApplication(application, "reject")}
                              className="text-red-600"
                            >
                              <XCircle className="w-4 h-4 mr-2" />
                              Reject
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {/* Performance Tab */}
      {activeTab === "performance" && (
        <Card className="overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-neutral-50 dark:bg-neutral-900">
                <TableHead>Seller</TableHead>
                <TableHead>Total Sales</TableHead>
                <TableHead>Items Sold</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Commission</TableHead>
                <TableHead>Join Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {MOCK_SELLER_PERFORMANCE.map((seller) => (
                <TableRow key={seller.id} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-neutral-200 dark:bg-neutral-700 rounded-full flex items-center justify-center">
                        <Star className="w-5 h-5 text-yellow-500" />
                      </div>
                      <div>
                        <p className="font-semibold text-black dark:text-white">
                          {seller.name}
                        </p>
                        <p className="text-sm text-neutral-500">
                          {seller.email}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <span className="font-bold text-green-600 dark:text-green-400">
                      {formatCurrency(seller.totalSales)}
                    </span>
                  </TableCell>
                  
                  <TableCell>
                    <span className="font-semibold text-blue-600 dark:text-blue-400">
                      {seller.itemsSold}
                    </span>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="font-semibold text-black dark:text-white">
                        {seller.averageRating}
                      </span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <span className="font-bold text-purple-600 dark:text-purple-400">
                      {formatCurrency(seller.commissionEarned)}
                    </span>
                  </TableCell>
                  
                  <TableCell>
                    {formatDate(seller.joinDate)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {/* Review Dialog */}
      <Dialog open={reviewDialog} onOpenChange={setReviewDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Review Application</DialogTitle>
            <DialogDescription>
              Review the seller application for {selectedApplication?.applicantName}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-black dark:text-white">
                Review Notes
              </label>
              <Textarea
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                placeholder="Add your review notes..."
                className="mt-1"
                rows={4}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setReviewDialog(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => submitReview("reject")}
              className="mr-2"
            >
              <XCircle className="w-4 h-4 mr-2" />
              Reject
            </Button>
            <Button onClick={() => submitReview("approve")}>
              <CheckCircle className="w-4 h-4 mr-2" />
              Approve
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Empty State */}
      {filteredApplications.length === 0 && activeTab !== "performance" && (
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
            No applications found
          </h3>
          <p className="text-neutral-500 dark:text-neutral-500">
            {searchTerm ? "Try adjusting your search" : "No seller applications to review"}
          </p>
        </div>
      )}
    </div>
  );
}
