"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { 
  Search, 
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  FileText,
  DollarSign,
  TrendingUp,
  Users,
  Package,
  Star,
  Award,
  AlertTriangle,
  Download,
  RefreshCw,
  Ban,
  UserCheck
} from "lucide-react";
import { toast } from "sonner";

interface EnhancedSellerManagementProps {
  activeTab?: "pending" | "approved" | "performance" | "commissions";
}

export function EnhancedSellerManagement({ activeTab = "pending" }: EnhancedSellerManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedApplication, setSelectedApplication] = useState<any>(null);
  const [showApplicationDialog, setShowApplicationDialog] = useState(false);
  const [reviewNotes, setReviewNotes] = useState("");

  // Real data queries
  const pendingApplications = useQuery(api.sellerApplicationsSimple.getAllApplications, {
    status: "pending"
  });

  const underReviewApplications = useQuery(api.sellerApplicationsSimple.getAllApplications, {
    status: "under_review"
  });

  const approvedSellers = useQuery(api.sellerQueries.getSellersByStatus, {
    status: "approved",
    limit: 50,
    includeMetrics: true
  });

  const sellerStats = useQuery(api.sellerQueries.getSellerStats);

  // Mutations
  const updateApplicationStatus = useMutation(api.sellerApplicationsSimple.updateApplicationStatus);
  const suspendSeller = useMutation(api.sellerApplications.suspendSeller);

  const handleApplicationAction = async (applicationId: string, action: "approve" | "reject" | "view") => {
    try {
      // Find application in either pending or under review
      const application = pendingApplications?.find(app => app._id === applicationId) || 
                         underReviewApplications?.find(app => app._id === applicationId);
      
      switch (action) {
        case "approve":
          await updateApplicationStatus({
            applicationId: applicationId as any,
            status: "approved",
            notes: reviewNotes || "Application approved"
          });
          toast.success("Application approved successfully");
          setReviewNotes("");
          break;
        
        case "reject":
          if (!reviewNotes.trim()) {
            toast.error("Please provide rejection notes");
            return;
          }
          await updateApplicationStatus({
            applicationId: applicationId as any,
            status: "rejected",
            notes: reviewNotes,
            rejectionReason: reviewNotes
          });
          toast.success("Application rejected");
          setReviewNotes("");
          break;
        
        case "view":
          setSelectedApplication(application);
          setShowApplicationDialog(true);
          break;
      }
    } catch (error: any) {
      toast.error(`Failed to ${action} application`, {
        description: error.message
      });
    }
  };

  const handleSellerAction = async (sellerId: string, action: "suspend" | "activate" | "view") => {
    try {
      switch (action) {
        case "suspend":
          await suspendSeller({
            profileId: sellerId as any,
            reason: "Administrative action",
            suspensionType: "temporary",
            suspensionDuration: 30,
            sendNotification: true
          });
          toast.success("Seller suspended successfully");
          break;
        case "view":
          // Implementation for viewing seller details
          break;
      }
    } catch (error: any) {
      toast.error(`Failed to ${action} seller`, {
        description: error.message
      });
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      case "under_review": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "approved": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "rejected": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "medium": return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      case "low": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  if (!pendingApplications && activeTab === "pending") {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600 dark:text-gray-400">Loading seller applications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Seller Management
          </h1>
          <p className="text-lg text-muted-foreground mt-1">
            Manage seller applications and performance
          </p>
        </div>
        <Button variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="p-6 bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border-amber-200 dark:border-amber-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-amber-700 dark:text-amber-400 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Pending Applications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-amber-900 dark:text-amber-300">
              {(pendingApplications?.length || 0) + (underReviewApplications?.length || 0)}
            </div>
            <Badge variant="secondary" className="mt-2 bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 border-amber-200 dark:border-amber-800">
              Awaiting review
            </Badge>
          </CardContent>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-400 flex items-center gap-2">
              <CheckCircle className="w-4 h-4" />
              Approved Sellers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-900 dark:text-green-300">
              {sellerStats?.totalApproved || 0}
            </div>
            <Badge className="mt-2 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-green-200 dark:border-green-800">
              Active sellers
            </Badge>
          </CardContent>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-400 flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Avg. Review Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-900 dark:text-blue-300">
              3.2 {/* Placeholder - would need to calculate from actual data */}
            </div>
            <Badge variant="secondary" className="mt-2 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border-blue-200 dark:border-blue-800">
              Days
            </Badge>
          </CardContent>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 border-purple-200 dark:border-purple-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-400 flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Monthly Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-900 dark:text-purple-300">
              {formatCurrency(sellerStats?.monthlyRevenue || 0)}
            </div>
            <Badge variant="secondary" className="mt-2 bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 border-purple-200 dark:border-purple-800">
              This month
            </Badge>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue={activeTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="commissions">Commissions</TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="w-5 h-5" />
                Search Applications
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search by business name or applicant email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="under_review">Under Review</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Applications Table */}
          <Card>
            <CardHeader>
              <CardTitle>Seller Applications</CardTitle>
              <CardDescription>
                Review and manage pending seller applications
              </CardDescription>
            </CardHeader>
            <CardContent>
              {pendingApplications && pendingApplications.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Business</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Applied</TableHead>
                      <TableHead>Documents</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {[...(pendingApplications || []), ...(underReviewApplications || [])].map((application) => (
                      <TableRow key={application._id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{application.businessName}</div>
                            <div className="text-sm text-gray-500">{application.businessType}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{application.firstName} {application.lastName}</div>
                            <div className="text-sm text-gray-500">{application.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(application.status)}>
                            {application.status.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getPriorityColor("medium")}>
                            medium
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {formatDate(application.submittedAt)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <FileText className="w-4 h-4" />
                            <span className="text-sm">
                              {application.specialties?.length || 0} specialties
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleApplicationAction(application._id, "view")}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleApplicationAction(application._id, "approve")}
                                className="text-green-600"
                              >
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Approve
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleApplicationAction(application._id, "reject")}
                                className="text-red-600"
                              >
                                <XCircle className="mr-2 h-4 w-4" />
                                Reject
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="w-12 h-12 mx-auto text-green-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    No pending applications found
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="approved" className="space-y-6">
          {/* Search and Filters for Approved Sellers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="w-5 h-5" />
                Search Approved Sellers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search by business name, contact name, or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sellers</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                    <SelectItem value="high_performer">High Performer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Approved Sellers Table */}
          <Card>
            <CardHeader>
              <CardTitle>Approved Sellers ({approvedSellers?.sellers?.length || 0})</CardTitle>
              <CardDescription>
                Manage active seller accounts and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              {approvedSellers === undefined ? (
                <div className="flex items-center justify-center min-h-[400px]">
                  <div className="text-center">
                    <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
                    <p className="text-gray-600 dark:text-gray-400">Loading approved sellers...</p>
                  </div>
                </div>
              ) : approvedSellers.sellers && approvedSellers.sellers.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Business</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Performance</TableHead>
                      <TableHead>Revenue</TableHead>
                      <TableHead>Listings</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {approvedSellers.sellers
                      .filter((seller) => {
                        if (!searchTerm) return true;
                        const searchLower = searchTerm.toLowerCase();
                        return (
                          seller.businessName?.toLowerCase().includes(searchLower) ||
                          seller.sellerName?.toLowerCase().includes(searchLower) ||
                          seller.email?.toLowerCase().includes(searchLower)
                        );
                      })
                      .filter((seller) => {
                        if (statusFilter === "all") return true;
                        if (statusFilter === "active") return seller.isActive === true;
                        if (statusFilter === "suspended") return seller.isActive === false;
                        if (statusFilter === "high_performer") return (seller.rating || 0) >= 4.5;
                        return true;
                      })
                      .map((seller) => (
                        <TableRow key={seller.profileId}>
                          <TableCell>
                            <div>
                              <div className="font-medium flex items-center gap-2">
                                {seller.businessName || "N/A"}
                                {(seller.rating || 0) >= 4.5 && (
                                  <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                )}
                              </div>
                              <div className="text-sm text-gray-500">
                                {seller.businessType || "Not specified"}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{seller.sellerName || "N/A"}</div>
                              <div className="text-sm text-gray-500">{seller.email}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                <span className="text-sm font-medium">
                                  {seller.rating?.toFixed(1) || "N/A"}
                                </span>
                              </div>
                              <div className="text-xs text-gray-500">
                                {seller.metrics?.totalOrders || 0} orders
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">
                                {formatCurrency(seller.metrics?.totalRevenue || 0)}
                              </div>
                              <div className="text-xs text-gray-500">
                                {formatCurrency(seller.metrics?.totalRevenue || 0 / 12 || 0)}/mo
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">
                                {seller.metrics?.activeProducts || 0}
                              </div>
                              <div className="text-xs text-gray-500">
                                {seller.metrics?.totalProducts || 0} listings
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={
                              seller.isActive 
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                                : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                            }>
                              {seller.isActive ? "active" : "suspended"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleSellerAction(seller.profileId, "view")}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Profile
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Package className="mr-2 h-4 w-4" />
                                  View Listings
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <TrendingUp className="mr-2 h-4 w-4" />
                                  Performance Report
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                {seller.isActive ? (
                                  <DropdownMenuItem 
                                    onClick={() => handleSellerAction(seller.profileId, "suspend")}
                                    className="text-red-600"
                                  >
                                    <Ban className="mr-2 h-4 w-4" />
                                    Suspend Seller
                                  </DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem 
                                    onClick={() => handleSellerAction(seller.profileId, "activate")}
                                    className="text-green-600"
                                  >
                                    <UserCheck className="mr-2 h-4 w-4" />
                                    Activate Seller
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    No approved sellers found
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    Sellers will appear here once their applications are approved
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Performance Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
                  <Star className="w-4 h-4" />
                  Top Performers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {approvedSellers?.sellers?.filter(s => (s.rating || 0) >= 4.5).length || 0}
                </div>
                <Badge className="mt-1">
                  4.5+ rating
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Avg Monthly Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {approvedSellers?.sellers && approvedSellers.sellers.length > 0 ? 
                    formatCurrency(
                      approvedSellers.sellers.reduce((sum, s) => sum + ((s.metrics?.totalRevenue || 0) / 12), 0) / 
                      approvedSellers.sellers.length
                    ) : '$0'
                  }
                </div>
                <Badge variant="secondary" className="mt-1">
                  Per seller
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
                  <Package className="w-4 h-4" />
                  Total Active Listings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {approvedSellers?.sellers?.reduce((sum, s) => sum + (s.metrics?.activeProducts || 0), 0) || 0}
                </div>
                <Badge variant="secondary" className="mt-1">
                  Platform wide
                </Badge>
              </CardContent>
            </Card>
          </div>

          {/* Performance Leaderboard */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Leaderboard</CardTitle>
              <CardDescription>
                Top performing sellers ranked by various metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              {approvedSellers === undefined ? (
                <div className="flex items-center justify-center min-h-[300px]">
                  <div className="text-center">
                    <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
                    <p className="text-gray-600 dark:text-gray-400">Loading performance data...</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Top Revenue Generators */}
                  <div>
                    <h4 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <DollarSign className="w-5 h-5 text-green-600" />
                      Top Revenue Generators
                    </h4>
                    <div className="space-y-3">
                      {approvedSellers.sellers
                        ?.sort((a, b) => (b.metrics?.totalRevenue || 0) - (a.metrics?.totalRevenue || 0))
                        .slice(0, 5)
                        .map((seller, index) => (
                          <div key={seller.profileId} className="flex items-center justify-between p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                            <div className="flex items-center gap-4">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-white ${
                                index === 0 ? 'bg-yellow-500' : 
                                index === 1 ? 'bg-gray-400' : 
                                index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                              }`}>
                                {index + 1}
                              </div>
                              <div>
                                <div className="font-medium">{seller.businessName || seller.sellerName || 'N/A'}</div>
                                <div className="text-sm text-gray-500">{seller.email}</div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-bold text-green-600">
                                {formatCurrency(seller.metrics?.totalRevenue || 0)}
                              </div>
                              <div className="text-sm text-gray-500">
                                {seller.metrics?.totalOrders || 0} sales
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>

                  {/* Top Rated Sellers */}
                  <div>
                    <h4 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <Star className="w-5 h-5 text-yellow-500" />
                      Highest Rated Sellers
                    </h4>
                    <div className="space-y-3">
                      {approvedSellers.sellers
                        ?.filter(s => (s.reviewCount || 0) > 0)
                        .sort((a, b) => (b.rating || 0) - (a.rating || 0))
                        .slice(0, 5)
                        .map((seller, index) => (
                          <div key={seller.profileId} className="flex items-center justify-between p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                            <div className="flex items-center gap-4">
                              <div className="w-8 h-8 rounded-full bg-yellow-500 flex items-center justify-center font-bold text-white">
                                {index + 1}
                              </div>
                              <div>
                                <div className="font-medium">{seller.businessName || seller.sellerName || 'N/A'}</div>
                                <div className="text-sm text-gray-500">{seller.email}</div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="flex items-center gap-2">
                                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                <span className="font-bold text-yellow-600">
                                  {seller.rating?.toFixed(1) || 'N/A'}
                                </span>
                              </div>
                              <div className="text-sm text-gray-500">
                                {seller.reviewCount || 0} reviews
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>

                  {/* Most Active Sellers */}
                  <div>
                    <h4 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <Package className="w-5 h-5 text-blue-600" />
                      Most Active Sellers
                    </h4>
                    <div className="space-y-3">
                      {approvedSellers.sellers
                        ?.sort((a, b) => (b.metrics?.activeProducts || 0) - (a.metrics?.activeProducts || 0))
                        .slice(0, 5)
                        .map((seller, index) => (
                          <div key={seller.profileId} className="flex items-center justify-between p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                            <div className="flex items-center gap-4">
                              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center font-bold text-white">
                                {index + 1}
                              </div>
                              <div>
                                <div className="font-medium">{seller.businessName || seller.sellerName || 'N/A'}</div>
                                <div className="text-sm text-gray-500">{seller.email}</div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-bold text-blue-600">
                                {seller.metrics?.activeProducts || 0}
                              </div>
                              <div className="text-sm text-gray-500">
                                Active listings
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="commissions" className="space-y-6">
          {/* Commission Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Total Platform Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(sellerStats?.totalRevenue || 0)}
                </div>
                <Badge className="mt-1">
                  All time
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Monthly Commission
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {formatCurrency(sellerStats?.monthlyRevenue || 0)}
                </div>
                <Badge variant="secondary" className="mt-1">
                  This month
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
                  <Award className="w-4 h-4" />
                  Avg Commission Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  5.0% {/* Platform standard commission rate */}
                </div>
                <Badge variant="secondary" className="mt-1">
                  Standard rate
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
                  <CheckCircle className="w-4 h-4" />
                  Processed Orders
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-indigo-600">
                  {sellerStats?.totalOrders || 0}
                </div>
                <Badge variant="secondary" className="mt-1">
                  Total orders
                </Badge>
              </CardContent>
            </Card>
          </div>

          {/* Commission Breakdown Table */}
          <Card>
            <CardHeader>
              <CardTitle>Commission Breakdown by Seller</CardTitle>
              <CardDescription>
                Detailed commission tracking for each approved seller
              </CardDescription>
            </CardHeader>
            <CardContent>
              {approvedSellers === undefined ? (
                <div className="flex items-center justify-center min-h-[400px]">
                  <div className="text-center">
                    <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
                    <p className="text-gray-600 dark:text-gray-400">Loading commission data...</p>
                  </div>
                </div>
              ) : approvedSellers.sellers && approvedSellers.sellers.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Seller</TableHead>
                      <TableHead>Total Sales</TableHead>
                      <TableHead>Seller Earnings</TableHead>
                      <TableHead>Platform Commission</TableHead>
                      <TableHead>Commission Rate</TableHead>
                      <TableHead>Orders</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {approvedSellers.sellers
                      .sort((a, b) => (b.metrics?.totalRevenue || 0) - (a.metrics?.totalRevenue || 0))
                      .map((seller) => {
                        const totalSales = seller.metrics?.totalRevenue || 0;
                        const platformCommission = totalSales * 0.05; // 5% commission rate
                        const sellerEarnings = totalSales - platformCommission;
                        
                        return (
                          <TableRow key={seller.profileId}>
                            <TableCell>
                              <div>
                                <div className="font-medium">{seller.businessName || seller.sellerName || 'N/A'}</div>
                                <div className="text-sm text-gray-500">{seller.email}</div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="font-medium">
                                {formatCurrency(totalSales)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="font-medium text-green-600">
                                {formatCurrency(sellerEarnings)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="font-medium text-purple-600">
                                {formatCurrency(platformCommission)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary">
                                5.0%
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="font-medium">
                                {seller.metrics?.totalOrders || 0}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={
                                seller.isActive 
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                                  : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                              }>
                                {seller.isActive ? "active" : "suspended"}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    No commission data available
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    Commission data will appear once sellers start making sales
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Commission Analytics */}
          <Card>
            <CardHeader>
              <CardTitle>Commission Analytics</CardTitle>
              <CardDescription>
                Insights into platform revenue and commission trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">Revenue Distribution</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Seller Earnings (95%)</span>
                      <span className="font-medium text-green-600">
                        {formatCurrency((sellerStats?.totalRevenue || 0) * 0.95)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Platform Commission (5%)</span>
                      <span className="font-medium text-purple-600">
                        {formatCurrency((sellerStats?.totalRevenue || 0) * 0.05)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">Performance Metrics</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Average Order Value</span>
                      <span className="font-medium">
                        {formatCurrency(sellerStats?.averageOrderValue || 0)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Active Sellers</span>
                      <span className="font-medium">
                        {sellerStats?.totalApproved || 0}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Total Products</span>
                      <span className="font-medium">
                        {sellerStats?.totalProducts || 0}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Application Details Dialog */}
      <Dialog open={showApplicationDialog} onOpenChange={setShowApplicationDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Seller Application Details</DialogTitle>
            <DialogDescription>
              Review application information and make a decision
            </DialogDescription>
          </DialogHeader>
          {selectedApplication && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Business Name</label>
                    <p className="text-sm text-gray-600">{selectedApplication.businessName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Business Type</label>
                    <p className="text-sm text-gray-600">{selectedApplication.businessType}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Contact Name</label>
                    <p className="text-sm text-gray-600">{selectedApplication.firstName} {selectedApplication.lastName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Email</label>
                    <p className="text-sm text-gray-600">{selectedApplication.email}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Application Date</label>
                    <p className="text-sm text-gray-600">{formatDate(selectedApplication.submittedAt)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Status</label>
                    <Badge className={getStatusColor(selectedApplication.status)}>
                      {selectedApplication.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Experience</label>
                    <p className="text-sm text-gray-600">{selectedApplication.yearsExperience} years</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Specialties</label>
                    <p className="text-sm text-gray-600">
                      {selectedApplication.specialties?.join(", ") || "None specified"}
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Review Notes</label>
                <Textarea
                  placeholder="Enter your review notes here..."
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  className="mt-2"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowApplicationDialog(false)}>
              Close
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => {
                handleApplicationAction(selectedApplication?._id, "reject");
                setShowApplicationDialog(false);
              }}
              disabled={!reviewNotes.trim()}
            >
              <XCircle className="w-4 h-4 mr-2" />
              Reject
            </Button>
            <Button onClick={() => {
              handleApplicationAction(selectedApplication?._id, "approve");
              setShowApplicationDialog(false);
            }}>
              <CheckCircle className="w-4 h-4 mr-2" />
              Approve
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
