"use client";

import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useRouter } from "next/navigation";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { toast } from "sonner";
import { 
  ArrowLeft,
  Save,
  User,
  Mail,
  Shield,
  CheckCircle,
  Ban,
  Clock,
  UserX
} from "lucide-react";

interface AdminUserEditProps {
  userId: string;
}

export function AdminUserEdit({ userId }: AdminUserEditProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Fetch user data using the new getUserById function
  const userData = useQuery(api.userManagement.getUserById, { userId: userId as any });

  // Mutations
  const updateVerificationStatus = useMutation(api.userManagement.updateVerificationStatus);

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    userType: "consumer" as "consumer" | "seller" | "admin",
    status: "active" as "active" | "suspended",
    subscriptionStatus: "active" as "active" | "inactive" | "trial",
  });

  // Update form data when user data loads
  useEffect(() => {
    if (userData) {
      setFormData({
        name: userData.name || "",
        email: userData.email || "",
        userType: userData.userType || "consumer",
        status: (userData.status === "active" || userData.status === "suspended") ? userData.status : "active",
        subscriptionStatus: userData.subscriptionStatus || "active",
      });
    }
  }, [userData]);

  const handleSave = async () => {
    try {
      setIsLoading(true);
      // Update verification status based on form data
      await updateVerificationStatus({
        userId: userId as any,
        isVerified: formData.status === "active"
      });
      toast.success("User updated successfully");
      router.push("/admin/users");
    } catch (error) {
      toast.error("Failed to update user");
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    try {
      const isVerified = newStatus === "active";
      await updateVerificationStatus({ 
        userId: userId as any, 
        isVerified,
        reason: isVerified ? "Admin activation" : "Admin suspension"
      });
      toast.success(`User ${isVerified ? 'activated' : 'suspended'} successfully`);
      setFormData(prev => ({ ...prev, status: newStatus as any }));
    } catch (error) {
      toast.error("Failed to update user status");
    }
  };

  if (userData === undefined) {
    return (
      <div className="container mx-auto px-6 py-6 space-y-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading user...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="container mx-auto px-6 py-6 space-y-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h3 className="text-lg font-light text-foreground mb-2">User not found</h3>
            <Button onClick={() => router.push("/admin/users")} variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Users
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-6 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            onClick={() => router.push("/admin/users")}
            className="rounded-xl"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
          <div>
            <h1 className="text-3xl font-light text-foreground">Edit User</h1>
            <p className="text-muted-foreground font-light mt-1">
              Manage user information and settings
            </p>
          </div>
        </div>
        <Button 
          onClick={handleSave}
          disabled={isLoading}
          className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl"
        >
          <Save className="w-4 h-4 mr-2" />
          Save Changes
        </Button>
      </div>

      {/* User Info Card */}
      <Card className="rounded-xl border border-border bg-card shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-light text-foreground">User Information</CardTitle>
          <CardDescription>Basic user details and contact information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="rounded-xl"
                disabled
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className="rounded-xl"
                disabled
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Settings Card */}
      <Card className="rounded-xl border border-border bg-card shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-light text-foreground">User Settings</CardTitle>
          <CardDescription>Account type, status, and subscription settings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="userType">User Type</Label>
              <Select 
                value={formData.userType} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, userType: value as any }))}
                disabled
              >
                <SelectTrigger className="rounded-xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="consumer">Consumer</SelectItem>
                  <SelectItem value="seller">Seller</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">Account Status</Label>
              <Select 
                value={formData.status} 
                onValueChange={(value) => handleStatusChange(value)}
              >
                <SelectTrigger className="rounded-xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="subscriptionStatus">Subscription Status</Label>
              <Select 
                value={formData.subscriptionStatus} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, subscriptionStatus: value as any }))}
                disabled
              >
                <SelectTrigger className="rounded-xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="trial">Trial</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current User Info */}
      <Card className="rounded-xl border border-border bg-card shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-light text-foreground">Current User Details</CardTitle>
          <CardDescription>Read-only information about the user</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-light text-muted-foreground">ID:</span>
              <span className="text-sm font-light">{userData._id}</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-light text-muted-foreground">Email:</span>
              <span className="text-sm font-light">{userData.email}</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-light text-muted-foreground">Type:</span>
              <Badge variant="outline" className="text-xs">
                {userData.userType}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              {userData.status === 'active' && <CheckCircle className="w-4 h-4 text-green-600" />}
              {userData.status === 'suspended' && <Ban className="w-4 h-4 text-red-600" />}
              <span className="text-sm font-light text-muted-foreground">Status:</span>
              <Badge variant="outline" className="text-xs">
                {userData.status}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
