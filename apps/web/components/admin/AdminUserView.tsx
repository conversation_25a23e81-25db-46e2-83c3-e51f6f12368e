"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  ArrowLeft,
  Edit,
  User,
  Mail,
  Shield,
  CheckCircle,
  Ban,
  Clock,
  UserX,
  Calendar,
  Activity,
  DollarSign,
  ShoppingCart,
  Crown,
  Package
} from "lucide-react";

interface AdminUserViewProps {
  userId: string;
}

export function AdminUserView({ userId }: AdminUserViewProps) {
  const router = useRouter();

  // Fetch user data
  const userData = useQuery(api.userManagement.getUserById, { userId: userId as any });

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'suspended': return <Ban className="w-4 h-4 text-red-600" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'inactive': return <UserX className="w-4 h-4 text-gray-600" />;
      default: return <User className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case 'suspended': return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case 'pending': return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case 'inactive': return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getUserTypeColor = (userType: string) => {
    switch (userType) {
      case 'consumer': return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case 'seller': return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case 'admin': return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  if (userData === undefined) {
    return (
      <div className="container mx-auto px-6 py-6 space-y-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading user...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="container mx-auto px-6 py-6 space-y-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h3 className="text-lg font-light text-foreground mb-2">User not found</h3>
            <Button onClick={() => router.push("/admin/users")} variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Users
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-6 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            onClick={() => router.push("/admin/users")}
            className="rounded-xl"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
          <div>
            <h1 className="text-3xl font-light text-foreground">User Details</h1>
            <p className="text-muted-foreground font-light mt-1">
              View user information and activity
            </p>
          </div>
        </div>
        <Button 
          onClick={() => router.push(`/admin/users/${userId}/edit`)}
          className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl"
        >
          <Edit className="w-4 h-4 mr-2" />
          Edit User
        </Button>
      </div>

      {/* User Profile Card */}
      <Card className="rounded-xl border border-border bg-card shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-light text-foreground">User Profile</CardTitle>
          <CardDescription>Basic user information and contact details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-start gap-6">
            <div className="w-20 h-20 bg-primary/5 rounded-xl overflow-hidden border border-border flex items-center justify-center">
              {userData.profileImage ? (
                <img
                  src={userData.profileImage}
                  alt={userData.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-primary/5 flex items-center justify-center">
                  <User className="w-8 h-8 text-muted-foreground" />
                </div>
              )}
            </div>
            <div className="flex-1 space-y-4">
              <div>
                <h2 className="text-2xl font-light text-foreground">{userData.name}</h2>
                <p className="text-muted-foreground font-light">{userData.email}</p>
              </div>
              <div className="flex items-center gap-4">
                <Badge 
                  variant="outline" 
                  className={`${getUserTypeColor(userData.userType)}`}
                >
                  <Shield className="w-3 h-3 mr-1" />
                  {userData.userType}
                </Badge>
                <Badge 
                  variant="outline" 
                  className={`${getStatusColor(userData.status)}`}
                >
                  {getStatusIcon(userData.status)}
                  <span className="ml-1">{userData.status}</span>
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="rounded-xl border border-border bg-card shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-light text-muted-foreground uppercase tracking-wide flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Join Date
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-light text-foreground">
              {formatDate(userData.joinDate)}
            </div>
            <p className="text-xs text-muted-foreground font-light mt-1">
              Member since
            </p>
          </CardContent>
        </Card>

        <Card className="rounded-xl border border-border bg-card shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-light text-muted-foreground uppercase tracking-wide flex items-center gap-2">
              <Activity className="w-4 h-4" />
              Last Active
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-light text-foreground">
              {formatDate(userData.lastActive)}
            </div>
            <p className="text-xs text-muted-foreground font-light mt-1">
              Recent activity
            </p>
          </CardContent>
        </Card>

        <Card className="rounded-xl border border-border bg-card shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-light text-muted-foreground uppercase tracking-wide flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Total Spent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-light text-foreground">
              {formatCurrency(userData.totalSpent)}
            </div>
            <p className="text-xs text-muted-foreground font-light mt-1">
              Lifetime value
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Additional Information */}
      <Card className="rounded-xl border border-border bg-card shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-light text-foreground">Additional Information</CardTitle>
          <CardDescription>Account details and preferences</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground">Account Details</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light text-muted-foreground">User ID:</span>
                  <span className="text-sm font-light font-mono">{userData._id}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light text-muted-foreground">Subscription Status:</span>
                  <Badge variant="outline" className="text-xs">
                    {userData.subscriptionStatus}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light text-muted-foreground">Account Created:</span>
                  <span className="text-sm font-light">{formatDate(userData._creationTime)}</span>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground">Platform Activity</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light text-muted-foreground">Orders Placed:</span>
                  <span className="text-sm font-light">0</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light text-muted-foreground">Products Listed:</span>
                  <span className="text-sm font-light">0</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light text-muted-foreground">Reviews Given:</span>
                  <span className="text-sm font-light">0</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
