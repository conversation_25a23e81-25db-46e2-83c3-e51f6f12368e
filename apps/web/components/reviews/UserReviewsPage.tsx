"use client";

import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { Star, Package, User, MessageSquare, Edit, Trash2, Calendar, Grid3X3, List } from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";
import { ReviewableOrders } from "@/components/seller/ReviewableOrders";
import { MarketplaceHeader } from "../marketplace/MarketplaceHeader";
import Link from "next/link";

export function UserReviewsPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<"reviews" | "reviewable">("reviews");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [editingReview, setEditingReview] = useState<{
    id: Id<"reviews">;
    rating: number;
    review: string;
  } | null>(null);

  // Get user's reviews using the new query
  const userReviews = useQuery(api.sellerReviews.getUserReviews, {
    userId: user?.userId || user?._id as Id<"users">,
    limit: 100,
  });

  // Get user's reviewable orders
  const reviewableOrders = useQuery(api.sellerReviews.getReviewableOrders, {});

  // Mutations
  const updateReview = useMutation(api.sellerReviews.updateSellerReview);
  const deleteReview = useMutation(api.sellerReviews.deleteSellerReview);

  if (!user) {
    return (
      <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
        <MarketplaceHeader />
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center max-w-md">
            <CardContent>
              <MessageSquare className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                Sign in to view reviews
              </h3>
              <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                Create an account to view and manage your reviews.
              </p>
              <Button asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const renderStars = (rating: number, size: "sm" | "md" = "md") => {
    const sizeClasses = {
      sm: "w-3 h-3",
      md: "w-4 h-4",
    };

    return (
      <div className="flex items-center space-x-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? "text-yellow-500 fill-current"
                : "text-gray-300 dark:text-gray-600"
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleEditReview = async () => {
    if (!editingReview) return;

    try {
      await updateReview({
        reviewId: editingReview.id,
        rating: editingReview.rating,
        review: editingReview.review,
      });

      setEditingReview(null);
    } catch (error) {
      console.error("Failed to update review:", error);
    }
  };

  const handleDeleteReview = async (reviewId: Id<"reviews">) => {
    if (confirm("Are you sure you want to delete this review?")) {
      try {
        await deleteReview({ reviewId });
      } catch (error) {
        console.error("Failed to delete review:", error);
      }
    }
  };

  const reviews = userReviews?.reviews || [];
  const hasReviews = reviews.length > 0;
  const hasReviewableOrders = reviewableOrders?.orders && reviewableOrders.orders.length > 0;

  return (
    <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
      <MarketplaceHeader />
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">My Reviews</h1>
            <p className="text-neutral-600 dark:text-neutral-400 mt-1">
              {userReviews ? `${userReviews.total} reviews written` : 'Manage your product and seller reviews'}
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reviews.length}</div>
              <p className="text-xs text-muted-foreground">
                reviews written
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Product Reviews</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {reviews.filter(r => r.product).length}
              </div>
              <p className="text-xs text-muted-foreground">
                product reviews
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Seller Reviews</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {reviews.filter(r => !r.product).length}
              </div>
              <p className="text-xs text-muted-foreground">
                seller reviews
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "reviews" | "reviewable")}>
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="reviews">
              <MessageSquare className="w-4 h-4 mr-2" />
              My Reviews ({reviews.length})
            </TabsTrigger>
            <TabsTrigger value="reviewable">
              <Package className="w-4 h-4 mr-2" />
              Ready to Review ({reviewableOrders?.orders?.length || 0})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="reviews" className="space-y-6">
            {hasReviews ? (
              <>
                {/* View Mode Toggle */}
                <div className="flex justify-end">
                  <div className="flex border rounded-lg overflow-hidden">
                    <Button
                      variant={viewMode === "grid" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("grid")}
                      className="rounded-none"
                    >
                      <Grid3X3 className="w-4 h-4" />
                    </Button>
                    <Button
                      variant={viewMode === "list" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("list")}
                      className="rounded-none"
                    >
                      <List className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {viewMode === "grid" ? (
                  // Grid view
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {reviews.map((review) => (
                      <Card key={review._id} className="group hover:shadow-lg transition-shadow duration-200">
                        <CardContent className="p-0">
                          <div className="relative overflow-hidden rounded-t-lg">
                            {review.product?.images && review.product.images[0] ? (
                              <div className="relative aspect-square bg-neutral-100 dark:bg-neutral-800">
                                <img
                                  src={review.product.images[0]}
                                  alt={review.product.title}
                                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                                />
                              </div>
                            ) : (
                              <div className="relative aspect-square bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center">
                                <Package className="w-16 h-16 text-neutral-400" />
                              </div>
                            )}

                            {/* Rating badge */}
                            <div className="absolute top-3 left-3">
                              <Badge className="bg-black/80 text-white text-xs flex items-center gap-1">
                                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                {review.rating}.0
                              </Badge>
                            </div>

                            {/* Review type badge */}
                            <Badge className="absolute top-3 right-3 bg-primary/80 text-white text-xs">
                              {review.product ? "Product" : "Seller"}
                            </Badge>
                          </div>

                          <div className="p-4 space-y-2">
                            <div className="space-y-1">
                              <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                                {review.product?.brand || review.seller?.name || "Unknown"}
                              </p>
                              <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                                {review.product?.title || `Review of ${review.seller?.name || "Seller"}`}
                              </h3>
                            </div>

                            <div className="flex items-center justify-between">
                              <p className="text-xs text-neutral-600 dark:text-neutral-400">
                                {formatDate(review.reviewDate)}
                              </p>
                              
                              <div className="flex items-center gap-1">
                                {renderStars(review.rating, "sm")}
                              </div>
                            </div>

                            {review.review && (
                              <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2">
                                {review.review}
                              </p>
                            )}

                            <div className="flex gap-2 pt-2">
                              <Button variant="outline" size="sm" className="flex-1">
                                <Edit className="w-4 h-4 mr-2" />
                                Edit
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                onClick={() => handleDeleteReview(review._id)}
                                className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  // List view
                  <div className="space-y-4">
                    {reviews.map((review) => (
                      <Card key={review._id} className="bg-background/50">
                        <CardContent className="p-6">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                {review.product ? (
                                  <Package className="w-5 h-5 text-muted-foreground" />
                                ) : (
                                  <User className="w-5 h-5 text-muted-foreground" />
                                )}
                                <div>
                                  <h4 className="font-medium">
                                    {review.product ? "Product Review" : "Seller Review"}
                                  </h4>
                                  <p className="text-sm text-muted-foreground">
                                    {formatDate(review.reviewDate)}
                                  </p>
                                </div>
                              </div>
                              
                              {review.product && (
                                <div className="ml-8 mb-2">
                                  <p className="font-medium">{review.product.title}</p>
                                  <p className="text-sm text-muted-foreground">{review.product.brand}</p>
                                </div>
                              )}
                              
                              <div className="ml-8 flex items-center space-x-4 text-sm text-muted-foreground">
                                <div className="flex items-center space-x-1">
                                  <Calendar className="w-4 h-4" />
                                  <span>Order #{review.order?.orderNumber || "N/A"}</span>
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex flex-col items-end space-y-2">
                              <div className="flex items-center space-x-1">
                                {renderStars(review.rating, "md")}
                                <span className="text-sm font-medium ml-2">
                                  {review.rating}.0
                                </span>
                              </div>
                              
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setEditingReview({
                                    id: review._id,
                                    rating: review.rating,
                                    review: review.review,
                                  })}
                                >
                                  <Edit className="w-4 h-4 mr-1" />
                                  Edit
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDeleteReview(review._id)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-4 h-4 mr-1" />
                                  Delete
                                </Button>
                              </div>
                            </div>
                          </div>
                          
                          {review.review && (
                            <div className="ml-8">
                              <p className="text-foreground">{review.review}</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </>
            ) : (
              <Card className="bg-background/50">
                <CardContent className="p-12 text-center">
                  <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Reviews Yet</h3>
                  <p className="text-muted-foreground mb-4">
                    You haven't written any reviews yet. Start by reviewing products you've purchased.
                  </p>
                  <Button onClick={() => setActiveTab("reviewable")}>
                    <Package className="w-4 h-4 mr-2" />
                    Review Products
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="reviewable" className="space-y-6">
            <ReviewableOrders />
          </TabsContent>
        </Tabs>
      </div>

      {/* Edit Review Dialog */}
      {editingReview && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Edit Review</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Rating</label>
                <div className="flex items-center space-x-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => setEditingReview({
                        ...editingReview,
                        rating: star,
                      })}
                      className="focus:outline-none"
                    >
                      <Star
                        className={`w-6 h-6 ${
                          star <= editingReview.rating
                            ? "text-yellow-500 fill-current"
                            : "text-gray-300"
                        }`}
                      />
                    </button>
                  ))}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Review</label>
                <textarea
                  value={editingReview.review}
                  onChange={(e) => setEditingReview({
                    ...editingReview,
                    review: e.target.value,
                  })}
                  className="w-full p-2 border rounded-md"
                  rows={4}
                />
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setEditingReview(null)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleEditReview}
                  className="flex-1"
                >
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
