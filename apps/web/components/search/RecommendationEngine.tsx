"use client";

import { useState, useEffect } from "react";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { 
  Heart,
  Eye,
  Star,
  TrendingUp,
  <PERSON>rkles,
  Clock,
  ArrowRight,
  ShoppingBag
} from "lucide-react";
import { useSearch, Product } from "@/contexts/SearchContext";
import { cn } from "@repo/ui/lib/utils";

interface RecommendationEngineProps {
  productId?: string;
  userId?: string;
  type?: "similar" | "personalized" | "trending" | "recently_viewed";
  maxItems?: number;
  className?: string;
}

// Mock recommendation data
const MOCK_PRODUCTS: Product[] = [
  {
    id: "1",
    title: "Hermès Birkin 30 Black Togo",
    brand: "Hermès",
    price: 15000,
    originalPrice: 18000,
    category: "Handbags",
    condition: "Excellent",
    size: "30cm",
    color: "Black",
    material: "Togo Leather",
    images: ["/api/placeholder/300/300"],
    seller: { id: "s1", name: "Luxury Boutique", rating: 4.9 },
    isAvailable: true,
    _creationTime: 1715769600000,
    views: 1250,
    likes: 89,
    tags: ["luxury", "investment", "rare"]
  },
  {
    id: "2",
    title: "Chanel Classic Flap Medium",
    brand: "Chanel",
    price: 8900,
    category: "Handbags",
    condition: "Very Good",
    color: "Black",
    material: "Caviar Leather",
    images: ["/api/placeholder/300/300"],
    seller: { id: "s2", name: "Elite Fashion", rating: 4.8 },
    isAvailable: true,
    _creationTime: 1715769600000,
    views: 890,
    likes: 67,
    tags: ["classic", "timeless", "investment"]
  },
  {
    id: "3",
    title: "Louis Vuitton Neverfull MM",
    brand: "Louis Vuitton",
    price: 1800,
    category: "Handbags",
    condition: "Good",
    size: "MM",
    color: "Monogram",
    material: "Canvas",
    images: ["/api/placeholder/300/300"],
    seller: { id: "s3", name: "Fashion House", rating: 4.7 },
    isAvailable: true,
    _creationTime: 1715769600000,
    views: 567,
    likes: 34,
    tags: ["everyday", "spacious", "classic"]
  },
  {
    id: "4",
    title: "Rolex Submariner Date",
    brand: "Rolex",
    price: 12500,
    category: "Watches",
    condition: "Excellent",
    color: "Black",
    material: "Stainless Steel",
    images: ["/api/placeholder/300/300"],
    seller: { id: "s4", name: "Timepiece Experts", rating: 4.9 },
    isAvailable: true,
    _creationTime: 1715769600000,
    views: 2100,
    likes: 156,
    tags: ["luxury", "sport", "investment"]
  }
];

export function RecommendationEngine({ 
  productId,
  userId,
  type = "personalized",
  maxItems = 4,
  className
}: RecommendationEngineProps) {
  const { state, getRecommendations } = useSearch();
  const [recommendations, setRecommendations] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadRecommendations();
  }, [productId, userId, type]);

  const loadRecommendations = async () => {
    setIsLoading(true);
    try {
      // For demo, use mock data based on type
      let filteredProducts = [...MOCK_PRODUCTS];
      
      switch (type) {
        case "similar":
          // Filter by same category if productId provided
          if (productId) {
            const currentProduct = MOCK_PRODUCTS.find(p => p.id === productId);
            if (currentProduct) {
              filteredProducts = MOCK_PRODUCTS.filter(p => 
                p.id !== productId && p.category === currentProduct.category
              );
            }
          }
          break;
          
        case "recently_viewed":
          filteredProducts = state.recentlyViewed.slice(0, maxItems);
          break;
          
        case "trending":
          // Sort by views and likes
          filteredProducts = MOCK_PRODUCTS.sort((a, b) => 
            (b.views + b.likes) - (a.views + a.likes)
          );
          break;
          
        case "personalized":
        default:
          // Use user preferences and history
          const userCategories = state.recommendationContext.preferences.categories;
          const userBrands = state.recommendationContext.preferences.brands;
          
          if (userCategories.length > 0 || userBrands.length > 0) {
            filteredProducts = MOCK_PRODUCTS.filter(p => 
              userCategories.includes(p.category) || userBrands.includes(p.brand)
            );
          }
          break;
      }
      
      setRecommendations(filteredProducts.slice(0, maxItems));
    } catch (error) {
      console.error("Failed to load recommendations:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getRecommendationTitle = () => {
    switch (type) {
      case "similar":
        return "Similar Items";
      case "recently_viewed":
        return "Recently Viewed";
      case "trending":
        return "Trending Now";
      case "personalized":
      default:
        return "Recommended for You";
    }
  };

  const getRecommendationIcon = () => {
    switch (type) {
      case "similar":
        return Sparkles;
      case "recently_viewed":
        return Clock;
      case "trending":
        return TrendingUp;
      case "personalized":
      default:
        return Heart;
    }
  };

  const getRecommendationColor = () => {
    switch (type) {
      case "similar":
        return "text-purple-600 dark:text-purple-400";
      case "recently_viewed":
        return "text-blue-600 dark:text-blue-400";
      case "trending":
        return "text-orange-600 dark:text-orange-400";
      case "personalized":
      default:
        return "text-pink-600 dark:text-pink-400";
    }
  };

  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="animate-pulse">
          <div className="h-6 bg-neutral-200 dark:bg-neutral-700 rounded w-48 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: maxItems }).map((_, i) => (
              <div key={i} className="space-y-3">
                <div className="h-48 bg-neutral-200 dark:bg-neutral-700 rounded-lg" />
                <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-3/4" />
                <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/2" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (recommendations.length === 0) {
    return null;
  }

  const Icon = getRecommendationIcon();

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Icon className={cn("w-6 h-6", getRecommendationColor())} />
          <h2 className="text-2xl font-bold text-black dark:text-white">
            {getRecommendationTitle()}
          </h2>
        </div>
        
        {recommendations.length > maxItems && (
          <Button variant="outline" className="h-10">
            View All
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        )}
      </div>

      {/* Recommendations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {recommendations.map((product) => (
          <Card key={product.id} className="group overflow-hidden hover:shadow-lg transition-all duration-300">
            {/* Product Image */}
            <div className="relative aspect-square overflow-hidden">
              <img
                src={product.images[0]}
                alt={product.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
              
              {/* Quick Actions */}
              <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Button
                  variant="secondary"
                  size="sm"
                  className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                >
                  <Heart className="w-4 h-4" />
                </Button>
              </div>
              
              {/* Badges */}
              <div className="absolute top-3 left-3 space-y-2">
                {product.originalPrice && product.originalPrice > product.price && (
                  <Badge variant="destructive" className="text-xs">
                    Sale
                  </Badge>
                )}
                {product.condition === "New" && (
                  <Badge className="bg-green-600 text-white text-xs">
                    New
                  </Badge>
                )}
              </div>
            </div>

            {/* Product Info */}
            <div className="p-4 space-y-3">
              {/* Brand & Title */}
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {product.brand}
                </p>
                <h3 className="font-semibold text-black dark:text-white line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {product.title}
                </h3>
              </div>

              {/* Price */}
              <div className="flex items-center space-x-2">
                <span className="text-lg font-bold text-black dark:text-white">
                  {formatCurrency(product.price)}
                </span>
                {product.originalPrice && product.originalPrice > product.price && (
                  <span className="text-sm text-neutral-500 line-through">
                    {formatCurrency(product.originalPrice)}
                  </span>
                )}
              </div>

              {/* Condition & Details */}
              <div className="flex items-center justify-between text-sm">
                <Badge variant="outline" className="text-xs">
                  {product.condition}
                </Badge>
                <div className="flex items-center space-x-3 text-neutral-500">
                  <div className="flex items-center space-x-1">
                    <Eye className="w-3 h-3" />
                    <span>{product.views}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Heart className="w-3 h-3" />
                    <span>{product.likes}</span>
                  </div>
                </div>
              </div>

              {/* Seller */}
              <div className="flex items-center justify-between pt-2 border-t border-neutral-200 dark:border-neutral-700">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-neutral-200 dark:bg-neutral-700 rounded-full flex items-center justify-center">
                    <span className="text-xs font-semibold text-neutral-600 dark:text-neutral-300">
                      {product.seller.name[0]}
                    </span>
                  </div>
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">
                    {product.seller.name}
                  </span>
                </div>
                
                <div className="flex items-center space-x-1">
                  <Star className="w-3 h-3 text-yellow-500 fill-current" />
                  <span className="text-xs text-neutral-600 dark:text-neutral-400">
                    {product.seller.rating}
                  </span>
                </div>
              </div>

              {/* Action Button */}
              <Button className="w-full h-10 bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200">
                <ShoppingBag className="w-4 h-4 mr-2" />
                View Details
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
