"use client";

import { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Progress } from "@repo/ui/components/progress";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";
import { 
  Camera,
  Upload,
  Sparkles,
  TrendingUp,
  Eye,
  Zap,
  Brain,
  Target,
  Image as ImageIcon,
  X,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { cn } from "@repo/ui/lib/utils";

interface AISearchResult {
  id: string;
  title: string;
  brand: string;
  price: number;
  confidence: number;
  similarity: number;
  image: string;
  category: string;
  tags: string[];
}

interface StyleMatch {
  style: string;
  confidence: number;
  description: string;
  color: string;
}

interface PricePrediction {
  predictedPrice: number;
  confidence: number;
  factors: {
    brand: number;
    condition: number;
    rarity: number;
    market: number;
  };
  priceRange: {
    min: number;
    max: number;
  };
}

interface AISearchFeaturesProps {
  className?: string;
}

// Mock AI results
const MOCK_AI_RESULTS: AISearchResult[] = [
  {
    id: "1",
    title: "Hermès Birkin 30 Black Togo",
    brand: "Hermès",
    price: 15000,
    confidence: 0.95,
    similarity: 0.92,
    image: "/api/placeholder/200/200",
    category: "Handbags",
    tags: ["luxury", "leather", "black", "togo"]
  },
  {
    id: "2",
    title: "Hermès Kelly 28 Black Box",
    brand: "Hermès",
    price: 12000,
    confidence: 0.88,
    similarity: 0.85,
    image: "/api/placeholder/200/200",
    category: "Handbags",
    tags: ["luxury", "leather", "black", "box"]
  }
];

const MOCK_STYLE_MATCHES: StyleMatch[] = [
  { style: "Classic Luxury", confidence: 0.92, description: "Timeless elegance with premium materials", color: "#8B5CF6" },
  { style: "Minimalist Chic", confidence: 0.78, description: "Clean lines and understated sophistication", color: "#06B6D4" },
  { style: "Business Professional", confidence: 0.65, description: "Polished and work-appropriate", color: "#10B981" }
];

const MOCK_PRICE_PREDICTION: PricePrediction = {
  predictedPrice: 14500,
  confidence: 0.87,
  factors: {
    brand: 0.95,
    condition: 0.85,
    rarity: 0.78,
    market: 0.92
  },
  priceRange: {
    min: 12000,
    max: 17000
  }
};

export function AISearchFeatures({ className }: AISearchFeaturesProps) {
  const [isImageSearchOpen, setIsImageSearchOpen] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [aiResults, setAiResults] = useState<AISearchResult[]>([]);
  const [styleMatches, setStyleMatches] = useState<StyleMatch[]>([]);
  const [pricePrediction, setPricePrediction] = useState<PricePrediction | null>(null);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string);
        analyzeImage();
      };
      reader.readAsDataURL(file);
    }
  };

  const analyzeImage = async () => {
    setIsAnalyzing(true);
    setAnalysisProgress(0);

    // Simulate AI analysis with progress
    const steps = [
      { progress: 20, message: "Processing image..." },
      { progress: 40, message: "Identifying objects..." },
      { progress: 60, message: "Matching styles..." },
      { progress: 80, message: "Predicting prices..." },
      { progress: 100, message: "Analysis complete!" }
    ];

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 800));
      setAnalysisProgress(step.progress);
    }

    // Set mock results
    setAiResults(MOCK_AI_RESULTS);
    setStyleMatches(MOCK_STYLE_MATCHES);
    setPricePrediction(MOCK_PRICE_PREDICTION);
    setIsAnalyzing(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(0)}%`;
  };

  const resetAnalysis = () => {
    setUploadedImage(null);
    setAiResults([]);
    setStyleMatches([]);
    setPricePrediction(null);
    setAnalysisProgress(0);
    setIsAnalyzing(false);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* AI Features Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Brain className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          <h2 className="text-2xl font-bold text-black dark:text-white">
            AI-Powered Search
          </h2>
        </div>
        <p className="text-neutral-600 dark:text-neutral-400">
          Use advanced AI to find exactly what you're looking for
        </p>
      </div>

      {/* AI Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Image Search */}
        <Dialog open={isImageSearchOpen} onOpenChange={setIsImageSearchOpen}>
          <DialogTrigger asChild>
            <Card className="p-6 cursor-pointer hover:shadow-lg transition-all duration-300 group">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                  <Camera className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-black dark:text-white">
                    Image Search
                  </h3>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Upload a photo to find similar items
                  </p>
                </div>
                <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                  <Sparkles className="w-3 h-3 mr-1" />
                  AI Powered
                </Badge>
              </div>
            </Card>
          </DialogTrigger>
          
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Camera className="w-5 h-5" />
                <span>AI Image Search</span>
              </DialogTitle>
              <DialogDescription>
                Upload an image to find similar luxury items using our AI technology
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Upload Area */}
              {!uploadedImage && (
                <div className="border-2 border-dashed border-neutral-300 dark:border-neutral-600 rounded-lg p-12 text-center">
                  <div className="space-y-4">
                    <div className="w-16 h-16 bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center mx-auto">
                      <Upload className="w-8 h-8 text-neutral-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-black dark:text-white mb-2">
                        Upload an image
                      </h3>
                      <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                        Drag and drop or click to select an image
                      </p>
                      <Button onClick={() => fileInputRef.current?.click()}>
                        <ImageIcon className="w-4 h-4 mr-2" />
                        Choose Image
                      </Button>
                    </div>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>
              )}

              {/* Uploaded Image & Analysis */}
              {uploadedImage && (
                <div className="space-y-6">
                  <div className="flex items-start space-x-6">
                    <div className="relative">
                      <img
                        src={uploadedImage}
                        alt="Uploaded"
                        className="w-48 h-48 object-cover rounded-lg"
                      />
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={resetAnalysis}
                        className="absolute top-2 right-2 h-8 w-8 p-0"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* Analysis Progress */}
                    {isAnalyzing && (
                      <div className="flex-1 space-y-4">
                        <div className="flex items-center space-x-2">
                          <Brain className="w-5 h-5 text-purple-600 animate-pulse" />
                          <span className="font-semibold text-black dark:text-white">
                            AI Analysis in Progress
                          </span>
                        </div>
                        <Progress value={analysisProgress} className="w-full" />
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          {analysisProgress < 100 ? "Analyzing image..." : "Analysis complete!"}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* AI Results */}
                  {!isAnalyzing && aiResults.length > 0 && (
                    <div className="space-y-6">
                      {/* Similar Items */}
                      <div>
                        <h4 className="text-lg font-semibold text-black dark:text-white mb-4">
                          Similar Items Found
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {aiResults.map((result) => (
                            <Card key={result.id} className="p-4">
                              <div className="flex space-x-4">
                                <img
                                  src={result.image}
                                  alt={result.title}
                                  className="w-20 h-20 object-cover rounded-lg"
                                />
                                <div className="flex-1 space-y-2">
                                  <div>
                                    <h5 className="font-semibold text-black dark:text-white">
                                      {result.title}
                                    </h5>
                                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                                      {result.brand}
                                    </p>
                                  </div>
                                  <div className="flex items-center justify-between">
                                    <span className="font-bold text-green-600 dark:text-green-400">
                                      {formatCurrency(result.price)}
                                    </span>
                                    <Badge variant="outline">
                                      {formatPercentage(result.similarity)} match
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            </Card>
                          ))}
                        </div>
                      </div>

                      {/* Style Analysis */}
                      <div>
                        <h4 className="text-lg font-semibold text-black dark:text-white mb-4">
                          Style Analysis
                        </h4>
                        <div className="space-y-3">
                          {styleMatches.map((match, index) => (
                            <div key={index} className="flex items-center space-x-4 p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                              <div 
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: match.color }}
                              />
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-black dark:text-white">
                                    {match.style}
                                  </span>
                                  <Badge variant="outline">
                                    {formatPercentage(match.confidence)}
                                  </Badge>
                                </div>
                                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                                  {match.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Price Prediction */}
                      {pricePrediction && (
                        <div>
                          <h4 className="text-lg font-semibold text-black dark:text-white mb-4">
                            AI Price Prediction
                          </h4>
                          <Card className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
                            <div className="space-y-4">
                              <div className="text-center">
                                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                                  {formatCurrency(pricePrediction.predictedPrice)}
                                </div>
                                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                                  Predicted market value
                                </p>
                                <Badge className="mt-2 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                                  {formatPercentage(pricePrediction.confidence)} confidence
                                </Badge>
                              </div>
                              
                              <div className="grid grid-cols-2 gap-4">
                                <div className="text-center">
                                  <p className="text-sm text-neutral-600 dark:text-neutral-400">Min</p>
                                  <p className="font-semibold text-black dark:text-white">
                                    {formatCurrency(pricePrediction.priceRange.min)}
                                  </p>
                                </div>
                                <div className="text-center">
                                  <p className="text-sm text-neutral-600 dark:text-neutral-400">Max</p>
                                  <p className="font-semibold text-black dark:text-white">
                                    {formatCurrency(pricePrediction.priceRange.max)}
                                  </p>
                                </div>
                              </div>
                              
                              <div className="space-y-2">
                                <p className="text-sm font-medium text-black dark:text-white">
                                  Prediction Factors:
                                </p>
                                {Object.entries(pricePrediction.factors).map(([factor, value]) => (
                                  <div key={factor} className="flex items-center justify-between">
                                    <span className="text-sm text-neutral-600 dark:text-neutral-400 capitalize">
                                      {factor}
                                    </span>
                                    <div className="flex items-center space-x-2">
                                      <Progress value={value * 100} className="w-20 h-2" />
                                      <span className="text-sm font-medium">
                                        {formatPercentage(value)}
                                      </span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </Card>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Style Matching */}
        <Card className="p-6 cursor-pointer hover:shadow-lg transition-all duration-300 group">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
              <Eye className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Style Matching
              </h3>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Find items that match your style
              </p>
            </div>
            <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
              <Target className="w-3 h-3 mr-1" />
              Smart Match
            </Badge>
          </div>
        </Card>

        {/* Price Predictions */}
        <Card className="p-6 cursor-pointer hover:shadow-lg transition-all duration-300 group">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
              <TrendingUp className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Price Predictions
              </h3>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                AI-powered market value analysis
              </p>
            </div>
            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
              <Zap className="w-3 h-3 mr-1" />
              Market AI
            </Badge>
          </div>
        </Card>
      </div>
    </div>
  );
}
