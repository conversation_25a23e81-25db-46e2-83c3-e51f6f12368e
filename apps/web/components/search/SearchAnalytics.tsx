"use client";

import { useState } from "react";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { But<PERSON> } from "@repo/ui/components/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { 
  Search,
  TrendingUp,
  Eye,
  MousePointer,
  ShoppingBag,
  BarChart3,
  Calendar,
  Download,
  Filter,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react";

interface SearchMetric {
  period: string;
  totalSearches: number;
  uniqueSearchers: number;
  clickThroughRate: number;
  conversionRate: number;
  avgSessionDuration: number;
}

interface PopularSearch {
  query: string;
  count: number;
  ctr: number;
  conversions: number;
  trend: "up" | "down" | "stable";
}

interface SearchAnalyticsProps {
  userType?: "seller" | "admin";
  sellerId?: string;
  className?: string;
}

// Mock analytics data
const MOCK_METRICS: SearchMetric[] = [
  {
    period: "Last 7 days",
    totalSearches: 12450,
    uniqueSearchers: 8900,
    clickThroughRate: 15.2,
    conversionRate: 3.8,
    avgSessionDuration: 245
  },
  {
    period: "Last 30 days",
    totalSearches: 45600,
    uniqueSearchers: 28900,
    clickThroughRate: 14.8,
    conversionRate: 4.1,
    avgSessionDuration: 238
  }
];

const MOCK_POPULAR_SEARCHES: PopularSearch[] = [
  { query: "Hermès Birkin", count: 2450, ctr: 18.5, conversions: 89, trend: "up" },
  { query: "Chanel Classic Flap", count: 1890, ctr: 16.2, conversions: 67, trend: "up" },
  { query: "Louis Vuitton Neverfull", count: 1650, ctr: 14.8, conversions: 45, trend: "stable" },
  { query: "Rolex Submariner", count: 1420, ctr: 22.1, conversions: 78, trend: "up" },
  { query: "Cartier Love Bracelet", count: 1200, ctr: 12.3, conversions: 34, trend: "down" },
  { query: "Gucci Dionysus", count: 980, ctr: 15.7, conversions: 28, trend: "stable" },
  { query: "Prada Re-Edition", count: 850, ctr: 13.9, conversions: 22, trend: "up" },
  { query: "Dior Saddle Bag", count: 720, ctr: 17.4, conversions: 31, trend: "up" }
];

export function SearchAnalytics({ 
  userType = "seller", 
  sellerId,
  className 
}: SearchAnalyticsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState("Last 7 days");
  const [sortBy, setSortBy] = useState("count");

  const currentMetrics = MOCK_METRICS.find(m => m.period === selectedPeriod) || MOCK_METRICS[0];

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <ArrowUpRight className="w-3 h-3 text-green-600" />;
      case "down":
        return <ArrowDownRight className="w-3 h-3 text-red-600" />;
      default:
        return <div className="w-3 h-3" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return "text-green-600 dark:text-green-400";
      case "down":
        return "text-red-600 dark:text-red-400";
      default:
        return "text-neutral-600 dark:text-neutral-400";
    }
  };

  const sortedSearches = [...MOCK_POPULAR_SEARCHES].sort((a, b) => {
    switch (sortBy) {
      case "ctr":
        return b.ctr - a.ctr;
      case "conversions":
        return b.conversions - a.conversions;
      case "count":
      default:
        return b.count - a.count;
    }
  });

  return (
    <div className={className}>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">
              Search Analytics
            </h1>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-1">
              {userType === "admin" 
                ? "Platform-wide search performance and insights"
                : "Track how customers find your products"
              }
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Last 7 days">Last 7 days</SelectItem>
                <SelectItem value="Last 30 days">Last 30 days</SelectItem>
                <SelectItem value="Last 90 days">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  Total Searches
                </p>
                <p className="text-3xl font-bold text-black dark:text-white mt-2">
                  {formatNumber(currentMetrics?.totalSearches || 0)}
                </p>
              </div>
              <Search className="w-8 h-8 text-blue-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  Unique Searchers
                </p>
                <p className="text-3xl font-bold text-black dark:text-white mt-2">
                  {formatNumber(currentMetrics?.uniqueSearchers || 0)}
                </p>
              </div>
              <Eye className="w-8 h-8 text-green-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  Click-Through Rate
                </p>
                <p className="text-3xl font-bold text-black dark:text-white mt-2">
                  {formatPercentage(currentMetrics?.clickThroughRate || 0)}
                </p>
              </div>
              <MousePointer className="w-8 h-8 text-purple-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  Conversion Rate
                </p>
                <p className="text-3xl font-bold text-black dark:text-white mt-2">
                  {formatPercentage(currentMetrics?.conversionRate || 0)}
                </p>
              </div>
              <ShoppingBag className="w-8 h-8 text-orange-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  Avg Session
                </p>
                <p className="text-3xl font-bold text-black dark:text-white mt-2">
                  {formatDuration(currentMetrics?.avgSessionDuration || 0)}
                </p>
              </div>
              <Calendar className="w-8 h-8 text-pink-500" />
            </div>
          </Card>
        </div>

        {/* Popular Searches */}
        <Card className="overflow-hidden">
          <div className="p-6 border-b border-neutral-200 dark:border-neutral-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                <h3 className="text-lg font-semibold text-black dark:text-white">
                  Popular Search Terms
                </h3>
              </div>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="count">Sort by Volume</SelectItem>
                  <SelectItem value="ctr">Sort by CTR</SelectItem>
                  <SelectItem value="conversions">Sort by Conversions</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow className="bg-neutral-50 dark:bg-neutral-900">
                <TableHead>Search Term</TableHead>
                <TableHead>Volume</TableHead>
                <TableHead>Click-Through Rate</TableHead>
                <TableHead>Conversions</TableHead>
                <TableHead>Trend</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedSearches.map((search, index) => (
                <TableRow key={index} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-black dark:text-white">
                        {search.query}
                      </span>
                      {index < 3 && (
                        <Badge variant="outline" className="text-xs">
                          Top {index + 1}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <span className="font-semibold text-blue-600 dark:text-blue-400">
                      {formatNumber(search.count)}
                    </span>
                  </TableCell>
                  
                  <TableCell>
                    <span className="font-semibold text-purple-600 dark:text-purple-400">
                      {formatPercentage(search.ctr)}
                    </span>
                  </TableCell>
                  
                  <TableCell>
                    <span className="font-semibold text-green-600 dark:text-green-400">
                      {search.conversions}
                    </span>
                  </TableCell>
                  
                  <TableCell>
                    <div className={`flex items-center space-x-1 ${getTrendColor(search.trend)}`}>
                      {getTrendIcon(search.trend)}
                      <span className="text-sm font-medium capitalize">
                        {search.trend}
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>

        {/* Search Performance Chart Placeholder */}
        <Card className="p-6">
          <div className="flex items-center space-x-2 mb-6">
            <BarChart3 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <h3 className="text-lg font-semibold text-black dark:text-white">
              Search Performance Over Time
            </h3>
          </div>
          
          <div className="h-64 bg-neutral-100 dark:bg-neutral-800 rounded-lg flex items-center justify-center">
            <div className="text-center space-y-2">
              <BarChart3 className="w-12 h-12 text-neutral-400 mx-auto" />
              <p className="text-neutral-600 dark:text-neutral-400">
                Interactive chart would be implemented here
              </p>
              <p className="text-sm text-neutral-500">
                Showing search volume, CTR, and conversion trends
              </p>
            </div>
          </div>
        </Card>

        {/* Search Insights */}
        {userType === "seller" && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-black dark:text-white mb-4">
              Search Insights & Recommendations
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <TrendingUp className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-blue-900 dark:text-blue-300">
                        Trending Opportunity
                      </h4>
                      <p className="text-sm text-blue-700 dark:text-blue-400 mt-1">
                        "Vintage Chanel" searches are up 45% this week. Consider adding vintage items to your inventory.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <Search className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-green-900 dark:text-green-300">
                        SEO Optimization
                      </h4>
                      <p className="text-sm text-green-700 dark:text-green-400 mt-1">
                        Your items appear in 78% of relevant searches. Add more detailed descriptions to improve visibility.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <MousePointer className="w-5 h-5 text-orange-600 dark:text-orange-400 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-orange-900 dark:text-orange-300">
                        Click-Through Rate
                      </h4>
                      <p className="text-sm text-orange-700 dark:text-orange-400 mt-1">
                        Your CTR is 12% above average. High-quality photos are driving engagement.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <ShoppingBag className="w-5 h-5 text-purple-600 dark:text-purple-400 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-purple-900 dark:text-purple-300">
                        Conversion Opportunity
                      </h4>
                      <p className="text-sm text-purple-700 dark:text-purple-400 mt-1">
                        Items priced 10-15% below market average have 3x higher conversion rates.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
