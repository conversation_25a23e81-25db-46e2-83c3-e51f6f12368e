"use client";

import { useState } from "react";
import { Card } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { 
  Filter,
  Grid,
  List,
  SlidersHorizontal,
  Sparkles,
  TrendingUp
} from "lucide-react";
import { EnhancedSearchBar } from "./EnhancedSearchBar";
import { SearchFilters } from "./SearchFilters";
import { RecommendationEngine } from "./RecommendationEngine";
import { AISearchFeatures } from "./AISearchFeatures";
import { useSearch } from "@/contexts/SearchContext";
import { cn } from "@repo/ui/lib/utils";

interface SearchPageProps {
  className?: string;
}

export function SearchPage({ className }: SearchPageProps) {
  const { state } = useSearch();
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showAIFeatures, setShowAIFeatures] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className={cn("min-h-screen bg-neutral-50 dark:bg-neutral-950", className)}>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Search Header */}
        <div className="text-center space-y-6">
          <div>
            <h1 className="text-4xl font-bold text-black dark:text-white mb-2">
              Discover Luxury
            </h1>
            <p className="text-lg text-neutral-600 dark:text-neutral-400">
              Find authenticated luxury items with our advanced search
            </p>
          </div>

          {/* Enhanced Search Bar */}
          <div className="max-w-4xl mx-auto">
            <EnhancedSearchBar
              placeholder="Search for luxury handbags, watches, jewelry..."
              showFilters={true}
              showImageSearch={true}
              onFilterClick={() => setShowFilters(!showFilters)}
            />
          </div>

          {/* Quick Actions */}
          <div className="flex items-center justify-center space-x-4">
            <Button
              variant={showAIFeatures ? "default" : "outline"}
              onClick={() => setShowAIFeatures(!showAIFeatures)}
              className="h-10"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              AI Features
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="h-10"
            >
              <SlidersHorizontal className="w-4 h-4 mr-2" />
              Advanced Filters
              {showFilters && (
                <Badge variant="destructive" className="ml-2 h-5 min-w-[20px] text-xs">
                  {/* Filter count would go here */}
                </Badge>
              )}
            </Button>
          </div>
        </div>

        {/* AI Features Section */}
        {showAIFeatures && (
          <Card className="p-8">
            <AISearchFeatures />
          </Card>
        )}

        {/* Main Content */}
        <div className="flex gap-8">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="w-80 flex-shrink-0">
              <SearchFilters
                isOpen={showFilters}
                onClose={() => setShowFilters(false)}
              />
            </div>
          )}

          {/* Search Results & Recommendations */}
          <div className="flex-1 space-y-8">
            {/* Search Results Header */}
            {state.currentQuery && (
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-black dark:text-white">
                    Search Results
                  </h2>
                  <p className="text-neutral-600 dark:text-neutral-400">
                    {state.totalResults.toLocaleString()} results for "{state.currentQuery}"
                  </p>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant={viewMode === "grid" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* Search Results */}
            {state.searchResults.length > 0 && (
              <div className={cn(
                "grid gap-6",
                viewMode === "grid" 
                  ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                  : "grid-cols-1"
              )}>
                {state.searchResults.map((product) => (
                  <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="aspect-square relative overflow-hidden">
                      <img
                        src={product.images[0]}
                        alt={product.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="p-4 space-y-2">
                      <div>
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          {product.brand}
                        </p>
                        <h3 className="font-semibold text-black dark:text-white line-clamp-2">
                          {product.title}
                        </h3>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-black dark:text-white">
                          {formatCurrency(product.price)}
                        </span>
                        <Badge variant="outline">
                          {product.condition}
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {/* Loading State */}
            {state.isLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {Array.from({ length: 8 }).map((_, i) => (
                  <Card key={i} className="overflow-hidden">
                    <div className="aspect-square bg-neutral-200 dark:bg-neutral-700 animate-pulse" />
                    <div className="p-4 space-y-2">
                      <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded animate-pulse" />
                      <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-3/4 animate-pulse" />
                      <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/2 animate-pulse" />
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {/* Recommendations */}
            <div className="space-y-8">
              {/* Recently Viewed */}
              {state.recentlyViewed.length > 0 && (
                <RecommendationEngine
                  type="recently_viewed"
                  maxItems={4}
                />
              )}

              {/* Personalized Recommendations */}
              <RecommendationEngine
                type="personalized"
                maxItems={4}
              />

              {/* Trending Items */}
              <RecommendationEngine
                type="trending"
                maxItems={4}
              />
            </div>

            {/* Empty State */}
            {!state.isLoading && state.currentQuery && state.searchResults.length === 0 && (
              <Card className="p-12 text-center">
                <div className="w-16 h-16 bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Filter className="w-8 h-8 text-neutral-400" />
                </div>
                <h3 className="text-xl font-semibold text-black dark:text-white mb-2">
                  No results found
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  Try adjusting your search terms or filters to find what you're looking for.
                </p>
                <div className="flex items-center justify-center space-x-4">
                  <Button variant="outline" onClick={() => setShowFilters(true)}>
                    <SlidersHorizontal className="w-4 h-4 mr-2" />
                    Adjust Filters
                  </Button>
                  <Button onClick={() => setShowAIFeatures(true)}>
                    <Sparkles className="w-4 h-4 mr-2" />
                    Try AI Search
                  </Button>
                </div>
              </Card>
            )}

            {/* No Query State */}
            {!state.currentQuery && !state.isLoading && (
              <div className="space-y-8">
                {/* Trending Searches */}
                {state.trendingSearches.length > 0 && (
                  <Card className="p-6">
                    <div className="flex items-center space-x-2 mb-4">
                      <TrendingUp className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                      <h3 className="text-lg font-semibold text-black dark:text-white">
                        Trending Searches
                      </h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {state.trendingSearches.map((trend, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800"
                        >
                          {trend}
                        </Badge>
                      ))}
                    </div>
                  </Card>
                )}

                {/* Default Recommendations */}
                <RecommendationEngine
                  type="trending"
                  maxItems={8}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
