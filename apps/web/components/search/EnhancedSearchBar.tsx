"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { Card } from "@repo/ui/components/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@repo/ui/components/popover";
import { 
  Search, 
  Filter,
  Camera,
  Clock,
  TrendingUp,
  X,
  ArrowRight,
  Sparkles,
  Tag,
  Package,
  Star
} from "lucide-react";
import { useSearch, SearchSuggestion } from "@/contexts/SearchContext";
import { cn } from "@repo/ui/lib/utils";

interface EnhancedSearchBarProps {
  placeholder?: string;
  showFilters?: boolean;
  showImageSearch?: boolean;
  className?: string;
  onFilterClick?: () => void;
}

// Mock trending searches
const MOCK_TRENDING = [
  "Hermès Birkin",
  "Chanel Classic Flap",
  "<PERSON> Neverfull",
  "Rolex Submariner",
  "Cartier Love Bracelet"
];

// Mock suggestions
const MOCK_SUGGESTIONS: SearchSuggestion[] = [
  { id: "1", text: "Hermès Birkin 30", type: "product", count: 45, image: "/api/placeholder/40/40" },
  { id: "2", text: "Hermès", type: "brand", count: 234 },
  { id: "3", text: "Handbags", type: "category", count: 1250 },
  { id: "4", text: "Hermès Kelly", type: "product", count: 28, image: "/api/placeholder/40/40" },
  { id: "5", text: "luxury handbags", type: "query", count: 89 },
];

export function EnhancedSearchBar({ 
  placeholder = "Search luxury items...", 
  showFilters = true,
  showImageSearch = true,
  className,
  onFilterClick
}: EnhancedSearchBarProps) {
  const { state, setQuery, performSearch, getSuggestions, setTrendingSearches } = useSearch();
  const [inputValue, setInputValue] = useState(state.currentQuery);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Load trending searches on mount
  useEffect(() => {
    setTrendingSearches(MOCK_TRENDING);
  }, [setTrendingSearches]);

  // Update input when query changes from context
  useEffect(() => {
    setInputValue(state.currentQuery);
  }, [state.currentQuery]);

  // Handle input changes and fetch suggestions
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (inputValue.trim().length > 1) {
        setIsLoading(true);
        try {
          // For demo, use mock suggestions
          const filteredSuggestions = MOCK_SUGGESTIONS.filter(s => 
            s.text.toLowerCase().includes(inputValue.toLowerCase())
          );
          setSuggestions(filteredSuggestions);
        } catch (error) {
          console.error("Failed to fetch suggestions:", error);
        } finally {
          setIsLoading(false);
        }
      } else {
        setSuggestions([]);
      }
    };

    const debounceTimer = setTimeout(fetchSuggestions, 300);
    return () => clearTimeout(debounceTimer);
  }, [inputValue]);

  // Handle clicks outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    setQuery(value);
    setShowSuggestions(true);
  };

  const handleSearch = (searchQuery?: string) => {
    const query = searchQuery || inputValue;
    if (query.trim()) {
      setQuery(query);
      performSearch(query);
      setShowSuggestions(false);
      inputRef.current?.blur();
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setInputValue(suggestion.text);
    handleSearch(suggestion.text);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    } else if (e.key === "Escape") {
      setShowSuggestions(false);
    }
  };

  const clearSearch = () => {
    setInputValue("");
    setQuery("");
    setSuggestions([]);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case "product":
        return Package;
      case "brand":
        return Star;
      case "category":
        return Tag;
      default:
        return Search;
    }
  };

  const getSuggestionColor = (type: string) => {
    switch (type) {
      case "product":
        return "text-blue-600 dark:text-blue-400";
      case "brand":
        return "text-purple-600 dark:text-purple-400";
      case "category":
        return "text-green-600 dark:text-green-400";
      default:
        return "text-neutral-600 dark:text-neutral-400";
    }
  };

  return (
    <div className={cn("relative w-full max-w-2xl", className)}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
          <Search className="w-5 h-5 text-neutral-400" />
        </div>
        
        <Input
          ref={inputRef}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowSuggestions(true)}
          placeholder={placeholder}
          className="pl-12 pr-32 h-14 text-lg rounded-2xl border-2 border-neutral-200 dark:border-neutral-700 focus:border-black dark:focus:border-white transition-colors"
        />
        
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
          {inputValue && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
          
          {showImageSearch && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800"
              title="Search by image"
            >
              <Camera className="w-4 h-4" />
            </Button>
          )}
          
          {showFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onFilterClick}
              className="h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800"
              title="Filters"
            >
              <Filter className="w-4 h-4" />
            </Button>
          )}
          
          <Button
            onClick={() => handleSearch()}
            className="h-10 px-6 bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 rounded-xl"
          >
            Search
          </Button>
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && (inputValue.length > 0 || state.searchHistory.length > 0 || state.trendingSearches.length > 0) && (
        <Card 
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 mt-2 z-50 max-h-96 overflow-y-auto shadow-lg border-2"
        >
          {/* Search Suggestions */}
          {suggestions.length > 0 && (
            <div className="p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Sparkles className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <span className="text-sm font-semibold text-neutral-700 dark:text-neutral-300">
                  Suggestions
                </span>
              </div>
              
              <div className="space-y-2">
                {suggestions.map((suggestion) => {
                  const Icon = getSuggestionIcon(suggestion.type);
                  
                  return (
                    <button
                      key={suggestion.id}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="w-full flex items-center space-x-3 p-3 hover:bg-neutral-50 dark:hover:bg-neutral-800 rounded-lg transition-colors text-left"
                    >
                      {suggestion.image ? (
                        <img
                          src={suggestion.image}
                          alt={suggestion.text}
                          className="w-8 h-8 object-cover rounded"
                        />
                      ) : (
                        <Icon className={`w-4 h-4 ${getSuggestionColor(suggestion.type)}`} />
                      )}
                      
                      <div className="flex-1">
                        <span className="text-black dark:text-white font-medium">
                          {suggestion.text}
                        </span>
                        {suggestion.count && (
                          <span className="text-xs text-neutral-500 ml-2">
                            {suggestion.count} items
                          </span>
                        )}
                      </div>
                      
                      <Badge variant="outline" className="text-xs capitalize">
                        {suggestion.type}
                      </Badge>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Search History */}
          {inputValue.length === 0 && state.searchHistory.length > 0 && (
            <div className="p-4 border-t border-neutral-200 dark:border-neutral-700">
              <div className="flex items-center space-x-2 mb-3">
                <Clock className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
                <span className="text-sm font-semibold text-neutral-700 dark:text-neutral-300">
                  Recent Searches
                </span>
              </div>
              
              <div className="space-y-2">
                {state.searchHistory.slice(0, 5).map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick({ 
                      id: index.toString(), 
                      text: search.query, 
                      type: "query" 
                    })}
                    className="w-full flex items-center space-x-3 p-2 hover:bg-neutral-50 dark:hover:bg-neutral-800 rounded-lg transition-colors text-left"
                  >
                    <Clock className="w-4 h-4 text-neutral-400" />
                    <span className="text-black dark:text-white">
                      {search.query}
                    </span>
                    <ArrowRight className="w-3 h-3 text-neutral-400 ml-auto" />
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Trending Searches */}
          {inputValue.length === 0 && state.trendingSearches.length > 0 && (
            <div className="p-4 border-t border-neutral-200 dark:border-neutral-700">
              <div className="flex items-center space-x-2 mb-3">
                <TrendingUp className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                <span className="text-sm font-semibold text-neutral-700 dark:text-neutral-300">
                  Trending Searches
                </span>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {state.trendingSearches.map((trend, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick({ 
                      id: index.toString(), 
                      text: trend, 
                      type: "query" 
                    })}
                    className="px-3 py-1.5 bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-full text-sm text-neutral-700 dark:text-neutral-300 transition-colors"
                  >
                    {trend}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="p-4 text-center">
              <div className="animate-spin w-5 h-5 border-2 border-neutral-300 border-t-black dark:border-t-white rounded-full mx-auto" />
              <span className="text-sm text-neutral-500 mt-2">Loading suggestions...</span>
            </div>
          )}

          {/* No Results */}
          {inputValue.length > 1 && suggestions.length === 0 && !isLoading && (
            <div className="p-4 text-center">
              <Search className="w-8 h-8 text-neutral-300 mx-auto mb-2" />
              <span className="text-sm text-neutral-500">No suggestions found</span>
            </div>
          )}
        </Card>
      )}
    </div>
  );
}
