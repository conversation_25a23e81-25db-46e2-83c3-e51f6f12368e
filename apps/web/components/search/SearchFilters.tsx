"use client";

import { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Badge } from "@repo/ui/components/badge";
import { Card } from "@repo/ui/components/card";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Slider } from "@repo/ui/components/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@repo/ui/components/collapsible";
import { 
  Filter,
  ChevronDown,
  X,
  RotateCcw,
  Star,
  Package,
  Tag,
  Palette,
  Ruler,
  Sparkles
} from "lucide-react";
import { useSearch, SearchFilters as SearchFiltersType } from "@/contexts/SearchContext";
import { cn } from "@repo/ui/lib/utils";

interface SearchFiltersProps {
  isOpen?: boolean;
  onClose?: () => void;
  className?: string;
}

// Filter options data
const CATEGORIES = [
  "Handbags", "Accessories", "Clothing", "Shoes", "Jewelry", "Watches", "Sunglasses", "Scarves"
];

const BRANDS = [
  "Hermès", "Chanel", "Louis Vuitton", "Gucci", "Prada", "Dior", "Cartier", "Rolex", 
  "Tiffany & Co.", "Bottega Veneta", "Saint Laurent", "Balenciaga"
];

const CONDITIONS = [
  { value: "new", label: "New with tags" },
  { value: "excellent", label: "Excellent" },
  { value: "very_good", label: "Very good" },
  { value: "good", label: "Good" },
  { value: "fair", label: "Fair" }
];

const SIZES = [
  "XXS", "XS", "S", "M", "L", "XL", "XXL", "One Size"
];

const COLORS = [
  { value: "black", label: "Black", color: "#000000" },
  { value: "white", label: "White", color: "#FFFFFF" },
  { value: "brown", label: "Brown", color: "#8B4513" },
  { value: "tan", label: "Tan", color: "#D2B48C" },
  { value: "red", label: "Red", color: "#DC2626" },
  { value: "pink", label: "Pink", color: "#EC4899" },
  { value: "blue", label: "Blue", color: "#2563EB" },
  { value: "green", label: "Green", color: "#16A34A" },
  { value: "gold", label: "Gold", color: "#F59E0B" },
  { value: "silver", label: "Silver", color: "#6B7280" }
];

const MATERIALS = [
  "Leather", "Canvas", "Silk", "Cotton", "Wool", "Cashmere", "Metal", "Gold", "Silver", "Platinum"
];

const SORT_OPTIONS = [
  { value: "relevance", label: "Most Relevant" },
  { value: "price_low", label: "Price: Low to High" },
  { value: "price_high", label: "Price: High to Low" },
  { value: "newest", label: "Newest First" },
  { value: "oldest", label: "Oldest First" },
  { value: "popularity", label: "Most Popular" }
];

export function SearchFilters({ isOpen = true, onClose, className }: SearchFiltersProps) {
  const { state, setFilters, clearSearch } = useSearch();
  const [expandedSections, setExpandedSections] = useState<string[]>([
    "categories", "price", "condition", "sort"
  ]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const handleFilterChange = (key: keyof SearchFiltersType, value: any) => {
    setFilters({ [key]: value });
  };

  const handleArrayFilterToggle = (key: keyof SearchFiltersType, value: string) => {
    const currentArray = state.filters[key] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    
    handleFilterChange(key, newArray);
  };

  const handlePriceChange = (values: number[]) => {
    handleFilterChange("priceRange", { min: values[0], max: values[1] });
  };

  const clearAllFilters = () => {
    clearSearch();
  };

  const getActiveFilterCount = () => {
    const filters = state.filters;
    let count = 0;
    
    if (filters.categories.length > 0) count += filters.categories.length;
    if (filters.brands.length > 0) count += filters.brands.length;
    if (filters.condition.length > 0) count += filters.condition.length;
    if (filters.size.length > 0) count += filters.size.length;
    if (filters.color.length > 0) count += filters.color.length;
    if (filters.material.length > 0) count += filters.material.length;
    if (filters.priceRange.min > 0 || filters.priceRange.max < 50000) count++;
    if (filters.availability !== "all") count++;
    
    return count;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const FilterSection = ({ 
    title, 
    icon: Icon, 
    sectionKey, 
    children 
  }: { 
    title: string; 
    icon: React.ComponentType<{ className?: string }>; 
    sectionKey: string; 
    children: React.ReactNode;
  }) => {
    const isExpanded = expandedSections.includes(sectionKey);
    
    return (
      <Collapsible open={isExpanded} onOpenChange={() => toggleSection(sectionKey)}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between h-12 px-4 hover:bg-neutral-50 dark:hover:bg-neutral-800"
          >
            <div className="flex items-center space-x-3">
              <Icon className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              <span className="font-medium text-black dark:text-white">{title}</span>
            </div>
            <ChevronDown className={cn(
              "w-4 h-4 text-neutral-400 transition-transform duration-200",
              isExpanded && "rotate-180"
            )} />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="px-4 pb-4">
          {children}
        </CollapsibleContent>
      </Collapsible>
    );
  };

  if (!isOpen) return null;

  return (
    <Card className={cn("w-80 h-fit", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700">
        <div className="flex items-center space-x-2">
          <Filter className="w-5 h-5 text-black dark:text-white" />
          <h3 className="font-semibold text-black dark:text-white">Filters</h3>
          {getActiveFilterCount() > 0 && (
            <Badge variant="default" className="h-5 min-w-[20px] text-xs">
              {getActiveFilterCount()}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-8 px-2 text-neutral-600 hover:text-black dark:text-neutral-400 dark:hover:text-white"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            Clear
          </Button>
          
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="max-h-[70vh] overflow-y-auto">
        {/* Sort */}
        <FilterSection title="Sort By" icon={Sparkles} sectionKey="sort">
          <Select 
            value={state.filters.sortBy} 
            onValueChange={(value) => handleFilterChange("sortBy", value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FilterSection>

        {/* Price Range */}
        <FilterSection title="Price Range" icon={Tag} sectionKey="price">
          <div className="space-y-4">
            <div className="px-2">
              <Slider
                value={[state.filters.priceRange.min, state.filters.priceRange.max]}
                onValueChange={handlePriceChange}
                max={50000}
                min={0}
                step={100}
                className="w-full"
              />
            </div>
            
            <div className="flex items-center space-x-2 text-sm">
              <span className="text-neutral-600 dark:text-neutral-400">
                {formatCurrency(state.filters.priceRange.min)}
              </span>
              <span className="text-neutral-400">-</span>
              <span className="text-neutral-600 dark:text-neutral-400">
                {formatCurrency(state.filters.priceRange.max)}
              </span>
            </div>
          </div>
        </FilterSection>

        {/* Categories */}
        <FilterSection title="Categories" icon={Package} sectionKey="categories">
          <div className="space-y-2">
            {CATEGORIES.map((category) => (
              <div key={category} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category}`}
                  checked={state.filters.categories.includes(category)}
                  onCheckedChange={() => handleArrayFilterToggle("categories", category)}
                />
                <Label 
                  htmlFor={`category-${category}`}
                  className="text-sm text-neutral-700 dark:text-neutral-300 cursor-pointer"
                >
                  {category}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Brands */}
        <FilterSection title="Brands" icon={Star} sectionKey="brands">
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {BRANDS.map((brand) => (
              <div key={brand} className="flex items-center space-x-2">
                <Checkbox
                  id={`brand-${brand}`}
                  checked={state.filters.brands.includes(brand)}
                  onCheckedChange={() => handleArrayFilterToggle("brands", brand)}
                />
                <Label 
                  htmlFor={`brand-${brand}`}
                  className="text-sm text-neutral-700 dark:text-neutral-300 cursor-pointer"
                >
                  {brand}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Condition */}
        <FilterSection title="Condition" icon={Package} sectionKey="condition">
          <div className="space-y-2">
            {CONDITIONS.map((condition) => (
              <div key={condition.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`condition-${condition.value}`}
                  checked={state.filters.condition.includes(condition.value)}
                  onCheckedChange={() => handleArrayFilterToggle("condition", condition.value)}
                />
                <Label 
                  htmlFor={`condition-${condition.value}`}
                  className="text-sm text-neutral-700 dark:text-neutral-300 cursor-pointer"
                >
                  {condition.label}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Size */}
        <FilterSection title="Size" icon={Ruler} sectionKey="size">
          <div className="grid grid-cols-3 gap-2">
            {SIZES.map((size) => (
              <Button
                key={size}
                variant={state.filters.size.includes(size) ? "default" : "outline"}
                size="sm"
                onClick={() => handleArrayFilterToggle("size", size)}
                className="h-8 text-xs"
              >
                {size}
              </Button>
            ))}
          </div>
        </FilterSection>

        {/* Color */}
        <FilterSection title="Color" icon={Palette} sectionKey="color">
          <div className="grid grid-cols-5 gap-2">
            {COLORS.map((color) => (
              <button
                key={color.value}
                onClick={() => handleArrayFilterToggle("color", color.value)}
                className={cn(
                  "w-8 h-8 rounded-full border-2 transition-all duration-200",
                  state.filters.color.includes(color.value)
                    ? "border-black dark:border-white scale-110"
                    : "border-neutral-300 dark:border-neutral-600 hover:scale-105"
                )}
                style={{ backgroundColor: color.color }}
                title={color.label}
              />
            ))}
          </div>
        </FilterSection>

        {/* Material */}
        <FilterSection title="Material" icon={Sparkles} sectionKey="material">
          <div className="space-y-2">
            {MATERIALS.map((material) => (
              <div key={material} className="flex items-center space-x-2">
                <Checkbox
                  id={`material-${material}`}
                  checked={state.filters.material.includes(material)}
                  onCheckedChange={() => handleArrayFilterToggle("material", material)}
                />
                <Label 
                  htmlFor={`material-${material}`}
                  className="text-sm text-neutral-700 dark:text-neutral-300 cursor-pointer"
                >
                  {material}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Availability */}
        <FilterSection title="Availability" icon={Package} sectionKey="availability">
          <Select 
            value={state.filters.availability} 
            onValueChange={(value) => handleFilterChange("availability", value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Items</SelectItem>
              <SelectItem value="available">Available Only</SelectItem>
              <SelectItem value="sold">Sold Items</SelectItem>
            </SelectContent>
          </Select>
        </FilterSection>
      </div>
    </Card>
  );
}
