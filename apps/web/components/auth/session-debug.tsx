"use client";

import { useEffect, useState } from "react";
import { authClient } from "@repo/backend/better-auth/client";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";

export function SessionDebug() {
  const [betterAuthSession, setBetterAuthSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get Convex user data
  const convexUser = useQuery(api.auth.getCurrentUser);

  const checkBetterAuthSession = async () => {
    try {
      setLoading(true);
      setError(null);
      const sessionData = await authClient.getSession();
      setBetterAuthSession(sessionData);
      console.log("Better Auth session data:", sessionData);
    } catch (err) {
      console.error("Better Auth session error:", err);
      setError(err instanceof Error ? err.message : "Session check failed");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkBetterAuthSession();
  }, []);

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-lg">Session Debug</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Convex Session Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-semibold">Convex Session:</h4>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status:</span>
            {convexUser ? (
              <Badge variant="default">Active</Badge>
            ) : (
              <Badge variant="destructive">No Session</Badge>
            )}
          </div>

          {convexUser && (
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-xs">User ID:</span>
                <span className="text-xs font-mono">{convexUser._id}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs">Email:</span>
                <span className="text-xs">{convexUser.email}</span>
              </div>
            </div>
          )}
        </div>

        <hr className="border-gray-200" />

        {/* Better Auth Session Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-semibold">Better Auth Session:</h4>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status:</span>
            {loading ? (
              <Badge variant="secondary">Loading...</Badge>
            ) : betterAuthSession ? (
              <Badge variant="default">Active</Badge>
            ) : (
              <Badge variant="destructive">No Session</Badge>
            )}
          </div>

          {error && (
            <div className="p-2 bg-red-50 border border-red-200 rounded-md">
              <p className="text-xs text-red-600">Error: {error}</p>
            </div>
          )}

          {betterAuthSession && (
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-xs">User ID:</span>
                <span className="text-xs font-mono">{betterAuthSession.user?.id || "N/A"}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs">Email:</span>
                <span className="text-xs">{betterAuthSession.user?.email || "N/A"}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs">Session ID:</span>
                <span className="text-xs font-mono">{betterAuthSession.session?.id?.slice(0, 8) || "N/A"}...</span>
              </div>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Button
            onClick={checkBetterAuthSession}
            variant="outline"
            size="sm"
            className="w-full"
            disabled={loading}
          >
            {loading ? "Checking..." : "Refresh Better Auth"}
          </Button>

          <Button
            onClick={() => {
              console.log("Convex user:", convexUser);
              console.log("Better Auth session:", betterAuthSession);
              console.log("Cookies:", document.cookie);
            }}
            variant="outline"
            size="sm"
            className="w-full"
          >
            Log All Session Data
          </Button>
        </div>

        <div className="text-xs text-gray-500">
          <p><strong>Note:</strong> Convex Better Auth uses Convex for session management, not traditional cookies.</p>
        </div>
      </CardContent>
    </Card>
  );
}
