"use client";

import { useState } from "react";
import { authClient } from "@repo/backend/better-auth/client";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";

export function AuthTest() {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("password123");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testSignIn = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      console.log("Testing sign in with:", { email, password });
      console.log("Auth client config:", authClient);

      const result = await authClient.signIn.email({
        email,
        password,
      });

      console.log("Sign in result:", result);
      setResult(result);

      if (result.error) {
        setError(result.error.message || "Unknown error");
      }
    } catch (err) {
      console.error("Sign in error:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  const testSignUp = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      console.log("Testing sign up with:", { email, password, name: "Test User" });
      console.log("Auth client config:", authClient);

      const result = await authClient.signUp.email({
        email,
        password,
        name: "Test User",
      });

      console.log("Sign up result:", result);
      setResult(result);

      if (result.error) {
        setError(result.error.message || "Unknown error");
      }
    } catch (err) {
      console.error("Sign up error:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  const testSession = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log("Testing get session");
      const session = await authClient.getSession();
      console.log("Session result:", session);
      setResult(session);
    } catch (err) {
      console.error("Session error:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Auth Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <Input
            type="password"
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Button
            onClick={testSignUp}
            disabled={loading}
            className="w-full"
          >
            {loading ? "Testing..." : "Test Sign Up"}
          </Button>

          <Button
            onClick={testSignIn}
            disabled={loading}
            variant="outline"
            className="w-full"
          >
            {loading ? "Testing..." : "Test Sign In"}
          </Button>

          <Button
            onClick={testSession}
            disabled={loading}
            variant="outline"
            className="w-full"
          >
            {loading ? "Testing..." : "Test Get Session"}
          </Button>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">Error: {error}</p>
          </div>
        )}

        {result && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm font-medium">Result:</p>
            <pre className="text-xs mt-1 overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="text-xs text-gray-500">
          <p><strong>Base URL:</strong> {process.env.NEXT_PUBLIC_CONVEX_SITE_URL || "http://localhost:3000"}</p>
          <p><strong>Convex URL:</strong> {process.env.NEXT_PUBLIC_CONVEX_URL}</p>
          <p><strong>Site URL:</strong> {process.env.NEXT_PUBLIC_CONVEX_SITE_URL}</p>
        </div>
      </CardContent>
    </Card>
  );
}
