"use client";

import { useAuth, useRequireAdmin } from "@/hooks/useBetterAuth";
import { ProtectedRouteProps } from "@repo/backend/better-auth/types";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export function ProtectedRoute({
  children,
  requireAuth = true,
  requireSubscription = false,
  allowedRoles = [],
  requireSellerVerification = false,
  fallback = <div>Loading...</div>,
  redirectTo = "/",
}: ProtectedRouteProps) {
  const {
    user,
    isLoading,
    isAuthenticated,
    hasActiveSubscription,
    sellerStatus,
  } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isLoading) return;

    // Check authentication
    if (requireAuth && !isAuthenticated) {
      router.push(`${redirectTo}?redirect=${encodeURIComponent(window.location.pathname)}`);
      return;
    }

    // Check subscription
    if (requireSubscription && !hasActiveSubscription) {
      router.push("/subscription");
      return;
    }

    // Check role-based access
    if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.userType)) {
      router.push("/unauthorized");
      return;
    }

    // Check seller verification
    if (requireSellerVerification && user?.userType === "seller" && !sellerStatus?.canSell) {
      router.push("/seller/verification");
      return;
    }
  }, [
    isLoading,
    isAuthenticated,
    hasActiveSubscription,
    user,
    sellerStatus,
    requireAuth,
    requireSubscription,
    allowedRoles,
    requireSellerVerification,
    router,
    redirectTo,
  ]);

  if (isLoading) {
    return <>{fallback}</>;
  }

  // Show fallback if requirements not met
  if (requireAuth && !isAuthenticated) return <>{fallback}</>;
  if (requireSubscription && !hasActiveSubscription) return <>{fallback}</>;
  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.userType)) return <>{fallback}</>;
  if (requireSellerVerification && user?.userType === "seller" && !sellerStatus?.canSell) return <>{fallback}</>;

  return <>{children}</>;
}

// Convenience components for common use cases
export const RequireAuth = ({ children, ...props }: Omit<ProtectedRouteProps, "requireAuth">) => (
  <ProtectedRoute requireAuth={true} {...props}>
    {children}
  </ProtectedRoute>
);

export const RequireSubscription = ({ children, ...props }: Omit<ProtectedRouteProps, "requireSubscription">) => (
  <ProtectedRoute requireAuth={true} requireSubscription={true} {...props}>
    {children}
  </ProtectedRoute>
);

export const RequireSeller = ({ children, ...props }: Omit<ProtectedRouteProps, "allowedRoles" | "requireSellerVerification">) => (
  <ProtectedRoute 
    requireAuth={true} 
    allowedRoles={["seller", "admin"]} 
    requireSellerVerification={true} 
    {...props}
  >
    {children}
  </ProtectedRoute>
);

export const RequireAdmin = ({ children, ...props }: Omit<ProtectedRouteProps, "allowedRoles">) => (
  <ProtectedRoute requireAuth={true} allowedRoles={["admin"]} {...props}>
    {children}
  </ProtectedRoute>
);
