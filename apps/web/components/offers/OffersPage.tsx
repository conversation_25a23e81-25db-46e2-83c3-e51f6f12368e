"use client";

import { useState, useMemo } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent } from "@repo/ui/components/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Skeleton } from "@repo/ui/components/skeleton";
import { 
  Search, 
  Package, 
  DollarSign, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock, 
  MoreHorizontal, 
  Eye, 
  Grid3X3, 
  List,
  MessageSquare,
  TrendingUp,
  TrendingDown,
  User,
  MapPin,
  Heart
} from "lucide-react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import { MarketplaceHeader } from "../marketplace/MarketplaceHeader";
import { toast } from "sonner";
import { OfferMessageModal } from "./OfferMessageModal";

interface OffersPageProps {
  className?: string;
}

type OfferStatus = "pending" | "accepted" | "declined" | "countered" | "expired" | "withdrawn" | "all";
type SortBy = "newest" | "oldest" | "amount_high" | "amount_low" | "status";
type ViewMode = "grid" | "list";

export function OffersPage({ className }: OffersPageProps) {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<OfferStatus>("all");
  const [sortBy, setSortBy] = useState<SortBy>("newest");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedOffer, setSelectedOffer] = useState<any>(null);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
  
  const limit = 12;
  const offset = currentPage * limit;

  // Query user's offers
  const offersData = useQuery(api.offerManagement.getBuyerOffers, {
    status: statusFilter === "all" ? undefined : statusFilter,
    limit,
  });

  // Withdraw offer mutation
  const withdrawOffer = useMutation(api.offerManagement.withdrawOffer);

  // Get unread message count
  const unreadMessageCount = useQuery(api.offerManagement.getUnreadOfferMessageCount);

  const isLoading = offersData === undefined;
  const offers = offersData || [];

  // Status badge styling - matching favorites page
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300", icon: Clock },
      accepted: { color: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300", icon: CheckCircle },
      declined: { color: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300", icon: XCircle },
      countered: { color: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300", icon: MessageSquare },
      expired: { color: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300", icon: AlertTriangle },
      withdrawn: { color: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300", icon: Package },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge 
        className={`absolute top-3 left-3 text-xs flex items-center gap-1 px-2 py-1 ${config.color}`}
      >
        <Icon className="w-3 h-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Format expiry date
  const formatExpiryDate = (timestamp: number) => {
    const now = Date.now();
    const timeLeft = timestamp - now;
    const daysLeft = Math.ceil(timeLeft / (1000 * 60 * 60 * 24));
    
    if (daysLeft <= 0) return "Expired";
    if (daysLeft === 1) return "Expires tomorrow";
    return `Expires in ${daysLeft} days`;
  };

  // Handle withdraw offer
  const handleWithdrawOffer = async (offerId: string) => {
    try {
      await withdrawOffer({ offerId: offerId as any });
      toast.success("Offer withdrawn successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to withdraw offer");
    }
  };

  // Handle open message modal
  const handleOpenMessage = (offer: any) => {
    setSelectedOffer(offer);
    setIsMessageModalOpen(true);
  };

  // Handle close message modal
  const handleCloseMessage = () => {
    setIsMessageModalOpen(false);
    setSelectedOffer(null);
  };

  // Filter and sort offers
  const filteredOffers = useMemo(() => {
    let filtered = offers;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(offer => 
        offer.product?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        offer.product?.brand?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        offer.seller?.name?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(offer => offer.status === statusFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return b._creationTime - a._creationTime;
        case "oldest":
          return a._creationTime - b._creationTime;
        case "amount_high":
          return b.offerAmount - a.offerAmount;
        case "amount_low":
          return a.offerAmount - b.offerAmount;
        case "status":
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    return filtered;
  }, [offers, searchQuery, statusFilter, sortBy]);

  // Pagination
  const totalPages = Math.ceil(filteredOffers.length / limit);
  const paginatedOffers = filteredOffers.slice(offset, offset + limit);

  if (!user) {
    return (
      <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
        <MarketplaceHeader />
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center max-w-md">
            <CardContent>
              <Package className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                Sign in to view offers
              </h3>
              <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                Create an account to track and manage your offers.
              </p>
              <Button asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
      <MarketplaceHeader />
      
      <div className={`max-w-7xl mx-auto p-6 space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">My Offers</h1>
            <p className="text-neutral-600 dark:text-neutral-400 mt-1">
              Track and manage your offers on luxury items
            </p>
            {unreadMessageCount && unreadMessageCount > 0 && (
              <div className="flex items-center gap-2 mt-2">
                <MessageSquare className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-blue-600 font-medium">
                  {unreadMessageCount} unread message{unreadMessageCount === 1 ? '' : 's'}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Controls */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                <Input
                  placeholder="Search offers by product, brand, or seller..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex items-center gap-3">
                {/* Status Filter */}
                <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as OfferStatus)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="accepted">Accepted</SelectItem>
                    <SelectItem value="declined">Declined</SelectItem>
                    <SelectItem value="countered">Countered</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                    <SelectItem value="withdrawn">Withdrawn</SelectItem>
                  </SelectContent>
                </Select>

                {/* Sort */}
                <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortBy)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="oldest">Oldest First</SelectItem>
                    <SelectItem value="amount_high">Amount High to Low</SelectItem>
                    <SelectItem value="amount_low">Amount Low to High</SelectItem>
                    <SelectItem value="status">By Status</SelectItem>
                  </SelectContent>
                </Select>

                {/* View Mode */}
                <div className="flex border rounded-lg overflow-hidden">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-none"
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-none"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border border-border rounded-xl">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-light text-muted-foreground mb-1">Total Offers</p>
                  <p className="text-2xl font-light text-foreground">{offers.length}</p>
                </div>
                <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <Package className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-border rounded-xl">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-light text-muted-foreground mb-1">Pending</p>
                  <p className="text-2xl font-light text-foreground">
                    {offers.filter(o => o.status === "pending").length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                  <Clock className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-border rounded-xl">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-light text-muted-foreground mb-1">Accepted</p>
                  <p className="text-2xl font-light text-foreground">
                    {offers.filter(o => o.status === "accepted").length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-border rounded-xl">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-light text-muted-foreground mb-1">Countered</p>
                  <p className="text-2xl font-light text-foreground">
                    {offers.filter(o => o.status === "countered").length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <MessageSquare className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content */}
        {isLoading ? (
          // Loading skeleton
          <div className={viewMode === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
            : "space-y-4"
          }>
            {Array.from({ length: limit }).map((_, i) => (
              <Card key={i}>
                <CardContent className={viewMode === "grid" ? "p-0" : "p-6"}>
                  {viewMode === "grid" ? (
                    <div className="space-y-4">
                      <Skeleton className="w-full h-48 rounded-t-lg" />
                      <div className="p-4 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-4 w-1/4" />
                      </div>
                    </div>
                  ) : (
                    <div className="flex gap-4">
                      <Skeleton className="w-24 h-24 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-4 w-1/4" />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredOffers.length === 0 ? (
          // Empty state
          <Card>
            <CardContent className="p-12 text-center">
              {searchQuery || statusFilter !== "all" ? (
                <>
                  <Search className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                    No matching offers
                  </h3>
                  <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                    Try adjusting your search criteria or filters
                  </p>
                  <Button onClick={() => { setSearchQuery(""); setStatusFilter("all"); }} variant="outline">
                    Clear Filters
                  </Button>
                </>
              ) : (
                <>
                  <Package className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                    No offers yet
                  </h3>
                  <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                    Start browsing the marketplace to make offers on items you love
                  </p>
                  <Button asChild>
                    <Link href="/marketplace">Browse Marketplace</Link>
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        ) : (
          // Offers grid/list
          <div className={viewMode === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
            : "space-y-4"
          }>
            {paginatedOffers.map((offer) => (
              <Card key={offer._id} className="group hover:shadow-lg transition-shadow duration-200">
                {viewMode === "grid" ? (
                  // Grid view
                  <CardContent className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <Link href={`/marketplace/product/${offer.product?._id}`}>
                        <div className="relative aspect-square bg-neutral-100 dark:bg-neutral-800">
                          {offer.product?.images && offer.product.images.length > 0 ? (
                            <>
                              <img
                                src={offer.product.images[0]}
                                alt={offer.product.title || "Product"}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                                onError={(e) => {
                                  const img = e.target as HTMLImageElement;
                                  const fallback = img.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                                  img.style.display = 'none';
                                  if (fallback) {
                                    fallback.classList.remove('hidden');
                                    fallback.classList.add('flex');
                                  }
                                }}
                              />
                              <div className="absolute inset-0 items-center justify-center fallback-icon hidden">
                                <Package className="w-16 h-16 text-neutral-400" />
                              </div>
                            </>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="w-16 h-16 text-neutral-400" />
                            </div>
                          )}
                        </div>
                      </Link>

                      {/* Status badge */}
                      {getStatusBadge(offer.status)}
                    </div>

                    <div className="p-4 space-y-2">
                      <div className="space-y-1">
                        <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                          {offer.product?.brand || "Brand"}
                        </p>
                        <Link href={`/marketplace/product/${offer.product?._id}`}>
                          <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                            {offer.product?.title || "Product Title"}
                          </h3>
                        </Link>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-light text-neutral-600 dark:text-neutral-400">Your Offer:</span>
                          <span className="font-medium text-primary text-lg">
                            {formatCurrency(offer.offerAmount)}
                          </span>
                        </div>
                        {offer.product?.price && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-light text-neutral-600 dark:text-neutral-400">List Price:</span>
                            <span className="font-medium text-neutral-900 dark:text-neutral-100">
                              {formatCurrency(offer.product.price)}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between pt-2">
                        <span className="text-xs text-neutral-500 dark:text-neutral-400 font-light">
                          {formatExpiryDate(offer.expiresAt)}
                        </span>
                        
                        {offer.seller && (
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-neutral-600 dark:text-neutral-400">
                              {offer.seller.name}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Link href={`/marketplace/product/${offer.product?._id}`} className="flex-1">
                          <Button variant="outline" size="sm" className="w-full">
                            <Eye className="w-4 h-4 mr-2" />
                            View Product
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleOpenMessage(offer)}
                          className="text-blue-600 border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                        >
                          <MessageSquare className="w-4 h-4" />
                        </Button>
                        {offer.status === "pending" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleWithdrawOffer(offer._id)}
                            className="text-destructive border-destructive hover:bg-destructive/10"
                          >
                            <Package className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                ) : (
                  // List view
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      <Link href={`/marketplace/product/${offer.product?._id}`} className="flex-shrink-0">
                        <div className="relative w-24 h-24 bg-neutral-100 dark:bg-neutral-800 rounded-lg overflow-hidden">
                          {offer.product?.images && offer.product.images.length > 0 ? (
                            <>
                              <img
                                src={offer.product.images[0]}
                                alt={offer.product.title || "Product"}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const img = e.target as HTMLImageElement;
                                  const fallback = img.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                                  img.style.display = 'none';
                                  if (fallback) {
                                    fallback.classList.remove('hidden');
                                    fallback.classList.add('flex');
                                  }
                                }}
                              />
                              <div className="absolute inset-0 items-center justify-center fallback-icon hidden">
                                <Package className="w-8 h-8 text-neutral-400" />
                              </div>
                            </>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="w-8 h-8 text-neutral-400" />
                            </div>
                          )}
                        </div>
                      </Link>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                              {offer.product?.brand || "Brand"}
                            </p>
                            <Link href={`/marketplace/product/${offer.product?._id}`}>
                              <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                                {offer.product?.title || "Product Title"}
                              </h3>
                            </Link>
                            <div className="flex items-center gap-4 mt-1">
                              <span className="font-medium text-primary">
                                {formatCurrency(offer.offerAmount)}
                              </span>
                              {offer.product?.price && (
                                <span className="text-sm text-neutral-500 dark:text-neutral-400">
                                  List: {formatCurrency(offer.product.price)}
                                </span>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-2 ml-4">
                            {getStatusBadge(offer.status)}
                          </div>
                        </div>

                        <div className="flex items-center justify-between mt-3">
                          <div className="flex items-center gap-4 text-sm text-neutral-500 dark:text-neutral-400">
                            {offer.seller && (
                              <span>{offer.seller.name}</span>
                            )}
                            <span>
                              {formatExpiryDate(offer.expiresAt)}
                            </span>
                          </div>

                          <div className="flex gap-2">
                            <Link href={`/marketplace/product/${offer.product?._id}`}>
                              <Button variant="outline" size="sm">
                                <Eye className="w-4 h-4 mr-2" />
                                View Product
                              </Button>
                            </Link>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleOpenMessage(offer)}
                              className="text-blue-600 border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                            >
                              <MessageSquare className="w-4 h-4" />
                            </Button>
                            {offer.status === "pending" && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleWithdrawOffer(offer._id)}
                                className="text-destructive border-destructive hover:bg-destructive/10"
                              >
                                <Package className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Showing {offset + 1} to {Math.min(offset + limit, filteredOffers.length)} of {filteredOffers.length} offers
            </p>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                disabled={currentPage === 0}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                disabled={currentPage === totalPages - 1}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Offer Message Modal */}
      <OfferMessageModal
        isOpen={isMessageModalOpen}
        onClose={handleCloseMessage}
        offer={selectedOffer}
      />
    </div>
  );
}
