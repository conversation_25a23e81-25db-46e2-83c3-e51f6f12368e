"use client";

import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent } from "@repo/ui/components/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { MessageBubble } from "@/components/common/MessageBubble";
import { MessageInput } from "@/components/common/MessageInput";
import { 
  X, 
  User, 
  Package, 
  ArrowLeft,
  MessageCircle
} from "lucide-react";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";


interface OfferMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  offer: {
    _id: string;
    offerAmount: number;
    status: string;
    expiresAt: number;
    product: {
      _id: string;
      title: string;
      brand: string;
      price: number;
      images: string[];
    } | null;
    seller: {
      _id: string;
      name: string;
      email: string;
      profileImage?: string;
    } | null;
    buyer: {
      _id: string;
      name: string;
      email: string;
      profileImage?: string;
    } | null;
  } | null;
}

const MESSAGE_TYPE_LABELS = {
  offer: "Offer",
  counter: "Counter Offer",
  negotiation: "Negotiation",
  question: "Question",
  info: "Information",
  other: "Other",
};

const MESSAGE_TYPE_COLORS = {
  offer: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  counter: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  negotiation: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300",
  question: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  info: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
  other: "bg-neutral-100 text-neutral-800 dark:bg-neutral-900/20 dark:text-neutral-300",
};

export function OfferMessageModal({ isOpen, onClose, offer }: OfferMessageModalProps) {
  const { user } = useAuth();
  const [messageType, setMessageType] = useState<keyof typeof MESSAGE_TYPE_LABELS>("negotiation");
  const [isSending, setIsSending] = useState(false);
  const [expandedMessages, setExpandedMessages] = useState<Set<string>>(new Set());
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Query messages for this offer
  const messages = useQuery(api.offerManagement.getOfferMessages, 
    offer ? { offerId: offer._id as any } : "skip"
  );

  // Mutations
  const sendMessage = useMutation(api.offerManagement.sendOfferMessage);
  const markAsRead = useMutation(api.offerManagement.markOfferMessagesAsRead);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Mark messages as read when modal opens
  useEffect(() => {
    if (isOpen && offer && user) {
      markAsRead({ offerId: offer._id as any }).catch(console.error);
    }
  }, [isOpen, offer, user, markAsRead]);

  if (!offer || !user) return null;

  const isBuyer = user.userId === offer.buyer?._id;
  const isSeller = user.userId === offer.seller?._id;
  const otherParty = isBuyer ? offer.seller : offer.buyer;

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Convert offer message to MessageBubble format
  const convertToMessageBubble = (offerMessage: any) => {
    const senderType: 'user' | 'customer' = offerMessage.senderId === user.userId ? 'user' : 'customer';
    return {
      _id: offerMessage._id,
      content: offerMessage.message,
      senderType,
      _creationTime: offerMessage._creationTime,
      messageType: offerMessage.messageType,
      isRead: offerMessage.isRead,
    };
  };

  const toggleMessageExpansion = (messageId: string) => {
    setExpandedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  };

  const handleSendMessage = async (messageText: string) => {
    if (!messageText.trim() || !offer) return;

    setIsSending(true);
    try {
      await sendMessage({
        offerId: offer._id as any,
        message: messageText.trim(),
        messageType,
      });
      
      setMessageType("negotiation");
      toast.success("Message sent successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to send message");
    } finally {
      setIsSending(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatExpiryDate = (timestamp: number) => {
    const now = Date.now();
    const timeLeft = timestamp - now;
    const daysLeft = Math.ceil(timeLeft / (1000 * 60 * 60 * 24));
    
    if (daysLeft <= 0) return "Expired";
    if (daysLeft === 1) return "Expires tomorrow";
    return `Expires in ${daysLeft} days`;
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'} transition-all duration-300`}>
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      <div className="relative bg-background rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="border-b border-border bg-card p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onClose}
                className="p-1 h-8 w-8 rounded-lg"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
              
              <Avatar className="w-10 h-10">
                <AvatarImage 
                  src={otherParty?.profileImage} 
                  alt="User" 
                />
                <AvatarFallback>
                  {getInitials(otherParty?.name || 'User')}
                </AvatarFallback>
              </Avatar>
              
              <div>
                <h3 className="font-medium text-foreground">
                  {otherParty?.name || 'Unknown User'}
                </h3>
                <p className="text-sm text-muted-foreground font-light">
                  {isBuyer ? 'Seller' : 'Buyer'}
                </p>
              </div>
            </div>
            
            <Button variant="outline" size="sm" className="rounded-xl">
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Product info if available */}
          {offer.product && (
            <div className="mt-3 pt-3 border-t border-border">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground font-light">
                  About: {offer.product.title}
                </span>
              </div>
            </div>
          )}
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Left Panel - Offer Details */}
          <div className="w-80 border-r border-border bg-muted/30 p-6 overflow-y-auto">
            <div className="space-y-6">
              {/* Product Info */}
              <div>
                <h3 className="font-medium text-foreground mb-3">Product Details</h3>
                <Card className="border-border bg-card">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="w-full h-32 bg-muted rounded-lg overflow-hidden">
                        {offer.product?.images && offer.product.images.length > 0 ? (
                          <img
                            src={offer.product.images[0]}
                            alt={offer.product.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Package className="w-8 h-8 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                          {offer.product?.brand}
                        </p>
                        <h4 className="font-medium text-foreground text-sm">
                          {offer.product?.title}
                        </h4>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Offer Details */}
              <div>
                <h3 className="font-medium text-foreground mb-3">Offer Details</h3>
                <Card className="border-border bg-card">
                  <CardContent className="p-4 space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Your Offer:</span>
                      <span className="font-medium text-primary">
                        {formatCurrency(offer.offerAmount)}
                      </span>
                    </div>
                    {offer.product?.price && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">List Price:</span>
                        <span className="font-medium text-foreground">
                          {formatCurrency(offer.product.price)}
                        </span>
                      </div>
                    )}
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Status:</span>
                      <Badge className="text-xs">
                        {offer.status.charAt(0).toUpperCase() + offer.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Expires:</span>
                      <span className="text-xs text-muted-foreground">
                        {formatExpiryDate(offer.expiresAt)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Other Party Info */}
              <div>
                <h3 className="font-medium text-foreground mb-3">
                  {isBuyer ? "Seller" : "Buyer"} Information
                </h3>
                <Card className="border-border bg-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-muted-foreground" />
                      </div>
                      <div>
                        <p className="font-medium text-foreground text-sm">
                          {otherParty?.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {otherParty?.email}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>

          {/* Right Panel - Messages */}
          <div className="flex-1 flex flex-col">
            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3 bg-gradient-to-b from-background to-muted/10">
              {messages ? (
                messages.length > 0 ? (
                  messages.map((message) => {
                    const isOwnMessage = message.senderId === user.userId;
                    const convertedMessage = convertToMessageBubble(message);
                    const isExpanded = expandedMessages.has(message._id);
                    
                    return (
                      <div key={message._id} className="relative">
                        <div 
                          className="cursor-pointer"
                          onClick={() => toggleMessageExpansion(message._id)}
                        >
                          <MessageBubble
                            message={convertedMessage}
                            isOwnMessage={isOwnMessage}
                            senderName={otherParty?.name || 'User'}
                            showAvatar={!isOwnMessage}
                          />
                        </div>
                        
                        {/* Message metadata - only show when expanded */}
                        <AnimatePresence>
                          {isExpanded && (
                            <motion.div
                              initial={{ opacity: 0, height: 0, y: -5 }}
                              animate={{ opacity: 1, height: "auto", y: 0 }}
                              exit={{ opacity: 0, height: 0, y: -5 }}
                              transition={{ duration: 0.2, ease: "easeInOut" }}
                              className={`mt-1 px-2 py-1.5 rounded-lg backdrop-blur-sm`}
                            >
                              <div className={`flex flex-col gap-1 text-xs text-muted-foreground ${
                                isOwnMessage ? 'justify-end' : 'justify-start'
                              }`}>
                                <div className={`flex items-center gap-1 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                                  <Badge 
                                    variant="outline" 
                                    className={`text-xs ${MESSAGE_TYPE_COLORS[message.messageType]}`}
                                  >
                                    {MESSAGE_TYPE_LABELS[message.messageType]}
                                  </Badge>
                                </div>
                                
                                {/* Read status for own messages */}
                                {isOwnMessage && (
                                  <div className={`flex items-center ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                                    {message.isRead ? (
                                      <div className="flex items-center gap-1">
                                        <span>Read</span>
                                      </div>
                                    ) : (
                                      <div className={`flex items-center gap-1 font-light`}>
                                        <span>Delivered</span>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    );
                  })
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <MessageCircle className="w-12 h-12 text-muted-foreground/50 mx-auto mb-3" />
                      <p className="text-muted-foreground font-light">
                        No messages yet. Start the conversation!
                      </p>
                    </div>
                  </div>
                )
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-3"></div>
                    <p className="text-muted-foreground font-light">Loading messages...</p>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="border-t border-border bg-card">
              <div className="p-3">
                <Select value={messageType} onValueChange={(value) => setMessageType(value as keyof typeof MESSAGE_TYPE_LABELS)}>
                  <SelectTrigger className="w-40 rounded-xl">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(MESSAGE_TYPE_LABELS).map(([key, label]) => (
                      <SelectItem key={key} value={key}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <MessageInput
                  onSendMessage={handleSendMessage}
                  placeholder="Type your message..."
                  disabled={isSending}
                  isLoading={isSending}
                  className="!p-0 !py-1"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
