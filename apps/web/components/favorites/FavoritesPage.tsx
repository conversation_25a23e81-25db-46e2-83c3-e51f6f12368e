"use client";

import { useState } from "react";
import { useUserFavorites, useFavoriteActions } from "@/hooks/useFavorites";
import { useAuth } from "@/hooks/useBetterAuth";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Input } from "@repo/ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { 
  Heart, 
  Search, 
  SortAsc, 
  Star, 
  Package, 
  Eye, 
  ShoppingBag,
  X,
  Grid3X3,
  List,
  Filter,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react";
import Link from "next/link";
import { MarketplaceHeader } from "../marketplace/MarketplaceHeader";
import { formatDistanceToNow } from "date-fns";

interface FavoritesPageProps {
  className?: string;
}

type ViewMode = "grid" | "list";
type SortBy = "newest" | "oldest" | "price_high" | "price_low" | "brand" | "status";
type StatusFilter = "all" | "available" | "sold" | "reserved";

const CONDITION_COLORS = {
  new: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  like_new: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  good: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
  fair: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300",
};

const CONDITION_LABELS = {
  new: "New",
  like_new: "Like New",
  good: "Good",
  fair: "Fair",
};

const STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  sold: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
  reserved: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
  draft: "bg-neutral-100 text-neutral-800 dark:bg-neutral-900/20 dark:text-neutral-300",
};

const STATUS_LABELS = {
  active: "Available",
  sold: "Sold",
  reserved: "Reserved",
  draft: "Draft",
};

const STATUS_ICONS = {
  active: CheckCircle,
  sold: CheckCircle,
  reserved: Clock,
  draft: AlertCircle,
};

export function FavoritesPage({ className }: FavoritesPageProps) {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortBy>("newest");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("all");
  const [currentPage, setCurrentPage] = useState(0);
  
  const limit = 12;
  const offset = currentPage * limit;

  const { favorites, total, hasMore, isLoading } = useUserFavorites({
    limit,
    offset,
    includeSold: true,
  });

  const { removeFromFavorites } = useFavoriteActions();

  // Filter favorites based on search and status
  const filteredFavorites = favorites.filter(product => {
    // Status filter
    if (statusFilter !== "all" && product.status !== statusFilter) {
      return false;
    }
    
    // Search filter
    if (!searchQuery.trim()) return true;
    
    const searchTerm = searchQuery.toLowerCase();
    return (
      product.title?.toLowerCase().includes(searchTerm) ||
      product.brand?.toLowerCase().includes(searchTerm) ||
      product.category?.toLowerCase().includes(searchTerm)
    );
  });

  // Sort favorites
  const sortedFavorites = [...filteredFavorites].sort((a, b) => {
    switch (sortBy) {
      case "newest":
        return (b._creationTime || 0) - (a._creationTime || 0);
      case "oldest":
        return (a._creationTime || 0) - (b._creationTime || 0);
      case "price_high":
        return (b.price || 0) - (a.price || 0);
      case "price_low":
        return (a.price || 0) - (b.price || 0);
      case "brand":
        return (a.brand || "").localeCompare(b.brand || "");
      case "status":
        return (a.status || "").localeCompare(b.status || "");
      default:
        return 0;
    }
  });

  const handleRemoveFavorite = async (productId: string) => {
    await removeFromFavorites(productId as any);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusBadge = (product: any) => {
    const status = product.status || "active";
    const IconComponent = STATUS_ICONS[status as keyof typeof STATUS_ICONS] || CheckCircle;
    
    return (
      <Badge
        className={`absolute top-3 left-3 text-xs flex items-center gap-1 ${
          STATUS_COLORS[status as keyof typeof STATUS_COLORS]
                        }`}
      >
        <IconComponent className="w-3 h-3" />
        {STATUS_LABELS[status as keyof typeof STATUS_LABELS]}
      </Badge>
    );
  };

  const getProductActions = (product: any) => {
    if (product.status === "sold") {
      return (
        <div className="p-4 space-y-3">
          <div className="text-center">
            <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-2">
              This item was sold
            </p>
            {product.orderInfo && (
              <p className="text-xs text-neutral-400 dark:text-neutral-500">
                Sold {formatDistanceToNow(product.orderInfo.soldDate)} ago
              </p>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1">
              <Eye className="w-4 h-4 mr-2" />
              View Details
            </Button>
          </div>
        </div>
      );
    }

    if (product.status === "reserved") {
      return (
        <div className="p-4 space-y-3">
          <div className="text-center">
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              This item is reserved
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1">
              <Eye className="w-4 h-4 mr-2" />
              View Details
            </Button>
          </div>
        </div>
      );
    }

    // Available products - show normal actions
    return (
      <div className="p-4 space-y-3">
        <div className="flex gap-2">
          <Button asChild className="flex-1">
            <Link href={`/marketplace/product/${product._id}`}>
              <Eye className="w-4 h-4 mr-2" />
              View Details
            </Link>
          </Button>
        </div>
      </div>
    );
  };

  if (!user) {
    return (
      <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
        <MarketplaceHeader />
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center max-w-md">
            <CardContent>
              <Heart className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                Sign in to view favorites
              </h3>
              <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                Create an account to save your favorite luxury items.
              </p>
              <Button asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
      <MarketplaceHeader />
      <div className={`max-w-7xl mx-auto p-6 space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">My Favorites</h1>
            <p className="text-neutral-600 dark:text-neutral-400 mt-1">
              {total > 0 ? `${total} saved ${total === 1 ? 'item' : 'items'}` : 'No saved items yet'}
            </p>
          </div>
        </div>

        {/* Controls */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                <Input
                  placeholder="Search your favorites..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex items-center gap-3">
                {/* Sort */}
                <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortBy)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="oldest">Oldest First</SelectItem>
                    <SelectItem value="price_high">Price: High to Low</SelectItem>
                    <SelectItem value="price_low">Price: Low to High</SelectItem>
                    <SelectItem value="brand">Brand A-Z</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                  </SelectContent>
                </Select>

                {/* Status Filter */}
                <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as StatusFilter)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="available">Available</SelectItem>
                    <SelectItem value="sold">Sold</SelectItem>
                    <SelectItem value="reserved">Reserved</SelectItem>
                  </SelectContent>
                </Select>

                {/* View Mode */}
                <div className="flex border rounded-lg overflow-hidden">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-none"
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-none"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Content */}
        {isLoading ? (
          // Loading skeleton
          <div className={viewMode === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
            : "space-y-4"
          }>
            {Array.from({ length: limit }).map((_, i) => (
              <Card key={i}>
                <CardContent className={viewMode === "grid" ? "p-0" : "p-6"}>
                  {viewMode === "grid" ? (
                    <div className="space-y-4">
                      <Skeleton className="w-full h-48 rounded-t-lg" />
                      <div className="p-4 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-4 w-1/4" />
                      </div>
                    </div>
                  ) : (
                    <div className="flex gap-4">
                      <Skeleton className="w-24 h-24 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-4 w-1/4" />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        ) : sortedFavorites.length === 0 ? (
          // Empty state
          <Card>
            <CardContent className="p-12 text-center">
              {searchQuery ? (
                <>
                  <Search className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                    No matching favorites
                  </h3>
                  <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                    Try adjusting your search or browse the marketplace for new items.
                  </p>
                  <Button onClick={() => setSearchQuery("")} variant="outline">
                    Clear Search
                  </Button>
                </>
              ) : (
                <>
                  <Heart className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                    No favorites yet
                  </h3>
                  <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                    Start browsing and heart items you love to save them here.
                  </p>
                  <Button asChild>
                    <Link href="/marketplace">Browse Marketplace</Link>
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        ) : (
          // Favorites grid/list
          <div className={viewMode === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
            : "space-y-4"
          }>
            {sortedFavorites.map((product) => (
              <Card key={product._id} className="group hover:shadow-lg transition-shadow duration-200">
                {viewMode === "grid" ? (
                  // Grid view
                  <CardContent className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <Link href={`/marketplace/product/${product._id}`}>
                        <div className="relative aspect-square bg-neutral-100 dark:bg-neutral-800">
                          {product.images?.[0] ? (
                            <>
                              <img
                                src={product.images[0]}
                                alt={product.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                                onError={(e) => {
                                  const img = e.target as HTMLImageElement;
                                  const fallback = img.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                                  img.style.display = 'none';
                                  if (fallback) {
                                    fallback.classList.remove('hidden');
                                    fallback.classList.add('flex');
                                  }
                                }}
                              />
                              <div className="absolute inset-0 items-center justify-center fallback-icon hidden">
                                <Package className="w-16 h-16 text-neutral-400" />
                              </div>
                            </>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="w-16 h-16 text-neutral-400" />
                            </div>
                          )}
                        </div>
                      </Link>

                      {/* Remove from favorites button */}
                      <Button
                        size="sm"
                        variant="secondary"
                        className="absolute top-3 right-3 w-8 h-8 p-0 bg-white/80 dark:bg-neutral-900/80 hover:bg-white dark:hover:bg-neutral-900 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        onClick={() => handleRemoveFavorite(product._id)}
                      >
                        <X className="w-4 h-4 text-red-500" />
                      </Button>

                      {/* Condition badge */}
                      <Badge
                        className={`absolute top-3 left-3 text-xs ${
                          CONDITION_COLORS[product.condition as keyof typeof CONDITION_COLORS]
                        }`}
                      >
                        {CONDITION_LABELS[product.condition as keyof typeof CONDITION_LABELS]}
                      </Badge>

                      {/* Status badge */}
                      {getStatusBadge(product)}
                    </div>

                    <div className="p-4 space-y-2">
                      <div className="space-y-1">
                        <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                          {product.brand}
                        </p>
                        <Link href={`/marketplace/product/${product._id}`}>
                          <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                            {product.title}
                          </h3>
                        </Link>
                      </div>

                      <div className="flex items-center justify-between">
                        <p className="text-lg font-bold text-black dark:text-white">
                          {formatCurrency(product.price)}
                        </p>
                        
                        {product.seller && (
                          <div className="flex items-center gap-1">
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                            <span className="text-xs text-neutral-600 dark:text-neutral-400">
                              {product.seller.rating?.toFixed(1) || "0.0"}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2 pt-2">
                        {getProductActions(product)}
                      </div>
                    </div>
                  </CardContent>
                ) : (
                  // List view
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      <Link href={`/marketplace/product/${product._id}`} className="flex-shrink-0">
                        <div className="relative w-24 h-24 bg-neutral-100 dark:bg-neutral-800 rounded-lg overflow-hidden">
                          {product.images?.[0] ? (
                            <>
                              <img
                                src={product.images[0]}
                                alt={product.title}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const img = e.target as HTMLImageElement;
                                  const fallback = img.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                                  img.style.display = 'none';
                                  if (fallback) {
                                    fallback.classList.remove('hidden');
                                    fallback.classList.add('flex');
                                  }
                                }}
                              />
                              <div className="absolute inset-0 items-center justify-center fallback-icon hidden">
                                <Package className="w-8 h-8 text-neutral-400" />
                              </div>
                            </>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="w-8 h-8 text-neutral-400" />
                            </div>
                          )}
                        </div>
                      </Link>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                              {product.brand}
                            </p>
                            <Link href={`/marketplace/product/${product._id}`}>
                              <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                                {product.title}
                              </h3>
                            </Link>
                            <p className="text-lg font-bold text-black dark:text-white mt-1">
                              {formatCurrency(product.price)}
                            </p>
                          </div>

                          <div className="flex items-center gap-2 ml-4">
                            <Badge
                              className={`text-xs ${
                                CONDITION_COLORS[product.condition as keyof typeof CONDITION_COLORS]
                              }`}
                            >
                              {CONDITION_LABELS[product.condition as keyof typeof CONDITION_LABELS]}
                            </Badge>
                            
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleRemoveFavorite(product._id)}
                              className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between mt-3">
                          <div className="flex items-center gap-4 text-sm text-neutral-500 dark:text-neutral-400">
                            {product.seller && (
                              <div className="flex items-center gap-1">
                                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                <span>{product.seller.rating?.toFixed(1) || "0.0"}</span>
                                <span>({product.seller.reviewCount || 0} reviews)</span>
                              </div>
                            )}
                            <span>
                              Added {formatDistanceToNow(new Date(product._creationTime || 0), { addSuffix: true })}
                            </span>
                          </div>

                          <div className="flex gap-2">
                            {getProductActions(product)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {total > limit && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Showing {offset + 1} to {Math.min(offset + limit, total)} of {total} favorites
            </p>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                disabled={currentPage === 0}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={!hasMore}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
