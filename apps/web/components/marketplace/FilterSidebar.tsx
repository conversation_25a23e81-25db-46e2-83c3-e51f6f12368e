"use client";

import { useState, useRef, useEffect } from "react";
import { ChevronDown, ChevronUp, X, Search, Filter } from "lucide-react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { 
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
  DrawerTrigger,
  DrawerClose
} from "@repo/ui/components/drawer";
import { FilterSelector } from "@/components/common/FilterSelector";
import { SectionHeader } from "@/components/common/SectionHeader";
import { PriceRangeSelector } from "@/components/common/PriceRangeSelector";
import designers from "@/lib/designers.json";

import { FilterState } from "./MarketplaceContent";
import { cn } from "@repo/ui/src/lib/utils";

// Simple mobile detection hook
function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
}

interface FilterSidebarProps {
  filters: FilterState;
  filterOptions?: {
    categories: Array<{ name: string; count: number }>;
    brands: Array<{ name: string; count: number }>;
    conditions: Array<{ name: string; count: number }>;
    priceRange: [number | null, number | null];
  };
  onFilterChange: (filters: Partial<FilterState>) => void;
  onClearFilters: () => void;
  activeFilterCount: number;
}

// Category icon mapping based on category names
const CATEGORY_ICONS: Record<string, string> = {
  "handbags": "👜",
  "watches": "⌚",
  "sneakers": "👟", 
  "jewelry": "💎",
  "clothing": "👔",
  "accessories": "🕶️",
  "collectibles": "🎨",
  "sunglasses": "🕶️",
  // Add fallback for any other categories
};

// Condition descriptions and colors using theme system
const CONDITION_INFO: Record<string, { description: string; color: string }> = {
  "new": { description: "Brand new with tags", color: "text-[hsl(var(--status-success))]" },
  "like_new": { description: "Excellent condition", color: "text-[hsl(var(--status-info))]" },
  "good": { description: "Minor signs of wear", color: "text-[hsl(var(--status-warning))]" },
  "fair": { description: "Noticeable wear", color: "text-[hsl(var(--status-error))]" }
};

export function FilterSidebar({
  filters,
  filterOptions,
  onFilterChange,
  onClearFilters,
  activeFilterCount,
}: FilterSidebarProps) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  
  const isMobile = useIsMobile();

  const handleBrandChange = (brands: string[]) => {
    onFilterChange({ brands });
  };

  const handleConditionChange = (conditions: string[]) => {
    onFilterChange({ conditions });
  };

  const handleCategoryChange = (categories: string[]) => {
    onFilterChange({ categories });
  };

  const handlePriceRangeChange = (range: [number | null, number | null]) => {
    onFilterChange({ priceRange: range });
  };

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Header with Clear Filters */}
      <SectionHeader
        title="Filters"
        size="lg"
        titleClassName="text-foreground"
        actions={
          activeFilterCount > 0 ? (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearFilters}
              className="text-sm font-light rounded-full px-4"
            >
              Reset Filters
            </Button>
          ) : undefined
        }
      />

      {/* Brands Section */}
      <div className="space-y-4">
        <SectionHeader title="BRANDS" uppercase />
        
        <FilterSelector
          selectedItems={filters.brands}
          availableItems={filterOptions?.brands || []}
          onSelectionChange={handleBrandChange}
          searchPlaceholder="Search brands..."
          searchSuggestions={designers.map(designer => ({ name: designer.name, count: 0 }))}
          maxDisplayedItems={10}
          maxSearchResults={8}
        />
      </div>

      {/* Conditions Section */}
      <div className="space-y-4">
        <SectionHeader title="CONDITION" uppercase />
        
        <FilterSelector
          selectedItems={filters.conditions}
          availableItems={filterOptions?.conditions || []}
          onSelectionChange={handleConditionChange}
          searchPlaceholder="Search conditions..."
          showSearch={false}
          maxDisplayedItems={10}
        />
      </div>

      {/* Categories Section */}
      <div className="space-y-4">
        <SectionHeader title="CATEGORY" uppercase />
        
        <FilterSelector
          selectedItems={filters.categories}
          availableItems={filterOptions?.categories || []}
          onSelectionChange={handleCategoryChange}
          searchPlaceholder="Search categories..."
          showSearch={false}
          maxDisplayedItems={15}
        />
      </div>

      {/* Price Section */}
      <div className="space-y-4">
        <SectionHeader title="PRICE" uppercase />
        
        <PriceRangeSelector
          value={filters.priceRange}
          onChange={handlePriceRangeChange}
        />
      </div>
    </div>
  );

  if (!filterOptions) {
    return (
      <div className="w-80 space-y-6">
        <div className="animate-pulse space-y-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <div className="h-4 w-24 bg-neutral-200 dark:bg-neutral-800 rounded" />
              <div className="space-y-2">
                {Array.from({ length: 3 }).map((_, j) => (
                  <div key={j} className="h-3 w-full bg-neutral-100 dark:bg-neutral-900 rounded" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Mobile: Show drawer trigger button
  if (isMobile) {
    return (
      <>
        <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
          <DrawerTrigger asChild>
            <Button 
              variant="outline" 
              className="w-full flex items-center justify-between rounded-xl font-light"
            >
              <span className="flex items-center gap-2 text-primary">
                <Filter className="w-4 h-4 text-muted" />
                Filters
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="font-light rounded-xl">
                    {activeFilterCount}
                  </Badge>
                )}
              </span>
              <ChevronDown className="w-4 h-4 text-muted" />
            </Button>
          </DrawerTrigger>
          
          <DrawerContent>
            <DrawerHeader className="text-left">
              <div className="flex items-center justify-between">
                <div>
                  <DrawerTitle className="text-xl font-light">Filters</DrawerTitle>
                  <DrawerDescription>
                    Refine your search results
                  </DrawerDescription>
                </div>
                <DrawerClose asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <X className="h-4 w-4" />
                  </Button>
                </DrawerClose>
              </div>
            </DrawerHeader>
            
            <div className="px-4 pb-6 overflow-y-auto max-h-[70vh]">
              <FilterContent />
            </div>
            
            <div className="px-4 pb-6 mt-2 mb-2">
              <Button 
                onClick={() => setIsDrawerOpen(false)}
                className="w-full rounded-xl"
              >
                Apply Filters
              </Button>
            </div>
          </DrawerContent>
        </Drawer>
      </>
    );
  }

  // Desktop: Show sidebar
  return (
    <div className="w-72 flex-shrink-0">
      <FilterContent />
    </div>
  );
}