import { Search, Filter, ShoppingBag, Sparkles, Package } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";

interface EmptyStateProps {
  type?: "no_results" | "no_products" | "no_favorites";
  onClearFilters?: () => void;
}

export function EmptyState({ type = "no_results", onClearFilters }: EmptyStateProps) {
  const getContent = () => {
    switch (type) {
      case "no_results":
        return {
          icon: <Search className="w-12 h-12 text-neutral-500 dark:text-neutral-400" />,
          title: "No luxury items found",
          description: "We couldn't find any items matching your search criteria. Try adjusting your filters or exploring different categories.",
          action: onClearFilters && (
            <Button onClick={onClearFilters} className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 rounded-full px-6 py-3">
              <Filter className="w-4 h-4 mr-2" />
              Clear all filters
            </Button>
          ),
        };
      case "no_products":
        return {
          icon: <Package className="w-12 h-12 text-neutral-500 dark:text-neutral-400" />,
          title: "Collection coming soon",
          description: "Our curated selection of luxury items is being carefully prepared. Be the first to know when exclusive pieces arrive.",
          action: (
            <Button className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 rounded-full px-6 py-3">
              <Sparkles className="w-4 h-4 mr-2" />
              Notify me when items arrive
            </Button>
          ),
        };
      case "no_favorites":
        return {
          icon: <ShoppingBag className="w-12 h-12 text-neutral-500 dark:text-neutral-400" />,
          title: "No favorites yet",
          description: "Start browsing our luxury collection and save items you love to create your personal wishlist.",
          action: (
            <Button asChild className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 rounded-full px-6 py-3">
              <a href="/marketplace">
                <Sparkles className="w-4 h-4 mr-2" />
                Browse Collection
              </a>
            </Button>
          ),
        };
      default:
        return {
          icon: <Search className="w-12 h-12 text-neutral-400" />,
          title: "Nothing here",
          description: "We couldn't find what you're looking for.",
          action: null,
        };
    }
  };

  const content = getContent();

  return (
    <div className="flex items-center justify-center min-h-[60vh] p-8">
      <Card className="max-w-lg w-full p-12 text-center bg-white dark:bg-neutral-900 border-0 ring-1 ring-neutral-200/50 dark:ring-neutral-800/50 rounded-3xl shadow-xl">
        <div className="flex flex-col items-center space-y-8">
          {/* Icon Container */}
          <div className="w-24 h-24 bg-gradient-to-br from-neutral-100 via-neutral-50 to-neutral-100 dark:from-neutral-800 dark:via-neutral-700 dark:to-neutral-800 rounded-3xl flex items-center justify-center shadow-inner">
            {content.icon}
          </div>

          {/* Content */}
          <div className="space-y-4">
            <h3 className="text-2xl font-bold text-black dark:text-white">
              {content.title}
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed text-lg max-w-md">
              {content.description}
            </p>
          </div>

          {/* Action */}
          {content.action && (
            <div className="pt-2">
              {content.action}
            </div>
          )}

          {/* Suggestions */}
          {type === "no_results" && (
            <div className="pt-6 border-t border-neutral-200 dark:border-neutral-800 w-full">
              <p className="text-sm text-neutral-500 mb-4 font-medium">Popular categories:</p>
              <div className="flex flex-wrap gap-3 justify-center">
                {["Handbags", "Watches", "Sneakers", "Jewelry"].map((category) => (
                  <Button
                    key={category}
                    variant="outline"
                    size="sm"
                    className="text-sm rounded-full border-neutral-300 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800 px-4 py-2"
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}