"use client";

import { useState } from "react";
import { Heart, Eye, ShoppingBag } from "lucide-react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { cn } from "@repo/ui/lib/utils";
import Link from "next/link";
import Image from "next/image";
import { useFavorite } from "@/hooks/useFavorites";
import { Id } from "@repo/backend/convex/_generated/dataModel";

interface Product {
  _id: Id<"products">;
  title: string;
  brand: string;
  price: number;
  images: string[];
  condition: string;
  category: {
    name: string;
    slug: string;
  } | string;
  seller?: {
    _id: string;
    name: string;
    businessName?: string;
    rating: number;
    reviewCount: number;
    verificationStatus?: string;
  } | null;
}

interface ProductCardProps {
  product: Product;
}

const CONDITION_COLORS = {
  new: "bg-[hsl(var(--status-success)/0.1)] text-[hsl(var(--status-success))]",
  like_new: "bg-[hsl(var(--status-info)/0.1)] text-[hsl(var(--status-info))]",
  good: "bg-[hsl(var(--status-warning)/0.1)] text-[hsl(var(--status-warning))]",
  fair: "bg-[hsl(var(--status-error)/0.1)] text-[hsl(var(--status-error))]",
};

const CONDITION_LABELS = {
  new: "New",
  like_new: "Like New",
  good: "Good",
  fair: "Fair",
};

export function ProductCard({ product }: ProductCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  
  const { isFavorited, isLoading: isFavoriteLoading, toggleFavorite } = useFavorite(product._id);

  const handleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    await toggleFavorite();
  };

  const handleQuickView = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // TODO: Implement quick view modal
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === product.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? product.images.length - 1 : prev - 1
    );
  };

  return (
    <Link href={`/marketplace/product/${product._id}`}>
      <div
        className={cn(
          "group relative bg-card rounded-3xl border border-border/50 overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-105",
          "cursor-pointer hover:border-border"
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Image Container */}
        <div className="relative aspect-square overflow-hidden bg-muted">
          {product.images.length > 0 ? (
            <>
              <Image
                src={product.images[currentImageIndex] || ""}
                alt={product.title}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
              />
              
              {/* Image Navigation */}
              {product.images.length > 1 && isHovered && (
                <>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      prevImage();
                    }}
                    className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-background/80 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-background"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      nextImage();
                    }}
                    className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-background/80 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-background"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </>
              )}

              {/* Image Indicators */}
              {product.images.length > 1 && (
                <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-1">
                  {product.images.map((_, index) => (
                    <div
                      key={index}
                      className={cn(
                        "w-1.5 h-1.5 rounded-full transition-colors duration-200",
                        index === currentImageIndex
                          ? "bg-card"
                          : "bg-card/50"
                      )}
                    />
                  ))}
                </div>
              )}
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <ShoppingBag className="w-12 h-12" />
            </div>
          )}

          {/* Condition Badge */}
          <Badge
            className={cn(
              "absolute top-3 left-3 text-xs font-medium",
              CONDITION_COLORS[product.condition as keyof typeof CONDITION_COLORS]
            )}
          >
            {CONDITION_LABELS[product.condition as keyof typeof CONDITION_LABELS]}
          </Badge>

          {/* Action Buttons */}
          <div className="absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <Button
              size="sm"
              variant="secondary"
              className="w-8 h-8 p-0 bg-background/80 hover:bg-background rounded-xl"
              onClick={handleFavorite}
              disabled={isFavoriteLoading}
            >
              <Heart
                className={cn(
                  "w-4 h-4 transition-colors duration-200",
                  isFavorited ? "fill-destructive text-destructive" : "text-primary",
                  isFavoriteLoading && "animate-pulse"
                )}
              />
            </Button>
          </div>
        </div>

        {/* Product Info */}
        <div className="p-4 space-y-3">
          <div className="space-y-1">
            <p className="text-xs font-light text-muted uppercase tracking-wide">
              {product.brand}
            </p>
            <h3 className="font-light text-card-foreground line-clamp-2 leading-tight">
              {product.title}
            </h3>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm font-light text-muted">
                {product.condition === 'new' ? 'Brand New' : 
                 product.condition === 'like_new' ? 'Excellent' :
                 product.condition === 'good' ? 'Good' : 'Fair'}
              </p>
              <p className="text-lg font-light text-card-foreground">
                ${product.price.toLocaleString()}
              </p>
            </div>
            <Badge variant="secondary" className="text-xs font-light rounded-full bg-muted/50">
              {typeof product.category === 'string' ? product.category : product.category.name}
            </Badge>
          </div>
        </div>

        {/* Hover Overlay */}
        <div className={cn(
          "absolute inset-0 bg-gradient-to-t from-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
        )} />
      </div>
    </Link>
  );
}