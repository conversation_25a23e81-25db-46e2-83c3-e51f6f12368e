"use client";

import { Star, MessageCircle, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";

interface Seller {
  _id: string;
  name: string;
  userType: string;
}

interface SellerInfoProps {
  seller: Seller;
}

export function SellerInfo({ seller }: SellerInfoProps) {
  // Mock data - in real app, fetch from seller profile
  const sellerStats = {
    rating: 4.8,
    totalSales: 127,
    responseTime: "< 1 hour",
    memberSince: "2023",
    isVerified: true,
  };

  const handleContactSeller = () => {
    // TODO: Implement messaging system
    console.log("Contact seller:", seller._id);
  };

  const handleViewProfile = () => {
    // TODO: Navigate to seller profile
    console.log("View seller profile:", seller._id);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
        Seller Information
      </h3>
      
      <div className="bg-neutral-50 dark:bg-neutral-900 rounded-xl p-6 space-y-4">
        {/* Seller Header */}
        <div className="flex items-start space-x-4">
          <Avatar className="w-12 h-12">
            <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${seller.name}`} />
            <AvatarFallback>
              {seller.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 space-y-2">
            <div className="flex items-center space-x-2">
              <h4 className="font-semibold text-neutral-900 dark:text-neutral-100">
                {seller.name}
              </h4>
              {sellerStats.isVerified && (
                <Badge variant="secondary" className="text-xs">
                  <Shield className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              )}
              {seller.userType === "premium_seller" && (
                <Badge className="text-xs bg-gradient-to-r from-amber-500 to-orange-500 text-white">
                  Premium Seller
                </Badge>
              )}
            </div>
            
            {/* Rating */}
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < Math.floor(sellerStats.rating)
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-neutral-300 dark:text-neutral-600"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                {sellerStats.rating}
              </span>
              <span className="text-sm text-neutral-500 dark:text-neutral-400">
                ({sellerStats.totalSales} sales)
              </span>
            </div>
          </div>
        </div>

        {/* Seller Stats */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-neutral-200 dark:border-neutral-700">
          <div className="space-y-1">
            <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
              Response Time
            </p>
            <p className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
              {sellerStats.responseTime}
            </p>
          </div>
          
          <div className="space-y-1">
            <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
              Member Since
            </p>
            <p className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
              {sellerStats.memberSince}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3 pt-4">
          <Button
            variant="outline"
            onClick={handleContactSeller}
            className="flex-1"
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Contact Seller
          </Button>
          <Button
            variant="ghost"
            onClick={handleViewProfile}
            className="flex-1"
          >
            View Profile
          </Button>
        </div>
      </div>
    </div>
  );
}