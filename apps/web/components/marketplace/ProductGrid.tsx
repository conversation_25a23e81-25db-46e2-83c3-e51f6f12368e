"use client";

import { useState } from "react";
import { Heart, Eye, ChevronDown, ChevronLeft, ChevronRight, Filter, X } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { ProductCard } from "./ProductCard";
import { EmptyState } from "./EmptyState";
import { FilterState } from "./MarketplaceContent";

interface Product {
  _id: string;
  title: string;
  brand: string;
  price: number;
  images: string[];
  condition: string;
  category: {
    name: string;
    slug: string;
  } | string;
  seller?: {
    _id: string;
    name: string;
    businessName?: string;
    rating: number;
    reviewCount: number;
    verificationStatus?: string;
  } | null;
}

interface PaginationInfo {
  total: number;
  page: number;
  totalPages: number;
  hasMore: boolean;
  nextCursor: string | null;
}

interface ProductGridProps {
  products?: Product[];
  sortBy: "newest" | "price_low" | "price_high" | "popular";
  onSortChange: (sort: "newest" | "price_low" | "price_high" | "popular") => void;
  isLoading: boolean;
  activeFilterCount: number;
  pagination?: PaginationInfo;
  onPageChange?: (page: number) => void;
  filters: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
}

const SORT_OPTIONS = {
  newest: "Newest First",
  price_low: "Price: Low to High",
  price_high: "Price: High to Low",
  popular: "Most Popular",
};

export function ProductGrid({
  products = [],
  sortBy,
  onSortChange,
  isLoading,
  activeFilterCount,
  pagination,
  onPageChange,
  filters,
  onFilterChange,
}: ProductGridProps) {
  if (isLoading) {
    return <ProductGridSkeleton />;
  }

  if (!products || products.length === 0) {
    return (
      <div className="space-y-6">
        <ProductGridHeader
          productCount={0}
          sortBy={sortBy}
          onSortChange={onSortChange}
          activeFilterCount={activeFilterCount}
          pagination={pagination}
          filters={filters}
          onFilterChange={onFilterChange}
        />
        <EmptyState />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ProductGridHeader
        productCount={pagination?.total || products.length}
        sortBy={sortBy}
        onSortChange={onSortChange}
        activeFilterCount={activeFilterCount}
        pagination={pagination}
        filters={filters}
        onFilterChange={onFilterChange}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => (
          <ProductCard key={product._id} product={product as any} />
        ))}
      </div>

      {/* Load More Button for Infinite Scroll */}
      {pagination && pagination.hasMore && (
        <div className="flex justify-center mt-8">
          <Button
            onClick={() => onPageChange?.(pagination.page + 1)}
            variant="outline"
            size="lg"
            className="rounded-full px-8 font-light"
          >
            Load More Products
          </Button>
        </div>
      )}

      {/* Show total count */}
      {pagination && (
        <div className="text-center text-sm text-muted mt-4">
          Showing {products.length} of {pagination.total} products
        </div>
      )}
    </div>
  );
}

function ProductGridHeader({
  productCount,
  sortBy,
  onSortChange,
  activeFilterCount,
  pagination,
  filters,
  onFilterChange,
}: {
  productCount: number;
  sortBy: string;
  onSortChange: (sort: any) => void;
  activeFilterCount: number;
  pagination?: PaginationInfo;
  filters: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
}) {
  return (
    <div className="flex items-center justify-between border-b border-border pb-4">
      <div className="flex items-center space-x-4">
        <h1 className="text-lg font-light text-foreground">
          {productCount.toLocaleString()} {productCount === 1 ? "result" : "results"}
        </h1>
        {activeFilterCount > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center space-x-2 rounded-full font-light">
                <Filter className="w-4 h-4 text-muted" />
                <span>{activeFilterCount} filter{activeFilterCount !== 1 ? "s" : ""}</span>
                <ChevronDown className="w-4 h-4 text-muted" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="rounded-2xl border-none min-w-[280px]">
              <div className="p-2">
                <h4 className="text-sm font-medium mb-3 text-foreground">Active Filters</h4>
                
                {/* Categories */}
                {filters.categories.length > 0 && (
                  <div className="mb-3">
                    <h5 className="text-xs text-muted uppercase tracking-wide mb-2">Categories</h5>
                    <div className="space-y-1">
                      {filters.categories.map((category) => (
                        <div key={category} className="flex items-center justify-between bg-muted/30 rounded-sm px-2 py-1">
                          <span className="text-sm">{category}</span>
                          <button
                            onClick={() => onFilterChange({ 
                              categories: filters.categories.filter(c => c !== category) 
                            })}
                            className="text-muted hover:text-foreground p-1 rounded-sm hover:bg-muted"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Brands */}
                {filters.brands.length > 0 && (
                  <div className="mb-3">
                    <h5 className="text-xs text-muted uppercase tracking-wide mb-2">Brands</h5>
                    <div className="space-y-1">
                      {filters.brands.map((brand) => (
                        <div key={brand} className="flex items-center justify-between bg-muted/30 rounded-sm px-2 py-1">
                          <span className="text-sm">{brand}</span>
                          <button
                            onClick={() => onFilterChange({ 
                              brands: filters.brands.filter(b => b !== brand) 
                            })}
                            className="text-muted hover:text-foreground p-1 rounded-sm hover:bg-muted"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Conditions */}
                {filters.conditions.length > 0 && (
                  <div className="mb-3">
                    <h5 className="text-xs text-muted uppercase tracking-wide mb-2">Conditions</h5>
                    <div className="space-y-1">
                      {filters.conditions.map((condition) => (
                        <div key={condition} className="flex items-center justify-between bg-muted/30 rounded-sm px-2 py-1">
                          <span className="text-sm">{condition}</span>
                          <button
                            onClick={() => onFilterChange({ 
                              conditions: filters.conditions.filter(c => c !== condition) 
                            })}
                            className="text-muted hover:text-foreground p-1 rounded-sm hover:bg-muted"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Price Range */}
                {(filters.priceRange[0] !== null || filters.priceRange[1] !== null) && (
                  <div className="mb-3">
                    <h5 className="text-xs text-muted uppercase tracking-wide mb-2">Price Range</h5>
                    <div className="flex items-center justify-between bg-muted/30 rounded-sm px-2 py-1">
                      <span className="text-sm">
                        {filters.priceRange[0] !== null ? `$${filters.priceRange[0].toLocaleString()}` : 'Any'} - {filters.priceRange[1] !== null ? `$${filters.priceRange[1].toLocaleString()}` : 'Any'}
                      </span>
                      <button
                        onClick={() => onFilterChange({ priceRange: [null, null] })}
                        className="text-muted hover:text-foreground p-1 rounded-sm hover:bg-muted"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                )}

                {/* Search Query */}
                {filters.searchQuery && (
                  <div className="mb-3">
                    <h5 className="text-xs text-muted uppercase tracking-wide mb-2">Search</h5>
                    <div className="flex items-center justify-between bg-muted/30 rounded-sm px-2 py-1">
                      <span className="text-sm">"{filters.searchQuery}"</span>
                      <button
                        onClick={() => onFilterChange({ searchQuery: "" })}
                        className="text-muted hover:text-foreground p-1 rounded-sm hover:bg-muted"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center space-x-2 rounded-full font-light">
            <span>{SORT_OPTIONS[sortBy as keyof typeof SORT_OPTIONS]}</span>
            <ChevronDown className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="rounded-2xl">
          {Object.entries(SORT_OPTIONS).map(([value, label]) => (
            <DropdownMenuItem
              key={value}
              onClick={() => onSortChange(value as any)}
              className={`font-light rounded-xl ${sortBy === value ? "bg-accent/10" : ""}`}
            >
              {label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

function ProductGridSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div className="h-8 w-32 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse" />
        <div className="h-10 w-48 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse" />
      </div>
      
      {/* Grid skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={i} className="space-y-3">
            <div className="aspect-square bg-neutral-200 dark:bg-neutral-800 rounded-lg animate-pulse" />
            <div className="space-y-2">
              <div className="h-4 w-3/4 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse" />
              <div className="h-3 w-1/2 bg-neutral-100 dark:bg-neutral-900 rounded animate-pulse" />
              <div className="h-4 w-1/3 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function Pagination({ 
  pagination, 
  onPageChange 
}: { 
  pagination: PaginationInfo; 
  onPageChange: (page: number) => void; 
}) {
  const currentPage = pagination.page;
  const totalPages = pagination.totalPages;

  const handlePrevious = () => {
    if (currentPage > 0) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (pagination.hasMore) {
      onPageChange(currentPage + 1);
    }
  };

  const handlePageClick = (page: number) => {
    onPageChange(page);
  };

  // Generate page numbers to show
  const getVisiblePages = () => {
    const maxVisible = 7;
    const pages = [];
    
    if (totalPages <= maxVisible) {
      // Show all pages if total is small
      for (let i = 0; i < totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show first, last, current, and surrounding pages
      const start = Math.max(0, currentPage - 2);
      const end = Math.min(totalPages - 1, currentPage + 2);
      
      if (start > 0) {
        pages.push(0);
        if (start > 1) pages.push(-1); // Ellipsis
      }
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      if (end < totalPages - 1) {
        if (end < totalPages - 2) pages.push(-1); // Ellipsis
        pages.push(totalPages - 1);
      }
    }
    
    return pages;
  };

  if (totalPages <= 1) return null;

  return (
    <div className="flex items-center justify-center space-x-2 mt-8">
      <Button
        variant="outline"
        size="sm"
        onClick={handlePrevious}
        disabled={currentPage === 0}
        className="flex items-center space-x-1"
      >
        <ChevronLeft className="w-4 h-4" />
        <span>Previous</span>
      </Button>

      <div className="flex items-center space-x-1">
        {getVisiblePages().map((page, index) => (
          page === -1 ? (
            <span key={`ellipsis-${index}`} className="px-2 text-neutral-500">
              ...
            </span>
          ) : (
            <Button
              key={page}
              variant={page === currentPage ? "default" : "outline"}
              size="sm"
              onClick={() => handlePageClick(page)}
              className="w-8 h-8 p-0"
            >
              {page + 1}
            </Button>
          )
        ))}
      </div>

      <Button
        variant="outline"
        size="sm"
        onClick={handleNext}
        disabled={!pagination.hasMore}
        className="flex items-center space-x-1"
      >
        <span>Next</span>
        <ChevronRight className="w-4 h-4" />
      </Button>
    </div>
  );
}