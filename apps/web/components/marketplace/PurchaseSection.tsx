"use client";

import { useState } from "react";
import { ShoppingBag, Heart, Minus, Plus, CreditCard } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { cn } from "@repo/ui/lib/utils";
import { Id } from "@repo/backend/convex/_generated/dataModel";

interface Product {
  _id: Id<"products">;
  title: string;
  brand: string;
  price: number;
  category: string;
  condition: string;
}

interface PurchaseSectionProps {
  product: Product;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
  onPurchase: () => void;
  isFavorited: boolean;
  onFavorite: () => void;
}

export function PurchaseSection({
  product,
  quantity,
  onQuantityChange,
  onPurchase,
  isFavorited,
  onFavorite,
}: PurchaseSectionProps) {
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= 10) {
      onQuantityChange(newQuantity);
    }
  };

  const handleAddToCart = async () => {
    setIsAddingToCart(true);
    // TODO: Implement add to cart functionality
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsAddingToCart(false);
  };

  const totalPrice = product.price * quantity;
  const estimatedTax = totalPrice * 0.08; // 8% tax
  const shippingCost = totalPrice > 500 ? 0 : 25; // Free shipping over $500
  const finalTotal = totalPrice + estimatedTax + shippingCost;

  return (
    <div className="space-y-8">
      <div className="bg-card border-0 rounded-xl p-8 space-y-8 shadow-sm hover:shadow-md transition-shadow duration-300 sticky top-8">
        {/* Quantity Selector */}
        {/* <div className="space-y-4">
          <label className="text-lg font-medium text-foreground">
            Quantity
          </label>
          <div className="flex items-center justify-center space-x-4 bg-muted rounded-2xl p-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(quantity - 1)}
              disabled={quantity <= 1}
              className="w-12 h-12 p-0 rounded-full border-2 border-border hover:border-foreground transition-colors duration-300 disabled:opacity-50"
            >
              <Minus className="w-5 h-5" />
            </Button>
            <span className="w-16 text-center text-2xl font-light text-foreground">
              {quantity}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(quantity + 1)}
              disabled={quantity >= 10}
              className="w-12 h-12 p-0 rounded-full border-2 border-border hover:border-foreground transition-colors duration-300 disabled:opacity-50"
            >
              <Plus className="w-5 h-5" />
            </Button>
          </div>
        </div> */}

        {/* Price Breakdown */}
        <div className="space-y-3 pt-4 border-t border-border">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground font-light">
              Item price ({quantity}x)
            </span>
            <span className="font-medium text-foreground">
              ${totalPrice.toLocaleString()}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground font-light">
              Estimated tax
            </span>
            <span className="font-medium text-foreground">
              ${estimatedTax.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground font-light">
              Shipping
            </span>
            <span className="font-medium text-foreground">
              {shippingCost === 0 ? (
                <Badge variant="secondary" className="text-xs font-light">Free</Badge>
              ) : (
                `$${shippingCost}`
              )}
            </span>
          </div>
          
          <div className="flex justify-between text-lg font-medium pt-2 border-t border-border">
            <span className="text-foreground">Total</span>
            <span className="text-foreground">
              ${finalTotal.toFixed(2)}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            onClick={onPurchase}
            className="w-full h-12 text-base font-light bg-primary hover:bg-primary/90 transition-colors duration-300 rounded-xl"
          >
            <CreditCard className="w-5 h-5 mr-2" />
            Buy Now - ${finalTotal.toFixed(2)}
          </Button>
          
          <Button
            variant="outline"
            onClick={handleAddToCart}
            disabled={isAddingToCart}
            className="w-full h-12 text-base font-light rounded-xl transition-colors duration-300"
          >
            <ShoppingBag className="w-5 h-5 mr-2" />
            {isAddingToCart ? "Adding..." : "Add to Cart"}
          </Button>
          
          <Button
            variant="ghost"
            onClick={onFavorite}
            className="w-full h-12 text-base font-light rounded-xl transition-colors duration-300"
          >
            <Heart
              className={cn(
                "w-5 h-5 mr-2 transition-colors",
                isFavorited ? "fill-red-500 text-red-500" : "text-muted-foreground"
              )}
            />
            {isFavorited ? "Remove from Wishlist" : "Add to Wishlist"}
          </Button>
        </div>

        {/* Security Notice */}
        <div className="bg-muted rounded-xl p-4 text-center">
          <p className="text-xs text-muted-foreground font-light">
            🔒 Secure checkout with 256-bit SSL encryption
          </p>
        </div>
      </div>
    </div>
  );
}