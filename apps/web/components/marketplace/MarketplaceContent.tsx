"use client";

import { useState, useMemo, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { FilterSidebar } from "./FilterSidebar";
import { ProductGrid } from "./ProductGrid";
import { MarketplaceHeader } from "./MarketplaceHeader";
import { BrowseHeroSection } from "./BrowseHeroSection";

export interface FilterState {
  categories: string[];
  priceRange: [number | null, number | null];
  brands: string[];
  conditions: string[];
  searchQuery: string;
}

const initialFilters: FilterState = {
  categories: [],
  priceRange: [null, null],
  brands: [],
  conditions: [],
  searchQuery: "",
};

export function MarketplaceContent() {
  const [filters, setFilters] = useState<FilterState>(initialFilters);
  const [sortBy, setSortBy] = useState<"newest" | "price_low" | "price_high" | "popular">("newest");

  const [currentPage, setCurrentPage] = useState(0);
  const [allProducts, setAllProducts] = useState<any[]>([]);
  const ITEMS_PER_PAGE = 24;

  // Fetch products with filters and pagination
  const productsResult = useQuery(api.productQueries.getMarketplaceProducts, {
    category: filters.categories.length === 1 ? filters.categories[0] as any : undefined,
    minPrice: filters.priceRange[0] !== null ? filters.priceRange[0] : undefined,
    maxPrice: filters.priceRange[1] !== null ? filters.priceRange[1] : undefined,
    brands: filters.brands.length > 0 ? filters.brands : undefined,
    conditions: filters.conditions.length > 0 ? filters.conditions : undefined,
    searchQuery: filters.searchQuery || undefined,
    sortBy,
    limit: ITEMS_PER_PAGE,
    offset: currentPage * ITEMS_PER_PAGE,
  });

  // Get filter options
  const filterOptions = useQuery(api.productQueries.getAggregatedFilterOptions);

  // Update allProducts when new results come in
  useEffect(() => {
    if (productsResult?.products) {
      if (currentPage === 0) {
        // Reset products when filters change
        setAllProducts(productsResult.products);
      } else {
        // Append new products for infinite scroll
        setAllProducts(prev => [...prev, ...productsResult.products]);
      }
    }
  }, [productsResult?.products, currentPage]);

  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(0); // Reset to first page when filters change
    setAllProducts([]); // Clear existing products
  };

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }));
    setCurrentPage(0); // Reset to first page when search changes
    setAllProducts([]); // Clear existing products
  };

  const clearFilters = () => {
    setFilters(initialFilters);
    setCurrentPage(0); // Reset to first page when clearing filters
    setAllProducts([]); // Clear existing products
  };

  const handleSortChange = (newSort: "newest" | "price_low" | "price_high" | "popular") => {
    setSortBy(newSort);
    setCurrentPage(0); // Reset to first page when sort changes
    setAllProducts([]); // Clear existing products
  };

  const handleLoadMore = () => {
    if (productsResult?.pagination?.hasMore) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const activeFilterCount = useMemo(() => {
    return (
      filters.categories.length +
      filters.brands.length +
      filters.conditions.length +
      (filters.searchQuery ? 1 : 0) +
      (filters.priceRange[0] !== null || filters.priceRange[1] !== null ? 1 : 0)
    );
  }, [filters]);

  return (  
    <div className="bg-card min-h-screen">
      <MarketplaceHeader onSearch={handleSearch} />
      <div className="">
        {/* Browse Hero Section */}
        <BrowseHeroSection
          filters={filters}
          productCount={productsResult?.pagination?.total || allProducts.length}
          featuredProducts={allProducts.slice(0, 3)}
        />
        
        <div className="flex gap-6 container mx-auto px-6 py-6">
          {/* Filter Sidebar - Desktop */}
          <div className="hidden md:block w-72 flex-shrink-0">
            <FilterSidebar
              filters={filters}
              filterOptions={filterOptions}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
              activeFilterCount={activeFilterCount}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Mobile Filter Button */}
            <div className="md:hidden mb-6">
              <FilterSidebar
                filters={filters}
                filterOptions={filterOptions}
                onFilterChange={handleFilterChange}
                onClearFilters={clearFilters}
                activeFilterCount={activeFilterCount}
              />
            </div>
            
            <ProductGrid
              products={allProducts}
              sortBy={sortBy}
              onSortChange={handleSortChange}
              isLoading={productsResult === undefined}
              activeFilterCount={activeFilterCount}
              pagination={productsResult?.pagination}
              onPageChange={handleLoadMore}
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
}