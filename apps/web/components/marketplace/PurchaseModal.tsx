"use client";

import { useState } from "react";
import { CreditCard, Lock, Truck, ArrowRight } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { Badge } from "@repo/ui/components/badge";

interface Product {
  _id: string;
  title: string;
  brand: string;
  price: number;
  images: string[];
}

interface PurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
  quantity: number;
}

export function PurchaseModal({ isOpen, onClose, product, quantity }: PurchaseModalProps) {
  const [step, setStep] = useState<'review' | 'payment' | 'success'>('review');
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'paypal'>('card');

  const subtotal = product.price * quantity;
  const tax = subtotal * 0.08;
  const shipping = subtotal > 500 ? 0 : 25;
  const total = subtotal + tax + shipping;

  const handleProceedToPayment = () => {
    setStep('payment');
  };

  const handleProcessPayment = async () => {
    setIsProcessing(true);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    setIsProcessing(false);
    setStep('success');
  };

  const handleClose = () => {
    setStep('review');
    setIsProcessing(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {step === 'review' && 'Review Your Order'}
            {step === 'payment' && 'Payment Details'}
            {step === 'success' && 'Order Confirmed!'}
          </DialogTitle>
        </DialogHeader>

        {step === 'review' && (
          <div className="space-y-6">
            {/* Product Summary */}
            <div className="flex space-x-4 p-4 bg-neutral-50 dark:bg-neutral-900 rounded-lg">
              <div className="w-16 h-16 bg-neutral-200 dark:bg-neutral-800 rounded-lg flex-shrink-0">
                {product.images[0] && (
                  <img
                    src={product.images[0]}
                    alt={product.title}
                    className="w-full h-full object-cover rounded-lg"
                  />
                )}
              </div>
              <div className="flex-1 space-y-1">
                <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase">
                  {product.brand}
                </p>
                <h4 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2">
                  {product.title}
                </h4>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">
                    Qty: {quantity}
                  </span>
                  <span className="font-semibold text-neutral-900 dark:text-neutral-100">
                    ${product.price.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-neutral-600 dark:text-neutral-400">Subtotal</span>
                <span className="text-neutral-900 dark:text-neutral-100">${subtotal.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-neutral-600 dark:text-neutral-400">Tax</span>
                <span className="text-neutral-900 dark:text-neutral-100">${tax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-neutral-600 dark:text-neutral-400">Shipping</span>
                <span className="text-neutral-900 dark:text-neutral-100">
                  {shipping === 0 ? 'Free' : `$${shipping}`}
                </span>
              </div>
              <Separator />
              <div className="flex justify-between text-lg font-semibold">
                <span className="text-neutral-900 dark:text-neutral-100">Total</span>
                <span className="text-neutral-900 dark:text-neutral-100">${total.toFixed(2)}</span>
              </div>
            </div>

            {/* Delivery Info */}
            <div className="bg-blue-50 dark:bg-blue-950 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Truck className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Estimated Delivery
                </span>
              </div>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                {new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
                  weekday: 'long',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            </div>

            <Button onClick={handleProceedToPayment} className="w-full">
              Proceed to Payment
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}

        {step === 'payment' && (
          <div className="space-y-6">
            {/* Payment Method Selection */}
            <div className="space-y-3">
              <Label>Payment Method</Label>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant={paymentMethod === 'card' ? 'default' : 'outline'}
                  onClick={() => setPaymentMethod('card')}
                  className="h-12"
                >
                  <CreditCard className="w-4 h-4 mr-2" />
                  Card
                </Button>
                <Button
                  variant={paymentMethod === 'paypal' ? 'default' : 'outline'}
                  onClick={() => setPaymentMethod('paypal')}
                  className="h-12"
                >
                  PayPal
                </Button>
              </div>
            </div>

            {paymentMethod === 'card' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="cardNumber">Card Number</Label>
                  <Input
                    id="cardNumber"
                    placeholder="1234 5678 9012 3456"
                    className="font-mono"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expiry">Expiry Date</Label>
                    <Input
                      id="expiry"
                      placeholder="MM/YY"
                      className="font-mono"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cvc">CVC</Label>
                    <Input
                      id="cvc"
                      placeholder="123"
                      className="font-mono"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name">Cardholder Name</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                  />
                </div>
              </div>
            )}

            {/* Security Notice */}
            <div className="bg-green-50 dark:bg-green-950 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <Lock className="w-4 h-4 text-green-600 dark:text-green-400" />
                <span className="text-sm font-medium text-green-900 dark:text-green-100">
                  Secure Payment
                </span>
              </div>
              <p className="text-xs text-green-800 dark:text-green-200 mt-1">
                Your payment information is encrypted and secure
              </p>
            </div>

            {/* Order Total */}
            <div className="bg-neutral-50 dark:bg-neutral-900 rounded-lg p-4">
              <div className="flex justify-between items-center">
                <span className="font-medium text-neutral-900 dark:text-neutral-100">
                  Total
                </span>
                <span className="text-xl font-bold text-neutral-900 dark:text-neutral-100">
                  ${total.toFixed(2)}
                </span>
              </div>
            </div>

            <Button
              onClick={handleProcessPayment}
              disabled={isProcessing}
              className="w-full"
            >
              {isProcessing ? 'Processing...' : `Pay $${total.toFixed(2)}`}
            </Button>
          </div>
        )}

        {step === 'success' && (
          <div className="space-y-6 text-center">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>

            <div className="space-y-2">
              <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
                Order Confirmed!
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400">
                Your order has been successfully placed and will be shipped soon.
              </p>
            </div>

            <div className="bg-neutral-50 dark:bg-neutral-900 rounded-lg p-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-neutral-600 dark:text-neutral-400">Order Number</span>
                  <span className="font-mono text-neutral-900 dark:text-neutral-100">
                    HV-{Date.now().toString().slice(-8)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-neutral-600 dark:text-neutral-400">Estimated Delivery</span>
                  <span className="text-neutral-900 dark:text-neutral-100">
                    {new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Button onClick={handleClose} className="w-full">
                Continue Shopping
              </Button>
              <Button variant="outline" className="w-full">
                View Order Details
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}