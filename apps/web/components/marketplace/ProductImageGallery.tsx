"use client";

import { useState } from "react";
import { ChevronLeft, ChevronRight, ZoomIn } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { cn } from "@repo/ui/lib/utils";
import Image from "next/image";

interface ProductImageGalleryProps {
  images: string[];
  title: string;
}

export function ProductImageGallery({ images, title }: ProductImageGalleryProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? images.length - 1 : prev - 1
    );
  };

  const selectImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  if (!images || images.length === 0) {
    return (
      <div className="aspect-square bg-neutral-100 dark:bg-neutral-800 rounded-xl flex items-center justify-center">
        <div className="text-center space-y-2">
          <div className="w-16 h-16 bg-neutral-200 dark:bg-neutral-700 rounded-lg mx-auto flex items-center justify-center">
            <Image 
              src="/placeholder-image.svg" 
              alt="No image" 
              width={32} 
              height={32}
              className="text-neutral-400"
            />
          </div>
          <p className="text-sm text-neutral-500 dark:text-neutral-400">No image available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Image */}
      <div className="relative aspect-square bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-800 dark:to-neutral-900 rounded-3xl overflow-hidden group shadow-2xl ring-1 ring-neutral-200/50 dark:ring-neutral-800/50">
        <Image
          src={images[currentImageIndex] || ""}
          alt={`${title} - Image ${currentImageIndex + 1}`}
          fill
          className={cn(
            "object-cover transition-all duration-500 ease-out",
            isZoomed ? "scale-150 cursor-zoom-out" : "cursor-zoom-in hover:scale-105"
          )}
          sizes="(max-width: 768px) 100vw, 50vw"
          priority
          onClick={() => setIsZoomed(!isZoomed)}
        />

        {/* Navigation Arrows */}
        {images.length > 1 && (
          <>
            <Button
              variant="secondary"
              size="sm"
              onClick={prevImage}
              className="absolute left-6 top-1/2 -translate-y-1/2 w-12 h-12 p-0 bg-white/95 dark:bg-black/95 hover:bg-white dark:hover:bg-black opacity-0 group-hover:opacity-100 transition-all duration-300 shadow-xl backdrop-blur-sm rounded-full border border-neutral-200/50 dark:border-neutral-800/50"
            >
              <ChevronLeft className="w-6 h-6" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={nextImage}
              className="absolute right-6 top-1/2 -translate-y-1/2 w-12 h-12 p-0 bg-white/95 dark:bg-black/95 hover:bg-white dark:hover:bg-black opacity-0 group-hover:opacity-100 transition-all duration-300 shadow-xl backdrop-blur-sm rounded-full border border-neutral-200/50 dark:border-neutral-800/50"
            >
              <ChevronRight className="w-6 h-6" />
            </Button>
          </>
        )}

        {/* Zoom Indicator */}
        <div className="absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-all duration-300">
          <div className="bg-white/95 dark:bg-black/95 rounded-xl px-3 py-2 flex items-center space-x-2 shadow-lg backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50">
            <ZoomIn className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
            <span className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
              Click to zoom
            </span>
          </div>
        </div>

        {/* Image Counter */}
        {images.length > 1 && (
          <div className="absolute bottom-6 left-6 bg-black/80 text-white rounded-xl px-4 py-2 backdrop-blur-sm">
            <span className="text-sm font-bold">
              {currentImageIndex + 1} / {images.length}
            </span>
          </div>
        )}
      </div>

      {/* Thumbnail Grid */}
      {images.length > 1 && (
        <div className="grid grid-cols-4 gap-4">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => selectImage(index)}
              className={cn(
                "relative aspect-square rounded-2xl overflow-hidden border-2 transition-all duration-300 hover:scale-105",
                index === currentImageIndex
                  ? "border-black dark:border-white ring-4 ring-black/20 dark:ring-white/20 shadow-xl"
                  : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500 shadow-lg hover:shadow-xl"
              )}
            >
              <Image
                src={image}
                alt={`${title} - Thumbnail ${index + 1}`}
                fill
                className="object-cover transition-transform duration-300 hover:scale-110"
                sizes="(max-width: 768px) 25vw, 12vw"
              />
              {index === currentImageIndex && (
                <div className="absolute inset-0 bg-black/10 dark:bg-white/10" />
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}