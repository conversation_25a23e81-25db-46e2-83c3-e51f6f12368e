"use client";

import { Badge } from "@repo/ui/components/badge";

interface Product {
  _id: string;
  title: string;
  brand: string;
  price: number;
  category: string;
  condition: string;
  _creationTime: number;
}

interface ProductSpecsProps {
  product: Product;
}

const CATEGORY_LABELS = {
  clothing: "Clothing",
  sneakers: "Sneakers",
  collectibles: "Collectibles", 
  accessories: "Accessories",
  handbags: "Handbags",
  watches: "Watches",
  jewelry: "Jewelry",
};

const CONDITION_DESCRIPTIONS = {
  new: "Brand new item with original packaging and tags",
  like_new: "Excellent condition with minimal signs of wear",
  good: "Good condition with some signs of normal wear",
  fair: "Fair condition with noticeable wear but still functional",
};

export function ProductSpecs({ product }: ProductSpecsProps) {
  const listedDate = new Date(product._creationTime).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const specs = [
    {
      label: "Brand",
      value: product.brand,
    },
    {
      label: "Category", 
      value: CATEGORY_LABELS[product.category as keyof typeof CATEGORY_LABELS] || product.category,
    },
    {
      label: "Condition",
      value: (
        <div className="space-y-1">
          <Badge variant="outline" className="capitalize">
            {product.condition.replace('_', ' ')}
          </Badge>
          <p className="text-xs text-neutral-500 dark:text-neutral-400">
            {CONDITION_DESCRIPTIONS[product.condition as keyof typeof CONDITION_DESCRIPTIONS]}
          </p>
        </div>
      ),
    },
    {
      label: "Listed Date",
      value: listedDate,
    },
    {
      label: "Item ID",
      value: product._id.slice(-8).toUpperCase(),
    },
  ];

  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
        Product Details
      </h3>
      
      <div className="space-y-4">
        {specs.map((spec, index) => (
          <div key={index} className="flex justify-between items-start py-2">
            <span className="text-sm font-medium text-neutral-500 dark:text-neutral-400 min-w-0 flex-shrink-0 w-24">
              {spec.label}
            </span>
            <div className="text-sm text-neutral-900 dark:text-neutral-100 text-right flex-1 min-w-0">
              {typeof spec.value === 'string' ? spec.value : spec.value}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}