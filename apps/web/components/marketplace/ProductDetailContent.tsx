"use client";

import { useState } from "react";
import { ArrowLeft, Heart, Share2, Shield, Truck, RotateCcw } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { ProductImageGallery } from "./ProductImageGallery";
import { SellerInfo } from "./SellerInfo";
import { PurchaseSection } from "./PurchaseSection";
import { ProductSpecs } from "./ProductSpecs";
import { ShareModal } from "./ShareModal";
import { PurchaseModal } from "./PurchaseModal";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useFavorite } from "@/hooks/useFavorites";
import { Id } from "@repo/backend/convex/_generated/dataModel";

interface Product {
  _id: Id<"products">;
  title: string;
  brand: string;
  price: number;
  images: string[];
  condition: string;
  category: string;
  description: string;
  sellerId: string;
  status: string;
  views?: number;
  seller?: {
    _id: string;
    name: string;
    userType: string;
  };
}

interface ProductDetailContentProps {
  product: Product;
}

const CONDITION_LABELS = {
  new: "New",
  like_new: "Like New", 
  good: "Good",
  fair: "Fair",
};

const CONDITION_COLORS = {
  new: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  like_new: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  good: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  fair: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
};

export function ProductDetailContent({ product }: ProductDetailContentProps) {
  const router = useRouter();
  const [showShareModal, setShowShareModal] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [quantity, setQuantity] = useState(1);

  const { isFavorited, toggleFavorite } = useFavorite(product._id);

  const estimatedDelivery = new Date();
  estimatedDelivery.setDate(estimatedDelivery.getDate() + 3);

  const handleFavorite = async () => {
    await toggleFavorite();
  };

  const handlePurchase = () => {
    setShowPurchaseModal(true);
  };

  const handleShare = () => {
    setShowShareModal(true);
  };

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900">
        <div className="container mx-auto px-6 py-12">
          {/* Breadcrumb */}
          <div className="flex items-center space-x-3 text-sm text-neutral-500 dark:text-neutral-400 mb-8">
            <Link
              href="/marketplace"
              className="hover:text-black dark:hover:text-white transition-colors font-medium"
            >
              Marketplace
            </Link>
            <span className="text-neutral-300 dark:text-neutral-600">/</span>
            <span className="capitalize hover:text-black dark:hover:text-white transition-colors cursor-pointer">
              {product.category}
            </span>
            <span className="text-neutral-300 dark:text-neutral-600">/</span>
            <span className="text-black dark:text-white font-semibold">
              {product.title}
            </span>
          </div>

          {/* Back Button */}
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-8 -ml-2 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-xl px-4 py-2"
          >
            <ArrowLeft className="w-5 h-5 mr-3" />
            <span className="font-medium">Back to marketplace</span>
          </Button>

          <div className="grid grid-cols-1 xl:grid-cols-5 gap-16">
            {/* Image Gallery */}
            <div className="xl:col-span-3 space-y-8">
              <ProductImageGallery images={product.images} title={product.title} />

              {/* Trust Badges */}
              <div className="grid grid-cols-3 gap-6 pt-8 border-t border-neutral-200/50 dark:border-neutral-800/50">
                <div className="flex flex-col items-center text-center space-y-3 p-4 bg-white dark:bg-neutral-900 rounded-2xl border border-neutral-200/50 dark:border-neutral-800/50 shadow-sm">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800 rounded-2xl flex items-center justify-center">
                    <Shield className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm font-bold text-black dark:text-white">
                      Authenticity
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      Guaranteed
                    </p>
                  </div>
                </div>
              
                <div className="flex flex-col items-center text-center space-y-3 p-4 bg-white dark:bg-neutral-900 rounded-2xl border border-neutral-200/50 dark:border-neutral-800/50 shadow-sm">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 rounded-2xl flex items-center justify-center">
                    <Truck className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm font-bold text-black dark:text-white">
                      Fast Shipping
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      2-3 days
                    </p>
                  </div>
                </div>

                <div className="flex flex-col items-center text-center space-y-3 p-4 bg-white dark:bg-neutral-900 rounded-2xl border border-neutral-200/50 dark:border-neutral-800/50 shadow-sm">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900 dark:to-purple-800 rounded-2xl flex items-center justify-center">
                    <RotateCcw className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm font-bold text-black dark:text-white">
                      Easy Returns
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      7 days
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Product Information */}
            <div className="xl:col-span-2 space-y-10">
              <div className="bg-white dark:bg-neutral-900 rounded-3xl p-8 border border-neutral-200/50 dark:border-neutral-800/50 shadow-xl">
                {/* Header */}
                <div className="space-y-6">
                  <div className="flex items-start justify-between">
                    <div className="space-y-3">
                      <p className="text-sm font-bold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                        {product.brand}
                      </p>
                      <h1 className="text-4xl font-bold text-black dark:text-white leading-tight">
                        {product.title}
                      </h1>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleFavorite}
                        className="w-10 h-10 p-0"
                      >
                        <Heart
                          className={`w-5 h-5 transition-colors ${
                            isFavorited 
                              ? "fill-red-500 text-red-500" 
                              : "text-neutral-400 hover:text-red-500"
                          }`}
                        />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleShare}
                        className="w-10 h-10 p-0"
                      >
                        <Share2 className="w-5 h-5 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300" />
                      </Button>
                    </div>
                  </div>

                  {/* Price and Condition */}
                  <div className="flex items-center space-x-4">
                    <p className="text-4xl font-bold text-neutral-900 dark:text-neutral-100">
                      ${product.price.toLocaleString()}
                    </p>
                    <Badge
                      className={`${CONDITION_COLORS[product.condition as keyof typeof CONDITION_COLORS]} font-medium`}
                    >
                      {CONDITION_LABELS[product.condition as keyof typeof CONDITION_LABELS]}
                    </Badge>
                  </div>

                  {/* Delivery Info */}
                  <div className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400">
                    <Truck className="w-4 h-4" />
                    <span>
                      Estimated delivery: {estimatedDelivery.toLocaleDateString('en-US', {
                        weekday: 'long',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                </div>

                <Separator />

                {/* Description */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                    Description
                  </h3>
                  <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed">
                    {product.description}
                  </p>
                </div>

                <Separator />

                {/* Product Specifications */}
                <ProductSpecs product={product as any} />

                <Separator />

                {/* Seller Information */}
                {product.seller && (
                  <>
                    <SellerInfo seller={product.seller} />
                    <Separator />
                  </>
                )}

                {/* Purchase Section */}
                <PurchaseSection
                  product={product}
                  quantity={quantity}
                  onQuantityChange={setQuantity}
                  onPurchase={handlePurchase}
                  isFavorited={isFavorited}
                  onFavorite={handleFavorite}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        product={product}
      />
      
      <PurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        product={product}
        quantity={quantity}
      />
    </>
  );
}