{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "./tailwind.config.ts", "css": "../../packages/ui/src/styles/globals.css", "baseColor": "neutral", "cssVariables": true}, "iconLibrary": "lucide", "aliases": {"components": "@/components", "hooks": "@/hooks", "lib": "@/lib", "utils": "@repo/ui/lib/utils", "ui": "@repo/ui/components"}}