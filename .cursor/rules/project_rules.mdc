---
alwaysApply: true
---

# HauteVault Cursor Rules

- Never use demo or mock data

# Design
- Always use our @packages/ui components that are ShadCN based

## Project Overview
HauteVault is a dual-sided marketplace for luxury goods that acts both as an e-commerce platform for consumers and a one-stop-shop inventory management solution for sellers. The platform operates on a $20/month subscription model, gatekeeping all inventory and purchase opportunities from non-subscribers.

## Tech Stack & Architecture
- **Frontend**: React 18+ with TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand or React Query
- **Authentication**: NextAuth.js with Google/Apple OAuth
- **Payment Processing**: Stripe for subscriptions and purchases
- **Database**: PostgreSQL with Prisma ORM
- **File Storage**: AWS S3 for product images
- **Framework**: Next.js 14+ (App Router)

## Color Palette & Design References
```css
:root {
  --primary-dark: #302923;     /* Rich brown */
  --primary-light: #F3F3EB;    /* Cream white */
  --secondary-sage: #979487;   /* <PERSON> gray */
  --accent-mauve: #856B63;     /* Dusty rose */
  --tertiary-tan: #998664;     /* Warm tan */
  --accent-green: #748470;     /* Muted sage green */
}
```

**Design Inspiration:**
- **Landing Page Theme**: Copy aesthetic from www.thehautevault.com (ignore content, focus on theme/colors)
- **Marketplace Style**: Emulate the style/vibe of https://shop.getbezel.com/ for the subscriber marketplace experience

## Design System Guidelines

### Typography
- **Primary Font**: Inter or similar modern sans-serif
- **Secondary Font**: Playfair Display for luxury headings
- **Hierarchy**: 
  - H1: 2.5rem, font-weight: 300 (light luxury feel)
  - H2: 2rem, font-weight: 400
  - Body: 1rem, font-weight: 400
  - Caption: 0.875rem, font-weight: 500

### Component Patterns
- **Buttons**: Rounded corners (6px), subtle shadows, hover states
- **Cards**: Clean borders, 12px radius, subtle elevation
- **Inputs**: Minimal borders, focus states with primary color
- **Navigation**: Clean, minimal, sticky header
- **Modals**: Backdrop blur, centered, smooth animations

## User Flow Architecture

### Landing Page (Non-Subscribers)
```typescript
// Landing page must show:
- Hero section with value proposition
- Blurred product samples with padlock overlays (no live listings)
- Subscription CTA ($20/month)
- "Apply as Seller" button (top right corner)
- Sample categories without pricing or purchase ability
- NO actual purchasable listings visible
```

### Authentication Flow
```typescript
// Auth states to handle:
- Non-authenticated users (landing only, no marketplace access)
- Authenticated non-subscribers (upgrade prompts, no inventory access)
- Active subscribers (full marketplace access with all features)
- Sellers (additional backend inventory management access)
```

### Marketplace (Subscribers Only)
```typescript
// Main marketplace features for subscribers:
- Product grid with filters (category, price, seller)
- Product detail modals with seller messaging
- Shopping cart with checkout flow
- User profile with purchase history
- Direct seller-to-consumer pricing (no HV fees/markups)
```

## Database Schema Considerations

### Core Models
```typescript
// User roles and permissions
enum UserRole {
  CUSTOMER = "CUSTOMER"
  SELLER = "SELLER"
  ADMIN = "ADMIN"
}

// Product categories
enum ProductCategory {
  CLOTHING = "CLOTHING"
  SNEAKERS = "SNEAKERS"
  ACCESSORIES = "ACCESSORIES"
  HANDBAGS = "HANDBAGS"
  COLLECTIBLES = "COLLECTIBLES"
}

// Key models to implement:
- User (with subscription status)
- Seller (with application status and approval workflow)
- Product (with seller info, pricing, marketplace visibility)
- Order (with payment tracking)
- Subscription (with Stripe integration)
- SellerInventory (private seller data, not visible to consumers)
- Invoice (for offline sales)
```

### Seller Application Fields
```typescript
// Required seller application information:
- Name
- Contact information
- Company name
- Company website
- Product specialty
- Estimated number of products in inventory
- Application status (pending, approved, rejected)
```

### Inventory Management (Private Seller Data)
```typescript
// Private fields visible only to sellers (NOT consumers):
- Source company/individual name
- Cost paid for item
- Payment method
- Payment date
- Internal notes
- Profit margins
```

## Feature Implementation Priority

### Phase 1 - Core Platform
1. Landing page with subscription gate (no live listings visible)
2. User authentication (Google/Apple OAuth)
3. Stripe subscription integration ($20/month)
4. Sample product display (blurred with padlocks for non-subscribers)
5. Seller application form with all required fields

### Phase 2 - Marketplace
1. Product listing and filtering (subscribers only)
2. Shopping cart and checkout (direct seller pricing)
3. User profiles and purchase history
4. Seller messaging system
5. Seller inventory management backend

### Phase 3 - Advanced Features
1. **Offline Sales & Invoicing System:**
   - "Sell Offline" button for each listing
   - Client information collection (name, address, phone, email)
   - Sale price and payment method input
   - PDF invoice generation and download
   - Invoice tracking in seller dashboard
2. **Seller Reporting Dashboard:**
   - Sales reporting (daily, weekly, monthly, yearly)
   - Active listing total value
   - Top customers by purchase amount
   - Inventory performance metrics
3. Future: Price comparison across platforms

## Component Structure

### Layout Components
```
components/
├── layout/
│   ├── Header.tsx (with auth state and seller application button)
│   ├── Footer.tsx
│   └── SubscriptionGate.tsx
├── ui/
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Modal.tsx
│   └── Input.tsx
└── features/
    ├── auth/
    ├── products/
    ├── sellers/
    ├── subscriptions/
    ├── invoicing/
    └── reporting/
```

## Styling Guidelines

### Luxury Aesthetic
- Generous whitespace and padding
- Subtle animations and transitions
- High-quality image optimization
- Clean, minimal interfaces
- Premium typography treatment
- **Reference thehautevault.com for landing page theme**

### Marketplace Design
- **Emulate getbezel.com style for subscriber marketplace**
- Clean product grids
- Professional seller interfaces
- Intuitive inventory management
- Modern dashboard aesthetics

### Responsive Design
- Mobile-first approach
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Touch-friendly interface elements
- Optimized product grids for all devices

### Accessibility
- WCAG 2.1 AA compliance
- Proper color contrast ratios
- Keyboard navigation support
- Screen reader optimization
- Focus indicators

## Security & Performance

### Security Measures
- Role-based access control
- Subscription status verification (strict gatekeeping)
- Input validation and sanitization
- Rate limiting on API endpoints
- Secure file upload handling
- Seller approval workflow

### Performance Optimization
- Image optimization and lazy loading
- Code splitting by route
- Database query optimization
- CDN for static assets
- Caching strategies for product data

## Development Conventions

### File Naming
- Components: PascalCase (ProductCard.tsx)
- Pages: kebab-case (product-details.tsx)
- Utilities: camelCase (formatPrice.ts)
- Types: PascalCase with .types.ts suffix

### Code Organization
- Feature-based folder structure
- Shared components in /components/ui
- Business logic in custom hooks
- Type definitions co-located with features
- Consistent import order (external, internal, relative)

### State Management
- Server state with React Query
- Client state with Zustand
- Form state with React Hook Form
- Authentication state with context/provider pattern

## API Design Patterns

### RESTful Endpoints
```
/api/auth/*          - Authentication routes
/api/products/*      - Product CRUD operations
/api/sellers/*       - Seller management and applications
/api/subscriptions/* - Stripe integration
/api/orders/*        - Order processing
/api/invoices/*      - Offline sales and invoicing
/api/reports/*       - Seller analytics and reporting
```

### Error Handling
- Consistent error response format
- User-friendly error messages
- Logging for debugging
- Graceful fallbacks for failed requests

## Testing Strategy
- Unit tests for utilities and hooks
- Integration tests for API routes
- Component tests with React Testing Library
- E2E tests for critical user flows
- Subscription flow testing with Stripe test mode
- Seller application workflow testing
- Invoice generation testing
- Unit tests for utilities and hooks
- Integration tests for API routes
- Component tests with React Testing Library
- E2E tests for critical user flows
- Subscription flow testing with Stripe test mode# HauteVault Cursor Rules

## Project Overview
HauteVault is a dual-sided marketplace for luxury goods that acts both as an e-commerce platform for consumers and a one-stop-shop inventory management solution for sellers. The platform operates on a $20/month subscription model, gatekeeping all inventory and purchase opportunities from non-subscribers.

## Tech Stack & Architecture
- **Frontend**: React 18+ with TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand or React Query
- **Authentication**: NextAuth.js with Google/Apple OAuth
- **Payment Processing**: Stripe for subscriptions and purchases
- **Database**: PostgreSQL with Prisma ORM
- **File Storage**: AWS S3 for product images
- **Framework**: Next.js 14+ (App Router)

## Color Palette & Design References
```css
:root {
  --primary-dark: #302923;     /* Rich brown */
  --primary-light: #F3F3EB;    /* Cream white */
  --secondary-sage: #979487;   /* Sage gray */
  --accent-mauve: #856B63;     /* Dusty rose */
  --tertiary-tan: #998664;     /* Warm tan */
  --accent-green: #748470;     /* Muted sage green */
}
```

**Design Inspiration:**
- **Landing Page Theme**: Copy aesthetic from www.thehautevault.com (ignore content, focus on theme/colors)
- **Marketplace Style**: Emulate the style/vibe of https://shop.getbezel.com/ for the subscriber marketplace experience

## Design System Guidelines

### Typography
- **Primary Font**: Inter or similar modern sans-serif
- **Secondary Font**: Playfair Display for luxury headings
- **Hierarchy**: 
  - H1: 2.5rem, font-weight: 300 (light luxury feel)
  - H2: 2rem, font-weight: 400
  - Body: 1rem, font-weight: 400
  - Caption: 0.875rem, font-weight: 500

### Component Patterns
- **Buttons**: Rounded corners (6px), subtle shadows, hover states
- **Cards**: Clean borders, 12px radius, subtle elevation
- **Inputs**: Minimal borders, focus states with primary color
- **Navigation**: Clean, minimal, sticky header
- **Modals**: Backdrop blur, centered, smooth animations

## User Flow Architecture

### Landing Page (Non-Subscribers)
```typescript
// Landing page must show:
- Hero section with value proposition
- Blurred product samples with padlock overlays (no live listings)
- Subscription CTA ($20/month)
- "Apply as Seller" button (top right corner)
- Sample categories without pricing or purchase ability
- NO actual purchasable listings visible
```

### Authentication Flow
```typescript
// Auth states to handle:
- Non-authenticated users (landing only, no marketplace access)
- Authenticated non-subscribers (upgrade prompts, no inventory access)
- Active subscribers (full marketplace access with all features)
- Sellers (additional backend inventory management access)
```

### Marketplace (Subscribers Only)
```typescript
// Main marketplace features for subscribers:
- Product grid with filters (category, price, seller)
- Product detail modals with seller messaging
- Shopping cart with checkout flow
- User profile with purchase history
- Direct seller-to-consumer pricing (no HV fees/markups)
```

## Database Schema Considerations

### Core Models
```typescript
// User roles and permissions
enum UserRole {
  CUSTOMER = "CUSTOMER"
  SELLER = "SELLER"
  ADMIN = "ADMIN"
}

// Product categories
enum ProductCategory {
  CLOTHING = "CLOTHING"
  SNEAKERS = "SNEAKERS"
  ACCESSORIES = "ACCESSORIES"
  HANDBAGS = "HANDBAGS"
  COLLECTIBLES = "COLLECTIBLES"
}

// Key models to implement:
- User (with subscription status)
- Seller (with application status and approval workflow)
- Product (with seller info, pricing, marketplace visibility)
- Order (with payment tracking)
- Subscription (with Stripe integration)
- SellerInventory (private seller data, not visible to consumers)
- Invoice (for offline sales)
```

### Seller Application Fields
```typescript
// Required seller application information:
- Name
- Contact information
- Company name
- Company website
- Product specialty
- Estimated number of products in inventory
- Application status (pending, approved, rejected)
```

### Inventory Management (Private Seller Data)
```typescript
// Private fields visible only to sellers (NOT consumers):
- Source company/individual name
- Cost paid for item
- Payment method
- Payment date
- Internal notes
- Profit margins
```

## Feature Implementation Priority

### Phase 1 - Core Platform
1. Landing page with subscription gate (no live listings visible)
2. User authentication (Google/Apple OAuth)
3. Stripe subscription integration ($20/month)
4. Sample product display (blurred with padlocks for non-subscribers)
5. Seller application form with all required fields

### Phase 2 - Marketplace
1. Product listing and filtering (subscribers only)
2. Shopping cart and checkout (direct seller pricing)
3. User profiles and purchase history
4. Seller messaging system
5. Seller inventory management backend

### Phase 3 - Advanced Features
1. **Offline Sales & Invoicing System:**
   - "Sell Offline" button for each listing
   - Client information collection (name, address, phone, email)
   - Sale price and payment method input
   - PDF invoice generation and download
   - Invoice tracking in seller dashboard
2. **Seller Reporting Dashboard:**
   - Sales reporting (daily, weekly, monthly, yearly)
   - Active listing total value
   - Top customers by purchase amount
   - Inventory performance metrics
3. Future: Price comparison across platforms

## Component Structure

### Layout Components
```
components/
├── layout/
│   ├── Header.tsx (with auth state and seller application button)
│   ├── Footer.tsx
│   └── SubscriptionGate.tsx
├── ui/
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Modal.tsx
│   └── Input.tsx
└── features/
    ├── auth/
    ├── products/
    ├── sellers/
    ├── subscriptions/
    ├── invoicing/
    └── reporting/
```

## Styling Guidelines

### Luxury Aesthetic
- Generous whitespace and padding
- Subtle animations and transitions
- High-quality image optimization
- Clean, minimal interfaces
- Premium typography treatment
- **Reference thehautevault.com for landing page theme**

### Marketplace Design
- **Emulate getbezel.com style for subscriber marketplace**
- Clean product grids
- Professional seller interfaces
- Intuitive inventory management
- Modern dashboard aesthetics

### Responsive Design
- Mobile-first approach
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Touch-friendly interface elements
- Optimized product grids for all devices

### Accessibility
- WCAG 2.1 AA compliance
- Proper color contrast ratios
- Keyboard navigation support
- Screen reader optimization
- Focus indicators

## Security & Performance

### Security Measures
- Role-based access control
- Subscription status verification (strict gatekeeping)
- Input validation and sanitization
- Rate limiting on API endpoints
- Secure file upload handling
- Seller approval workflow

### Performance Optimization
- Image optimization and lazy loading
- Code splitting by route
- Database query optimization
- CDN for static assets
- Caching strategies for product data

## Development Conventions

### File Naming
- Components: PascalCase (ProductCard.tsx)
- Pages: kebab-case (product-details.tsx)
- Utilities: camelCase (formatPrice.ts)
- Types: PascalCase with .types.ts suffix

### Code Organization
- Feature-based folder structure
- Shared components in /components/ui
- Business logic in custom hooks
- Type definitions co-located with features
- Consistent import order (external, internal, relative)

### State Management
- Server state with React Query
- Client state with Zustand
- Form state with React Hook Form
- Authentication state with context/provider pattern

## API Design Patterns

### RESTful Endpoints
```
/api/auth/*          - Authentication routes
/api/products/*      - Product CRUD operations
/api/sellers/*       - Seller management and applications
/api/subscriptions/* - Stripe integration
/api/orders/*        - Order processing
/api/invoices/*      - Offline sales and invoicing
/api/reports/*       - Seller analytics and reporting
```

### Error Handling
- Consistent error response format
- User-friendly error messages
- Logging for debugging
- Graceful fallbacks for failed requests

## Testing Strategy
- Unit tests for utilities and hooks
- Integration tests for API routes
- Component tests with React Testing Library
- E2E tests for critical user flows
- Subscription flow testing with Stripe test mode
- Seller application workflow testing
- Invoice generation testing
- Unit tests for utilities and hooks
- Integration tests for API routes
- Component tests with React Testing Library
- E2E tests for critical user flows
- Subscription flow testing with Stripe test 

### Images
Make sure to always use `Image` from `next/image` for all images.