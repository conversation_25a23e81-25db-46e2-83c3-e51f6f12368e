# Bulk Upload Inventory Guide

This guide explains how to use the bulk upload feature to create multiple products at once from a CSV file.

## Overview

The bulk upload feature allows sellers to create multiple products simultaneously by uploading a CSV file. All products are created as drafts without images, which can then be added manually later.

## Features

- **CSV Format Support**: Upload CSV files with product data
- **Template Download**: Pre-formatted CSV template with example data
- **Validation**: Automatic validation of required fields and data types
- **Error Reporting**: Detailed error messages for invalid data
- **Preview**: See all products before uploading
- **Bulk Creation**: Create up to 50 products at once (based on subscription limits)

## Getting Started

### 1. Download the Template

1. Click the "BULK UPLOAD" button in your inventory management page
2. Click "Download Template" to get the CSV template
3. The template includes example data and all available fields

### 2. Prepare Your CSV File

Use the template as a starting point and fill in your product information:

#### Required Fields
- **title**: Product name (minimum 3 characters)
- **price**: Selling price (must be greater than 0)
- **category**: One of: clothing, sneakers, collectibles, accessories, handbags, jewelry, watches, sunglasses
- **brand**: Product brand name
- **condition**: One of: new, like_new, excellent, very_good, good, fair

#### Optional Fields
- **description**: Product description (auto-generated if empty)
- **size**: Product size
- **color**: Product color
- **material**: Product material
- **year**: Year of manufacture
- **originalPrice**: Original retail price
- **sku**: Stock keeping unit
- **ownershipType**: owned or consigned (defaults to owned)
- **source**: Where you acquired the item
- **costPaid**: How much you paid for the item
- **paymentMethod**: How you paid for the item
- **tags**: Semicolon-separated tags (e.g., "luxury;designer;chanel")
- **weight**: Product weight in kg
- **shippingCost**: Shipping cost
- **length/width/height**: Product dimensions in cm

### 3. Upload Your File

1. Click "BULK UPLOAD" button
2. Click "Choose File" and select your CSV file
3. Review the validation results
4. Fix any errors if needed
5. Click "Upload X Products" to create all products

## CSV Format Examples

### Basic Product
```csv
title,price,category,brand,condition
"Supreme Box Logo Hoodie",800,clothing,Supreme,good
```

### Detailed Product
```csv
title,description,price,category,brand,condition,size,color,material,year,originalPrice,sku,ownershipType,source,costPaid,paymentMethod,tags,weight,shippingCost,length,width,height
"Chanel Classic Flap Bag","Authentic Chanel Classic Flap Bag in black caviar leather with gold hardware",8500,handbags,Chanel,excellent,Medium,Black,Caviar Leather,2022,9500,CH-CF-BLK-001,owned,Boutique Purchase,7500,Credit Card,"chanel;classic;flap;black;caviar;luxury;handbag",0.8,50,25,8,16
```

## Validation Rules

### Title
- Minimum 3 characters
- Cannot be empty

### Price
- Must be a valid number
- Must be greater than 0

### Category
- Must be one of the predefined categories
- Case-insensitive

### Condition
- Must be one of the predefined conditions
- Case-insensitive

### Brand
- Cannot be empty
- Will be trimmed of whitespace

## Error Handling

If validation errors occur:

1. **Review Error Messages**: Each error shows the row number, field name, and issue
2. **Fix CSV File**: Update the problematic rows in your CSV file
3. **Re-upload**: Upload the corrected file
4. **Clear Data**: Use the "Clear" button to reset if needed

## After Upload

1. **Products Created**: All valid products are created as drafts
2. **Status**: Products appear in your inventory as "Draft"
3. **Add Images**: Edit each product to add images manually
4. **Publish**: Publish products when ready to go live

## Tips for Success

1. **Use the Template**: Start with the provided template to avoid formatting issues
2. **Check Data Types**: Ensure numbers are valid (no currency symbols, commas, etc.)
3. **Validate Categories**: Use exact category names from the allowed list
4. **Test with Small Files**: Start with 5-10 products to test the process
5. **Backup Your Data**: Keep a copy of your original CSV file

## Troubleshooting

### Common Issues

**"Invalid category" Error**
- Check that category names match exactly: clothing, sneakers, collectibles, accessories, handbags, jewelry, watches, sunglasses

**"Invalid condition" Error**
- Use exact condition names: new, like_new, excellent, very_good, good, fair

**"Price must be greater than 0" Error**
- Remove currency symbols ($, €, £)
- Remove commas from numbers
- Ensure price is a valid decimal number

**File Upload Fails**
- Check file format (must be .csv or .txt)
- Ensure file size is under 10MB
- Verify CSV format (comma-separated values)

### Getting Help

If you encounter persistent issues:

1. Check the error messages for specific guidance
2. Verify your CSV format matches the template
3. Try uploading a smaller subset of products first
4. Contact support with your error details

## Limitations

- **File Size**: Maximum 10MB
- **Product Count**: Limited by your subscription plan (Basic: 50, Premium: 200, Enterprise: unlimited)
- **Images**: Not supported in bulk upload - add manually after creation
- **File Format**: CSV and TXT only (Excel files must be exported as CSV)

## Best Practices

1. **Organize Your Data**: Group similar products together
2. **Use Consistent Formatting**: Maintain consistent naming conventions
3. **Include All Required Fields**: Fill in required fields for every product
4. **Review Before Upload**: Double-check your data for accuracy
5. **Start Small**: Begin with a few products to test the process
6. **Keep Templates**: Save your working CSV templates for future use
