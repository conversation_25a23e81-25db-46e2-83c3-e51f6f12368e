# Supplier Management System

## Overview

The Supplier Management System allows sellers to track their relationships with suppliers, monitor financial transactions, and maintain detailed records of all business interactions. This system provides comprehensive insights into supplier performance, outstanding balances, and inventory tracking.

## Features

### 1. Supplier Database
- **Supplier Profiles**: Store comprehensive information about each supplier
- **Contact Management**: Track contact persons, emails, phones, and addresses
- **Business Terms**: Record payment terms, credit limits, and special agreements
- **Tags & Notes**: Categorize suppliers and add detailed notes

### 2. Financial Tracking
- **Transaction Management**: Record purchases, payments, refunds, and adjustments
- **Payment Tracking**: Monitor outstanding balances and overdue amounts
- **Due Date Management**: Track payment deadlines and overdue transactions
- **Financial Analytics**: View spending trends and supplier performance

### 3. Inventory Integration
- **Product Linking**: Connect products to their source suppliers
- **Cost Tracking**: Monitor costs paid for each product
- **Inventory Analytics**: View active products and sold items per supplier

### 4. Dashboard Integration
- **Supplier Overview**: Quick summary of supplier metrics on main dashboard
- **Outstanding Balances**: Monitor pending payments and overdue amounts
- **Supplier Counts**: Track total and active supplier relationships

## Database Schema

### Suppliers Table
```typescript
suppliers: defineTable({
  sellerId: v.id("users"), // The seller tracking this supplier
  supplierName: v.string(),
  supplierEmail: v.optional(v.string()),
  supplierPhone: v.optional(v.string()),
  supplierAddress: v.optional(v.string()),
  contactPerson: v.optional(v.string()),
  paymentTerms: v.optional(v.string()),
  creditLimit: v.optional(v.number()),
  notes: v.optional(v.string()),
  isActive: v.boolean(),
  relationshipStartDate: v.number(),
  lastContactDate: v.optional(v.number()),
  tags: v.array(v.string()),
  updatedAt: v.number(),
})
```

### Supplier Transactions Table
```typescript
supplierTransactions: defineTable({
  supplierId: v.id("suppliers"),
  sellerId: v.id("users"),
  productId: v.optional(v.id("products")),
  transactionType: v.union(
    v.literal("purchase"),
    v.literal("payment"),
    v.literal("refund"),
    v.literal("consignment_fee"),
    v.literal("adjustment")
  ),
  amount: v.number(),
  currency: v.string(),
  transactionDate: v.number(),
  dueDate: v.optional(v.number()),
  status: v.union(
    v.literal("pending"),
    v.literal("completed"),
    v.literal("overdue"),
    v.literal("cancelled")
  ),
  paymentMethod: v.optional(v.string()),
  referenceNumber: v.optional(v.string()),
  description: v.string(),
  notes: v.optional(v.string()),
  updatedAt: v.number(),
})
```

## API Functions

### Supplier Management
- `createSupplier`: Add a new supplier
- `getSuppliers`: Retrieve all suppliers for a seller
- `getSupplier`: Get specific supplier details
- `updateSupplier`: Update supplier information
- `deleteSupplier`: Remove a supplier

### Transaction Management
- `createSupplierTransaction`: Record new transactions
- `getSupplierTransactions`: Get transaction history
- `getSupplierAnalytics`: Calculate supplier performance metrics
- `getSupplierSummary`: Get dashboard summary data

## Usage

### Adding a Supplier
1. Navigate to **Suppliers** in the seller dashboard
2. Click **Add Supplier**
3. Fill in supplier details (name, contact info, payment terms)
4. Add tags for categorization
5. Save the supplier profile

### Recording Transactions
1. View supplier profile
2. Go to **Transactions** tab
3. Click **Add Transaction**
4. Select transaction type (purchase, payment, etc.)
5. Enter amount, dates, and description
6. Save transaction

### Viewing Supplier Analytics
1. Access supplier profile
2. Navigate to **Analytics** tab
3. View financial summaries and trends
4. Monitor outstanding balances and overdue amounts

## Integration Points

### Product Management
- Products can be linked to suppliers via `sourceInfo.source`
- Cost tracking through `sourceInfo.costPaid`
- Purchase date recording via `sourceInfo.purchaseDate`

### Dashboard Integration
- Supplier summary widget on main seller dashboard
- Quick access to supplier management
- Overview of outstanding balances and overdue amounts

## Benefits

1. **Financial Control**: Track all supplier expenses and outstanding balances
2. **Relationship Management**: Maintain detailed supplier profiles and contact information
3. **Inventory Tracking**: Link products to suppliers for better sourcing insights
4. **Payment Monitoring**: Avoid overdue payments and maintain good supplier relationships
5. **Analytics**: Make informed decisions based on supplier performance data

## Future Enhancements

- **Automated Payment Reminders**: Email notifications for upcoming due dates
- **Supplier Performance Ratings**: Rate suppliers based on reliability and quality
- **Bulk Import**: Import supplier data from spreadsheets or other systems
- **Integration APIs**: Connect with accounting software and payment systems
- **Advanced Reporting**: Generate detailed supplier performance reports
- **Mobile App**: Access supplier information on mobile devices

## Security & Access Control

- Suppliers are isolated by seller (sellerId)
- Only authenticated sellers can access their supplier data
- All operations require proper authentication and authorization
- Data is protected through Convex's built-in security features
