# HauteVault Seller Review System

This document describes the comprehensive seller review system that ensures only verified transactions can generate reviews, maintaining the integrity and trustworthiness of the platform.

## Overview

The seller review system is designed to prevent fake reviews and ensure that all reviews come from legitimate, completed transactions. Users can only review sellers after their orders have been delivered, creating a trustworthy review ecosystem.

## Key Features

### ✅ Verified Purchase Requirements
- **Order Status Validation**: Only orders with "delivered" status can be reviewed
- **Transaction Verification**: Reviews are automatically linked to specific order IDs
- **Duplicate Prevention**: Users cannot review the same seller multiple times for the same order
- **Authentication Required**: Only authenticated users can submit reviews

### ✅ Review Management
- **Rating System**: 1-5 star ratings with visual star display
- **Text Reviews**: Minimum 10 characters, maximum 1000 characters
- **Review Updates**: Users can edit their own reviews
- **Review Deletion**: Users can delete their own reviews (admins can delete any)

### ✅ Seller Profile Integration
- **Automatic Rating Calculation**: Seller ratings are automatically updated
- **Review Count Tracking**: Total number of reviews is maintained
- **Rating Distribution**: Visual breakdown of ratings (5-star, 4-star, etc.)

## Database Schema

### Reviews Table
```typescript
reviews: defineTable({
  userId: v.id("users"),           // Reviewer
  productId: v.id("products"),     // Product purchased
  sellerId: v.id("users"),         // Seller being reviewed
  orderId: v.id("orders"),         // Verified transaction
  rating: v.number(),              // 1-5 star rating
  review: v.string(),              // Review text
  isVerifiedPurchase: v.boolean(), // Always true
  reviewDate: v.number(),          // Timestamp
})
```

### Indexes
- `by_userId`: Find reviews by user
- `by_sellerId`: Find reviews for a seller
- `by_orderId`: Find review by order
- `by_rating`: Filter by rating
- `by_verified_purchase`: Filter verified purchases

## API Endpoints

### 1. addSellerReview
**Purpose**: Submit a new review for a seller
**Requirements**: 
- User must be authenticated
- Order must be delivered
- Order must belong to the user
- No existing review for this order

```typescript
await ctx.runMutation(api.sellerReviews.addSellerReview, {
  sellerId: "seller_id",
  orderId: "order_id",
  rating: 5,
  review: "Excellent seller! Item exactly as described."
});
```

### 2. getSellerReviews
**Purpose**: Retrieve reviews for a specific seller
**Features**:
- Pagination support
- Rating filtering
- Verified purchase filtering
- Rating statistics

```typescript
const reviews = await ctx.runQuery(api.sellerReviews.getSellerReviews, {
  sellerId: "seller_id",
  limit: 20,
  offset: 0,
  rating: 5, // Optional: filter by rating
  verifiedOnly: true // Default: true
});
```

### 3. getReviewableOrders
**Purpose**: Get orders that are ready for review
**Returns**: Delivered orders without existing reviews

```typescript
const reviewableOrders = await ctx.runQuery(api.sellerReviews.getReviewableOrders, {
  sellerId: "seller_id" // Optional: filter by specific seller
});
```

### 4. updateSellerReview
**Purpose**: Update an existing review
**Requirements**: Only the original reviewer can update

```typescript
await ctx.runMutation(api.sellerReviews.updateSellerReview, {
  reviewId: "review_id",
  rating: 4, // Optional
  review: "Updated review text" // Optional
});
```

### 5. deleteSellerReview
**Purpose**: Delete a review
**Requirements**: Original reviewer or admin

```typescript
await ctx.runMutation(api.sellerReviews.deleteSellerReview, {
  reviewId: "review_id"
});
```

## Frontend Components

### SellerReviews
**Location**: `apps/web/components/seller/SellerReviews.tsx`
**Purpose**: Display seller reviews and allow users to submit new ones
**Features**:
- Review submission form with order selection
- Rating visualization
- Review list with pagination
- Rating distribution charts
- Verified purchase badges

### UserReviewManagement
**Location**: `apps/web/components/seller/UserReviewManagement.tsx`
**Purpose**: Allow users to manage their own reviews
**Features**:
- Edit existing reviews
- Delete reviews
- View reviewable orders
- Review statistics

## Integration Points

### Seller Profile Page
The `SellerReviews` component is integrated into the seller profile page (`/seller/[id]`) to display all reviews for that seller.

### User Dashboard
The `UserReviewManagement` component is integrated into the marketplace dashboard to allow users to manage their reviews.

### Order Management
The review system integrates with the existing order management system:
- Reviews can only be created for delivered orders
- Order status changes trigger review availability
- Payment confirmation releases reviews

## Security Features

### Authorization
- Users can only review their own purchases
- Users can only edit/delete their own reviews
- Admins have full review management access

### Validation
- Rating must be 1-5 stars
- Review text must be 10-1000 characters
- Order must be in "delivered" status
- No duplicate reviews per order

### Data Integrity
- All reviews are linked to verified transactions
- Seller ratings are automatically recalculated
- Review counts are maintained accurately

## Usage Examples

### Adding a Review
1. User completes a purchase and receives delivery
2. User visits seller profile page
3. User clicks "Write a Review" button
4. User selects the specific order to review
5. User provides rating and review text
6. Review is submitted and linked to the order

### Managing Reviews
1. User visits dashboard
2. User navigates to "My Reviews" section
3. User can view, edit, or delete their reviews
4. User can see orders ready for review

### Viewing Seller Reviews
1. User visits seller profile page
2. User scrolls to "Customer Reviews" section
3. User can see rating distribution
4. User can browse individual reviews
5. User can filter reviews by rating

## Future Enhancements

### Planned Features
- **Review Moderation**: Admin tools for review approval
- **Review Responses**: Allow sellers to respond to reviews
- **Review Analytics**: Advanced seller performance metrics
- **Review Incentives**: Reward users for helpful reviews
- **Review Categories**: Rate different aspects (communication, shipping, etc.)

### Technical Improvements
- **Review Caching**: Optimize review loading performance
- **Review Search**: Full-text search within reviews
- **Review Export**: Data export for analytics
- **Review API**: Public API for third-party integrations

## Testing

### Test Cases
1. **Valid Review Submission**: User with delivered order can submit review
2. **Invalid Order Status**: User cannot review pending/shipped orders
3. **Duplicate Prevention**: User cannot review same order twice
4. **Authorization**: User cannot edit others' reviews
5. **Rating Validation**: Only 1-5 star ratings accepted
6. **Text Validation**: Review text length requirements enforced

### Test Data
Use the existing order management test data to verify review functionality:
- Create orders with different statuses
- Test review submission for delivered orders
- Verify rating calculations
- Test review management operations

## Troubleshooting

### Common Issues
1. **Review Not Appearing**: Check order status is "delivered"
2. **Cannot Submit Review**: Verify user owns the order
3. **Rating Not Updated**: Check seller profile update logic
4. **Permission Errors**: Verify user authentication and ownership

### Debug Steps
1. Check order status in database
2. Verify user authentication
3. Check review table constraints
4. Review error logs for specific issues

## Conclusion

The seller review system provides a robust, secure way for users to share their experiences while maintaining platform integrity. By linking reviews to verified transactions, we ensure that all feedback is genuine and trustworthy, building confidence in the HauteVault marketplace.
