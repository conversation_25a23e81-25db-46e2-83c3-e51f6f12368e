#!/bin/bash

# HauteVault Auth Setup Script
# This script helps set up authentication configuration for development

echo "🔧 Setting up HauteVault Authentication..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if .env file exists
if [ ! -f "packages/backend/.env" ]; then
    echo -e "${YELLOW}Creating .env file from .env.example...${NC}"
    cp packages/backend/.env.example packages/backend/.env
    echo -e "${GREEN}✅ Created packages/backend/.env${NC}"
else
    echo -e "${BLUE}ℹ️  .env file already exists${NC}"
fi

# Generate BETTER_AUTH_SECRET if not set
if ! grep -q "BETTER_AUTH_SECRET=.*[^[:space:]]" packages/backend/.env; then
    echo -e "${YELLOW}Generating BETTER_AUTH_SECRET...${NC}"
    SECRET=$(openssl rand -base64 32)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/BETTER_AUTH_SECRET=.*/BETTER_AUTH_SECRET=$SECRET/" packages/backend/.env
    else
        # Linux
        sed -i "s/BETTER_AUTH_SECRET=.*/BETTER_AUTH_SECRET=$SECRET/" packages/backend/.env
    fi
    echo -e "${GREEN}✅ Generated BETTER_AUTH_SECRET${NC}"
else
    echo -e "${BLUE}ℹ️  BETTER_AUTH_SECRET already set${NC}"
fi

# Set APP_URL if not set
if ! grep -q "APP_URL=.*[^[:space:]]" packages/backend/.env; then
    echo -e "${YELLOW}Setting APP_URL for development...${NC}"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sed -i '' "s/APP_URL=.*/APP_URL=http:\/\/localhost:3000/" packages/backend/.env
    else
        sed -i "s/APP_URL=.*/APP_URL=http:\/\/localhost:3000/" packages/backend/.env
    fi
    echo -e "${GREEN}✅ Set APP_URL to http://localhost:3000${NC}"
fi

# Set NODE_ENV if not set
if ! grep -q "NODE_ENV=.*[^[:space:]]" packages/backend/.env; then
    echo -e "${YELLOW}Setting NODE_ENV for development...${NC}"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sed -i '' "s/NODE_ENV=.*/NODE_ENV=development/" packages/backend/.env
    else
        sed -i "s/NODE_ENV=.*/NODE_ENV=development/" packages/backend/.env
    fi
    echo -e "${GREEN}✅ Set NODE_ENV to development${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Auth setup complete!${NC}"
echo ""
echo -e "${BLUE}📋 Next steps:${NC}"
echo "1. Set your Convex deployment URL in NEXT_PUBLIC_CONVEX_URL"
echo "2. (Optional) Set up Resend API key for email functionality"
echo "3. (Optional) Configure Google/Apple OAuth for social login"
echo ""
echo -e "${YELLOW}⚠️  For production:${NC}"
echo "- Generate a new BETTER_AUTH_SECRET"
echo "- Set proper APP_URL"
echo "- Configure email service (Resend)"
echo "- Set up social OAuth providers"
echo ""
echo -e "${BLUE}📖 See packages/backend/.env for all configuration options${NC}"
