#!/usr/bin/env node
import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL);

async function clearUserAuth(email) {
  try {
    console.log(`Clearing auth data for: ${email}`);
    
    const result = await client.mutation("auth:clearUserSessions", { email });
    console.log(`✓ Cleared ${result.cleared} sessions for user ${result.user}`);
    
    console.log('Auth data cleared successfully!');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function deleteUser(email) {
  try {
    console.log(`Deleting unverified user: ${email}`);
    
    const result = await client.mutation("auth:deleteUnverifiedUser", { email });
    console.log(`✓ Deleted user ${result.userId}`);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function listSessions() {
  try {
    const sessions = await client.query("auth:listSessions");
    console.table(sessions);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Parse command line arguments
const command = process.argv[2];
const email = process.argv[3];

switch (command) {
  case 'clear':
    if (!email) {
      console.error('Usage: node clear-auth.js clear <email>');
      process.exit(1);
    }
    clearUserAuth(email);
    break;
    
  case 'delete':
    if (!email) {
      console.error('Usage: node clear-auth.js delete <email>');
      process.exit(1);
    }
    deleteUser(email);
    break;
    
  case 'list':
    listSessions();
    break;
    
  default:
    console.log('Available commands:');
    console.log('  clear <email>  - Clear sessions for user');
    console.log('  delete <email> - Delete unverified user');
    console.log('  list          - List all sessions');
    break;
}