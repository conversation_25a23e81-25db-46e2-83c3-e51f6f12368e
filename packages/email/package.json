{"name": "@repo/email", "private": true, "version": "0.0.0", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules"}, "dependencies": {"@react-email/components": "^0.3.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.19.8", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6"}, "exports": {".": {"import": "./src/index.ts", "require": "./src/index.ts"}, "./templates/*": {"import": "./src/templates/*.tsx", "require": "./src/templates/*.tsx"}, "./utils": {"import": "./src/utils.ts", "require": "./src/utils.ts"}}}