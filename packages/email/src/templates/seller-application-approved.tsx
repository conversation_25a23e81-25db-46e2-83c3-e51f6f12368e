import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Button,
  Hr,
} from "@react-email/components";

interface SellerApplicationApprovedProps {
  name: string;
  businessName: string;
}

export default function SellerApplicationApproved({
  name = "<PERSON>",
  businessName = "Luxury Goods LLC",
}: SellerApplicationApprovedProps) {
  return (
    <Html>
      <Head />
      <Preview>Congratulations! Your seller application has been approved</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Heading style={h1}>HauteVault</Heading>
          </Section>
          
          <Section style={content}>
            <Heading style={h2}>🎉 Application Approved!</Heading>
            
            <Text style={text}>Dear {name},</Text>
            
            <Text style={text}>
              Congratulations! We are excited to inform you that your seller application for <strong>{businessName}</strong> has been approved.
            </Text>
            
            <Text style={text}>
              You are now part of the HauteVault marketplace and can start listing your luxury goods to reach our discerning customer base.
            </Text>
            
            <Section style={nextSteps}>
              <Text style={sectionTitle}>Next Steps:</Text>
              <Text style={step}>1. Complete your seller profile setup</Text>
              <Text style={step}>2. Upload your first product listings</Text>
              <Text style={step}>3. Configure your shipping preferences</Text>
              <Text style={step}>4. Review our seller guidelines and policies</Text>
            </Section>
            
            <Button style={button} href={`${process.env.APP_URL}/seller/dashboard`}>
              Access Seller Dashboard
            </Button>
            
            <Section style={benefits}>
              <Text style={sectionTitle}>What's Included:</Text>
              <Text style={benefit}>✓ Access to premium customer base</Text>
              <Text style={benefit}>✓ Professional listing tools</Text>
              <Text style={benefit}>✓ Secure payment processing</Text>
              <Text style={benefit}>✓ Marketing and promotional support</Text>
              <Text style={benefit}>✓ Dedicated seller support</Text>
            </Section>
            
            <Hr style={hr} />
            
            <Text style={footer}>
              Welcome to HauteVault! If you have any questions, our seller support team is here to help at{" "}
              <a href="mailto:<EMAIL>" style={link}>
                <EMAIL>
              </a>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: "#f6f9fc",
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "20px 0 48px",
  marginBottom: "64px",
};

const header = {
  padding: "32px 24px",
  backgroundColor: "#000000",
  textAlign: "center" as const,
};

const content = {
  padding: "24px",
};

const h1 = {
  color: "#ffffff",
  fontSize: "24px",
  fontWeight: "600",
  margin: "0",
};

const h2 = {
  color: "#333333",
  fontSize: "20px",
  fontWeight: "600",
  margin: "0 0 16px",
};

const text = {
  color: "#333333",
  fontSize: "16px",
  lineHeight: "24px",
  margin: "0 0 16px",
};

const nextSteps = {
  backgroundColor: "#f8f9fa",
  padding: "16px",
  borderRadius: "8px",
  margin: "24px 0",
};

const benefits = {
  backgroundColor: "#f0f9ff",
  padding: "16px",
  borderRadius: "8px",
  margin: "24px 0",
};

const sectionTitle = {
  color: "#333333",
  fontSize: "16px",
  fontWeight: "600",
  margin: "0 0 12px",
};

const step = {
  color: "#666666",
  fontSize: "14px",
  margin: "0 0 8px",
  paddingLeft: "16px",
};

const benefit = {
  color: "#0369a1",
  fontSize: "14px",
  margin: "0 0 6px",
};

const button = {
  backgroundColor: "#000000",
  borderRadius: "8px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "600",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "12px 24px",
  margin: "24px 0",
};

const hr = {
  borderColor: "#e6ebf1",
  margin: "32px 0",
};

const footer = {
  color: "#666666",
  fontSize: "14px",
  lineHeight: "20px",
  margin: "0",
};

const link = {
  color: "#000000",
  textDecoration: "underline",
};