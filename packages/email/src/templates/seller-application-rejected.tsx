import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Button,
  Hr,
} from "@react-email/components";

interface SellerApplicationRejectedProps {
  name: string;
  reason: string;
}

export default function SellerApplicationRejected({
  name = "<PERSON>",
  reason = "Application did not meet our requirements",
}: SellerApplicationRejectedProps) {
  return (
    <Html>
      <Head />
      <Preview>Update on your seller application</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Heading style={h1}>HauteVault</Heading>
          </Section>
          
          <Section style={content}>
            <Heading style={h2}>Application Update</Heading>
            
            <Text style={text}>Dear {name},</Text>
            
            <Text style={text}>
              Thank you for your interest in becoming a seller on HauteVault. After careful review of your application, we regret to inform you that we are unable to approve your seller account at this time.
            </Text>
            
            <Section style={reasonSection}>
              <Text style={reasonTitle}>Reason for Decision:</Text>
              <Text style={reasonText}>{reason}</Text>
            </Section>
            
            <Text style={text}>
              We understand this may be disappointing, and we appreciate the time you took to complete the application process.
            </Text>
            
            <Text style={text}>
              You are welcome to reapply in the future if your circumstances change or if you can address the concerns mentioned above.
            </Text>
            
            <Button style={button} href={`${process.env.APP_URL}/seller/apply`}>
              Submit New Application
            </Button>
            
            <Hr style={hr} />
            
            <Text style={footer}>
              If you have questions about this decision or would like more specific feedback, please contact our seller support team at{" "}
              <a href="mailto:<EMAIL>" style={link}>
                <EMAIL>
              </a>
            </Text>
            
            <Text style={footer}>
              Thank you for your interest in HauteVault.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: "#f6f9fc",
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "20px 0 48px",
  marginBottom: "64px",
};

const header = {
  padding: "32px 24px",
  backgroundColor: "#000000",
  textAlign: "center" as const,
};

const content = {
  padding: "24px",
};

const h1 = {
  color: "#ffffff",
  fontSize: "24px",
  fontWeight: "600",
  margin: "0",
};

const h2 = {
  color: "#333333",
  fontSize: "20px",
  fontWeight: "600",
  margin: "0 0 16px",
};

const text = {
  color: "#333333",
  fontSize: "16px",
  lineHeight: "24px",
  margin: "0 0 16px",
};

const reasonSection = {
  backgroundColor: "#fef2f2",
  padding: "16px",
  borderRadius: "8px",
  margin: "24px 0",
  borderLeft: "4px solid #ef4444",
};

const reasonTitle = {
  color: "#991b1b",
  fontSize: "16px",
  fontWeight: "600",
  margin: "0 0 8px",
};

const reasonText = {
  color: "#7f1d1d",
  fontSize: "14px",
  margin: "0",
};

const button = {
  backgroundColor: "#000000",
  borderRadius: "8px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "600",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "12px 24px",
  margin: "24px 0",
};

const hr = {
  borderColor: "#e6ebf1",
  margin: "32px 0",
};

const footer = {
  color: "#666666",
  fontSize: "14px",
  lineHeight: "20px",
  margin: "0 0 16px",
};

const link = {
  color: "#000000",
  textDecoration: "underline",
};