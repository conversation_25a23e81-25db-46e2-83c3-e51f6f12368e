@import "tailwindcss";
@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.9711 0.0074 80.7211);
  --foreground: oklch(0.3000 0.0358 30.2042);
  --card: oklch(0.9711 0.0074 80.7211);
  --card-foreground: oklch(0.3000 0.0358 30.2042);
  --popover: oklch(0.9711 0.0074 80.7211);
  --popover-foreground: oklch(0.3000 0.0358 30.2042);
  --primary: oklch(0.35 0.15 140);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.85 0.08 140);
  --secondary-foreground: oklch(0.25 0.12 140);
  --muted: oklch(0.9370 0.0142 74.4218);
  --muted-foreground: oklch(0.4495 0.0486 39.2110);
  --accent: oklch(0.75 0.12 140);
  --accent-foreground: oklch(0.25 0.12 140);
  --destructive: oklch(0.5386 0.1937 26.7249);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8805 0.0208 74.6428);
  --input: oklch(0.8805 0.0208 74.6428);
  --ring: oklch(0.35 0.15 140);
  --chart-1: oklch(0.45 0.14 140);
  --chart-2: oklch(0.55 0.13 140);
  --chart-3: oklch(0.35 0.15 140);
  --chart-4: oklch(0.25 0.12 140);
  --chart-5: oklch(0.15 0.10 140);
  --sidebar: oklch(0.9370 0.0142 74.4218);
  --sidebar-foreground: oklch(0.3000 0.0358 30.2042);
  --sidebar-primary: oklch(0.35 0.15 140);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.75 0.12 140);
  --sidebar-accent-foreground: oklch(0.25 0.12 140);
  --sidebar-border: oklch(0.8805 0.0208 74.6428);
  --sidebar-ring: oklch(0.35 0.15 140);
  --font-sans: Instrument Sans, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Instrument Serif, ui-serif, serif;
  --font-mono: Source Code Pro, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0.075em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2683 0.0279 150.7681);
  --foreground: oklch(0.9423 0.0097 72.6595);
  --card: oklch(0.3327 0.0271 146.9867);
  --card-foreground: oklch(0.9423 0.0097 72.6595);
  --popover: oklch(0.3327 0.0271 146.9867);
  --popover-foreground: oklch(0.9423 0.0097 72.6595);
  --primary: oklch(0.45 0.14 140);
  --primary-foreground: oklch(0.15 0.10 140);
  --secondary: oklch(0.25 0.12 140);
  --secondary-foreground: oklch(0.85 0.08 140);
  --muted: oklch(0.3327 0.0271 146.9867);
  --muted-foreground: oklch(0.8579 0.0174 76.0955);
  --accent: oklch(0.55 0.13 140);
  --accent-foreground: oklch(0.9423 0.0097 72.6595);
  --destructive: oklch(0.5386 0.1937 26.7249);
  --destructive-foreground: oklch(0.9423 0.0097 72.6595);
  --border: oklch(0.3942 0.0265 142.9926);
  --input: oklch(0.3942 0.0265 142.9926);
  --ring: oklch(0.45 0.14 140);
  --chart-1: oklch(0.65 0.11 140);
  --chart-2: oklch(0.55 0.13 140);
  --chart-3: oklch(0.45 0.14 140);
  --chart-4: oklch(0.35 0.15 140);
  --chart-5: oklch(0.25 0.12 140);
  --sidebar: oklch(0.2683 0.0279 150.7681);
  --sidebar-foreground: oklch(0.9423 0.0097 72.6595);
  --sidebar-primary: oklch(0.45 0.14 140);
  --sidebar-primary-foreground: oklch(0.15 0.10 140);
  --sidebar-accent: oklch(0.55 0.13 140);
  --sidebar-accent-foreground: oklch(0.9423 0.0097 72.6595);
  --sidebar-border: oklch(0.3942 0.0265 142.9926);
  --sidebar-ring: oklch(0.45 0.14 140);
  --font-sans: Instrument Sans, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Instrument Serif, ui-serif, serif;
  --font-mono: Source Code Pro, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}