import { v } from "convex/values";
import { query } from "./_generated/server";

export const getSellerSales = query({
  args: {
    sellerId: v.id("users"),
    paymentMethod: v.optional(v.union(
      v.literal("cash"),
      v.literal("check"),
      v.literal("bank_transfer"),
      v.literal("credit_card"),
      v.literal("paypal"),
      v.literal("other")
    )),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("offlineSales").withIndex("sellerId", (q) => q.eq("sellerId", args.sellerId));
    
    if (args.paymentMethod) {
      query = query.filter((q) => q.eq(q.field("paymentMethod"), args.paymentMethod));
    }

    const sales = await query
      .order("desc")
      .take(args.limit || 50);

    // Get product details for each sale
    const salesWithProducts = await Promise.all(
      sales.map(async (sale) => {
        const product = await ctx.db.get(sale.productId);
        return {
          ...sale,
          product: product ? {
            title: product.title,
            primaryImageId: product.primaryImageId,
            brand: product.brand,
          } : null,
        };
      })
    );

    return salesWithProducts;
  },
});

export const getRecentSales = query({
  args: {
    sellerId: v.id("users"),
    days: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const daysAgo = Date.now() - ((args.days || 7) * 24 * 60 * 60 * 1000);
    
    const sales = await ctx.db
      .query("offlineSales")
      .withIndex("sellerId", (q) => q.eq("sellerId", args.sellerId))
      .filter((q) => q.gte(q.field("saleDate"), daysAgo))
      .order("desc")
      .take(10);

    // Get product details
    const salesWithProducts = await Promise.all(
      sales.map(async (sale) => {
        const product = await ctx.db.get(sale.productId);
        return {
          ...sale,
          product: product ? {
            title: product.title,
            primaryImageId: product.primaryImageId,
          } : null,
        };
      })
    );

    return salesWithProducts;
  },
});