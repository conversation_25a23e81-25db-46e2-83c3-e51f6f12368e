import { v } from "convex/values";
import { action, internalAction, internalMutation, internalQuery, mutation, query } from "./_generated/server";
import { internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";
import { requireAuth } from "./lib/auth_utils";

export const generateInvoiceNumber = (sellerId: string): string => {
  const timestamp = Date.now().toString().slice(-6);
  const sellerPrefix = sellerId.slice(-4).toUpperCase();
  return `HV-${sellerPrefix}-${timestamp}`;
};

export const createInvoice = mutation({
  args: {
    offlineSaleId: v.id("offlineSales"),
    paymentTerms: v.optional(v.string()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const offlineSale = await ctx.db.get(args.offlineSaleId);
    if (!offlineSale) {
      throw new Error("Offline sale not found");
    }

    const now = Date.now();
    const dueDate = now + (30 * 24 * 60 * 60 * 1000); // 30 days from now
    const invoiceNumber = generateInvoiceNumber(offlineSale.sellerId);

    // Get product for item description
    const product = await ctx.db.get(offlineSale.productId);
    const itemDescription = product ? `${product.title} - ${product.brand}` : "Product";
    const tax = 0; // Default tax
    const totalAmount = offlineSale.salePrice + tax;

    const invoiceId = await ctx.db.insert("invoices", {
      sellerId: offlineSale.sellerId,
      offlineSaleId: args.offlineSaleId,
      productId: offlineSale.productId,
      invoiceNumber,
      clientName: offlineSale.clientName,
      clientEmail: offlineSale.clientEmail,
      clientPhone: offlineSale.clientPhone,
      clientAddress: offlineSale.clientAddress,
      itemDescription,
      salePrice: offlineSale.salePrice,
      tax,
      totalAmount,
      paymentMethod: offlineSale.paymentMethod,
      status: "draft",
      dueDate,
      paymentTerms: args.paymentTerms || "Payment due within 30 days",
      notes: args.notes,
      emailsSent: [],
      updatedAt: now,
    });

    return invoiceId;
  },
});

export const getInvoices = query({
  args: {},
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    const invoices = await ctx.db
      .query("invoices")
      .withIndex("by_sellerId", (q: any) => q.eq("sellerId", user._id))
      .order("desc")
      .collect();

    // Get related data for each invoice
    const invoicesWithDetails = await Promise.all(
      invoices.map(async (invoice) => {
        const offlineSale = await ctx.db.get(invoice.offlineSaleId);
        const product = offlineSale ? await ctx.db.get(offlineSale.productId) : null;
        
        return {
          ...invoice,
          offlineSale,
          product,
        };
      })
    );

    return invoicesWithDetails;
  },
});

export const getInvoice = query({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      return null;
    }

    const offlineSale = await ctx.db.get(invoice.offlineSaleId);
    const product = offlineSale ? await ctx.db.get(offlineSale.productId) : null;
    const seller = await ctx.db.get(invoice.sellerId);

    return {
      ...invoice,
      offlineSale,
      product,
      seller,
    };
  },
});

export const updateInvoiceStatus = mutation({
  args: {
    invoiceId: v.id("invoices"),
    status: v.union(
      v.literal("draft"),
      v.literal("sent"),
      v.literal("paid"),
      v.literal("overdue"),
      v.literal("cancelled")
    ),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Update invoice status
    await ctx.db.patch(args.invoiceId, {
      status: args.status,
      updatedAt: now,
    });

    // If marking as paid, also update related entities
    if (args.status === "paid") {
      // Get the invoice to find related entities
      const invoice = await ctx.db.get(args.invoiceId);
      if (!invoice) {
        throw new Error("Invoice not found");
      }

      // Update offline sale status to paid
      if (invoice.offlineSaleId) {
        try {
          await ctx.db.patch(invoice.offlineSaleId, {
            status: "paid",
          });
        } catch (error) {
          console.error("Failed to update offline sale status:", error);
          // Continue with other updates even if this fails
        }
      }

      // Update product status to sold if it exists
      if (invoice.productId) {
        try {
          await ctx.db.patch(invoice.productId, {
            status: "sold",
            updatedAt: now,
          });
        } catch (error) {
          console.error("Failed to update product status:", error);
          // Continue with other updates even if this fails
        }
      }

      // Update invoice with paid date
      await ctx.db.patch(args.invoiceId, {
        paidDate: now,
        updatedAt: now,
      });

      // Log the payment event
      try {
        await ctx.db.insert("analytics", {
          eventType: "invoice_paid",
          userId: invoice.sellerId,
          sellerId: invoice.sellerId,
          productId: invoice.productId,
          timestamp: now,
          metadata: {
            source: args.invoiceId,
            category: "sales",
            revenue: invoice.totalAmount,
            paymentMethod: invoice.paymentMethod,
          },
        });
      } catch (error) {
        console.error("Failed to log payment event:", error);
        // Don't fail the main operation if logging fails
      }
    }
  },
});

export const bulkUpdateInvoiceStatus = mutation({
  args: {
    invoiceIds: v.array(v.id("invoices")),
    status: v.union(
      v.literal("draft"),
      v.literal("sent"),
      v.literal("paid"),
      v.literal("overdue"),
      v.literal("cancelled")
    ),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const results = [];

    for (const invoiceId of args.invoiceIds) {
      try {
        // Update invoice status
        await ctx.db.patch(invoiceId, {
          status: args.status,
          updatedAt: now,
        });

        // If marking as paid, also update related entities
        if (args.status === "paid") {
          const invoice = await ctx.db.get(invoiceId);
          if (invoice) {
            // Update offline sale status to paid
            if (invoice.offlineSaleId) {
              try {
                await ctx.db.patch(invoice.offlineSaleId, {
                  status: "paid",
                });
              } catch (error) {
                console.error(`Failed to update offline sale ${invoice.offlineSaleId}:`, error);
              }
            }

            // Update product status to sold if it exists
            if (invoice.productId) {
              try {
                await ctx.db.patch(invoice.productId, {
                  status: "sold",
                  updatedAt: now,
                });
              } catch (error) {
                console.error(`Failed to update product ${invoice.productId}:`, error);
              }
            }

            // Update invoice with paid date
            await ctx.db.patch(invoiceId, {
              paidDate: now,
              updatedAt: now,
            });

            // Log the payment event
            try {
              await ctx.db.insert("analytics", {
                eventType: "invoice_paid",
                userId: invoice.sellerId,
                sellerId: invoice.sellerId,
                productId: invoice.productId,
                timestamp: now,
                metadata: {
                  source: invoiceId,
                  category: "sales",
                  revenue: invoice.totalAmount,
                  paymentMethod: invoice.paymentMethod,
                },
              });
            } catch (error) {
              console.error("Failed to log payment event:", error);
            }
          }
        }

        results.push({ invoiceId, success: true });
      } catch (error) {
        console.error(`Failed to update invoice ${invoiceId}:`, error);
        results.push({ invoiceId, success: false, error: error instanceof Error ? error.message : String(error) });
      }
    }

    return results;
  },
});

export const markInvoiceAsPaid = mutation({
  args: {
    invoiceId: v.id("invoices"),
    paymentMethod: v.optional(v.string()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Get the invoice
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Update invoice status to paid
    await ctx.db.patch(args.invoiceId, {
      status: "paid",
      paidDate: now,
      updatedAt: now,
      notes: args.notes ? `${invoice.notes || ""}\n\nPayment received: ${args.notes}`.trim() : invoice.notes,
    });

    // Update offline sale status to paid
    if (invoice.offlineSaleId) {
      try {
        await ctx.db.patch(invoice.offlineSaleId, {
          status: "paid",
        });
      } catch (error) {
        console.error("Failed to update offline sale status:", error);
      }
    }

    // Update product status to sold if it exists
    if (invoice.productId) {
      try {
        await ctx.db.patch(invoice.productId, {
          status: "sold",
          updatedAt: now,
        });
      } catch (error) {
        console.error("Failed to update product status:", error);
      }
    }

    // Log the payment event
    try {
      await ctx.db.insert("analytics", {
        eventType: "invoice_paid",
        userId: invoice.sellerId,
        sellerId: invoice.sellerId,
        productId: invoice.productId,
        timestamp: now,
        metadata: {
          source: args.invoiceId,
          category: "sales",
          revenue: invoice.totalAmount,
          paymentMethod: args.paymentMethod || invoice.paymentMethod,
        },
      });
    } catch (error) {
      console.error("Failed to log payment event:", error);
    }

    return { success: true, invoiceId: args.invoiceId };
  },
});

export const generateInvoicePDF = action({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args): Promise<Id<"_storage">> => {
    // This will call an internal action to generate the PDF
    return await ctx.runAction(internal.invoices.generatePDFInternal, {
      invoiceId: args.invoiceId,
    });
  },
});

export const generatePDFInternal = internalAction({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args): Promise<Id<"_storage">> => {
    // Get invoice data
    const invoiceData = await ctx.runQuery(internal.invoices.getInvoiceForPDF, {
      invoiceId: args.invoiceId,
    });

    if (!invoiceData) {
      throw new Error("Invoice not found");
    }

    // Generate PDF using a PDF library (we'll implement this)
    const pdfBuffer = await generatePDFBuffer(invoiceData);
    
    // Store PDF in Convex storage
    const storageId = await ctx.storage.store(
      new Blob([pdfBuffer], { type: "application/pdf" }),
      { sha256: `invoice-${invoiceData.invoiceNumber}.pdf` }
    );

    // Update invoice with PDF storage ID
    await ctx.runMutation(internal.invoices.updateInvoicePDF, {
      invoiceId: args.invoiceId,
      pdfStorageId: storageId,
    });

    return storageId;
  },
});

export const getInvoiceForPDF = internalQuery({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) return null;

    const offlineSale = await ctx.db.get(invoice.offlineSaleId);
    const product = offlineSale ? await ctx.db.get(offlineSale.productId) : null;
    const seller = await ctx.db.get(invoice.sellerId);

    return {
      ...invoice,
      offlineSale,
      product,
      seller,
    };
  },
});

export const updateInvoicePDF = internalMutation({
  args: {
    invoiceId: v.id("invoices"),
    pdfStorageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.invoiceId, {
      pdfStorageId: args.pdfStorageId,
      updatedAt: Date.now(),
    });
  },
});

export const sendInvoiceEmail = action({
  args: {
    invoiceId: v.id("invoices"),
    customMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.runQuery(internal.invoices.getInvoiceForPDF, {
      invoiceId: args.invoiceId,
    });

    if (!invoice || !invoice.offlineSale) {
      throw new Error("Invoice or sale not found");
    }

    // Generate PDF if not exists
    let pdfStorageId = invoice.pdfStorageId;
    if (!pdfStorageId) {
      pdfStorageId = await ctx.runAction(internal.invoices.generatePDFInternal, {
        invoiceId: args.invoiceId,
      });
    }

    // Get PDF URL for attachment
    const pdfUrl = await ctx.storage.getUrl(pdfStorageId);
    
    // Send email with invoice
    await ctx.runAction(internal.invoices.sendInvoiceEmailInternal, {
      invoiceId: args.invoiceId,
      recipientEmail: invoice.offlineSale.clientEmail,
      recipientName: invoice.offlineSale.clientName,
      pdfUrl: pdfUrl!,
      customMessage: args.customMessage,
    });

    // Update invoice status and email log
    await ctx.runMutation(internal.invoices.logEmailSent, {
      invoiceId: args.invoiceId,
      recipientEmail: invoice.offlineSale.clientEmail,
    });
  },
});

export const sendInvoiceEmailInternal = internalAction({
  args: {
    invoiceId: v.id("invoices"),
    recipientEmail: v.string(),
    recipientName: v.string(),
    pdfUrl: v.string(),
    customMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // This would integrate with your email service
    // For now, we'll use the existing sendEmail function
    const { sendEmail } = await import("./lib/email");
    
    await sendEmail(ctx, {
      to: args.recipientEmail,
      subject: `Invoice from HauteVault - ${args.invoiceId}`,
      react: {
        // We'll create an invoice email template
        type: "invoice",
        data: {
          recipientName: args.recipientName,
          invoiceId: args.invoiceId,
          pdfUrl: args.pdfUrl,
          customMessage: args.customMessage,
        },
      },
    });
  },
});

export const logEmailSent = internalMutation({
  args: {
    invoiceId: v.id("invoices"),
    recipientEmail: v.string(),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    const emailLogString = `${new Date().toISOString()} - Sent to ${args.recipientEmail}`;

    await ctx.db.patch(args.invoiceId, {
      emailsSent: [...invoice.emailsSent, emailLogString],
      status: invoice.status === "draft" ? "sent" : invoice.status,
      updatedAt: Date.now(),
    });
  },
});

// Helper function to generate PDF (would use a PDF library)
async function generatePDFBuffer(invoiceData: any): Promise<ArrayBuffer> {
  // This is a placeholder - you would use a library like jsPDF or Puppeteer
  // For now, return a simple buffer
  const content = `
    HAUTEVAULT INVOICE
    
    Invoice #: ${invoiceData.invoiceNumber}
    Date: ${new Date(invoiceData.issueDate).toLocaleDateString()}
    Due Date: ${new Date(invoiceData.dueDate).toLocaleDateString()}
    
    Bill To:
    ${invoiceData.offlineSale?.clientName}
    ${invoiceData.offlineSale?.clientEmail}
    
    Product: ${invoiceData.product?.title}
    Price: $${invoiceData.offlineSale?.salePrice}
    
    Payment Terms: ${invoiceData.paymentTerms}
  `;
  
  return new TextEncoder().encode(content).buffer as ArrayBuffer;
}