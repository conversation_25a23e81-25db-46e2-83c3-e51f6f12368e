import { v } from "convex/values";
import { action, mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAuth 
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get or create Stripe customer for the authenticated user
 */
export const getOrCreateCustomer = action({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    // Check if user already has a Stripe customer ID
    const existingCustomer = await ctx.runQuery(internal.stripeCustomers.getUserStripeCustomer, {
      userId: user._id,
    });

    if (existingCustomer?.stripeCustomerId) {
      return {
        success: true,
        customerId: existingCustomer.stripeCustomerId,
        isNew: false,
      };
    }

    // Create new Stripe customer
    const stripe = (await import("./lib/stripe")).default;
    
    try {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user._id,
        },
      });

      // Store customer ID in user record
      await ctx.runMutation(internal.stripeCustomers.updateUserStripeCustomer, {
        userId: user._id,
        stripeCustomerId: customer.id,
      });

      return {
        success: true,
        customerId: customer.id,
        isNew: true,
      };
    } catch (error) {
      console.error("Error creating Stripe customer:", error);
      throw new ConvexError("Failed to create customer account");
    }
  },
});

/**
 * Update customer information in Stripe
 */
export const updateCustomer = action({
  args: {
    name: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.object({
      line1: v.string(),
      line2: v.optional(v.string()),
      city: v.string(),
      state: v.string(),
      postal_code: v.string(),
      country: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    const customerData = await ctx.runQuery(internal.stripeCustomers.getUserStripeCustomer, {
      userId: user._id,
    });

    if (!customerData?.stripeCustomerId) {
      throw new ConvexError("No Stripe customer found");
    }

    const stripe = (await import("./lib/stripe")).default;
    
    try {
      const updateData: any = {};
      
      if (args.name) updateData.name = args.name;
      if (args.email) updateData.email = args.email;
      if (args.phone) updateData.phone = args.phone;
      if (args.address) updateData.address = args.address;

      await stripe.customers.update(customerData.stripeCustomerId, updateData);

      return {
        success: true,
        message: "Customer information updated successfully",
      };
    } catch (error) {
      console.error("Error updating Stripe customer:", error);
      throw new ConvexError("Failed to update customer information");
    }
  },
});

/**
 * Get customer's payment methods
 */
export const getPaymentMethods = action({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    const customerData = await ctx.runQuery(internal.stripeCustomers.getUserStripeCustomer, {
      userId: user._id,
    });

    if (!customerData?.stripeCustomerId) {
      return {
        success: true,
        paymentMethods: [],
      };
    }

    const stripe = (await import("./lib/stripe")).default;
    
    try {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerData.stripeCustomerId,
        type: 'card',
      });

      // Get default payment method
      const customer = await stripe.customers.retrieve(customerData.stripeCustomerId);
      const defaultPaymentMethodId = typeof customer !== 'string' && 
        customer.invoice_settings?.default_payment_method;

      const formattedMethods = paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type,
        card: pm.card,
        isDefault: pm.id === defaultPaymentMethodId,
      }));

      return {
        success: true,
        paymentMethods: formattedMethods,
      };
    } catch (error) {
      console.error("Error fetching payment methods:", error);
      throw new ConvexError("Failed to fetch payment methods");
    }
  },
});

/**
 * Delete a payment method
 */
export const deletePaymentMethod = action({
  args: {
    paymentMethodId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    const stripe = (await import("./lib/stripe")).default;
    
    try {
      // Detach payment method from customer
      await stripe.paymentMethods.detach(args.paymentMethodId);

      return {
        success: true,
        message: "Payment method removed successfully",
      };
    } catch (error) {
      console.error("Error deleting payment method:", error);
      throw new ConvexError("Failed to remove payment method");
    }
  },
});

/**
 * Set default payment method
 */
export const setDefaultPaymentMethod = action({
  args: {
    paymentMethodId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    const customerData = await ctx.runQuery(internal.stripeCustomers.getUserStripeCustomer, {
      userId: user._id,
    });

    if (!customerData?.stripeCustomerId) {
      throw new ConvexError("No Stripe customer found");
    }

    const stripe = (await import("./lib/stripe")).default;
    
    try {
      await stripe.customers.update(customerData.stripeCustomerId, {
        invoice_settings: {
          default_payment_method: args.paymentMethodId,
        },
      });

      return {
        success: true,
        message: "Default payment method updated successfully",
      };
    } catch (error) {
      console.error("Error setting default payment method:", error);
      throw new ConvexError("Failed to update default payment method");
    }
  },
});

/**
 * Internal query to get user's Stripe customer ID
 */
export const getUserStripeCustomer = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    return user ? {
      stripeCustomerId: user.stripeCustomerId,
      email: user.email,
      name: user.name,
    } : null;
  },
});

/**
 * Internal mutation to update user's Stripe customer ID
 */
export const updateUserStripeCustomer = mutation({
  args: {
    userId: v.id("users"),
    stripeCustomerId: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      stripeCustomerId: args.stripeCustomerId,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Export internal functions
export const internal = {
  stripeCustomers: {
    getUserStripeCustomer,
    updateUserStripeCustomer,
  },
};
