# HauteVault Seller Application Management System

This document describes the comprehensive seller application management system for HauteVault, including application submission, review processes, profile management, and administrative controls.

## Overview

The seller application system provides a secure, comprehensive workflow for onboarding sellers to the HauteVault luxury marketplace. It includes document validation, encryption for sensitive data, audit logging, and administrative controls for managing seller accounts.

## Core Features

### ✅ Application Submission
- **Comprehensive validation** of business information
- **Document upload and verification** with file type/size validation
- **Bank details encryption** for secure storage
- **Automatic email notifications** to applicants and admins
- **Audit logging** for compliance and tracking

### ✅ Administrative Review
- **Status management** (pending, under review, approved, rejected)
- **Admin notes and rejection reasons** for clear communication
- **Commission rate configuration** per seller
- **Bulk operations** for efficient processing
- **Email notifications** for status changes

### ✅ Profile Management
- **Self-service updates** for approved sellers
- **Re-approval workflow** for major changes
- **Document management** (add/remove verification documents)
- **Bank details updates** with encryption
- **Activity tracking** and audit logs

### ✅ Account Controls
- **Seller suspension** (temporary or permanent)
- **Product hiding** during suspension
- **Notification system** for account changes
- **Administrative oversight** and logging

## Mutations Reference

### 1. submitSellerApplication

Submit a comprehensive seller application with validation and document upload.

```typescript
await ctx.runMutation(api.sellerApplications.submitSellerApplication, {
  businessName: "Luxury Boutique LLC",
  businessType: "LLC",
  taxId: "12-3456789",
  address: {
    street: "123 Fashion Ave",
    city: "New York",
    state: "NY",
    zipCode: "10001",
    country: "USA",
  },
  phone: "******-0123",
  bankDetails: {
    accountHolderName: "Luxury Boutique LLC",
    bankName: "Chase Bank",
    accountNumber: "****************", // Will be encrypted
    routingNumber: "*********",
    accountType: "checking",
  },
  verificationDocuments: [
    {
      type: "business_license",
      storageId: "storage_id_1",
      fileName: "business_license.pdf",
      fileSize: 2048000,
      uploadedAt: Date.now(),
    },
  ],
  productCategories: ["clothing", "accessories"],
  hasBusinessLicense: true,
  agreeToTerms: true,
});
```

**Features:**
- Validates existing applications to prevent duplicates
- Encrypts sensitive bank account information
- Validates document file sizes and types
- Sends confirmation email to applicant
- Creates audit log entry
- Updates user type to "seller"

### 2. updateApplicationStatus (Admin Only)

Update the status of seller applications with proper notifications.

```typescript
await ctx.runMutation(api.sellerApplications.updateApplicationStatus, {
  profileId: "seller_profile_id",
  status: "approved", // "approved" | "rejected" | "under_review" | "pending"
  adminNotes: "All documents verified successfully",
  commissionRate: 0.08, // 8% commission for approved sellers
  sendNotification: true,
});
```

**Features:**
- Admin-only access with role validation
- Automatic email notifications based on status
- Commission rate configuration for approved sellers
- Comprehensive audit logging
- User type management (revert to consumer if rejected)

### 3. updateSellerProfile

Update seller profile information with re-approval workflow for major changes.

```typescript
await ctx.runMutation(api.sellerApplications.updateSellerProfile, {
  businessName: "Updated Business Name",
  phone: "******-9999",
  bankDetails: {
    accountHolderName: "Updated Name",
    bankName: "New Bank",
    accountNumber: "****************",
    routingNumber: "*********",
    accountType: "checking",
  },
});
```

**Features:**
- Self-service updates for sellers
- Admin override capabilities
- Automatic re-approval for major changes
- Bank details encryption
- Validation prevents updates during review
- Comprehensive audit logging

### 4. suspendSeller (Admin Only)

Suspend seller accounts with automatic product hiding and notifications.

```typescript
await ctx.runMutation(api.sellerApplications.suspendSeller, {
  profileId: "seller_profile_id",
  reason: "Multiple customer complaints",
  suspensionType: "temporary", // "temporary" | "permanent"
  suspensionDuration: 30, // Days for temporary suspension
  sendNotification: true,
});
```

**Features:**
- Temporary or permanent suspension options
- Automatic hiding of all active products
- Email notification to suspended seller
- Comprehensive audit logging
- Admin-only access with role validation

## Document Management

### Upload Process

```typescript
// 1. Generate upload URL
const uploadUrl = await ctx.runMutation(api.sellerApplications.generateDocumentUploadUrl);

// 2. Upload file to Convex storage
const response = await fetch(uploadUrl, {
  method: "POST",
  headers: { "Content-Type": file.type },
  body: file,
});
const { storageId } = await response.json();

// 3. Add document to profile
await ctx.runMutation(api.sellerApplications.addVerificationDocument, {
  document: {
    type: "business_license",
    storageId,
    fileName: file.name,
    fileSize: file.size,
  },
});
```

### Document Types

- **business_license**: Business registration documents
- **tax_document**: Tax ID verification, tax returns
- **identity_document**: Driver's license, passport
- **bank_statement**: Bank account verification
- **other**: Additional supporting documents

### Validation Rules

- **File Size**: Maximum 10MB per document
- **File Types**: PDF, JPEG, PNG, WebP images
- **Required Documents**: At least one verification document
- **Storage Validation**: Verify files exist in Convex storage

## Security Features

### Data Encryption

```typescript
// Bank account numbers are masked for storage
const encryptedBankDetails = {
  ...bankDetails,
  accountNumber: `***${bankDetails.accountNumber.slice(-4)}`, // Only last 4 digits stored
};
```

### Access Control

- **Role-based permissions**: Admin-only operations clearly defined
- **Profile ownership**: Users can only update their own profiles
- **Admin override**: Admins can update any seller profile
- **Suspension controls**: Only admins can suspend accounts

### Audit Logging

All operations generate comprehensive audit logs:

```typescript
await ctx.db.insert("analytics", {
  eventType: "seller_application_submitted",
  userId: user._id,
  timestamp: Date.now(),
  metadata: {
    source: profileId,
    category: "seller_management",
  },
});
```

## Email Notifications

### Application Received
- Sent to applicant upon submission
- Includes application ID and estimated review time
- Confirms receipt of all documents

### Status Updates
- **Approved**: Welcome email with next steps
- **Rejected**: Explanation with improvement suggestions
- **Under Review**: Progress update (optional)

### Account Actions
- **Suspension**: Notification with reason and duration
- **Profile Changes**: Confirmation of major updates

## Administrative Features

### Application Statistics

```typescript
const stats = await ctx.runQuery(api.sellerApplications.getApplicationStatistics, {
  timeRange: "30d", // "7d" | "30d" | "90d" | "1y"
});

// Returns:
// - Total applications in period
// - Status breakdown (pending, approved, rejected)
// - Approval rate percentage
// - Average processing time
```

### Bulk Operations

- **Status updates**: Process multiple applications
- **Document review**: Batch document validation
- **Commission rates**: Update rates for multiple sellers
- **Suspension management**: Bulk account actions

## Error Handling

### Validation Errors

```typescript
try {
  await ctx.runMutation(api.sellerApplications.submitSellerApplication, data);
} catch (error) {
  switch (error.message) {
    case "You already have a pending seller application":
      // Handle duplicate application
      break;
    case "Invalid account number":
      // Handle bank validation error
      break;
    case "At least one verification document is required":
      // Handle missing documents
      break;
  }
}
```

### Authorization Errors

- **Admin-only operations**: Clear error messages for unauthorized access
- **Profile ownership**: Validation for profile update permissions
- **Suspension requirements**: Proper role checking for account actions

## Integration Examples

### React Component Usage

```typescript
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";

function SellerApplicationForm() {
  const submitApplication = useMutation(api.sellerApplications.submitSellerApplication);
  
  const handleSubmit = async (formData) => {
    try {
      const result = await submitApplication(formData);
      showSuccess(result.message);
    } catch (error) {
      showError(error.message);
    }
  };
  
  return <ApplicationForm onSubmit={handleSubmit} />;
}
```

### Admin Dashboard

```typescript
function AdminApplicationReview() {
  const updateStatus = useMutation(api.sellerApplications.updateApplicationStatus);
  const stats = useQuery(api.sellerApplications.getApplicationStatistics);
  
  const handleApprove = async (profileId) => {
    await updateStatus({
      profileId,
      status: "approved",
      commissionRate: 0.08,
      adminNotes: "Application approved after document review",
    });
  };
  
  return (
    <div>
      <ApplicationStats stats={stats} />
      <ApplicationList onApprove={handleApprove} />
    </div>
  );
}
```

## Best Practices

### 1. Document Validation

```typescript
// Always validate documents before submission
const validateDocument = (file) => {
  if (file.size > 10 * 1024 * 1024) {
    throw new Error("File too large");
  }
  
  const allowedTypes = ["application/pdf", "image/jpeg", "image/png"];
  if (!allowedTypes.includes(file.type)) {
    throw new Error("Invalid file type");
  }
};
```

### 2. Secure Bank Details

```typescript
// Never log or expose full bank account numbers
const logBankUpdate = (bankDetails) => {
  console.log(`Bank updated: ${bankDetails.bankName} ***${bankDetails.accountNumber.slice(-4)}`);
};
```

### 3. Status Change Notifications

```typescript
// Always notify users of important status changes
await updateApplicationStatus({
  profileId,
  status: "approved",
  sendNotification: true, // Ensure notifications are sent
});
```

### 4. Audit Trail Maintenance

```typescript
// Log all administrative actions
const logAdminAction = async (action, details) => {
  await ctx.db.insert("analytics", {
    eventType: `admin_${action}`,
    userId: adminId,
    timestamp: Date.now(),
    metadata: {
      ...details,
      category: "admin_action",
    },
  });
};
```

## File Structure

```
packages/backend/convex/
├── sellerApplications.ts          # Core application mutations
├── sellerApplications.test.ts     # Usage examples and tests
└── SELLER_APPLICATIONS.md         # This documentation
```

The seller application management system provides a comprehensive, secure foundation for onboarding and managing sellers in the HauteVault luxury marketplace platform.
