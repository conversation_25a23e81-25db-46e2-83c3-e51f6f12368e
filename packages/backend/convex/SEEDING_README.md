# Database Seeding Guide

This guide explains how to seed your Convex database with sample data for testing and development.

## Available Seed Functions

### 1. `seedCategories`
Creates the basic category structure for the marketplace:
- Clothing
- Sneakers  
- Shoes
- Handbags
- Accessories
- Jewelry
- Watches
- Sunglasses
- Collectibles
- Home & Decor
- Beauty
- Tech & Gadgets
- Luggage & Travel
- Kids
- Sports & Outdoors
- Art
- Vintage

### 2. `seedProducts`
Creates 10 sample luxury products across different categories:
- **Chanel Classic Flap Bag** - Black Caviar ($8,500)
- **Rolex Submariner Date** - Stainless Steel ($12,500)
- **Nike Air Jordan 1** - Chicago ($2,800)
- **Cartier Love Bracelet** - 18K Yellow Gold ($7,200)
- **Supreme Box Logo Hoodie** - Black ($1,200)
- **<PERSON> 55** - Monogram Canvas ($3,200)
- **Ray-Ban Aviator Classic** - Gold Frame ($180)
- **Pokemon Charizard 1st Edition** - PSA 10 ($45,000)
- **<PERSON><PERSON>irkin 30** - <PERSON><PERSON>ther ($18,000)
- **Adidas Yeezy Boost 350 V2** - Zebra ($450)

### 3. `getFreeStockImageUrls`
Provides free stock image URLs from Unsplash for each product. These can be used to:
- Download images and upload to Convex storage
- Update the `products.images` field with storage IDs
- Provide visual representation for products

### 4. `testSeeding`
Query function to verify that seeding was successful and see what was created.

### 5. `clearAllData`
**⚠️ Use with caution!** Clears all data from the database.

## How to Use

### Step 1: Seed Categories
First, run the categories seeding function:
```typescript
// In your Convex dashboard or via API call
await convex.mutation("seedCategories")();
```

### Step 2: Seed Products
After categories are created, seed the products:
```typescript
// In your Convex dashboard or via API call
await convex.mutation("seedProducts")();
```

### Step 3: Get Free Stock Images
Get URLs for free stock images to use with your products:
```typescript
// In your Convex dashboard or via API call
const imageUrls = await convex.query("getFreeStockImageUrls")();
console.log(imageUrls);
```

### Step 4: Verify Seeding
Check that everything was created successfully:
```typescript
// In your Convex dashboard or via API call
const result = await convex.query("testSeeding")();
console.log(result);
```

## Adding Images to Products

### Option 1: Use Free Stock Images (Recommended for Development)
1. Call `getFreeStockImageUrls()` to get image URLs
2. Download the images from the URLs
3. Upload them to Convex storage using your storage upload function
4. Update the `products.images` field with the storage IDs

### Option 2: Upload Your Own Images
1. Use your existing image upload functionality
2. Store the storage IDs in the `products.images` array
3. Set the first image ID as `primaryImageId`

## Important Notes

1. **Order Matters**: Always seed categories before products, as products reference category slugs.

2. **Seller ID**: The products are currently hardcoded to use seller ID `j57f3232xwfgzj0xhf005qpr597nq4x6`. Make sure this user exists in your database.

3. **Images**: The products currently have empty image arrays. Use `getFreeStockImageUrls()` to get free stock images, or upload your own images to Convex storage.

4. **One-Time Use**: These seed functions check if data already exists and will skip seeding if products/categories are already present.

5. **Development Only**: These seed functions are intended for development and testing environments.

## Customization

To customize the seed data:
1. Modify the `sampleProducts` array in `seedProducts`
2. Add new categories in `seedCategories`
3. Update the seller ID if needed
4. Add real image storage IDs using the free stock image URLs

## Troubleshooting

- **"Products already exist"**: Clear data first using `clearAllData`
- **"Categories already exist"**: Categories are already seeded
- **Type errors**: Ensure all required fields match the schema exactly
- **Seller not found**: Verify the seller ID exists in your users table
- **No images**: Use `getFreeStockImageUrls()` to get free stock images
