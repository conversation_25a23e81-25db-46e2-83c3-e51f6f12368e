import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Users table - Core user information with enhanced fields
  users: defineTable({
    email: v.string(),
    name: v.string(),
    userType: v.union(v.literal("consumer"), v.literal("seller"), v.literal("admin")),
    subscriptionStatus: v.union(v.literal("active"), v.literal("inactive"), v.literal("trial")),
    subscriptionPlan: v.optional(v.union(
      v.literal("basic"),
      v.literal("premium"),
      v.literal("enterprise")
    )),
    subscriptionExpiresAt: v.optional(v.number()),
    profileImage: v.optional(v.string()),
    phone: v.optional(v.string()),
    isVerified: v.optional(v.boolean()),
    lastLoginAt: v.optional(v.number()),
    stripeCustomerId: v.optional(v.string()),
    updatedAt: v.number(),
  })
    .index("by_email", ["email"])
    .index("by_userType", ["userType"])
    .index("by_subscriptionStatus", ["subscriptionStatus"]),



  // Enhanced products table with comprehensive luxury marketplace features
  products: defineTable({
    sellerId: v.id("users"), // Changed to reference users directly

    // Customer-facing information
    title: v.string(),
    description: v.string(),
    price: v.number(),
    category: v.union(
      v.literal("clothing"),
      v.literal("sneakers"),
      v.literal("collectibles"),
      v.literal("accessories"),
      v.literal("handbags"),
      v.literal("jewelry"),
      v.literal("watches"),
      v.literal("sunglasses")
    ),
    subcategory: v.optional(v.string()),
    brand: v.string(),
    model: v.optional(v.string()),
    condition: v.union(
      v.literal("new"),
      v.literal("like_new"),
      v.literal("excellent"),
      v.literal("very_good"),
      v.literal("good"),
      v.literal("fair")
    ),
    images: v.array(v.id("_storage")), // Storage IDs that get converted to URLs in queries
    primaryImageId: v.optional(v.string()),
    estimatedDelivery: v.optional(v.number()),
    
    // Enhanced source information for luxury marketplace
    sourceInfo: v.optional(v.object({
      source: v.string(),
      costPaid: v.optional(v.number()),
      paymentMethod: v.optional(v.string()),
      purchaseDate: v.optional(v.number()),
      receipt: v.optional(v.string()),
    })),
    
    // Product details
    originalPrice: v.optional(v.number()),
    sku: v.optional(v.string()),
    size: v.optional(v.string()),
    color: v.optional(v.string()),
    material: v.optional(v.string()),
    year: v.optional(v.number()),
    
    // Consignment information
    ownershipType: v.union(
      v.literal("owned"),
      v.literal("consigned")
    ),
    consignmentInfo: v.optional(v.object({
      consignorId: v.optional(v.id("users")), // Reference to the product owner (for consigned items)
      consignorName: v.optional(v.string()),
      consignorEmail: v.optional(v.string()),
      consignorPhone: v.optional(v.string()),
      commissionRate: v.optional(v.number()), // Percentage that seller takes (e.g., 0.3 for 30%)
      agreementDate: v.optional(v.number()),
      agreementDuration: v.optional(v.number()), // Duration in days
      minimumPrice: v.optional(v.number()), // Minimum price the consignor will accept
      specialTerms: v.optional(v.string()), // Any special terms or notes
    })),
    
    // Inventory
    status: v.union(
      v.literal("draft"),
      v.literal("active"),
      v.literal("sold"),
      v.literal("reserved"),
      v.literal("archived"),
      v.literal("sold_offline"),
      v.literal("on_hold")
    ),
    quantity: v.number(),
    
    // Auto-save drafts
    isDraft: v.boolean(),
    lastSavedAt: v.number(),
    
    // Metadata
    updatedAt: v.number(),
    publishedAt: v.optional(v.number()),
    
    // SEO
    metaTitle: v.optional(v.string()),
    metaDescription: v.optional(v.string()),
    tags: v.array(v.string()),
    
    // Shipping
    weight: v.optional(v.number()),
    dimensions: v.optional(v.object({
      length: v.number(),
      width: v.number(),
      height: v.number(),
    })),
    shippingCost: v.optional(v.number()),
    
    // Analytics
    views: v.number(),
    favorites: v.number(),
    
    // Reviews and ratings
    rating: v.optional(v.number()),
    reviewCount: v.optional(v.number()),
  })
    .index("by_sellerId", ["sellerId"])
    .index("by_category", ["category"])
    .index("by_brand", ["brand"])
    .index("by_status", ["status"])
    .index("by_price", ["price"])
    .index("by_views", ["views"])
    .index("by_ownershipType", ["ownershipType"])
    .index("by_category_status", ["category", "status"])
    .index("by_brand_status", ["brand", "status"])
    .index("by_seller_status", ["sellerId", "status"])
    .index("by_seller_ownership", ["sellerId", "ownershipType"])
    .index("by_rating", ["rating"])
    .searchIndex("search_title", {
      searchField: "title",
      filterFields: ["category", "status", "sellerId"],
    }),

  userSearchHistory: defineTable({
    userId: v.id("users"),
    searchQuery: v.string(),
    timestamp: v.number(),
    resultCount: v.optional(v.number()),
    category: v.optional(v.string()),
    brand: v.optional(v.string()),
  })
    .index("by_userId", ["userId"])
    .index("by_userId_timestamp", ["userId", "timestamp"])
    .index("by_userId_query", ["userId", "searchQuery"]),

  searchEvents: defineTable({
    query: v.string(),
    userId: v.optional(v.id("users")),
    sessionId: v.optional(v.string()),
    timestamp: v.number(),
    resultCount: v.number(),
    source: v.union(v.literal("search_bar"), v.literal("search_popover"), v.literal("search_page")),
    category: v.optional(v.string()),
    brand: v.optional(v.string()),
    clickThroughRate: v.optional(v.number()),
    searchDuration: v.optional(v.number()),
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_query", ["query"])
    .index("by_category", ["category"])
    .index("by_brand", ["brand"])
    .index("by_userId", ["userId"])
    .index("by_query_timestamp", ["query", "timestamp"]),

  // Orders table - Purchase transactions
  orders: defineTable({
    productId: v.id("products"),
    buyerId: v.id("users"),
    sellerId: v.id("users"),
    orderNumber: v.string(),
    orderStatus: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("payment_pending"),
      v.literal("paid"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled"),
      v.literal("refunded"),
      v.literal("disputed")
    ),
    totalAmount: v.number(),
    subtotal: v.number(),
    tax: v.optional(v.number()),
    shippingCost: v.optional(v.number()),
    platformFee: v.optional(v.number()),
    sellerEarnings: v.optional(v.number()),
    shippingAddress: v.object({
      name: v.string(),
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
      phone: v.optional(v.string()),
    }),
    billingAddress: v.optional(v.object({
      name: v.string(),
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    })),
    paymentMethod: v.string(),
    paymentIntentId: v.optional(v.string()),
    stripePaymentIntentId: v.optional(v.string()),
    stripeTransferId: v.optional(v.string()),
    stripeApplicationFee: v.optional(v.number()),
    trackingNumber: v.optional(v.string()),
    carrier: v.optional(v.string()),
    estimatedDelivery: v.optional(v.number()),
    orderDate: v.number(),
    confirmedDate: v.optional(v.number()),
    paidDate: v.optional(v.number()),
    shippedDate: v.optional(v.number()),
    deliveredDate: v.optional(v.number()),
    cancelledDate: v.optional(v.number()),
    cancellationReason: v.optional(v.string()),
    notes: v.optional(v.string()),
    updatedAt: v.number(),
  })
    .index("by_productId", ["productId"])
    .index("by_buyerId", ["buyerId"])
    .index("by_sellerId", ["sellerId"])
    .index("by_orderStatus", ["orderStatus"])
    .index("by_orderDate", ["orderDate"])
    .index("by_orderNumber", ["orderNumber"])
    .index("by_buyer_status", ["buyerId", "orderStatus"])
    .index("by_seller_status", ["sellerId", "orderStatus"]),

  // Offers table - Product offers from buyers to sellers
  offers: defineTable({
    productId: v.id("products"),
    buyerId: v.id("users"),
    sellerId: v.id("users"),
    offerAmount: v.number(),
    message: v.optional(v.string()),
    status: v.union(
      v.literal("pending"),
      v.literal("accepted"),
      v.literal("declined"),
      v.literal("countered"),
      v.literal("expired"),
      v.literal("withdrawn")
    ),
    counterOffer: v.optional(v.number()),
    counterMessage: v.optional(v.string()),
    sellerResponse: v.optional(v.object({
      message: v.string(),
      responseType: v.union(
        v.literal("info"),
        v.literal("negotiate"),
        v.literal("other")
      ),
      timestamp: v.number(),
    })),
    expiresAt: v.number(), // 7 days from creation
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_productId", ["productId"])
    .index("by_buyerId", ["buyerId"])
    .index("by_sellerId", ["sellerId"])
    .index("by_status", ["status"])
    .index("by_seller_status", ["sellerId", "status"])
    .index("by_buyer_status", ["buyerId", "status"])
    .index("by_expiresAt", ["expiresAt"]),

  // Offer Messages table - Communication between buyers and sellers during negotiations
  offerMessages: defineTable({
    offerId: v.id("offers"),
    senderId: v.id("users"),
    receiverId: v.id("users"),
    message: v.string(),
    messageType: v.union(
      v.literal("offer"),
      v.literal("counter"),
      v.literal("negotiation"),
      v.literal("question"),
      v.literal("info"),
      v.literal("other")
    ),
    isRead: v.boolean(),
    readAt: v.optional(v.number()),
    updatedAt: v.number(),
  })
    .index("by_offerId", ["offerId"])
    .index("by_senderId", ["senderId"])
    .index("by_receiverId", ["receiverId"])
    .index("by_sender_receiver", ["senderId", "receiverId"])
    .index("by_unread", ["receiverId", "isRead"]),

  offlineSales: defineTable({
    sellerId: v.id("users"),
    productId: v.id("products"),
    
    // Client information
    clientName: v.string(),
    clientEmail: v.string(),
    clientPhone: v.optional(v.string()),
    clientAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    
    // Sale details
    salePrice: v.number(),
    paymentMethod: v.union(
      v.literal("cash"),
      v.literal("check"),
      v.literal("bank_transfer"),
      v.literal("credit_card"),
      v.literal("paypal"),
      v.literal("other")
    ),
    
    // Sale metadata
    saleDate: v.number(),
    status: v.union(
      v.literal("pending_payment"),
      v.literal("paid"),
      v.literal("cancelled")
    ),
    notes: v.optional(v.string()),
  })
    .index("sellerId", ["sellerId"])
    .index("productId", ["productId"])
    .index("by_status", ["status"]),

  // Enhanced invoices table for offline sales invoicing
  invoices: defineTable({
    sellerId: v.id("users"),
    offlineSaleId: v.id("offlineSales"),
    productId: v.optional(v.id("products")),
    invoiceNumber: v.string(),
    clientName: v.string(),
    clientEmail: v.string(),
    clientPhone: v.optional(v.string()),
    clientAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    itemDescription: v.string(),
    salePrice: v.number(),
    tax: v.optional(v.number()),
    totalAmount: v.number(),
    paymentMethod: v.string(),
    paymentTerms: v.optional(v.string()),
    status: v.union(
      v.literal("draft"),
      v.literal("sent"),
      v.literal("paid"),
      v.literal("overdue"),
      v.literal("cancelled")
    ),
    dueDate: v.optional(v.number()),
    paidDate: v.optional(v.number()),
    notes: v.optional(v.string()),
    sentAt: v.optional(v.number()),
    updatedAt: v.number(),
    emailsSent: v.array(v.string()),
    pdfStorageId: v.optional(v.id("_storage")),
  })
    .index("by_sellerId", ["sellerId"])
    .index("by_invoiceNumber", ["invoiceNumber"])
    .index("by_status", ["status"])
    .index("by_clientEmail", ["clientEmail"])
    .index("by_seller_status", ["sellerId", "status"]),

  // Analytics events for tracking user interactions
  analytics: defineTable({
    eventType: v.string(),
    userId: v.optional(v.id("users")),
    sellerId: v.optional(v.id("users")), // Changed to reference users table
    productId: v.optional(v.id("products")),
    timestamp: v.number(),
    metadata: v.optional(v.object({
      signupMethod: v.optional(v.string()),
      revenue: v.optional(v.number()),
      category: v.optional(v.string()),
      source: v.optional(v.string()),
      reviewId: v.optional(v.id("reviews")),
      sellerId: v.optional(v.id("users")),
      productId: v.optional(v.id("products")),
      orderId: v.optional(v.id("orders")),
      rating: v.optional(v.number()),
      review: v.optional(v.string()),
      isVerifiedPurchase: v.optional(v.boolean()),
      reviewDate: v.optional(v.number()),
      paymentMethod: v.optional(v.string()),
      verificationMethod: v.optional(v.string()),
      verifiedPurchaseId: v.optional(v.id("verifiedPurchases")),
    })),
  })
    .index("by_eventType", ["eventType"])
    .index("by_userId", ["userId"])
    .index("by_sellerId", ["sellerId"])
    .index("by_timestamp", ["timestamp"]),

  // Enhanced seller profiles table with comprehensive verification and business details
  sellerProfiles: defineTable({
    userId: v.id("users"),
    businessName: v.optional(v.string()),
    businessType: v.optional(v.string()),
    taxId: v.optional(v.string()),
    address: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    phone: v.string(),
    verificationStatus: v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("under_review")
    ),
    verificationDocuments: v.optional(v.array(v.object({
      type: v.string(),
      url: v.string(),
      uploadedAt: v.number(),
    }))),
    bankDetails: v.optional(v.object({
      accountHolderName: v.string(),
      bankName: v.string(),
      accountNumber: v.string(), // Should be encrypted in production
      routingNumber: v.string(),
      accountType: v.union(v.literal("checking"), v.literal("savings")),
    })),

    // Stripe Connect integration
    stripeConnectAccountId: v.optional(v.string()),
    stripeAccountStatus: v.optional(v.union(
      v.literal("pending"),
      v.literal("restricted"),
      v.literal("enabled"),
      v.literal("disabled")
    )),
    stripeOnboardingComplete: v.optional(v.boolean()),
    stripeChargesEnabled: v.optional(v.boolean()),
    stripePayoutsEnabled: v.optional(v.boolean()),
    stripeDetailsSubmitted: v.optional(v.boolean()),
    stripeRequirements: v.optional(v.array(v.string())),
    payoutSchedule: v.optional(v.object({
      interval: v.union(v.literal("daily"), v.literal("weekly"), v.literal("monthly")),
      weeklyAnchor: v.optional(v.string()),
      monthlyAnchor: v.optional(v.number()),
    })),
    commissionRate: v.optional(v.number()),
    totalSales: v.optional(v.number()),
    totalEarnings: v.optional(v.number()),
    rating: v.optional(v.number()),
    reviewCount: v.optional(v.number()),
    applicationDate: v.number(),
    approvedDate: v.optional(v.number()),
    rejectedDate: v.optional(v.number()),
    rejectionReason: v.optional(v.string()),
    notes: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    updatedAt: v.number(),
  })
    .index("by_userId", ["userId"])
    .index("by_verificationStatus", ["verificationStatus"])
    .index("by_applicationDate", ["applicationDate"])
    .index("by_rating", ["rating"]),

  favorites: defineTable({
    userId: v.id("users"),
    productId: v.id("products"),
  })
    .index("by_user_id", ["userId"])
    .index("by_product_id", ["productId"]),

  cart: defineTable({
    userId: v.id("users"),
    productId: v.id("products"),
    quantity: v.number(),
  })
    .index("by_user_id", ["userId"])
    .index("by_product_id", ["productId"])
    .index("by_user_product", ["userId", "productId"]),

  categories: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    slug: v.string(),
    isActive: v.boolean(),
    parentCategoryId: v.optional(v.id("categories")),
    sortOrder: v.optional(v.number()),
    imageUrl: v.optional(v.string()),
  })
    .index("slug", ["slug"])
    .index("isActive", ["isActive"])
    .index("parentCategoryId", ["parentCategoryId"])
    .index("sortOrder", ["sortOrder"]),

  sellerApplications: defineTable({
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    dateOfBirth: v.string(),
    businessName: v.string(),
    businessType: v.union(
      v.literal("individual"),
      v.literal("llc"),
      v.literal("corporation"),
      v.literal("partnership")
    ),
    taxId: v.string(),
    businessAddress: v.string(),
    businessCity: v.string(),
    businessState: v.string(),
    businessZip: v.string(),
    businessCountry: v.string(),
    yearsExperience: v.string(),
    previousPlatforms: v.array(v.string()),
    monthlyVolume: v.string(),
    specialties: v.array(v.string()),
    termsAccepted: v.boolean(),
    privacyAccepted: v.boolean(),
    status: v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("requires_info")
    ),
    submittedAt: v.number(),
    reviewedAt: v.optional(v.number()),
    notes: v.optional(v.string()),
    rejectionReason: v.optional(v.string()),
  })
    .index("email", ["email"])
    .index("status", ["status"]),

  // User preferences for notifications, display, privacy, etc.
  userPreferences: defineTable({
    userId: v.id("users"),
    
    // Email notifications
    emailNotifications: v.object({
      orderUpdates: v.boolean(),
      promotions: v.boolean(),
      newProducts: v.boolean(),
      priceAlerts: v.boolean(),
      newsletter: v.boolean(),
      sellerUpdates: v.boolean(),
      communityActivity: v.boolean(),
    }),
    
    // Push notifications
    pushNotifications: v.object({
      enabled: v.boolean(),
      orderStatus: v.boolean(),
      bidUpdates: v.boolean(),
      wishlistAlerts: v.boolean(),
      marketing: v.boolean(),
    }),
    
    // SMS notifications
    smsNotifications: v.object({
      enabled: v.boolean(),
      orderUpdates: v.boolean(),
      security: v.boolean(),
    }),
    
    // Display preferences
    display: v.object({
      theme: v.union(v.literal("light"), v.literal("dark"), v.literal("system")),
      currency: v.string(),
      language: v.string(),
      timezone: v.string(),
    }),
    
    // Privacy settings
    privacy: v.object({
      profileVisibility: v.union(v.literal("public"), v.literal("private")),
      showPurchaseHistory: v.boolean(),
      showWishlist: v.boolean(),
      allowMessages: v.boolean(),
    }),
    
    // Marketplace preferences
    marketplace: v.object({
      defaultView: v.union(v.literal("grid"), v.literal("list")),
      itemsPerPage: v.number(),
      autoplayVideos: v.boolean(),
      showSimilarItems: v.boolean(),
      enableRecommendations: v.boolean(),
    }),
    
    // Search & discovery
    search: v.object({
      saveSearchHistory: v.boolean(),
      personalizedResults: v.boolean(),
      trendingCategories: v.boolean(),
      locationBasedResults: v.boolean(),
    }),
    updatedAt: v.number(),
  })
    .index("by_userId", ["userId"]),

  // Extended user profile information
  userProfiles: defineTable({
    userId: v.id("users"),
    
    // Personal information
    bio: v.optional(v.string()),
    location: v.optional(v.string()),
    dateOfBirth: v.optional(v.number()), // Unix timestamp
    gender: v.optional(v.union(
      v.literal("male"),
      v.literal("female"),
      v.literal("non-binary"),
      v.literal("prefer-not-to-say")
    )),
    
    // Social media
    website: v.optional(v.string()),
    instagram: v.optional(v.string()),
    twitter: v.optional(v.string()),
    linkedin: v.optional(v.string()),
    facebook: v.optional(v.string()),
    
    // Professional information
    occupation: v.optional(v.string()),
    company: v.optional(v.string()),
    interests: v.optional(v.array(v.string())),
    
    // Preferences
    preferredCategories: v.optional(v.array(v.string())),
    preferredBrands: v.optional(v.array(v.string())),
    
    updatedAt: v.number(),
  })
    .index("by_userId", ["userId"]),

  // Shipping addresses for users
  shippingAddresses: defineTable({
    userId: v.id("users"),
    type: v.union(v.literal("home"), v.literal("work"), v.literal("other")),
    firstName: v.string(),
    lastName: v.string(),
    company: v.optional(v.string()),
    street: v.string(),
    street2: v.optional(v.string()),
    city: v.string(),
    state: v.string(),
    zip: v.string(),
    country: v.string(),
    phone: v.optional(v.string()),
    isDefault: v.boolean(),
    deliveryInstructions: v.optional(v.string()),
    updatedAt: v.number(),
  })
    .index("by_userId", ["userId"]),

  reviews: defineTable({
    userId: v.id("users"),
    productId: v.id("products"),
    sellerId: v.id("users"),
    orderId: v.id("orders"), // Link to the verified transaction
    rating: v.number(),
    review: v.string(),
    isVerifiedPurchase: v.boolean(), // Always true for reviews from orders
    reviewDate: v.number(),
  })
    .index("by_userId", ["userId"])
    .index("by_productId", ["productId"])
    .index("by_sellerId", ["sellerId"])
    .index("by_orderId", ["orderId"])
    .index("by_rating", ["rating"])
    .index("by_verified_purchase", ["isVerifiedPurchase"]),

  // Messages between consumers and sellers
  messages: defineTable({
    conversationId: v.string(), // Generated ID to group messages between two users
    senderId: v.id("users"),
    recipientId: v.id("users"),
    productId: v.optional(v.id("products")), // Optional product context
    content: v.string(),
    messageType: v.union(
      v.literal("text"),
      v.literal("image"),
      v.literal("offer")
    ),
    isRead: v.boolean(),
    isSystemMessage: v.optional(v.boolean()),
    metadata: v.optional(v.object({
      offerAmount: v.optional(v.number()),
      imageUrl: v.optional(v.string()),
    })),
    updatedAt: v.number(),
  })
    .index("by_conversationId", ["conversationId"])
    .index("by_senderId", ["senderId"])
    .index("by_recipientId", ["recipientId"])
    .index("by_productId", ["productId"])
    .index("by_recipient_read", ["recipientId", "isRead"]),

  // Support tickets for customer service
  supportTickets: defineTable({
    ticketNumber: v.string(), // Auto-generated ticket number
    userId: v.id("users"), // Customer who created the ticket
    assignedAdminId: v.optional(v.id("users")), // Admin assigned to handle the ticket
    subject: v.string(),
    description: v.string(),
    priority: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    ),
    status: v.union(
      v.literal("open"),
      v.literal("in_progress"),
      v.literal("waiting_for_customer"),
      v.literal("resolved"),
      v.literal("closed")
    ),
    category: v.union(
      v.literal("general"),
      v.literal("technical"),
      v.literal("billing"),
      v.literal("order_issue"),
      v.literal("product_issue"),
      v.literal("seller_issue"),
      v.literal("refund"),
      v.literal("other")
    ),
    tags: v.array(v.string()),
    attachments: v.optional(v.array(v.object({
      name: v.string(),
      url: v.string(),
      size: v.number(),
      type: v.string(),
    }))),
    updatedAt: v.number(),
    resolvedAt: v.optional(v.number()),
    closedAt: v.optional(v.number()),
  })
    .index("by_ticketNumber", ["ticketNumber"])
    .index("by_userId", ["userId"])
    .index("by_assignedAdminId", ["assignedAdminId"])
    .index("by_status", ["status"])
    .index("by_priority", ["priority"])
    .index("by_category", ["category"])
    .index("by_status_priority", ["status", "priority"])
    .index("by_admin_status", ["assignedAdminId", "status"]),

  // Support messages within tickets
  supportMessages: defineTable({
    ticketId: v.id("supportTickets"),
    senderId: v.id("users"),
    senderType: v.union(v.literal("customer"), v.literal("admin")),
    content: v.string(),
    messageType: v.union(
      v.literal("text"),
      v.literal("image"),
      v.literal("file"),
      v.literal("system")
    ),
    isInternal: v.optional(v.boolean()), // For admin-only notes
    attachments: v.optional(v.array(v.object({
      name: v.string(),
      url: v.string(),
      size: v.number(),
      type: v.string(),
    }))),
    updatedAt: v.number(),
  })
    .index("by_ticketId", ["ticketId"])
    .index("by_senderId", ["senderId"])
    .index("by_ticketId_senderId", ["ticketId", "senderId"]),

  // Admin roles and permissions
  adminRoles: defineTable({
    userId: v.id("users"),
    role: v.union(
      v.literal("super_admin"),
      v.literal("admin"),
      v.literal("support_agent"),
      v.literal("moderator")
    ),
    permissions: v.array(v.string()), // Array of permission strings
    isActive: v.boolean(),
    assignedCategories: v.optional(v.array(v.string())), // Categories this admin can handle
    maxConcurrentTickets: v.optional(v.number()), // Max tickets this admin can handle simultaneously
    updatedAt: v.number(),
  })
    .index("by_userId", ["userId"])
    .index("by_role", ["role"])
    .index("by_isActive", ["isActive"]),

  // Conversations metadata for easy listing
  conversations: defineTable({
    conversationId: v.string(),
    participantIds: v.array(v.id("users")), // Always 2 participants
    productId: v.optional(v.id("products")),
    lastMessageId: v.optional(v.id("messages")),
    lastMessageAt: v.number(),
    lastMessagePreview: v.string(),
    // Store unread counts for each participant
    participant1Id: v.id("users"),
    participant2Id: v.id("users"),
    participant1UnreadCount: v.number(),
    participant2UnreadCount: v.number(),
    isActive: v.boolean(),
    updatedAt: v.number(),
  })
    .index("by_conversationId", ["conversationId"])
    .index("by_participant1", ["participant1Id"])
    .index("by_participant2", ["participant2Id"])
    .index("by_lastMessageAt", ["lastMessageAt"])
    .index("by_productId", ["productId"]),

  disputes: defineTable({
    orderId: v.id("orders"),
    disputeType: v.union(v.literal("refund"), v.literal("return")),
    disputeStatus: v.union(v.literal("pending"), v.literal("resolved"), v.literal("escalated")),
    disputeDate: v.number(),
    disputeReason: v.string(),
    disputeDocuments: v.array(v.id("_storage")),
    disputeNotes: v.optional(v.string()),
    disputeResolution: v.optional(v.string()),
  })
    .index("by_orderId", ["orderId"])
    .index("by_disputeStatus", ["disputeStatus"]),

  // Shipping carriers and tracking information
  carriers: defineTable({
    name: v.string(),
    trackingUrl: v.optional(v.string()),
  })
    .index("by_name", ["name"]),

  // Verified purchases for review system
  verifiedPurchases: defineTable({
    orderId: v.id("orders"),
    buyerId: v.id("users"),
    sellerId: v.id("users"),
    productId: v.id("products"),
    purchaseDate: v.number(),
    purchaseAmount: v.number(),
    paymentMethod: v.string(),
    isVerified: v.boolean(),
    verificationMethod: v.union(
      v.literal("payment_confirmation"),
      v.literal("admin_verification"),
      v.literal("manual_verification"),
      v.literal("offline_sale")
    ),
    verificationNotes: v.optional(v.string()),
    verifiedBy: v.optional(v.id("users")), // Who verified this purchase
    verifiedAt: v.optional(v.number()),
    updatedAt: v.number(),
  })
    .index("by_orderId", ["orderId"])
    .index("by_buyerId", ["buyerId"])
    .index("by_sellerId", ["sellerId"])
    .index("by_productId", ["productId"])
    .index("by_verificationMethod", ["verificationMethod"])
    .index("by_purchaseDate", ["purchaseDate"]),

  // Suppliers table - Track all suppliers for sellers
  suppliers: defineTable({
    sellerId: v.id("users"), // The seller who is tracking this supplier
    supplierName: v.string(),
    supplierEmail: v.optional(v.string()),
    supplierPhone: v.optional(v.string()),
    supplierAddress: v.optional(v.string()),
    contactPerson: v.optional(v.string()),
    paymentTerms: v.optional(v.string()), // e.g., "Net 30", "COD", etc.
    creditLimit: v.optional(v.number()),
    notes: v.optional(v.string()),
    isActive: v.boolean(),
    relationshipStartDate: v.number(),
    lastContactDate: v.optional(v.number()),
    tags: v.array(v.string()), // e.g., ["wholesale", "consignment", "reliable"]
    updatedAt: v.number(),
  })
    .index("by_sellerId", ["sellerId"])
    .index("by_sellerId_active", ["sellerId", "isActive"])
    .index("by_supplierName", ["supplierName"]),

  // Supplier transactions table - Track all financial interactions
  supplierTransactions: defineTable({
    supplierId: v.id("suppliers"),
    sellerId: v.id("users"),
    productId: v.optional(v.id("products")), // Optional for non-product transactions
    transactionType: v.union(
      v.literal("purchase"),
      v.literal("payment"),
      v.literal("refund"),
      v.literal("consignment_fee"),
      v.literal("adjustment")
    ),
    amount: v.number(),
    currency: v.string(),
    transactionDate: v.number(),
    dueDate: v.optional(v.number()),
    status: v.union(
      v.literal("pending"),
      v.literal("completed"),
      v.literal("overdue"),
      v.literal("cancelled")
    ),
    paymentMethod: v.optional(v.string()),
    referenceNumber: v.optional(v.string()),
    description: v.string(),
    notes: v.optional(v.string()),
    updatedAt: v.number(),
  })
    .index("by_supplierId", ["supplierId"])
    .index("by_sellerId", ["sellerId"])
    .index("by_status", ["status"])
    .index("by_transactionDate", ["transactionDate"])
    .index("by_supplier_status", ["supplierId", "status"]),

  // Stripe events and webhooks tracking
  stripeEvents: defineTable({
    stripeEventId: v.string(), // Stripe event ID for idempotency
    eventType: v.string(), // e.g., "payment_intent.succeeded", "account.updated"
    accountId: v.optional(v.string()), // Stripe Connect account ID if applicable
    paymentIntentId: v.optional(v.string()),
    transferId: v.optional(v.string()),
    orderId: v.optional(v.id("orders")),
    userId: v.optional(v.id("users")),
    processed: v.boolean(),
    processingError: v.optional(v.string()),
    eventData: v.optional(v.any()), // Store the full event data for debugging
    receivedAt: v.number(),
    processedAt: v.optional(v.number()),
  })
    .index("by_stripeEventId", ["stripeEventId"])
    .index("by_eventType", ["eventType"])
    .index("by_accountId", ["accountId"])
    .index("by_paymentIntentId", ["paymentIntentId"])
    .index("by_orderId", ["orderId"])
    .index("by_processed", ["processed"])
    .index("by_receivedAt", ["receivedAt"]),

  // Stripe Connect account onboarding tracking
  stripeOnboarding: defineTable({
    userId: v.id("users"),
    stripeAccountId: v.string(),
    onboardingUrl: v.optional(v.string()),
    onboardingExpiresAt: v.optional(v.number()),
    status: v.union(
      v.literal("pending"),
      v.literal("in_progress"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("expired")
    ),
    completedAt: v.optional(v.number()),
    failureReason: v.optional(v.string()),
    refreshCount: v.number(),
    lastRefreshAt: v.optional(v.number()),
    updatedAt: v.number(),
  })
    .index("by_userId", ["userId"])
    .index("by_stripeAccountId", ["stripeAccountId"])
    .index("by_status", ["status"])
    .index("by_onboardingExpiresAt", ["onboardingExpiresAt"]),
});
