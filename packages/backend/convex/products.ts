import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const getFilteredProducts = query({
  args: {
    categories: v.optional(v.array(v.string())),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    brands: v.optional(v.array(v.string())),
    conditions: v.optional(v.array(v.string())),
    searchQuery: v.optional(v.string()),
    sortBy: v.optional(v.union(
      v.literal("newest"),
      v.literal("price_low"),
      v.literal("price_high"),
      v.literal("popular")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("products").withIndex("by_status", (q) => 
      q.eq("status", "active")
    );

    let products = await query.collect();

    // Get categories to resolve category names
    const categories = await ctx.db.query("categories").collect();
    const categoryMap = new Map(categories.map(c => [c._id, c.name]));

    // Transform products to include category name and images as strings
    const transformedProducts = products.map(product => ({
      ...product,
      category: categoryMap.get(product.category as any) || "Unknown",
      images: product.images.map(id => id.toString()),
    }));

    // Apply filters
    let filteredProducts = transformedProducts;
    if (args.categories && args.categories.length > 0) {
      filteredProducts = filteredProducts.filter(p => args.categories!.includes(p.category));
    }

    if (args.brands && args.brands.length > 0) {
      filteredProducts = filteredProducts.filter(p => args.brands!.includes(p.brand));
    }

    if (args.conditions && args.conditions.length > 0) {
      filteredProducts = filteredProducts.filter(p => args.conditions!.includes(p.condition));
    }

    if (args.minPrice !== undefined) {
      filteredProducts = filteredProducts.filter(p => p.price >= args.minPrice!);
    }

    if (args.maxPrice !== undefined) {
      filteredProducts = filteredProducts.filter(p => p.price <= args.maxPrice!);
    }

    if (args.searchQuery) {
      const searchLower = args.searchQuery.toLowerCase();
      filteredProducts = filteredProducts.filter(p => 
        p.title.toLowerCase().includes(searchLower) ||
        p.brand.toLowerCase().includes(searchLower) ||
        p.description.toLowerCase().includes(searchLower)
      );
    }

    // Sort products
    switch (args.sortBy) {
      case "price_low":
        filteredProducts.sort((a, b) => a.price - b.price);
        break;
      case "price_high":
        filteredProducts.sort((a, b) => b.price - a.price);
        break;
      case "popular":
        filteredProducts.sort((a, b) => (b.views || 0) - (a.views || 0));
        break;
      case "newest":
      default:
        filteredProducts.sort((a, b) => b._creationTime - a._creationTime);
        break;
    }

    // Apply limit
    if (args.limit) {
      filteredProducts = filteredProducts.slice(0, args.limit);
    }

    return filteredProducts;
  },
});

export const getFilterOptions = query({
  args: {},
  handler: async (ctx) => {
    const products = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect(); 

    const allCategories = await ctx.db.query("categories").collect();
    const categories = allCategories.map(c => c.name).sort();
    const brands = [...new Set(products.map(p => p.brand))].sort();
    const conditions = [...new Set(products.map(p => p.condition))];
    
    const prices = products.map(p => p.price);
    const priceRange: [number, number] = [
      Math.min(...prices, 0),
      Math.max(...prices, 10000)
    ];

    return {
      categories,
      brands,
      conditions,
      priceRange,
    };
  },
});

export const getProductById = query({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    
    if (!product || product.status !== "active") {
      return null;
    }

    // Note: View counting removed from query (should be in mutation)

    // Get seller info
    const seller = await ctx.db.get(product.sellerId);
    
    // Get seller profile for additional info
    const sellerProfile = seller ? await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", seller._id))
      .first() : null;

    // Get seller's total sales count
    const totalSales = seller ? await ctx.db
      .query("products")
      .withIndex("by_seller_status", (q) => q.eq("sellerId", seller._id).eq("status", "sold"))
      .collect() : [];

    // Get category name from categories table using the category slug
    const category = await ctx.db
      .query("categories")
      .withIndex("slug", (q) => q.eq("slug", product.category))
      .first();
    
    // Convert storage IDs to URLs
    const imageUrls = await Promise.all(
      product.images
        .filter(imageId => imageId !== "")
        .map(async (imageId) => {
          const url = await ctx.storage.getUrl(imageId);
          return url;
        })
    );
    const validImageUrls = imageUrls.filter(url => url !== null) as string[];
    
    return {
      ...product,
      category: category?.name || product.category,
      images: validImageUrls,
      seller: seller ? {
        _id: seller._id,
        name: seller.name,
        userType: seller.userType,
        rating: sellerProfile?.rating || 0,
        reviewCount: sellerProfile?.reviewCount || 0,
        totalSales: totalSales.length,
        memberSince: seller._creationTime,
        isVerified: sellerProfile?.verificationStatus === "approved",
        responseTime: "within 24 hours", // Default response time
        location: sellerProfile?.address ? `${sellerProfile.address.city}, ${sellerProfile.address.state}` : "Location not specified",
        businessName: sellerProfile?.businessName,
        verificationStatus: sellerProfile?.verificationStatus || "pending",
      } : null,
    };
  },
});

export const getFeaturedProducts = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const products = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .order("desc")
      .take(args.limit || 8);

    return products;
  },
});

export const getRelatedProducts = query({
  args: { 
    productId: v.id("products"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const currentProduct = await ctx.db.get(args.productId);
    if (!currentProduct) return [];

    const limit = args.limit || 4;

    // Get products from the same category, excluding the current product
    const relatedProducts = await ctx.db
      .query("products")
      .withIndex("by_category_status", (q) => 
        q.eq("category", currentProduct.category).eq("status", "active")
      )
      .filter((q) => q.neq(q.field("_id"), args.productId))
      .take(limit);

    // Convert storage IDs to URLs and format products
    const productsWithUrls = await Promise.all(
      relatedProducts.map(async (product) => {
        const imageUrls = await Promise.all(
          product.images
            .filter(imageId => imageId !== "")
            .slice(0, 1) // Only get first image for related products
            .map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url;
            })
        );
        const validImageUrls = imageUrls.filter(url => url !== null) as string[];

        return {
          _id: product._id,
          title: product.title,
          brand: product.brand,
          price: product.price,
          condition: product.condition,
          images: validImageUrls,
          category: product.category,
        };
      })
    );

    return productsWithUrls;
  },
});

export const incrementProductViews = mutation({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // Increment the view count
    await ctx.db.patch(args.productId, {
      views: (product.views || 0) + 1,
    });

    return { success: true };
  },
});

export const getRecentlyViewed = query({
  args: { userId: v.id("users"), limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    // This would require a separate analytics table to track views
    // For now, return empty array
    return [];
  },
});