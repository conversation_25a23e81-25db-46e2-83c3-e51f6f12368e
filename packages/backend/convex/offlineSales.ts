import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { requireAuth } from "./lib/auth_utils";

export const createOfflineSale = mutation({
  args: {
    productId: v.id("products"),
    clientName: v.string(),
    clientEmail: v.string(),
    clientPhone: v.optional(v.string()),
    clientAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    salePrice: v.number(),
    paymentMethod: v.union(
      v.literal("cash"),
      v.literal("check"),
      v.literal("bank_transfer"),
      v.literal("credit_card"),
      v.literal("paypal"),
      v.literal("other")
    ),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Get the product and verify ownership
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    const now = Date.now();

    // Create offline sale record
    const offlineSaleId = await ctx.db.insert("offlineSales", {
      sellerId: product.sellerId,
      productId: args.productId,
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      salePrice: args.salePrice,
      paymentMethod: args.paymentMethod,
      saleDate: now,
      status: "pending_payment",
      notes: args.notes,
    });

    // Update product status to on_hold
    await ctx.db.patch(args.productId, {
      status: "on_hold",
      updatedAt: now,
    });

    // Create invoice automatically
    const invoiceId = await ctx.db.insert("invoices", {
      sellerId: product.sellerId,
      offlineSaleId,
      productId: args.productId,
      invoiceNumber: `HV-${Date.now()}-${offlineSaleId.slice(-6)}`,
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      itemDescription: `${product.title} - ${product.brand}`,
      salePrice: args.salePrice,
      tax: 0,
      totalAmount: args.salePrice,
      paymentMethod: args.paymentMethod,
      status: "draft",
      dueDate: now + (30 * 24 * 60 * 60 * 1000), // 30 days from now
      paymentTerms: "Payment due within 30 days",
      notes: args.notes,
      emailsSent: [],
      updatedAt: now,
    });

    return { offlineSaleId, invoiceId };
  },
});

export const getOfflineSales = query({
  args: {},
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    const sales = await ctx.db
      .query("offlineSales")
      .withIndex("sellerId", (q: any) => q.eq("sellerId", user._id))
      .order("desc")
      .collect();

    // Get product details for each sale
    const salesWithProducts = await Promise.all(
      sales.map(async (sale) => {
        const product = await ctx.db.get(sale.productId);
        return {
          ...sale,
          product,
        };
      })
    );

    return salesWithProducts;
  },
});

export const getOfflineSale = query({
  args: {
    saleId: v.id("offlineSales"),
  },
  handler: async (ctx, args) => {
    const sale = await ctx.db.get(args.saleId);
    if (!sale) {
      return null;
    }

    const product = await ctx.db.get(sale.productId);
    const seller = await ctx.db.get(sale.sellerId);

    return {
      ...sale,
      product,
      seller,
    };
  },
});

export const markOfflineSaleAsPaid = mutation({
  args: {
    saleId: v.id("offlineSales"),
  },
  handler: async (ctx, args) => {
    const sale = await ctx.db.get(args.saleId);
    if (!sale) {
      throw new Error("Offline sale not found");
    }

    const now = Date.now();

    // Update offline sale status to paid
    await ctx.db.patch(args.saleId, {
      status: "paid",
    });

    // Update product status to sold and remove from marketplace
    await ctx.db.patch(sale.productId, {
      status: "sold",
      updatedAt: now,
    });

    // Update invoice status to paid
    const invoice = await ctx.db
      .query("invoices")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", sale.sellerId))
      .filter((q) => q.eq(q.field("offlineSaleId"), args.saleId))
      .first();

    if (invoice) {
      await ctx.db.patch(invoice._id, {
        status: "paid",
        paidDate: now,
        updatedAt: now,
      });
    }

    return { success: true };
  },
});