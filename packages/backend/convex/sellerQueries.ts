import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import { requireAuth, requireAdmin, requireRole } from "./lib/auth_utils";
import { calculateSellerRevenue, calculateSellerCostsAndProfit } from "./lib/utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get seller profile data with proper authorization and data filtering
 */
export const getSellerProfile = query({
  args: {
    sellerId: v.optional(v.id("users")), // Optional for self-lookup
    includePrivateData: v.optional(v.boolean()), // Admin can see sensitive data
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Determine target seller
    const targetSellerId = args.sellerId || currentUser._id;
    
    // Authorization check
    const canViewPrivateData = currentUser.userType === "admin" || 
                              (targetSellerId === currentUser._id && args.includePrivateData);
    
    // Get seller user
    const seller = await ctx.db.get(targetSellerId);
    if (!seller) {
      throw new ConvexError("Seller not found");
    }

    if (seller.userType !== "seller") {
      throw new ConvexError("User is not a seller");
    }

    // Get seller profile
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", targetSellerId))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    // Get seller's products count
    const products = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
      .collect();

    const activeProducts = products.filter(p => p.status === "active").length;
    const soldProducts = products.filter(p => p.status === "sold").length;
    const totalProducts = products.length;

    // Get seller's orders for revenue calculation
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
      .filter((q) => q.eq(q.field("orderStatus"), "delivered"))
      .collect();

    const totalRevenue = calculateSellerRevenue(orders);
    const totalOrders = orders.length;

    // Calculate average order value
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentOrders = orders.filter(order => order.orderDate >= thirtyDaysAgo);
    const monthlyRevenue = calculateSellerRevenue(recentOrders);

    // Prepare response data
    const publicData = {
      userId: seller._id,
      name: seller.name,
      email: canViewPrivateData ? seller.email : undefined,
      profileImage: seller.profileImage,
      businessName: sellerProfile.businessName,
      businessType: sellerProfile.businessType,
      verificationStatus: sellerProfile.verificationStatus,
      isActive: sellerProfile.isActive,
      rating: sellerProfile.rating,
      reviewCount: sellerProfile.reviewCount,
      joinedDate: seller._creationTime,
      lastActive: seller.lastLoginAt,
      
      // Public metrics
      metrics: {
        totalProducts,
        activeProducts,
        soldProducts,
        totalOrders,
        averageOrderValue: Math.round(averageOrderValue * 100) / 100,
        monthlyRevenue: Math.round(monthlyRevenue * 100) / 100,
        totalRevenue: canViewPrivateData ? Math.round(totalRevenue * 100) / 100 : undefined,
      },
    };

    // Add private data for authorized users
    if (canViewPrivateData) {
      return {
        ...publicData,
        phone: sellerProfile.phone,
        address: sellerProfile.address,
        taxId: sellerProfile.taxId,
        bankDetails: sellerProfile.bankDetails, // Already encrypted
        verificationDocuments: sellerProfile.verificationDocuments,
        commissionRate: sellerProfile.commissionRate,
        applicationDate: sellerProfile.applicationDate,
        approvedDate: sellerProfile.approvedDate,
        rejectedDate: sellerProfile.rejectedDate,
        rejectionReason: sellerProfile.rejectionReason,
        notes: sellerProfile.notes,
        subscriptionStatus: seller.subscriptionStatus,
        subscriptionPlan: seller.subscriptionPlan,
        subscriptionExpiresAt: seller.subscriptionExpiresAt,
      };
    }

    return publicData;
  },
});

/**
 * Get pending seller applications (admin only)
 */
export const getPendingApplications = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("all_pending")
    )),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    sortBy: v.optional(v.union(
      v.literal("applicationDate"),
      v.literal("businessName"),
      v.literal("expectedVolume")
    )),
    sortOrder: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
  },
  handler: async (ctx, args) => {
    // Only admins can view pending applications
    await requireAdmin(ctx);

    const status = args.status || "pending";
    const limit = Math.min(args.limit || 50, 100); // Max 100 results
    const offset = args.offset || 0;
    const sortBy = args.sortBy || "applicationDate";
    const sortOrder = args.sortOrder || "desc";

    // Get seller profiles based on status
    let profiles;
    if (status === "all_pending") {
      profiles = await ctx.db
        .query("sellerProfiles")
        .filter((q) => q.or(
          q.eq(q.field("verificationStatus"), "pending"),
          q.eq(q.field("verificationStatus"), "under_review")
        ))
        .collect();
    } else {
      profiles = await ctx.db
        .query("sellerProfiles")
        .filter((q) => q.eq(q.field("verificationStatus"), status))
        .collect();
    }

    // Enrich with user data and metrics
    const enrichedApplications = await Promise.all(
      profiles.map(async (profile) => {
        const user = await ctx.db.get(profile.userId);
        if (!user) return null;

        // Calculate application age
        const applicationAge = Math.floor((Date.now() - profile.applicationDate) / (24 * 60 * 60 * 1000));

        // Get document count
        const documentCount = profile.verificationDocuments?.length || 0;

        // Check if user has active subscription
        const hasActiveSubscription = user.subscriptionStatus === "active" && 
          (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

        return {
          profileId: profile._id,
          userId: user._id,
          businessName: profile.businessName || "Not provided",
          businessType: profile.businessType,
          applicantName: user.name,
          applicantEmail: user.email,
          phone: profile.phone,
          verificationStatus: profile.verificationStatus,
          applicationDate: profile.applicationDate,
          applicationAge,
          documentCount,
          hasActiveSubscription,
          subscriptionPlan: user.subscriptionPlan,
          address: profile.address,
          notes: profile.notes,
          priority: calculateApplicationPriority(profile, applicationAge, documentCount),
        };
      })
    );

    // Filter out null results
    const validApplications = enrichedApplications.filter(app => app !== null);

    // Sort applications
    validApplications.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case "applicationDate":
          comparison = a.applicationDate - b.applicationDate;
          break;
        case "businessName":
          comparison = a.businessName.localeCompare(b.businessName);
          break;
        default:
          comparison = a.applicationDate - b.applicationDate;
      }
      
      return sortOrder === "desc" ? -comparison : comparison;
    });

    // Apply pagination
    const paginatedApplications = validApplications.slice(offset, offset + limit);

    // Calculate summary statistics
    const summary = {
      total: validApplications.length,
      pending: validApplications.filter(app => app.verificationStatus === "pending").length,
      underReview: validApplications.filter(app => app.verificationStatus === "under_review").length,
      averageProcessingTime: calculateAverageProcessingTime(validApplications),
      highPriority: validApplications.filter(app => app.priority === "high").length,
      withActiveSubscription: validApplications.filter(app => app.hasActiveSubscription).length,
    };

    return {
      applications: paginatedApplications,
      summary,
      pagination: {
        total: validApplications.length,
        limit,
        offset,
        hasMore: offset + limit < validApplications.length,
      },
      filters: {
        status,
        sortBy,
        sortOrder,
      },
    };
  },
});

/**
 * Get seller performance metrics
 */
export const getSellerMetrics = query({
  args: {
    sellerId: v.optional(v.id("users")),
    timeRange: v.optional(v.union(
      v.literal("7d"),
      v.literal("30d"),
      v.literal("90d"),
      v.literal("1y"),
      v.literal("all")
    )),
    includeComparisons: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Determine target seller
    const targetSellerId = args.sellerId || currentUser._id;
    
    // Authorization check
    if (args.sellerId && args.sellerId !== currentUser._id && currentUser.userType !== "admin") {
      throw new ConvexError("Unauthorized: Can only view own metrics or admin access required");
    }

    // Check if user is a seller
    if (currentUser.userType !== "seller" && currentUser.userType !== "admin") {
      throw new ConvexError("Seller access required. Please apply to become a seller first.");
    }

    // Get seller
    const seller = await ctx.db.get(targetSellerId);
    if (!seller || seller.userType !== "seller") {
      throw new ConvexError("Seller not found");
    }

    const timeRange = args.timeRange || "30d";
    const includeComparisons = args.includeComparisons ?? false;
    
    const now = Date.now();
    const timeRanges = {
      "7d": 7 * 24 * 60 * 60 * 1000,
      "30d": 30 * 24 * 60 * 60 * 1000,
      "90d": 90 * 24 * 60 * 60 * 1000,
      "1y": 365 * 24 * 60 * 60 * 1000,
      "all": 0,
    };
    
    const startTime = timeRange === "all" ? 0 : now - timeRanges[timeRange];

    // Get seller's products
    const allProducts = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
      .collect();

    const productsInRange = timeRange === "all" 
      ? allProducts 
      : allProducts.filter(p => p._creationTime >= startTime);

    // Get seller's orders
    const allOrders = await ctx.db
      .query("orders")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
      .collect();

    const ordersInRange = timeRange === "all"
      ? allOrders
      : allOrders.filter(o => o.orderDate >= startTime);

    // Calculate metrics
    const totalProducts = allProducts.length;
    const activeProducts = allProducts.filter(p => p.status === "active").length;
    const soldProducts = allProducts.filter(p => p.status === "sold").length;
    const newProductsInRange = productsInRange.length;

    const totalOrders = ordersInRange.length;
    const completedOrders = ordersInRange.filter(o => o.orderStatus === "delivered").length;
    const pendingOrders = ordersInRange.filter(o => 
      ["pending", "confirmed", "shipped"].includes(o.orderStatus)
    ).length;

    const totalRevenue = calculateSellerRevenue(
      ordersInRange.filter(o => o.orderStatus === "delivered")
    );

    const averageOrderValue = completedOrders > 0 ? totalRevenue / completedOrders : 0;

    // Calculate conversion rate
    const productViews = allProducts.reduce((sum, product) => sum + (product.views || 0), 0);
    const conversionRate = productViews > 0 ? (completedOrders / productViews) * 100 : 0;

    // Get top performing products
    const topProducts = allProducts
      .filter(p => p.status === "sold" || p.status === "active")
      .sort((a, b) => (b.views || 0) - (a.views || 0))
      .slice(0, 5)
      .map(p => ({
        id: p._id,
        title: p.title,
        price: p.price,
        views: p.views || 0,
        status: p.status,
      }));

    let comparisons = null;
    if (includeComparisons && timeRange !== "all") {
      // Calculate previous period for comparison
      const previousStartTime = startTime - timeRanges[timeRange];
      const previousOrders = allOrders.filter(o => 
        o.orderDate >= previousStartTime && o.orderDate < startTime
      );
      
      const previousRevenue = calculateSellerRevenue(
        previousOrders.filter(o => o.orderStatus === "delivered")
      );

      const revenueChange = previousRevenue > 0 
        ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 
        : totalRevenue > 0 ? 100 : 0;

      const orderChange = previousOrders.length > 0
        ? ((totalOrders - previousOrders.length) / previousOrders.length) * 100
        : totalOrders > 0 ? 100 : 0;

      comparisons = {
        revenueChange: Math.round(revenueChange * 100) / 100,
        orderChange: Math.round(orderChange * 100) / 100,
        previousPeriodRevenue: Math.round(previousRevenue * 100) / 100,
        previousPeriodOrders: previousOrders.length,
      };
    }

    return {
      sellerId: targetSellerId,
      timeRange,
      generatedAt: now,
      
      // Product metrics
      products: {
        total: totalProducts,
        active: activeProducts,
        sold: soldProducts,
        newInRange: newProductsInRange,
        topPerforming: topProducts,
      },
      
      // Order metrics
      orders: {
        total: totalOrders,
        completed: completedOrders,
        pending: pendingOrders,
        conversionRate: Math.round(conversionRate * 100) / 100,
      },
      
      // Revenue metrics
      revenue: {
        total: Math.round(totalRevenue * 100) / 100,
        averageOrderValue: Math.round(averageOrderValue * 100) / 100,
        currency: "USD", // Could be configurable
      },
      
      // Performance indicators
      performance: {
        productViews: productViews,
        conversionRate: Math.round(conversionRate * 100) / 100,
        averageOrderValue: Math.round(averageOrderValue * 100) / 100,
        repeatCustomerRate: Math.round(calculateRepeatCustomerRate(ordersInRange) * 100) / 100,
      },
      
      comparisons,
    };
  },
});

/**
 * Get sellers by verification status with search and filtering (admin only)
 */
export const getSellersByStatus = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("all")
    )),
    searchQuery: v.optional(v.string()),
    subscriptionFilter: v.optional(v.union(
      v.literal("active"),
      v.literal("inactive"),
      v.literal("trial"),
      v.literal("all")
    )),
    businessTypeFilter: v.optional(v.string()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    sortBy: v.optional(v.union(
      v.literal("businessName"),
      v.literal("applicationDate"),
      v.literal("revenue"),
      v.literal("products"),
      v.literal("lastActive")
    )),
    sortOrder: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
    includeMetrics: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Only admins can view seller lists
    await requireRole(ctx, ["admin"]);

    const status = args.status || "all";
    const searchQuery = args.searchQuery?.toLowerCase().trim();
    const subscriptionFilter = args.subscriptionFilter || "all";
    const limit = Math.min(args.limit || 50, 200); // Max 200 results
    const offset = args.offset || 0;
    const sortBy = args.sortBy || "applicationDate";
    const sortOrder = args.sortOrder || "desc";
    const includeMetrics = args.includeMetrics ?? false;

    // Get seller profiles based on status
    let profiles;
    if (status === "all") {
      profiles = await ctx.db.query("sellerProfiles").collect();
    } else {
      profiles = await ctx.db
        .query("sellerProfiles")
        .filter((q) => q.eq(q.field("verificationStatus"), status))
        .collect();
    }

    // Enrich with user data and apply filters
    const enrichedSellers = await Promise.all(
      profiles.map(async (profile) => {
        const user = await ctx.db.get(profile.userId);
        if (!user) return null;

        // Apply subscription filter
        if (subscriptionFilter !== "all" && user.subscriptionStatus !== subscriptionFilter) {
          return null;
        }

        // Apply business type filter
        if (args.businessTypeFilter && profile.businessType !== args.businessTypeFilter) {
          return null;
        }

        // Apply search filter
        if (searchQuery) {
          const searchableText = [
            profile.businessName || "",
            user.name || "",
            user.email || "",
            profile.businessType || "",
          ].join(" ").toLowerCase();

          if (!searchableText.includes(searchQuery)) {
            return null;
          }
        }

        // Calculate basic metrics if requested
        let metrics = null;
        if (includeMetrics) {
          const products = await ctx.db
            .query("products")
            .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id))
            .collect();

          const orders = await ctx.db
            .query("orders")
            .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id))
            .filter((q) => q.eq(q.field("orderStatus"), "delivered"))
            .collect();

          const totalRevenue = calculateSellerRevenue(orders);

          metrics = {
            totalProducts: products.length,
            activeProducts: products.filter(p => p.status === "active").length,
            totalOrders: orders.length,
            totalRevenue: Math.round(totalRevenue * 100) / 100,
          };
        }

        // Check subscription status
        const isSubscriptionActive = user.subscriptionStatus === "active" &&
          (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

        return {
          profileId: profile._id,
          userId: user._id,
          businessName: profile.businessName || "Not provided",
          businessType: profile.businessType,
          sellerName: user.name,
          email: user.email,
          phone: profile.phone,
          verificationStatus: profile.verificationStatus,
          isActive: profile.isActive,
          applicationDate: profile.applicationDate,
          approvedDate: profile.approvedDate,
          rejectedDate: profile.rejectedDate,
          lastActive: user.lastLoginAt,
          subscriptionStatus: user.subscriptionStatus,
          subscriptionPlan: user.subscriptionPlan,
          isSubscriptionActive,
          commissionRate: profile.commissionRate,
          rating: profile.rating || 0,
          reviewCount: profile.reviewCount || 0,
          address: profile.address,
          metrics,
          tags: generateSellerTags(profile, user, isSubscriptionActive),
        };
      })
    );

    // Filter out null results
    const validSellers = enrichedSellers.filter(seller => seller !== null);

    // Sort sellers
    validSellers.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case "businessName":
          comparison = a.businessName.localeCompare(b.businessName);
          break;
        case "applicationDate":
          comparison = a.applicationDate - b.applicationDate;
          break;
        case "revenue":
          comparison = (a.metrics?.totalRevenue || 0) - (b.metrics?.totalRevenue || 0);
          break;
        case "products":
          comparison = (a.metrics?.totalProducts || 0) - (b.metrics?.totalProducts || 0);
          break;
        case "lastActive":
          comparison = (a.lastActive || 0) - (b.lastActive || 0);
          break;
        default:
          comparison = a.applicationDate - b.applicationDate;
      }

      return sortOrder === "desc" ? -comparison : comparison;
    });

    // Apply pagination
    const paginatedSellers = validSellers.slice(offset, offset + limit);

    // Calculate summary statistics
    const summary = {
      total: validSellers.length,
      byStatus: {
        pending: validSellers.filter(s => s.verificationStatus === "pending").length,
        underReview: validSellers.filter(s => s.verificationStatus === "under_review").length,
        approved: validSellers.filter(s => s.verificationStatus === "approved").length,
        rejected: validSellers.filter(s => s.verificationStatus === "rejected").length,
      },
      bySubscription: {
        active: validSellers.filter(s => s.subscriptionStatus === "active").length,
        inactive: validSellers.filter(s => s.subscriptionStatus === "inactive").length,
        trial: validSellers.filter(s => s.subscriptionStatus === "trial").length,
      },
      activeSubscriptions: validSellers.filter(s => s.isSubscriptionActive).length,
      averageRating: validSellers.length > 0
        ? Math.round((validSellers.reduce((sum, s) => sum + s.rating, 0) / validSellers.length) * 100) / 100
        : 0,
    };

    // Add aggregated metrics if requested
    let aggregatedMetrics = undefined;
    if (includeMetrics) {
      aggregatedMetrics = {
        totalProducts: validSellers.reduce((sum, s) => sum + (s.metrics?.totalProducts || 0), 0),
        totalRevenue: Math.round(validSellers.reduce((sum, s) => sum + (s.metrics?.totalRevenue || 0), 0) * 100) / 100,
        totalOrders: validSellers.reduce((sum, s) => sum + (s.metrics?.totalOrders || 0), 0),
      };
    }

    return {
      sellers: paginatedSellers,
      summary: {
        ...summary,
        aggregatedMetrics,
      },
      pagination: {
        total: validSellers.length,
        limit,
        offset,
        hasMore: offset + limit < validSellers.length,
      },
      filters: {
        status,
        searchQuery: args.searchQuery,
        subscriptionFilter,
        businessTypeFilter: args.businessTypeFilter,
        sortBy,
        sortOrder,
        includeMetrics,
      },
      exportData: generateExportData(paginatedSellers), // For CSV/Excel export
    };
  },
});

/**
 * Get public seller profile and products for marketplace display
 * This is public and doesn't require authentication
 */
export const getPublicSellerProfile = query({
  args: {
    sellerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // No authentication required - this is public data
    
    // Get seller user
    const seller = await ctx.db.get(args.sellerId);
    if (!seller) {
      throw new ConvexError("Seller not found");
    }

    if (seller.userType !== "seller") {
      throw new ConvexError("User is not a seller");
    }

    // Get seller profile
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.sellerId))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    // Get basic stats
    const products = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", args.sellerId))
      .collect();

    const activeProducts = products.filter(p => p.status === "active").length;
    const soldProducts = products.filter(p => p.status === "sold").length;

    // Only return public data
    return {
      _id: seller._id,
      name: seller.name,
      profileImage: seller.profileImage,
      businessName: sellerProfile.businessName,
      businessType: sellerProfile.businessType,
      verificationStatus: sellerProfile.verificationStatus,
      isActive: sellerProfile.isActive,
      rating: sellerProfile.rating || 0,
      reviewCount: sellerProfile.reviewCount || 0,
      joinedDate: seller._creationTime,
      lastActive: seller.lastLoginAt,
      stats: {
        activeProducts,
        soldProducts,
        totalProducts: products.length,
      },
    };
  },
});

/**
 * Get seller's marketplace products for public display
 */
export const getSellerMarketplaceProducts = query({
  args: {
    sellerId: v.id("users"),
    category: v.optional(v.string()),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    brands: v.optional(v.array(v.string())),
    conditions: v.optional(v.array(v.string())),
    searchQuery: v.optional(v.string()),
    sortBy: v.optional(v.union(
      v.literal("newest"),
      v.literal("price_low"),
      v.literal("price_high"),
      v.literal("popular")
    )),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // No authentication required - this is public marketplace data
    
    const limit = Math.min(args.limit || 24, 100);
    const offset = args.offset || 0;
    const sortBy = args.sortBy || "newest";

    // Get seller
    const seller = await ctx.db.get(args.sellerId);
    if (!seller || seller.userType !== "seller") {
      throw new ConvexError("Seller not found");
    }

    // Get all active products for this seller
    let products = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", args.sellerId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    // Apply filters
    if (args.category) {
      products = products.filter(p => p.category === args.category);
    }

    if (args.minPrice !== undefined) {
      products = products.filter(p => p.price >= args.minPrice!);
    }

    if (args.maxPrice !== undefined) {
      products = products.filter(p => p.price <= args.maxPrice!);
    }

    if (args.brands && args.brands.length > 0) {
      products = products.filter(p => p.brand && args.brands!.includes(p.brand));
    }

    if (args.conditions && args.conditions.length > 0) {
      products = products.filter(p => args.conditions!.includes(p.condition));
    }

    if (args.searchQuery) {
      const searchTerm = args.searchQuery.toLowerCase().trim();
      products = products.filter(p => {
        const searchableText = [
          p.title,
          p.description,
          p.brand || "",
          ...(p.tags || [])
        ].join(" ").toLowerCase();
        
        return searchableText.includes(searchTerm);
      });
    }

    // Sort products
    products.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return b.publishedAt! - a.publishedAt!;
        case "price_low":
          return a.price - b.price;
        case "price_high":
          return b.price - a.price;
        case "popular":
          return (b.views || 0) - (a.views || 0);
        default:
          return b.publishedAt! - a.publishedAt!;
      }
    });

    const totalProducts = products.length;

    // Apply pagination
    const paginatedProducts = products.slice(offset, offset + limit);

    // Get seller profile for additional info
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.sellerId))
      .first();

    // Enrich with product data and images
    const enrichedProducts = await Promise.all(
      paginatedProducts.map(async (product) => {
        // Convert storage IDs to URLs
        const imageUrls = await Promise.all(
          product.images
            .filter(imageId => imageId !== "")
            .map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url;
            })
        );
        const validImageUrls = imageUrls.filter(url => url !== null) as string[];

        return {
          _id: product._id,
          title: product.title,
          description: product.description,
          price: product.price,
          condition: product.condition,
          brand: product.brand,
          size: product.size,
          color: product.color,
          material: product.material,
          tags: product.tags,
          images: validImageUrls,
          views: product.views || 0,
          likes: product.favorites || 0,
          publishedAt: product.publishedAt,
          category: {
            name: product.category,
            slug: product.category,
          },
          seller: {
            _id: seller._id,
            name: seller.name,
            businessName: sellerProfile?.businessName,
            rating: sellerProfile?.rating || 0,
            reviewCount: sellerProfile?.reviewCount || 0,
            verificationStatus: sellerProfile?.verificationStatus,
          },
        };
      })
    );

    return {
      products: enrichedProducts,
      pagination: {
        total: totalProducts,
        hasMore: offset + limit < totalProducts,
        nextCursor: offset + limit < totalProducts ? (offset + limit).toString() : null,
      },
    };
  },
});

/**
 * Get filtered seller dashboard data
 */
export const getFilteredSellerDashboard = query({
  args: {
    sellerId: v.optional(v.id("users")),
    chartFilter: v.optional(v.union(v.literal("all"), v.literal("listed"), v.literal("unlisted"))),
    filters: v.optional(v.object({
      productTitle: v.optional(v.string()),
      productSku: v.optional(v.string()),
      category: v.optional(v.string()),
      soldStartDate: v.optional(v.number()),
      soldEndDate: v.optional(v.number()),
      ownershipType: v.optional(v.union(v.literal("all"), v.literal("owned"), v.literal("consigned"))),
      listingStatus: v.optional(v.union(v.literal("all"), v.literal("listed"), v.literal("unlisted"))),
    })),
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    const targetSellerId = args.sellerId || currentUser._id;
    
    // Authorization check
    if (args.sellerId && args.sellerId !== currentUser._id && currentUser.userType !== "admin") {
      throw new ConvexError("Unauthorized");
    }

    // Check if user is a seller
    if (currentUser.userType !== "seller" && currentUser.userType !== "admin") {
      throw new ConvexError("Seller access required. Please apply to become a seller first.");
    }

    // Get seller
    const seller = await ctx.db.get(targetSellerId);
    if (!seller || seller.userType !== "seller") {
      throw new ConvexError("Seller not found");
    }

    const filters = args.filters || {};
    const now = Date.now();

    // Build product query with filters
    let productsQuery = ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId));

    // Get all products first, then filter
    const allProducts = await productsQuery.collect();
    
    // Apply filters to products
    const filteredProducts = allProducts.filter(product => {
      // Filter by title
      if (filters.productTitle && !product.title.toLowerCase().includes(filters.productTitle.toLowerCase())) {
        return false;
      }
      
      // Filter by SKU
      if (filters.productSku && product.sku && !product.sku.toLowerCase().includes(filters.productSku.toLowerCase())) {
        return false;
      }
      
      // Filter by category
      if (filters.category && filters.category !== "all" && product.category !== filters.category) {
        return false;
      }
      
      // Filter by listing status
      if (filters.listingStatus && filters.listingStatus !== "all") {
        if (filters.listingStatus === "listed" && product.status !== "active") {
          return false;
        }
        if (filters.listingStatus === "unlisted" && product.status === "active") {
          return false;
        }
      }
      
      // Note: ownershipType filter would require additional data model changes
      // For now, we'll mock this by randomly assigning ownership types
      
      return true;
    });

    // Get orders within date range if specified
    let ordersQuery = ctx.db
      .query("orders")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId));
    
    const allOrders = await ordersQuery.collect();
    
    // Apply date filters to orders
    const filteredOrders = allOrders.filter(order => {
      if (filters.soldStartDate && order.orderDate < filters.soldStartDate) {
        return false;
      }
      if (filters.soldEndDate && order.orderDate > filters.soldEndDate) {
        return false;
      }
      return true;
    });

    // Calculate metrics from filtered data
    const activeProducts = filteredProducts.filter(p => p.status === "active").length;
    const totalProducts = filteredProducts.length;
    
    const completedOrders = filteredOrders.filter(o => o.orderStatus === "delivered");
    const pendingOrders = filteredOrders.filter(o =>
      ["pending", "confirmed", "shipped"].includes(o.orderStatus)
    ).length;

    const totalRevenue = calculateSellerRevenue(completedOrders);
    
    // Calculate real costs and profit from product cost data
    const { totalCost, grossProfit, profitMargin } = calculateSellerCostsAndProfit(completedOrders, filteredProducts);
    
    // Calculate real inventory value from actual product prices
    const inventoryValue = filteredProducts
      .filter(p => p.status === "active" || p.status === "draft")
      .reduce((sum, product) => sum + (product.price || 0), 0);
    
    // Calculate actual ownership split from product data
    const ownedProducts = filteredProducts.filter(p => 
      (p.ownershipType === "owned" || !p.ownershipType) && 
      (p.status === "active" || p.status === "draft")
    ).length;
    const consignedProducts = filteredProducts.filter(p => 
      p.ownershipType === "consigned" && 
      (p.status === "active" || p.status === "draft")
    ).length;

    // Generate inventory chart data (last 6 months) with chart-specific filtering
    const chartFilter = args.chartFilter || "all";
    const chartFilteredProducts = allProducts.filter(product => {
      // Apply chart-specific listing status filter
      if (chartFilter === "listed" && product.status !== "active") {
        return false;
      }
      if (chartFilter === "unlisted" && product.status === "active") {
        return false;
      }
      return true;
    });
    
    // Calculate ownership counts for chart data based on chart filter
    // Since chartFilteredProducts is already filtered by status based on chartFilter,
    // we don't need to filter by status again
    const chartOwnedProducts = chartFilteredProducts.filter(p => 
      p.ownershipType === "owned" || !p.ownershipType
    ).length;
    const chartConsignedProducts = chartFilteredProducts.filter(p => 
      p.ownershipType === "consigned"
    ).length;
    

    
    const inventoryChartData = generateInventoryChartData(
      chartFilteredProducts, 
      chartOwnedProducts, 
      chartConsignedProducts,
      filters
    );

    return {
      filters: args.filters,
      metrics: {
        totalSales: completedOrders.length,
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        totalCost: Math.round(totalCost * 100) / 100,
        grossProfit: Math.round(grossProfit * 100) / 100,
        averageGrossMargin: totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0,
        averageTransactionValue: completedOrders.length > 0 ? totalRevenue / completedOrders.length : 0,
        inventoryValue,
        activeProducts,
        totalProducts,
        // Use chart-filtered counts to respect the chart filter setting
        ownedProducts: chartOwnedProducts,
        consignedProducts: chartConsignedProducts,
        pendingOrders,
        completedOrders: completedOrders.length,
      },
      chartData: {
        inventory: inventoryChartData,
      },
      products: filteredProducts.slice(0, 10), // Return first 10 for preview
      orders: filteredOrders.slice(0, 10), // Return first 10 for preview
      summary: {
        appliedFilters: Object.keys(filters).filter(key => {
          const value = filters[key as keyof typeof filters];
          return value && value !== "all" && value !== "";
        }).length,
        totalFilteredProducts: filteredProducts.length,
        totalFilteredOrders: filteredOrders.length,
      }
    };
  },
});

/**
 * Helper function to generate inventory chart data
 */
function generateInventoryChartData(products: any[], ownedProducts: number, consignedProducts: number, filters: any = {}) {
  const now = new Date();
  const periods = [];
  
  // Determine if date range is set
  const hasDateRange = filters.soldStartDate || filters.soldEndDate;
  
  if (hasDateRange) {
    // Use date range when filters are applied
    const startDate = filters.soldStartDate ? new Date(filters.soldStartDate) : new Date(now.getTime() - (6 * 30 * 24 * 60 * 60 * 1000)); // 6 months ago
    const endDate = filters.soldEndDate ? new Date(filters.soldEndDate) : now;
    
    // Generate periods based on date range
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000));
    
    if (daysDiff <= 7) {
      // Show daily for week or less
      for (let i = 0; i < Math.min(7, daysDiff); i++) {
        const date = new Date(startDate.getTime() + (i * 24 * 60 * 60 * 1000));
        const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
        const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime();
        const dayEnd = dayStart + (24 * 60 * 60 * 1000) - 1;
        
        // Count products that existed by this day
        const productsExistingByDay = products.filter(p => p._creationTime <= dayEnd);
        
        const dayOwnedProducts = productsExistingByDay.filter(p => 
          p.ownershipType === "owned" || !p.ownershipType
        ).length;
        const dayConsignedProducts = productsExistingByDay.filter(p => 
          p.ownershipType === "consigned"
        ).length;
        
        periods.push({
          month: dayName,
          owned: dayOwnedProducts,
          consigned: dayConsignedProducts,
        });
      }
    } else if (daysDiff <= 31) {
      // Show weekly for month or less
      const weeks = Math.ceil(daysDiff / 7);
      for (let i = 0; i < weeks; i++) {
        const weekStart = new Date(startDate.getTime() + (i * 7 * 24 * 60 * 60 * 1000));
        const weekEnd = new Date(Math.min(weekStart.getTime() + (7 * 24 * 60 * 60 * 1000) - 1, endDate.getTime()));
        const weekName = `Week ${i + 1}`;
        
        // Count products that existed by end of this week
        const productsExistingByWeek = products.filter(p => p._creationTime <= weekEnd.getTime());
        
        const weekOwnedProducts = productsExistingByWeek.filter(p => 
          p.ownershipType === "owned" || !p.ownershipType
        ).length;
        const weekConsignedProducts = productsExistingByWeek.filter(p => 
          p.ownershipType === "consigned"
        ).length;
        
        periods.push({
          month: weekName,
          owned: weekOwnedProducts,
          consigned: weekConsignedProducts,
        });
      }
    } else {
      // Show monthly for longer periods
      const monthsDiff = Math.ceil(daysDiff / 30);
      for (let i = 0; i < monthsDiff; i++) {
        const date = new Date(startDate.getFullYear(), startDate.getMonth() + i, 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0).getTime();
        const monthName = date.toLocaleDateString('en-US', { month: 'short' });
        
        // Count products that existed by end of this month
        const productsExistingByMonth = products.filter(p => p._creationTime <= monthEnd);
        
        const monthOwnedProducts = productsExistingByMonth.filter(p => 
          p.ownershipType === "owned" || !p.ownershipType
        ).length;
        const monthConsignedProducts = productsExistingByMonth.filter(p => 
          p.ownershipType === "consigned"
        ).length;
        
        periods.push({
          month: monthName,
          owned: monthOwnedProducts,
          consigned: monthConsignedProducts,
        });
      }
    }
  } else {
    // Default: Show current week (7 days) but only show inventory from actual creation dates
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime();
      const dayEnd = dayStart + (24 * 60 * 60 * 1000) - 1;
      
      // Count products that existed by this day (created before or on this day)
      const productsExistingByDay = products.filter(p => p._creationTime <= dayEnd);
      
      // Calculate ownership for products that existed by this day
      // Products array is already filtered by chart filter, so just filter by ownership
      const dayOwnedProducts = productsExistingByDay.filter(p => 
        p.ownershipType === "owned" || !p.ownershipType
      ).length;
      const dayConsignedProducts = productsExistingByDay.filter(p => 
        p.ownershipType === "consigned"
      ).length;
      
      periods.push({
        month: dayName,
        owned: dayOwnedProducts,
        consigned: dayConsignedProducts,
      });
    }
  }
  
  return periods;
}

/**
 * Helper function to calculate products for a period with smart scaling
 */
function calculatePeriodProducts(totalProducts: number, progress: number, totalPeriods: number): number {
  if (totalProducts === 0) {
    return 0;
  }
  
  if (totalProducts <= 3) {
    // For small numbers, show at least 1 for recent periods
    if (progress >= 0.7) { // Last 30% of periods
      return totalProducts;
    } else if (progress >= 0.4) { // Middle periods
      return Math.max(0, totalProducts - 1);
    } else { // Early periods
      return Math.max(0, totalProducts - 2);
    }
  } else {
    // Normal scaling for larger numbers
    return Math.max(0, Math.floor(totalProducts * progress));
  }
}

/**
 * Get seller dashboard summary
 */
export const getSellerDashboardSummary = query({
  args: {
    sellerId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);

    // Determine target seller
    const targetSellerId = args.sellerId || currentUser._id;

    // Authorization check
    if (args.sellerId && args.sellerId !== currentUser._id && currentUser.userType !== "admin") {
      throw new ConvexError("Unauthorized");
    }

    // Get seller
    const seller = await ctx.db.get(targetSellerId);
    if (!seller || seller.userType !== "seller") {
      throw new ConvexError("Seller not found");
    }

    // Get seller profile
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", targetSellerId))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);

    // Get products
    const products = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
      .collect();

    // Get recent orders
    const recentOrders = await ctx.db
      .query("orders")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
      .filter((q) => q.gte(q.field("orderDate"), thirtyDaysAgo))
      .collect();

    // Calculate key metrics
    const activeProducts = products.filter(p => p.status === "active").length;
    const pendingOrders = recentOrders.filter(o =>
      ["pending", "confirmed", "shipped"].includes(o.orderStatus)
    ).length;

    const completedOrders = recentOrders.filter(o => o.orderStatus === "delivered");
    const monthlyRevenue = calculateSellerRevenue(completedOrders);

    // Get recent activity
    const recentActivity = await getRecentSellerActivity(ctx, targetSellerId, 10);

    // Check subscription status
    const isSubscriptionActive = seller.subscriptionStatus === "active" &&
      (!seller.subscriptionExpiresAt || seller.subscriptionExpiresAt > now);

    const subscriptionDaysRemaining = seller.subscriptionExpiresAt
      ? Math.max(0, Math.ceil((seller.subscriptionExpiresAt - now) / (1000 * 60 * 60 * 24)))
      : null;

    // Get notifications/alerts
    const alerts = [];

    if (!isSubscriptionActive) {
      alerts.push({
        type: "warning",
        message: "Your subscription has expired. Renew to continue selling.",
        action: "renew_subscription",
      });
    } else if (subscriptionDaysRemaining && subscriptionDaysRemaining <= 7) {
      alerts.push({
        type: "info",
        message: `Your subscription expires in ${subscriptionDaysRemaining} days.`,
        action: "renew_subscription",
      });
    }

    if (sellerProfile.verificationStatus !== "approved") {
      alerts.push({
        type: "warning",
        message: "Complete your seller verification to start selling.",
        action: "complete_verification",
      });
    }

    if (activeProducts === 0) {
      alerts.push({
        type: "info",
        message: "You have no active products. Add your first listing!",
        action: "add_product",
      });
    }

    return {
      seller: {
        name: seller.name,
        businessName: sellerProfile.businessName,
        verificationStatus: sellerProfile.verificationStatus,
        rating: sellerProfile.rating || 0,
        reviewCount: sellerProfile.reviewCount || 0,
      },

      metrics: {
        activeProducts,
        pendingOrders,
        monthlyRevenue: Math.round(monthlyRevenue * 100) / 100,
        completedOrders: completedOrders.length,
      },

      subscription: {
        status: seller.subscriptionStatus,
        plan: seller.subscriptionPlan,
        isActive: isSubscriptionActive,
        daysRemaining: subscriptionDaysRemaining,
      },

      recentActivity,
      alerts,

      quickActions: [
        { id: "add_product", label: "Add New Product", icon: "plus" },
        { id: "view_orders", label: "View Orders", icon: "orders" },
        { id: "analytics", label: "View Analytics", icon: "chart" },
        { id: "profile", label: "Edit Profile", icon: "user" },
      ],
    };
  },
});

/**
 * Helper function to calculate application priority
 */
function calculateApplicationPriority(
  profile: any,
  applicationAge: number,
  documentCount: number
): "high" | "medium" | "low" {
  let score = 0;

  // Age factor (older applications get higher priority)
  if (applicationAge > 7) score += 2;
  else if (applicationAge > 3) score += 1;

  // Document completeness
  if (documentCount >= 3) score += 2;
  else if (documentCount >= 2) score += 1;

  // Expected volume
  if (profile.expectedMonthlyVolume && profile.expectedMonthlyVolume > 10000) score += 2;
  else if (profile.expectedMonthlyVolume && profile.expectedMonthlyVolume > 5000) score += 1;

  // Business license
  if (profile.hasBusinessLicense) score += 1;

  if (score >= 5) return "high";
  if (score >= 3) return "medium";
  return "low";
}

/**
 * Helper function to calculate average processing time
 */
function calculateAverageProcessingTime(applications: any[]): number {
  const processedApps = applications.filter(app =>
    app.verificationStatus === "approved" || app.verificationStatus === "rejected"
  );

  if (processedApps.length === 0) return 0;

  const totalTime = processedApps.reduce((sum, app) => {
    // This would need actual processing date from the profile
    // For now, estimate based on application age
    return sum + app.applicationAge;
  }, 0);

  return Math.round(totalTime / processedApps.length);
}

/**
 * Helper function to calculate repeat customer rate
 */
function calculateRepeatCustomerRate(orders: any[]): number {
  if (orders.length === 0) return 0;

  const customerOrders = new Map<string, number>();

  orders.forEach(order => {
    const customerId = order.buyerId;
    customerOrders.set(customerId, (customerOrders.get(customerId) || 0) + 1);
  });

  const repeatCustomers = Array.from(customerOrders.values()).filter(count => count > 1).length;
  const totalCustomers = customerOrders.size;

  return totalCustomers > 0 ? (repeatCustomers / totalCustomers) * 100 : 0;
}

/**
 * Helper function to generate seller tags
 */
function generateSellerTags(profile: any, user: any, isSubscriptionActive: boolean): string[] {
  const tags = [];

  if (profile.verificationStatus === "approved") tags.push("verified");
  if (isSubscriptionActive) tags.push("active_subscription");
  if (profile.hasBusinessLicense) tags.push("licensed");
  if (profile.rating && profile.rating >= 4.5) tags.push("top_rated");
  if (profile.expectedMonthlyVolume && profile.expectedMonthlyVolume > 10000) tags.push("high_volume");
  if (user._creationTime > Date.now() - (30 * 24 * 60 * 60 * 1000)) tags.push("new_seller");

  return tags;
}

/**
 * Helper function to generate export data
 */
function generateExportData(sellers: any[]) {
  return sellers.map(seller => ({
    business_name: seller.businessName,
    seller_name: seller.sellerName,
    email: seller.email,
    verification_status: seller.verificationStatus,
    subscription_status: seller.subscriptionStatus,
    subscription_plan: seller.subscriptionPlan,
    total_products: seller.metrics?.totalProducts || 0,
    total_revenue: seller.metrics?.totalRevenue || 0,
    rating: seller.rating,
    application_date: new Date(seller.applicationDate).toISOString().split('T')[0],
    last_active: seller.lastActive ? new Date(seller.lastActive).toISOString().split('T')[0] : "Never",
  }));
}

/**
 * Helper function to get recent seller activity
 */
async function getRecentSellerActivity(ctx: any, sellerId: string, limit: number = 10) {
  const activities = await ctx.db
    .query("analytics")
    .withIndex("by_userId", (q: any) => q.eq("userId", sellerId))
    .order("desc")
    .take(limit);

  return activities.map((activity: any) => ({
    type: activity.eventType,
    timestamp: activity.timestamp,
    description: formatActivityDescription(activity.eventType, activity.metadata),
  }));
}

/**
 * Get admin seller statistics and metrics
 */
export const getSellerStats = query({
  args: {},
  handler: async (ctx) => {
    // Only admins can view seller statistics
    await requireAdmin(ctx);

    // Get all seller profiles
    const allSellerProfiles = await ctx.db.query("sellerProfiles").collect();
    
    // Get all users with seller type
    const sellerUsers = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("userType"), "seller"))
      .collect();

    // Calculate basic counts
    const totalSellers = sellerUsers.length;
    const totalApproved = allSellerProfiles.filter(p => p.verificationStatus === "approved").length;
    const totalPending = allSellerProfiles.filter(p => p.verificationStatus === "pending").length;
    const totalRejected = allSellerProfiles.filter(p => p.verificationStatus === "rejected").length;

    // Get all orders to calculate revenue
    const allOrders = await ctx.db.query("orders").collect();
    const completedOrders = allOrders.filter(o => o.orderStatus === "delivered");
    
    // Calculate time-based metrics
    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
    
    const recentOrders = completedOrders.filter(o => o.orderDate >= thirtyDaysAgo);
    const monthlyRevenue = calculateSellerRevenue(recentOrders);
    const totalRevenue = calculateSellerRevenue(completedOrders);

    // Get all products to calculate inventory metrics
    const allProducts = await ctx.db.query("products").collect();
    const activeProducts = allProducts.filter(p => p.status === "active").length;
    const soldProducts = allProducts.filter(p => p.status === "sold").length;

    // Calculate averages
    const averageOrderValue = completedOrders.length > 0 ? totalRevenue / completedOrders.length : 0;
    const averageProductsPerSeller = totalApproved > 0 ? allProducts.length / totalApproved : 0;

    // Get active subscriptions
    const activeSubscriptions = sellerUsers.filter(u => 
      u.subscriptionStatus === "active" && 
      (!u.subscriptionExpiresAt || u.subscriptionExpiresAt > now)
    ).length;

    return {
      // Basic counts
      totalSellers,
      totalApproved,
      totalPending,
      totalRejected,
      activeSubscriptions,
      
      // Product metrics
      totalProducts: allProducts.length,
      activeProducts,
      soldProducts,
      averageProductsPerSeller: Math.round(averageProductsPerSeller * 100) / 100,
      
      // Revenue metrics
      totalRevenue: Math.round(totalRevenue * 100) / 100,
      monthlyRevenue: Math.round(monthlyRevenue * 100) / 100,
      averageOrderValue: Math.round(averageOrderValue * 100) / 100,
      
      // Order metrics
      totalOrders: completedOrders.length,
      monthlyOrders: recentOrders.length,
      
      // Performance metrics
      approvalRate: totalSellers > 0 ? Math.round((totalApproved / totalSellers) * 100) : 0,
      subscriptionRate: totalSellers > 0 ? Math.round((activeSubscriptions / totalSellers) * 100) : 0,
      
      // Time-based data
      generatedAt: now,
      periodStart: thirtyDaysAgo,
    };
  },
});

/**
 * Get seller sales chart data
 */
export const getSellerSalesChart = query({
  args: {
    sellerId: v.optional(v.id("users")),
    timeRange: v.optional(v.union(
      v.literal("7d"),
      v.literal("30d"),
      v.literal("90d"),
      v.literal("1y")
    )),
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    const targetSellerId = args.sellerId || currentUser._id;
    
    // Authorization check
    if (args.sellerId && args.sellerId !== currentUser._id && currentUser.userType !== "admin") {
      throw new ConvexError("Unauthorized");
    }

    // Get seller
    const seller = await ctx.db.get(targetSellerId);
    if (!seller || seller.userType !== "seller") {
      throw new ConvexError("Seller not found");
    }

    const timeRange = args.timeRange || "30d";
    const now = Date.now();
    const timeRanges = {
      "7d": 7 * 24 * 60 * 60 * 1000,
      "30d": 30 * 24 * 60 * 60 * 1000,
      "90d": 90 * 24 * 60 * 60 * 1000,
      "1y": 365 * 24 * 60 * 60 * 1000,
    };
    
    const startTime = now - timeRanges[timeRange];

    // Get all sold products for this seller
    const soldProducts = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
      .filter((q) => q.eq(q.field("status"), "sold"))
      .collect();

    // Filter by time range (using updatedAt as proxy for when it was sold)
    const soldProductsInRange = soldProducts.filter(p => 
      p.updatedAt && p.updatedAt >= startTime
    );

    // Generate chart data based on time range
    const periods = [];
    const currentDate = new Date();
    
    if (timeRange === "7d") {
      // Show daily for last 7 days
      for (let i = 6; i >= 0; i--) {
        const date = new Date(currentDate.getTime() - (i * 24 * 60 * 60 * 1000));
        const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
        const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime();
        const dayEnd = dayStart + (24 * 60 * 60 * 1000) - 1;
        
        const soldThisDay = soldProductsInRange.filter(p => 
          p.updatedAt && p.updatedAt >= dayStart && p.updatedAt <= dayEnd
        );

        const ownedSold = soldThisDay.filter(p => 
          p.ownershipType === "owned" || !p.ownershipType
        ).length;
        const consignedSold = soldThisDay.filter(p => 
          p.ownershipType === "consigned"
        ).length;
        
        periods.push({
          period: dayName,
          owned: ownedSold,
          consigned: consignedSold,
          total: soldThisDay.length,
        });
      }
    } else {
      // Show weekly for longer periods
      const daysInRange = timeRanges[timeRange] / (24 * 60 * 60 * 1000);
      const weeksInRange = Math.ceil(daysInRange / 7);
      
      for (let i = weeksInRange - 1; i >= 0; i--) {
        const weekStart = new Date(currentDate.getTime() - ((i + 1) * 7 * 24 * 60 * 60 * 1000));
        const weekEnd = new Date(currentDate.getTime() - (i * 7 * 24 * 60 * 60 * 1000));
        const weekName = `Week ${weeksInRange - i}`;
        
        const soldThisWeek = soldProductsInRange.filter(p => 
          p.updatedAt && p.updatedAt >= weekStart.getTime() && p.updatedAt <= weekEnd.getTime()
        );

        const ownedSold = soldThisWeek.filter(p => 
          p.ownershipType === "owned" || !p.ownershipType
        ).length;
        const consignedSold = soldThisWeek.filter(p => 
          p.ownershipType === "consigned"
        ).length;
        
        periods.push({
          period: weekName,
          owned: ownedSold,
          consigned: consignedSold,
          total: soldThisWeek.length,
        });
      }
    }

    // Calculate summary metrics
    const totalSold = soldProductsInRange.length;
    const totalOwnedSold = soldProductsInRange.filter(p => 
      p.ownershipType === "owned" || !p.ownershipType
    ).length;
    const totalConsignedSold = soldProductsInRange.filter(p => 
      p.ownershipType === "consigned"
    ).length;
    const totalRevenue = soldProductsInRange.reduce((sum, p) => sum + (p.price || 0), 0);

    return {
      timeRange,
      chartData: periods,
      summary: {
        totalSold,
        totalOwnedSold,
        totalConsignedSold,
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        averageSalePrice: totalSold > 0 ? Math.round((totalRevenue / totalSold) * 100) / 100 : 0,
      },
      generatedAt: now,
    };
  },
});

/**
 * Helper function to format activity descriptions
 */
function formatActivityDescription(eventType: string, _metadata?: any): string {
  switch (eventType) {
    case "product_created":
      return "Added a new product";
    case "product_updated":
      return "Updated a product";
    case "order_received":
      return "Received a new order";
    case "order_shipped":
      return "Shipped an order";
    case "profile_updated":
      return "Updated seller profile";
    default:
      return eventType.replace(/_/g, " ");
  }
}

/**
 * Get public sellers for consumer browsing (no authentication required)
 */
export const getPublicSellers = query({
  args: {
    searchQuery: v.optional(v.string()),
    category: v.optional(v.string()),
    businessType: v.optional(v.string()),
    minRating: v.optional(v.number()),
    sortBy: v.optional(v.union(
      v.literal("rating"),
      v.literal("products"),
      v.literal("joinedDate"),
      v.literal("businessName")
    )),
    sortOrder: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // No authentication required - this is public marketplace data
    
    const searchQuery = args.searchQuery?.toLowerCase().trim();
    const category = args.category;
    const businessType = args.businessType;
    const minRating = args.minRating;
    const sortBy = args.sortBy || "rating";
    const sortOrder = args.sortOrder || "desc";
    const limit = Math.min(args.limit || 24, 100); // Max 100 results
    const offset = args.offset || 0;

    // Get all approved and active sellers
    let sellerProfiles = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_verificationStatus", (q) => q.eq("verificationStatus", "approved"))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Get user data for each seller
    const enrichedSellers = await Promise.all(
      sellerProfiles.map(async (profile) => {
        const user = await ctx.db.get(profile.userId);
        if (!user || user.userType !== "seller") return null;

        // Get seller's products count and categories
        const products = await ctx.db
          .query("products")
          .withIndex("by_sellerId", (q) => q.eq("sellerId", profile.userId))
          .filter((q) => q.eq(q.field("status"), "active"))
          .collect();

        const activeProducts = products.length;
        const categories = [...new Set(products.map(p => p.category))];

        // Calculate average rating from products
        const productsWithRatings = products.filter(p => p.rating);
        const averageRating = productsWithRatings.length > 0 
          ? productsWithRatings.reduce((sum, p) => sum + (p.rating || 0), 0) / productsWithRatings.length
          : profile.rating || 0;

        return {
          userId: profile.userId,
          businessName: profile.businessName || user.name,
          businessType: profile.businessType,
          profileImage: user.profileImage,
          rating: averageRating,
          reviewCount: profile.reviewCount || 0,
          activeProducts,
          categories,
          joinedDate: user._creationTime,
          lastActive: user.lastLoginAt,
          address: profile.address,
        };
      })
    );

    // Filter out null values and apply filters
    let filteredSellers = enrichedSellers.filter(seller => seller !== null);

    // Apply search filter
    if (searchQuery) {
      filteredSellers = filteredSellers.filter(seller => 
        seller!.businessName.toLowerCase().includes(searchQuery) ||
        seller!.businessType?.toLowerCase().includes(searchQuery) ||
        seller!.categories.some(cat => cat.toLowerCase().includes(searchQuery))
      );
    }

    // Apply category filter
    if (category) {
      filteredSellers = filteredSellers.filter(seller => 
        seller!.categories.includes(category as any)
      );
    }

    // Apply business type filter
    if (businessType) {
      filteredSellers = filteredSellers.filter(seller => 
        seller!.businessType === businessType
      );
    }

    // Apply rating filter
    if (minRating) {
      filteredSellers = filteredSellers.filter(seller => 
        seller!.rating >= minRating
      );
    }

    // Sort sellers
    filteredSellers.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case "rating":
          comparison = (b!.rating || 0) - (a!.rating || 0);
          break;
        case "products":
          comparison = b!.activeProducts - a!.activeProducts;
          break;
        case "joinedDate":
          comparison = b!.joinedDate - a!.joinedDate;
          break;
        case "businessName":
          comparison = a!.businessName.localeCompare(b!.businessName);
          break;
        default:
          comparison = (b!.rating || 0) - (a!.rating || 0);
      }
      return sortOrder === "asc" ? -comparison : comparison;
    });

    const totalSellers = filteredSellers.length;

    // Apply pagination
    const paginatedSellers = filteredSellers.slice(offset, offset + limit);

    return {
      sellers: paginatedSellers,
      pagination: {
        total: totalSellers,
        limit,
        offset,
        hasMore: offset + limit < totalSellers,
        totalPages: Math.ceil(totalSellers / limit),
        currentPage: Math.floor(offset / limit) + 1,
      },
    };
  },
});
