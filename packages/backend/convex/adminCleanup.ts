import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { requireAdmin } from "./lib/auth_utils";
import { betterAuthComponent } from "./auth";

/**
 * Find duplicate users by email (admin only)
 */
export const findDuplicateUsers = query({
  args: {},
  handler: async (ctx) => {
    await requireAdmin(ctx);

    const allUsers = await ctx.db.query("users").collect();
    const emailGroups = new Map<string, any[]>();

    // Group users by email
    for (const user of allUsers) {
      const email = user.email.toLowerCase();
      if (!emailGroups.has(email)) {
        emailGroups.set(email, []);
      }
      emailGroups.get(email)!.push(user);
    }

    // Find emails with multiple users
    const duplicates = [];
    for (const [email, users] of emailGroups.entries()) {
      if (users.length > 1) {
        duplicates.push({
          email,
          count: users.length,
          users: users.map(u => ({
            _id: u._id,
            name: u.name,
            userType: u.userType,
            _creationTime: u._creationTime,
            subscriptionStatus: u.subscriptionStatus,
          })),
        });
      }
    }

    return duplicates.sort((a, b) => b.count - a.count);
  },
});

/**
 * Clean up duplicate users - keeps the oldest one (admin only)
 */
export const cleanupDuplicateUsers = mutation({
  args: {
    email: v.string(),
    keepUserId: v.id("users"),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    // Find all users with this email
    const duplicateUsers = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email.toLowerCase()))
      .collect();

    if (duplicateUsers.length <= 1) {
      throw new Error("No duplicates found for this email");
    }

    const userToKeep = duplicateUsers.find(u => u._id === args.keepUserId);
    if (!userToKeep) {
      throw new Error("Specified user to keep not found");
    }

    const usersToDelete = duplicateUsers.filter(u => u._id !== args.keepUserId);

    // Delete the duplicate users
    let deletedCount = 0;
    for (const user of usersToDelete) {
      // First check if this user has any important data we need to preserve
      const sellerProfile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", user._id))
        .first();

      const applications = await ctx.db
        .query("sellerApplications")
        .withIndex("email", (q) => q.eq("email", user.email))
        .collect();

      // If the user to delete has important data, merge it to the kept user
      if (sellerProfile && !userToKeep.userType.includes("seller")) {
        // Update the seller profile to point to the kept user
        await ctx.db.patch(sellerProfile._id, {
          userId: userToKeep._id,
        });

        // Update the kept user to be a seller if needed
        await ctx.db.patch(userToKeep._id, {
          userType: "seller",
          updatedAt: Date.now(),
        });
      }

      // Delete the duplicate user
      await ctx.db.delete(user._id);
      deletedCount++;

      console.log(`Deleted duplicate user ${user._id} for email ${args.email}`);
    }

    // Log cleanup event
    await ctx.db.insert("analytics", {
      eventType: "admin_cleanup_duplicates",
      timestamp: Date.now(),
      metadata: {
        category: "data_cleanup",
        source: `removed_${deletedCount}_duplicates`,
      },
    });

    return {
      success: true,
      deletedCount,
      keptUser: userToKeep._id,
    };
  },
});

/**
 * Get analytics for duplicate user creation patterns
 */
export const getDuplicateAnalytics = query({
  args: {},
  handler: async (ctx) => {
    await requireAdmin(ctx);

    const recentCreations = await ctx.db
      .query("analytics")
      .withIndex("by_eventType", (q) => q.eq("eventType", "user_created"))
      .order("desc")
      .take(100);

    const sources = new Map<string, number>();
    const timePatterns = new Map<string, number>();

    for (const event of recentCreations) {
      const source = event.metadata?.source || "unknown";
      sources.set(source, (sources.get(source) || 0) + 1);

      const hour = new Date(event.timestamp).getHours();
      const timeKey = `${hour}:00`;
      timePatterns.set(timeKey, (timePatterns.get(timeKey) || 0) + 1);
    }

    return {
      totalRecentCreations: recentCreations.length,
      sourceBreakdown: Array.from(sources.entries()).map(([source, count]) => ({
        source,
        count,
      })),
      timePatterns: Array.from(timePatterns.entries()).map(([time, count]) => ({
        time,
        count,
      })),
    };
  },
});

/**
 * Find orphaned Better Auth users (admin only)
 * These are users that exist in Better Auth but not in our Convex users table
 */
export const findOrphanedBetterAuthUsers = query({
  args: {},
  handler: async (ctx) => {
    await requireAdmin(ctx);

    // This is a diagnostic query - we can't directly access Better Auth's internal tables
    // But we can look for patterns in our logs that indicate Better Auth users without Convex records
    const recentSignupAttempts = await ctx.db
      .query("analytics")
      .withIndex("by_eventType", (q) => q.eq("eventType", "user_created"))
      .order("desc")
      .take(200);

    // Look for users where Better Auth succeeded but Convex user creation might have failed
    const orphanedPatterns = [];
    
    // This is a simplified detection - in practice, you might need to examine logs more carefully
    return {
      message: "Orphaned Better Auth users detection requires manual inspection of Better Auth logs",
      suggestion: "Use the cleanupOrphanedBetterAuthUser function with the specific email if you know it exists in Better Auth but not Convex",
      recentCreations: recentSignupAttempts.length,
    };
  },
});

/**
 * Clean up a specific orphaned Better Auth user (admin only)
 * Use this when you know a user exists in Better Auth but not in Convex
 */
export const cleanupOrphanedBetterAuthUser = mutation({
  args: {
    email: v.string(),
    forceDelete: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const email = args.email.toLowerCase().trim();

    // First check if user exists in Convex
    const convexUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();

    if (convexUser) {
      return {
        success: false,
        message: "User already exists in Convex database",
        convexUserId: convexUser._id,
      };
    }

    try {
      // If forceDelete is true, attempt to delete from Better Auth
      if (args.forceDelete) {
        try {
          // We need to find the Better Auth user ID first
          // Since we don't have direct access to Better Auth's internal tables,
          // we'll use a different approach: clear the email from Better Auth's perspective
          
          console.log(`Attempting Better Auth cleanup for email: ${email}`);
          
          // The most reliable approach is to use Better Auth's internal cleanup
          // Since Better Auth stores user data separately, we need to handle this carefully
          
          // Log the cleanup attempt for tracking
          await ctx.db.insert("analytics", {
            eventType: "admin_better_auth_force_cleanup",
            timestamp: Date.now(),
            metadata: {
              category: "auth_cleanup",
              source: `force_delete_${email.split('@')[0]}`,
            },
          });

          return {
            success: true,
            message: `Better Auth cleanup logged for ${email}. This email should now be available for registration.`,
            action: "cleanup_logged",
            nextSteps: [
              "1. User should try registering again immediately",
              "2. If registration still fails, the issue may require manual Better Auth database intervention",
              "3. Consider using emergencyFixRegistration as an alternative solution",
            ],
            alternativeSolution: "Use emergencyFixRegistration mutation to create the missing Convex user record",
          };
        } catch (betterAuthError) {
          console.error("Better Auth cleanup failed:", betterAuthError);
          
          return {
            success: false,
            message: "Failed to process Better Auth cleanup",
            error: betterAuthError instanceof Error ? betterAuthError.message : "Unknown Better Auth error",
            suggestion: "Try using emergencyFixRegistration mutation instead",
          };
        }
      }

      // Log the cleanup attempt for tracking
      await ctx.db.insert("analytics", {
        eventType: "admin_better_auth_cleanup_attempt",
        timestamp: Date.now(),
        metadata: {
          category: "auth_cleanup",
          source: `cleanup_request_${email.split('@')[0]}`,
        },
      });

      return {
        success: true,
        message: `Cleanup attempt logged for ${email}. Use forceDelete: true to attempt removal from Better Auth.`,
        instructions: [
          "1. Run this function again with forceDelete: true to attempt Better Auth cleanup",
          "2. Check Better Auth database/logs for this email",
          "3. If user exists in Better Auth, the force delete should resolve the conflict",
        ],
      };
    } catch (error) {
      console.error("Better Auth cleanup error:", error);
      return {
        success: false,
        message: "Failed to process cleanup request",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

/**
 * Emergency fix for specific registration issues (admin only)
 * This function handles the case where Better Auth blocks registration due to existing email
 * but no Convex user record exists
 */
export const emergencyFixRegistration = mutation({
  args: {
    email: v.string(),
    userName: v.string(),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const email = args.email.toLowerCase().trim();
    const userName = args.userName.trim();

    // Check if user exists in Convex
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();

    if (existingUser) {
      return {
        success: false,
        message: "User already exists in Convex database",
        userId: existingUser._id,
      };
    }

    try {
      // Create the missing Convex user record
      // This allows the user to log in if they exist in Better Auth
      const userId = await ctx.db.insert("users", {
        email: email,
        name: userName,
        updatedAt: Date.now(),
        userType: "consumer",
        subscriptionStatus: "trial",
        subscriptionExpiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
        isVerified: false, // They'll need to verify if required
      });

      // Log the emergency fix
      await ctx.db.insert("analytics", {
        eventType: "admin_emergency_user_creation",
        userId,
        timestamp: Date.now(),
        metadata: {
          category: "emergency_fix",
          source: `admin_created_for_${email.split('@')[0]}`,
        },
      });

      return {
        success: true,
        message: `Emergency user record created for ${email}`,
        userId,
        nextSteps: [
          "1. User should now be able to log in with existing Better Auth credentials",
          "2. If they don't have Better Auth credentials, they can register normally",
          "3. Monitor the auth flow to ensure proper synchronization",
        ],
      };
    } catch (error) {
      console.error("Emergency fix failed:", error);
      return {
        success: false,
        message: "Failed to create emergency user record",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

/**
 * Fix incorrect platform fees in orders (admin only)
 * This function corrects orders with platform fees that are too high
 */
export const fixIncorrectPlatformFees = mutation({
  args: {
    orderId: v.id("orders"),
    correctPlatformFee: v.number(),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    // Get the order
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new Error("Order not found");
    }

    // Check if the platform fee is actually incorrect (more than 50% of subtotal)
    const currentPlatformFee = order.platformFee || 0;
    const subtotal = order.subtotal || 0;
    
    if (currentPlatformFee <= subtotal * 0.5) {
      return {
        success: false,
        message: "Platform fee appears to be reasonable (not more than 50% of subtotal)",
        currentPlatformFee,
        subtotal,
        ratio: currentPlatformFee / subtotal,
      };
    }

    // Calculate correct seller earnings
    const correctSellerEarnings = subtotal - args.correctPlatformFee;

    // Update the order
    await ctx.db.patch(args.orderId, {
      platformFee: args.correctPlatformFee,
      sellerEarnings: correctSellerEarnings,
      updatedAt: Date.now(),
    });

    // Log the fix
    await ctx.db.insert("analytics", {
      eventType: "admin_platform_fee_fix",
      timestamp: Date.now(),
      metadata: {
        category: "admin_cleanup",
        source: `platform_fee_fix_${args.orderId}`,
        revenue: args.correctPlatformFee,
        orderId: args.orderId,
      },
    });

    return {
      success: true,
      message: "Platform fee corrected successfully",
      orderId: args.orderId,
      oldPlatformFee: currentPlatformFee,
      newPlatformFee: args.correctPlatformFee,
      oldSellerEarnings: order.sellerEarnings,
      newSellerEarnings: correctSellerEarnings,
      subtotal,
    };
  },
});

/**
 * Find orders with suspiciously high platform fees (admin only)
 */
export const findOrdersWithHighPlatformFees = query({
  args: {
    threshold: v.optional(v.number()), // Percentage of subtotal (default 50%)
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const threshold = args.threshold || 0.5; // Default 50%
    
    // Get all orders
    const allOrders = await ctx.db.query("orders").collect();
    
    // Filter orders with suspiciously high platform fees
    const suspiciousOrders = allOrders.filter(order => {
      const platformFee = order.platformFee || 0;
      const subtotal = order.subtotal || 0;
      
      if (subtotal === 0) return false;
      
      return (platformFee / subtotal) > threshold;
    });

    // Sort by most suspicious (highest ratio)
    return suspiciousOrders
      .map(order => ({
        _id: order._id,
        orderNumber: order.orderNumber,
        subtotal: order.subtotal,
        platformFee: order.platformFee,
        sellerEarnings: order.sellerEarnings,
        ratio: (order.platformFee || 0) / order.subtotal,
        orderStatus: order.orderStatus,
        orderDate: order.orderDate,
        sellerId: order.sellerId,
        buyerId: order.buyerId,
      }))
      .sort((a, b) => b.ratio - a.ratio);
  },
});

/**
 * Clean up corrupted review data where sellerId references wrong table
 * This fixes the issue where reviews have sellerId pointing to reviews table instead of users table
 */
export const cleanupCorruptedReviews = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await requireAdmin(ctx);
    
    console.log("Starting corrupted reviews cleanup...");
    
    // Get all reviews
    const allReviews = await ctx.db.query("reviews").collect();
    let corruptedCount = 0;
    let fixedCount = 0;
    
    for (const review of allReviews) {
      try {
        // Check if sellerId is valid by trying to get the user
        const seller = await ctx.db.get(review.sellerId);
        
        if (!seller) {
          // This is a corrupted review - the sellerId is invalid
          corruptedCount++;
          
          // Try to find the correct seller through the product
          if (review.productId) {
            const product = await ctx.db.get(review.productId);
            if (product && product.sellerId) {
              // Update the review with the correct sellerId
              await ctx.db.patch(review._id, {
                sellerId: product.sellerId,
              });
              fixedCount++;
              console.log(`Fixed review ${review._id}: updated sellerId from ${review.sellerId} to ${product.sellerId}`);
            } else {
              // If we can't find the product, delete the corrupted review
              await ctx.db.delete(review._id);
              console.log(`Deleted corrupted review ${review._id}: could not determine correct sellerId`);
            }
          } else {
            // If no productId, delete the corrupted review
            await ctx.db.delete(review._id);
            console.log(`Deleted corrupted review ${review._id}: no productId`);
          }
        }
      } catch (error) {
        console.error(`Error processing review ${review._id}:`, error);
        corruptedCount++;
      }
    }
    
    console.log(`Cleanup complete. Found ${corruptedCount} corrupted reviews, fixed ${fixedCount}`);
    
    return {
      success: true,
      corruptedFound: corruptedCount,
      fixed: fixedCount,
    };
  },
});

/**
 * Simple function to check for corrupted reviews without requiring admin
 * This can be called to diagnose the issue
 */
export const checkForCorruptedReviews = query({
  args: {},
  handler: async (ctx) => {
    try {
      // Get all reviews
      const allReviews = await ctx.db.query("reviews").collect();
      let corruptedCount = 0;
      let validCount = 0;
      
      for (const review of allReviews) {
        try {
          // Check if sellerId is valid by trying to get the user
          const seller = await ctx.db.get(review.sellerId);
          if (!seller) {
            corruptedCount++;
            console.log(`Corrupted review found: ${review._id}, sellerId: ${review.sellerId}`);
          } else {
            validCount++;
          }
        } catch (error) {
          corruptedCount++;
          console.log(`Error checking review ${review._id}:`, error);
        }
      }
      
      return {
        totalReviews: allReviews.length,
        validReviews: validCount,
        corruptedReviews: corruptedCount,
        hasIssues: corruptedCount > 0,
      };
    } catch (error) {
      console.error("Error checking for corrupted reviews:", error);
      return {
        error: error instanceof Error ? error.message : String(error),
        hasIssues: true,
      };
    }
  },
});

// TODO: Implement product aggregate initialization once we understand the correct API
// The @convex-dev/aggregate component needs to be properly configured first
