import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { getAuthUser, requireAuth } from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get user preferences
 */
export const getUserPreferences = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);

    const preferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    // Return default preferences if none exist
    if (!preferences) {
      return {
        // Email Notifications
        emailNotifications: {
          orderUpdates: true,
          promotions: true,
          newProducts: false,
          priceAlerts: true,
          newsletter: true,
          sellerUpdates: false,
          communityActivity: true,
        },

        // Push Notifications
        pushNotifications: {
          enabled: true,
          orderStatus: true,
          bidUpdates: true,
          wishlistAlerts: true,
          marketing: false,
        },

        // SMS Notifications
        smsNotifications: {
          enabled: false,
          orderUpdates: false,
          security: true,
        },

        // Display Preferences
        display: {
          theme: "system",
          currency: "USD",
          language: "en",
          timezone: "America/New_York",
        },

        // Privacy Settings
        privacy: {
          profileVisibility: "public",
          showPurchaseHistory: false,
          showWishlist: true,
          allowMessages: true,
        },

        // Marketplace Preferences
        marketplace: {
          defaultView: "grid",
          itemsPerPage: 24,
          autoplayVideos: true,
          showSimilarItems: true,
          enableRecommendations: true,
        },

        // Search & Discovery
        search: {
          saveSearchHistory: true,
          personalizedResults: true,
          trendingCategories: true,
          locationBasedResults: true,
        },
      };
    }

    return preferences;
  },
});

/**
 * Update user preferences
 */
export const updateUserPreferences = mutation({
  args: {
    emailNotifications: v.optional(v.object({
      orderUpdates: v.optional(v.boolean()),
      promotions: v.optional(v.boolean()),
      newProducts: v.optional(v.boolean()),
      priceAlerts: v.optional(v.boolean()),
      newsletter: v.optional(v.boolean()),
      sellerUpdates: v.optional(v.boolean()),
      communityActivity: v.optional(v.boolean()),
    })),
    pushNotifications: v.optional(v.object({
      enabled: v.optional(v.boolean()),
      orderStatus: v.optional(v.boolean()),
      bidUpdates: v.optional(v.boolean()),
      wishlistAlerts: v.optional(v.boolean()),
      marketing: v.optional(v.boolean()),
    })),
    smsNotifications: v.optional(v.object({
      enabled: v.optional(v.boolean()),
      orderUpdates: v.optional(v.boolean()),
      security: v.optional(v.boolean()),
    })),
    display: v.optional(v.object({
      theme: v.optional(v.union(v.literal("light"), v.literal("dark"), v.literal("system"))),
      currency: v.optional(v.string()),
      language: v.optional(v.string()),
      timezone: v.optional(v.string()),
    })),
    privacy: v.optional(v.object({
      profileVisibility: v.optional(v.union(v.literal("public"), v.literal("private"))),
      showPurchaseHistory: v.optional(v.boolean()),
      showWishlist: v.optional(v.boolean()),
      allowMessages: v.optional(v.boolean()),
    })),
    marketplace: v.optional(v.object({
      defaultView: v.optional(v.union(v.literal("grid"), v.literal("list"))),
      itemsPerPage: v.optional(v.number()),
      autoplayVideos: v.optional(v.boolean()),
      showSimilarItems: v.optional(v.boolean()),
      enableRecommendations: v.optional(v.boolean()),
    })),
    search: v.optional(v.object({
      saveSearchHistory: v.optional(v.boolean()),
      personalizedResults: v.optional(v.boolean()),
      trendingCategories: v.optional(v.boolean()),
      locationBasedResults: v.optional(v.boolean()),
    })),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get existing preferences
    const existingPreferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    if (existingPreferences) {
      // Update existing preferences
      const updateData: any = {
        updatedAt: Date.now(),
      };

      if (args.emailNotifications) {
        updateData.emailNotifications = {
          ...existingPreferences.emailNotifications,
          ...args.emailNotifications,
        };
      }

      if (args.pushNotifications) {
        updateData.pushNotifications = {
          ...existingPreferences.pushNotifications,
          ...args.pushNotifications,
        };
      }

      if (args.smsNotifications) {
        updateData.smsNotifications = {
          ...existingPreferences.smsNotifications,
          ...args.smsNotifications,
        };
      }

      if (args.display) {
        updateData.display = {
          ...existingPreferences.display,
          ...args.display,
        };
      }

      if (args.privacy) {
        updateData.privacy = {
          ...existingPreferences.privacy,
          ...args.privacy,
        };
      }

      if (args.marketplace) {
        updateData.marketplace = {
          ...existingPreferences.marketplace,
          ...args.marketplace,
        };
      }

      if (args.search) {
        updateData.search = {
          ...existingPreferences.search,
          ...args.search,
        };
      }

      await ctx.db.patch(existingPreferences._id, updateData);
    } else {
      // Create new preferences with defaults and provided values
      const newPreferences = {
        userId: user._id,
        
        emailNotifications: {
          orderUpdates: true,
          promotions: true,
          newProducts: false,
          priceAlerts: true,
          newsletter: true,
          sellerUpdates: false,
          communityActivity: true,
          ...args.emailNotifications,
        },

        pushNotifications: {
          enabled: true,
          orderStatus: true,
          bidUpdates: true,
          wishlistAlerts: true,
          marketing: false,
          ...args.pushNotifications,
        },

        smsNotifications: {
          enabled: false,
          orderUpdates: false,
          security: true,
          ...args.smsNotifications,
        },

        display: {
          theme: "system" as const,
          currency: "USD",
          language: "en",
          timezone: "America/New_York",
          ...args.display,
        },

        privacy: {
          profileVisibility: "public" as const,
          showPurchaseHistory: false,
          showWishlist: true,
          allowMessages: true,
          ...args.privacy,
        },

        marketplace: {
          defaultView: "grid" as const,
          itemsPerPage: 24,
          autoplayVideos: true,
          showSimilarItems: true,
          enableRecommendations: true,
          ...args.marketplace,
        },

        search: {
          saveSearchHistory: true,
          personalizedResults: true,
          trendingCategories: true,
          locationBasedResults: true,
          ...args.search,
        },

        updatedAt: Date.now(),
      };

      await ctx.db.insert("userPreferences", newPreferences);
    }

    // Log preferences update
    await ctx.db.insert("analytics", {
      eventType: "preferences_updated",
      userId: user._id,
      timestamp: Date.now(),
      metadata: {
        source: user._id,
        category: "preferences",
        revenue: 0,
      },
    });

    return {
      success: true,
      message: "Preferences updated successfully",
    };
  },
});

/**
 * Reset user preferences to defaults
 */
export const resetUserPreferences = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);

    const existingPreferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    if (existingPreferences) {
      await ctx.db.delete(existingPreferences._id);
    }

    // Log preferences reset
    await ctx.db.insert("analytics", {
      eventType: "preferences_reset",
      userId: user._id,
      timestamp: Date.now(),
      metadata: {
        source: user._id,
        category: "preferences",
        revenue: 0,
      },
    });

    return {
      success: true,
      message: "Preferences reset to defaults",
    };
  },
});
