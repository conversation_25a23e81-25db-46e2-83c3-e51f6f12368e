import { v } from "convex/values";
import { mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import {
  getAuthUser,
  requireAuth,
  requireAdmin
} from "./lib/auth_utils";
import { api } from "./_generated/api";
import type { Id } from "./_generated/dataModel";

/**
 * Create a new order for marketplace purchase
 */
export const createOrder = mutation({
  args: {
    productId: v.id("products"),
    shippingAddress: v.object({
      name: v.string(),
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
      phone: v.optional(v.string()),
    }),
    paymentMethod: v.string(), // Stripe payment method ID
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Verify user has active subscription
    const hasActiveSubscription = user.subscriptionStatus === "active" && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    // if (!hasActiveSubscription) {
    //   throw new ConvexError("Active subscription required to make purchases");
    // }

    // Get product
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new ConvexError("Product not found");
    }

    // Validate product availability
    if (product.status !== "active") {
      throw new ConvexError("Product is not available for purchase");
    }

    // Prevent self-purchase
    if (product.sellerId === user._id) {
      throw new ConvexError("Cannot purchase your own product");
    }

    // Get seller information
    const seller = await ctx.db.get(product.sellerId);
    if (!seller) {
      throw new ConvexError("Seller not found");
    }

    // Get seller profile for commission rate
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", product.sellerId))
      .first();

    if (!sellerProfile || sellerProfile.verificationStatus !== "approved" || !sellerProfile.isActive) {
      throw new ConvexError("Seller is not available for transactions");
    }

    // Check for existing pending orders for this product
    const existingOrder = await ctx.db
      .query("orders")
      .withIndex("by_productId", (q) => q.eq("productId", args.productId))
      .filter((q) => q.or(
        q.eq(q.field("orderStatus"), "pending"),
        q.eq(q.field("orderStatus"), "confirmed"),
        q.eq(q.field("orderStatus"), "shipped")
      ))
      .first();

    if (existingOrder) {
      throw new ConvexError("Product already has a pending order");
    }

    const now = Date.now();
    const commissionRate = sellerProfile.commissionRate || 0.1; // Default 10%
    
    // Calculate pricing
    const productPrice = product.price;
    const shippingCost = product.shippingCost || 0;
    const subtotal = productPrice + shippingCost;
    const tax = calculateTax(subtotal, args.shippingAddress.state); // Implement tax calculation
    const totalAmount = subtotal + tax;
    const commissionAmount = productPrice * commissionRate;
    const sellerEarnings = productPrice - commissionAmount;

    // Generate order number
    const orderNumber = `HV-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    // Create order record
    const orderId = await ctx.db.insert("orders", {
      orderNumber,
      buyerId: user._id,
      sellerId: product.sellerId,
      productId: args.productId,
      orderStatus: "pending", 
      totalAmount,
      subtotal,
      platformFee: commissionAmount,
      sellerEarnings,
      shippingCost,
      tax,
      shippingAddress: args.shippingAddress,
      paymentMethod: args.paymentMethod,
      notes: args.notes,
      orderDate: now,
      updatedAt: now,
    });

    // Update product status to prevent other purchases
    await ctx.db.patch(args.productId, {
      status: "reserved",
      updatedAt: now,
    });

    // Log order creation
    await ctx.db.insert("analytics", {
      eventType: "order_created",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: orderId,
        category: "order_management",
        revenue: totalAmount,
      },
    });

    // Get seller's Stripe Connect account
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", product.sellerId))
      .first();

    if (!sellerProfile?.stripeConnectAccountId || !sellerProfile.stripeChargesEnabled) {
      throw new ConvexError("Seller payment account not configured or not enabled");
    }

    // Create Stripe payment intent with Connect transfer
    let paymentResult;
    try {
      paymentResult = await ctx.runAction(api.stripePayments.createPaymentIntent, {
        amount: totalAmount,
        currency: 'usd',
        connectedAccountId: sellerProfile.stripeConnectAccountId,
        metadata: {
          orderId,
          orderNumber,
          productId: args.productId,
          sellerId: product.sellerId,
          buyerId: user._id,
        },
      });
    } catch (error) {
      // If payment intent creation fails, clean up the order
      await ctx.db.delete(orderId);
      throw new ConvexError("Failed to create payment intent");
    }

    // Update order with payment intent information
    await ctx.db.patch(orderId, {
      stripePaymentIntentId: paymentResult.paymentIntentId,
      stripeApplicationFee: paymentResult.fees.platformFee,
      sellerEarnings: paymentResult.fees.sellerAmount,
      updatedAt: Date.now(),
    });

    // TODO: Send notifications
    // await sendOrderNotification({
    //   type: 'order_created',
    //   buyerId: user._id,
    //   sellerId: product.sellerId,
    //   orderId,
    //   orderNumber,
    // });

    return {
      success: true,
      orderId,
      orderNumber,
      totalAmount,
      paymentClientSecret: paymentResult.clientSecret,
      paymentIntentId: paymentResult.paymentIntentId,
      fees: paymentResult.fees,
      message: "Order created successfully",
    };
  },
});

/**
 * Update order status with tracking and notifications
 */
export const updateOrderStatus = mutation({
  args: {
    orderId: v.id("orders"),
    status: v.union(
      v.literal("confirmed"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled")
    ),
    trackingNumber: v.optional(v.string()),
    carrier: v.optional(v.string()),
    estimatedDelivery: v.optional(v.number()),
    notes: v.optional(v.string()),
    sendNotification: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get order
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Authorization check - seller, buyer, or admin can update
    if (order.sellerId !== user._id && order.buyerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Unauthorized to update this order");
    }

    // Validate status transition
    const validTransitions: Record<string, string[]> = {
      pending: ["confirmed", "payment_pending", "cancelled"],
      payment_pending: ["paid", "cancelled"],
      confirmed: ["shipped", "cancelled"],
      paid: ["shipped", "cancelled"],
      shipped: ["delivered", "cancelled"],
      delivered: [], // Final state
      cancelled: [], // Final state
      refunded: [], // Final state
      disputed: ["delivered", "cancelled", "refunded"], // Can be resolved
    };

    if (!validTransitions[order.orderStatus]?.includes(args.status)) {
      throw new ConvexError(`Cannot transition from ${order.orderStatus} to ${args.status}`);
    }

    // Only seller can confirm/ship orders
    if ((args.status === "confirmed" || args.status === "shipped") && order.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Only the seller can confirm or ship orders");
    }

    // Only buyer can confirm delivery (or admin)
    if (args.status === "delivered" && order.buyerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Only the buyer can confirm delivery");
    }

    const now = Date.now();
    const updateData: any = {
      orderStatus: args.status,
      updatedAt: now,
    };

    // Add status-specific fields
    if (args.status === "confirmed") {
      updateData.confirmedDate = now;
    } else if (args.status === "shipped") {
      updateData.shippedDate = now;
      if (args.trackingNumber) updateData.trackingNumber = args.trackingNumber;
      if (args.carrier) updateData.carrier = args.carrier;
      if (args.estimatedDelivery) updateData.estimatedDelivery = args.estimatedDelivery;
    } else if (args.status === "delivered") {
      updateData.deliveredDate = now;
    } else if (args.status === "cancelled") {
      updateData.cancelledDate = now;
    }

    if (args.notes) {
      updateData.notes = args.notes;
    }

    // Update order
    await ctx.db.patch(args.orderId, updateData);

    // Handle product status changes
    if (args.status === "delivered") {
      // Mark product as sold
      await ctx.db.patch(order.productId, {
        status: "sold",
        updatedAt: now,
      });

      // Update seller profile stats
      const sellerProfile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", order.sellerId))
        .first();

      if (sellerProfile) {
        await ctx.db.patch(sellerProfile._id, {
          totalSales: (sellerProfile.totalSales || 0) + 1,
          totalEarnings: (sellerProfile.totalEarnings || 0) + (order.sellerEarnings || 0),
          updatedAt: now,
        });
      }
    } else if (args.status === "cancelled") {
      // Return product to active status
      await ctx.db.patch(order.productId, {
        status: "active",
        updatedAt: now,
      });
    }

    // Log status change
    await ctx.db.insert("analytics", {
      eventType: `order_${args.status}`,
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.orderId,
        category: "order_management",
      },
    });

    // TODO: Process payment actions
    // if (args.status === "delivered") {
    //   await releasePaymentToSeller({
    //     orderId: args.orderId,
    //     sellerId: order.sellerId,
    //     amount: order.sellerEarnings,
    //   });
    // } else if (args.status === "cancelled") {
    //   await processRefund({
    //     orderId: args.orderId,
    //     buyerId: order.buyerId,
    //     amount: order.totalAmount,
    //   });
    // }

    // TODO: Send notifications
    // if (args.sendNotification !== false) {
    //   await sendOrderStatusNotification({
    //     orderId: args.orderId,
    //     status: args.status,
    //     buyerId: order.buyerId,
    //     sellerId: order.sellerId,
    //     trackingNumber: args.trackingNumber,
    //     carrier: args.carrier,
    //   });
    // }

    return {
      success: true,
      message: `Order ${args.status} successfully`,
      status: args.status,
      updatedAt: now,
    };
  },
});

/**
 * Cancel an order with refund processing
 */
export const cancelOrder = mutation({
  args: {
    orderId: v.id("orders"),
    reason: v.string(),
    refundAmount: v.optional(v.number()), // Partial refund support
    sendNotification: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get order
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Authorization check
    if (order.buyerId !== user._id && order.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Unauthorized to cancel this order");
    }

    // Check if order can be cancelled
    if (order.orderStatus === "delivered") {
      throw new ConvexError("Cannot cancel delivered orders");
    }

    if (order.orderStatus === "cancelled") {
      throw new ConvexError("Order is already cancelled");
    }

    // Validate cancellation timing
    const now = Date.now();
    const orderAge = now - order.orderDate;
    const maxCancellationTime = 24 * 60 * 60 * 1000; // 24 hours

    // Buyers can cancel within 24 hours, sellers can cancel anytime before shipping
    if (order.buyerId === user._id && orderAge > maxCancellationTime && order.orderStatus !== "pending") {
      throw new ConvexError("Order can only be cancelled within 24 hours of placement");
    }

    if (order.sellerId === user._id && order.orderStatus === "shipped") {
      throw new ConvexError("Cannot cancel shipped orders");
    }

    const refundAmount = args.refundAmount || order.totalAmount;
    
    // Validate refund amount
    if (refundAmount > order.totalAmount) {
      throw new ConvexError("Refund amount cannot exceed order total");
    }

    // Update order status
    await ctx.db.patch(args.orderId, {
      orderStatus: "cancelled",
      cancelledDate: now,
      cancellationReason: args.reason,
      notes: `Refund amount: $${refundAmount}. Reason: ${args.reason}`,
      updatedAt: now,
    });

    // Return product to active status
    await ctx.db.patch(order.productId, {
      status: "active",
      updatedAt: now,
    });

    // Log cancellation
    await ctx.db.insert("analytics", {
      eventType: "order_cancelled",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.orderId,
        category: "order_management",
        revenue: -refundAmount, // Negative revenue for cancellation
      },
    });

    // TODO: Process refund
    // await processRefund({
    //   orderId: args.orderId,
    //   buyerId: order.buyerId,
    //   amount: refundAmount,
    //   reason: args.reason,
    // });

    // TODO: Send notifications
    // if (args.sendNotification !== false) {
    //   await sendCancellationNotification({
    //     orderId: args.orderId,
    //     buyerId: order.buyerId,
    //     sellerId: order.sellerId,
    //     reason: args.reason,
    //     refundAmount,
    //   });
    // }

    return {
      success: true,
      message: "Order cancelled successfully",
      refundAmount,
      cancelledAt: now,
    };
  },
});

/**
 * Confirm delivery and release payment to seller
 */
export const confirmDelivery = mutation({
  args: {
    orderId: v.id("orders"),
    rating: v.optional(v.number()), // 1-5 star rating for seller
    review: v.optional(v.string()),
    requestFeedback: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get order
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Only buyer can confirm delivery
    if (order.buyerId !== user._id) {
      throw new ConvexError("Only the buyer can confirm delivery");
    }

    // Check order status
    if (order.orderStatus !== "shipped") {
      throw new ConvexError("Order must be shipped before confirming delivery");
    }

    // Validate rating if provided
    if (args.rating && (args.rating < 1 || args.rating > 5)) {
      throw new ConvexError("Rating must be between 1 and 5 stars");
    }

    const now = Date.now();

    // Update order status
    await ctx.db.patch(args.orderId, {
      orderStatus: "delivered",
      deliveredDate: now,
      notes: args.review ? `Rating: ${args.rating}/5. Review: ${args.review}` : (args.rating ? `Rating: ${args.rating}/5` : order.notes),
      updatedAt: now,
    });

    // Mark product as sold
    await ctx.db.patch(order.productId, {
      status: "sold",
      updatedAt: now,
    });

    // Update seller profile stats
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", order.sellerId))
      .first();

    if (sellerProfile) {
      let newRating = sellerProfile.rating || 0;
      let newReviewCount = sellerProfile.reviewCount || 0;

      // Update rating if provided
      if (args.rating) {
        const totalRatingPoints = (sellerProfile.rating || 0) * (sellerProfile.reviewCount || 0);
        newReviewCount = newReviewCount + 1;
        newRating = (totalRatingPoints + args.rating) / newReviewCount;
      }

      await ctx.db.patch(sellerProfile._id, {
        totalSales: (sellerProfile.totalSales || 0) + 1,
        totalEarnings: (sellerProfile.totalEarnings || 0) + (order.sellerEarnings || 0),
        rating: newRating,
        reviewCount: newReviewCount,
        updatedAt: now,
      });
    }

    // Create review record if rating/review provided
    if (args.rating || args.review) {
      await ctx.db.insert("reviews", {
        userId: user._id,
        productId: order.productId,
        sellerId: order.sellerId,
        orderId: args.orderId,
        rating: args.rating || 0,
        review: args.review || "",
        isVerifiedPurchase: true,
        reviewDate: now,
      });
    }

    // Log delivery confirmation
    await ctx.db.insert("analytics", {
      eventType: "order_delivered",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.orderId,
        category: "order_management",
      },
    });

    // TODO: Release payment to seller
    // await releasePaymentToSeller({
    //   orderId: args.orderId,
    //   sellerId: order.sellerId,
    //   amount: order.sellerEarnings,
    //   commissionAmount: order.commissionAmount,
    // });

    // TODO: Send notifications
    // await sendDeliveryConfirmationNotification({
    //   orderId: args.orderId,
    //   buyerId: order.buyerId,
    //   sellerId: order.sellerId,
    //   rating: args.rating,
    //   hasReview: !!args.review,
    // });

    // TODO: Request feedback if requested
    // if (args.requestFeedback) {
    //   await sendFeedbackRequest({
    //     buyerId: order.buyerId,
    //     sellerId: order.sellerId,
    //     orderId: args.orderId,
    //   });
    // }

    return {
      success: true,
      message: "Delivery confirmed successfully",
      deliveredAt: now,
      paymentReleased: true,
      sellerEarnings: order.sellerEarnings,
    };
  },
});

/**
 * Process automatic delivery confirmation (for tracking-based delivery)
 */
export const autoConfirmDelivery = mutation({
  args: {
    orderId: v.id("orders"),
    trackingConfirmation: v.object({
      carrier: v.string(),
      trackingNumber: v.string(),
      deliveredAt: v.number(),
      signedBy: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    // This would typically be called by a webhook from shipping carriers
    await requireAdmin(ctx); // Only admin/system can auto-confirm

    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    if (order.orderStatus !== "shipped") {
      throw new ConvexError("Order must be shipped for auto-confirmation");
    }

    const now = Date.now();

    // Update order with tracking confirmation
    await ctx.db.patch(args.orderId, {
      orderStatus: "delivered",
      deliveredDate: args.trackingConfirmation.deliveredAt,
      trackingNumber: args.trackingConfirmation.trackingNumber,
      carrier: args.trackingConfirmation.carrier,
      updatedAt: now,
    });

    // Mark product as sold
    await ctx.db.patch(order.productId, {
      status: "sold",
      updatedAt: now,
    });
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", order.sellerId))
      .first();

    if (sellerProfile) {
      await ctx.db.patch(sellerProfile._id, {
        totalSales: (sellerProfile.totalSales || 0) + 1,
        totalEarnings: (sellerProfile.totalEarnings || 0) + (order.sellerEarnings || 0),
        updatedAt: now,
      });
    }

    // Log auto-confirmation
    await ctx.db.insert("analytics", {
      eventType: "order_auto_delivered",
      userId: order.buyerId,
      timestamp: now,
      metadata: {
        source: args.orderId,
        category: "order_management",
      },
    });

    // TODO: Release payment and send notifications
    // await releasePaymentToSeller({ ... });
    // await sendAutoDeliveryNotification({ ... });

    return {
      success: true,
      message: "Delivery auto-confirmed via tracking",
      deliveredAt: args.trackingConfirmation.deliveredAt,
    };
  },
});

/**
 * Dispute an order (buyer protection)
 */
export const disputeOrder = mutation({
  args: {
    orderId: v.id("orders"),
    reason: v.union(
      v.literal("item_not_received"),
      v.literal("item_not_as_described"),
      v.literal("item_damaged"),
      v.literal("wrong_item"),
      v.literal("other")
    ),
    description: v.string(),
    evidence: v.optional(v.array(v.id("_storage"))), // Photo evidence
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Only buyer can dispute
    if (order.buyerId !== user._id) {
      throw new ConvexError("Only the buyer can dispute an order");
    }

    // Check dispute eligibility
    if (order.orderStatus === "cancelled") {
      throw new ConvexError("Cannot dispute cancelled orders");
    }

    // Check for existing dispute
    const existingDispute = await ctx.db
      .query("disputes")
      .withIndex("by_orderId", (q) => q.eq("orderId", args.orderId))
      .first();

    if (existingDispute) {
      throw new ConvexError("Dispute already exists for this order");
    }

    const now = Date.now();

    // Create dispute record
    const disputeId = await ctx.db.insert("disputes", {
      orderId: args.orderId,
      disputeType: "refund", // Default to refund dispute
      disputeStatus: "pending",
      disputeDate: now,
      disputeReason: args.description,
      disputeDocuments: args.evidence || [],
      disputeNotes: `Dispute reason: ${args.reason}`,
    });

    // Update order status
    await ctx.db.patch(args.orderId, {
      orderStatus: "disputed",
      notes: `Dispute created: ${args.reason} - ${args.description}`,
      updatedAt: now,
    });

    // Log dispute
    await ctx.db.insert("analytics", {
      eventType: "order_disputed",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.orderId,
        category: "order_management",
      },
    });

    // TODO: Send dispute notifications
    // await sendDisputeNotification({
    //   disputeId,
    //   orderId: args.orderId,
    //   buyerId: order.buyerId,
    //   sellerId: order.sellerId,
    //   reason: args.reason,
    // });

    return {
      success: true,
      disputeId,
      message: "Dispute created successfully",
      disputeDate: now,
    };
  },
});

/**
 * Helper function to calculate tax based on shipping address
 */
function calculateTax(subtotal: number, state: string): number {
  // Simplified tax calculation - in production, use a tax service like TaxJar
  const taxRates: Record<string, number> = {
    "CA": 0.0875, // California
    "NY": 0.08,   // New York
    "TX": 0.0625, // Texas
    "FL": 0.06,   // Florida
    // Add more states as needed
  };

  const taxRate = taxRates[state] || 0; // No tax for unlisted states
  return Math.round(subtotal * taxRate * 100) / 100;
}

/**
 * Helper function to generate order notifications
 */
async function scheduleOrderNotifications(ctx: any, orderId: string, type: string) {
  // This would integrate with your notification system
  // For now, just log the notification intent
  await ctx.db.insert("analytics", {
    eventType: "notification_scheduled",
    userId: "system",
    timestamp: Date.now(),
    metadata: {
      source: orderId,
      category: "notifications",
      type,
    },
  });
}

/**
 * Confirm payment and create verified purchase
 * This is called after successful payment processing via Stripe
 */
export const confirmPayment = mutation({
  args: {
    orderId: v.id("orders"),
    paymentIntentId: v.string(),
    paymentMethod: v.string(),
    billingAddress: v.optional(v.object({
      name: v.string(),
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get order
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Verify user is the buyer
    if (order.buyerId !== user._id) {
      throw new ConvexError("Unauthorized to confirm payment for this order");
    }

    // Check order status
    if (order.orderStatus !== "pending") {
      throw new ConvexError("Order is not in pending status");
    }

    // Verify the payment intent matches
    if (order.stripePaymentIntentId !== args.paymentIntentId) {
      throw new ConvexError("Payment intent ID mismatch");
    }

    const now = Date.now();

    // Update order with payment confirmation
    await ctx.db.patch(args.orderId, {
      orderStatus: "paid",
      paymentIntentId: args.paymentIntentId,
      paymentMethod: args.paymentMethod,
      billingAddress: args.billingAddress,
      paidDate: now,
      updatedAt: now,
    });

    // Update product status to sold
    await ctx.db.patch(order.productId, {
      status: "sold",
      updatedAt: now,
    });

    // Create verified purchase record for reviews
    const verifiedPurchaseId = await ctx.db.insert("verifiedPurchases", {
      orderId: args.orderId,
      buyerId: user._id,
      sellerId: order.sellerId,
      productId: order.productId,
      purchaseDate: now,
      purchaseAmount: order.totalAmount,
      paymentMethod: args.paymentMethod,
      isVerified: true,
      verificationMethod: "payment_confirmation",
      updatedAt: now,
    });

    // Log payment confirmation
    await ctx.db.insert("analytics", {
      eventType: "payment_confirmed",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.orderId,
        category: "order_management",
        revenue: order.totalAmount,
        paymentMethod: args.paymentMethod,
        verifiedPurchaseId,
      },
    });

    // Schedule order notifications
    await scheduleOrderNotifications(ctx, args.orderId, "payment_confirmed");

    return {
      success: true,
      orderId: args.orderId,
      verifiedPurchaseId,
      message: "Payment confirmed and verified purchase created",
      paidDate: now,
    };
  },
});

/**
 * Create a verified purchase from an existing order
 * This allows manual creation of verified purchases for testing or admin purposes
 */
export const createVerifiedPurchase = mutation({
  args: {
    orderId: v.id("orders"),
    verificationMethod: v.union(
      v.literal("payment_confirmation"),
      v.literal("admin_verification"),
      v.literal("manual_verification"),
      v.literal("offline_sale")
    ),
    verificationNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get order
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Check if verified purchase already exists
    const existingVerifiedPurchase = await ctx.db
      .query("verifiedPurchases")
      .withIndex("by_orderId", (q) => q.eq("orderId", args.orderId))
      .first();

    if (existingVerifiedPurchase) {
      throw new ConvexError("Verified purchase already exists for this order");
    }

    // Authorization: buyer, seller, or admin can create verified purchase
    if (order.buyerId !== user._id && order.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Unauthorized to create verified purchase for this order");
    }

    const now = Date.now();

    // Create verified purchase record
    const verifiedPurchaseId = await ctx.db.insert("verifiedPurchases", {
      orderId: args.orderId,
      buyerId: order.buyerId,
      sellerId: order.sellerId,
      productId: order.productId,
      purchaseDate: order.orderDate,
      purchaseAmount: order.totalAmount,
      paymentMethod: order.paymentMethod,
      isVerified: true,
      verificationMethod: args.verificationMethod,
      verificationNotes: args.verificationNotes,
      verifiedBy: user._id,
      verifiedAt: now,
      updatedAt: now,
    });

    // Log verified purchase creation
    await ctx.db.insert("analytics", {
      eventType: "verified_purchase_created",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.orderId,
        category: "order_management",
        verificationMethod: args.verificationMethod,
        verifiedPurchaseId,
      },
    });

    return {
      success: true,
      verifiedPurchaseId,
      message: "Verified purchase created successfully",
      verifiedAt: now,
    };
  },
});
