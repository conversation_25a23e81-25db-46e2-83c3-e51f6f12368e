# HauteVault User Management System

This document describes the comprehensive user management system for HauteVault, including mutations, queries, and best practices.

## Overview

The user management system provides secure, role-based user operations with proper validation, authorization, and audit logging. It integrates seamlessly with Better Auth and supports the luxury marketplace's specific requirements.

## Core Features

### ✅ User Lifecycle Management
- **User Creation**: Secure user registration with validation
- **Profile Updates**: Comprehensive profile management
- **Subscription Management**: Plan upgrades, downgrades, and cancellations
- **Account Deletion**: Soft and hard deletion with data cleanup

### ✅ Security & Authorization
- **Role-Based Access Control**: Consumer, Seller, Admin roles
- **Permission Validation**: Granular permission checking
- **Input Validation**: Comprehensive data validation
- **Audit Logging**: Complete activity tracking

### ✅ Profile Image Management
- **Secure Upload**: Validated file uploads with size/type restrictions
- **Image Processing**: Automatic optimization and validation
- **Storage Management**: Efficient storage with cleanup utilities

## Mutations

### 1. createUserManually
Creates a new user account with proper validation and setup.

```typescript
await ctx.runMutation(api.userManagement.createUserManually, {
  email: "<EMAIL>",
  name: "<PERSON>",
  userType: "consumer", // "consumer" | "seller" | "admin"
  subscriptionPlan: "basic", // optional
  phone: "******-0123", // optional
  profileImage: "https://...", // optional
});
```

**Features:**
- Email format validation
- Duplicate email prevention
- Automatic trial subscription setup
- Seller profile creation for seller accounts
- Analytics event logging

### 2. updateUserProfile
Updates user profile information with authorization checks.

```typescript
await ctx.runMutation(api.userManagement.updateUserProfile, {
  userId: "user_id", // optional (defaults to current user)
  name: "Updated Name",
  phone: "******-9999",
  userType: "seller", // admin only
  isVerified: true, // admin only
});
```

**Features:**
- Self-update or admin-update capabilities
- Role change validation
- Automatic seller profile creation
- Input sanitization and validation

### 3. updateSubscriptionStatus
Manages user subscription status and billing.

```typescript
await ctx.runMutation(api.userManagement.updateSubscriptionStatus, {
  subscriptionStatus: "active",
  subscriptionPlan: "premium",
  subscriptionExpiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000),
  paymentIntentId: "pi_stripe_id",
  renewalType: "upgrade",
  sendNotification: true,
});
```

**Features:**
- Subscription validation
- Expiration date management
- Payment tracking
- Notification system integration
- Analytics event logging

### 4. deleteUser (Admin Only)
Handles user account deletion with data cleanup.

```typescript
// Soft delete (recommended)
await ctx.runMutation(api.userManagement.deleteUser, {
  userId: "user_id",
  reason: "User requested deletion",
  hardDelete: false,
});

// Hard delete (GDPR compliance)
await ctx.runMutation(api.userManagement.deleteUser, {
  userId: "user_id",
  reason: "GDPR data deletion request",
  hardDelete: true,
});
```

**Features:**
- Soft delete with data preservation
- Hard delete with complete data removal
- Related data cleanup (products, orders, etc.)
- Admin-only access
- Comprehensive audit logging

## Profile Image Management

### Upload Process
```typescript
// 1. Generate upload URL
const uploadUrl = await ctx.runMutation(api.userProfileImages.generateUploadUrl);

// 2. Upload file to Convex storage
const response = await fetch(uploadUrl, {
  method: "POST",
  headers: { "Content-Type": file.type },
  body: file,
});
const { storageId } = await response.json();

// 3. Update user profile
await ctx.runMutation(api.userProfileImages.updateProfileImage, {
  storageId,
});
```

### Validation
```typescript
// Validate before upload
const validation = await ctx.runQuery(api.userProfileImages.validateImageFile, {
  fileSize: file.size,
  fileType: file.type,
});

if (!validation.isValid) {
  console.error("Validation errors:", validation.errors);
}
```

## Admin Operations

### User Statistics
```typescript
const stats = await ctx.runQuery(api.userManagement.getUserStats);
// Returns comprehensive user statistics
```

### User Search
```typescript
const users = await ctx.runQuery(api.userManagement.searchUsers, {
  query: "john",
  userType: "consumer",
  subscriptionStatus: "active",
  limit: 20,
});
```

### Bulk Operations
```typescript
await ctx.runMutation(api.userManagement.bulkUpdateSubscriptions, {
  userIds: ["user1", "user2", "user3"],
  subscriptionStatus: "active",
  subscriptionPlan: "premium",
  reason: "Promotional upgrade",
});
```

## Error Handling

All mutations include comprehensive error handling:

```typescript
try {
  await ctx.runMutation(api.userManagement.createUserManually, userData);
} catch (error) {
  if (error.message === "User with this email already exists") {
    // Handle duplicate email
  } else if (error.message === "Invalid email format") {
    // Handle validation error
  }
}
```

## Security Considerations

### Authorization Levels
1. **Self-Operations**: Users can update their own profiles
2. **Admin Operations**: Admins can manage all users
3. **Role Restrictions**: Strict role-based access control

### Data Validation
- Email format validation
- Phone number format validation
- File type and size validation
- Input sanitization

### Audit Logging
All operations are logged with:
- Event type and timestamp
- User performing the action
- Target user (if different)
- Metadata and context

## Best Practices

### 1. Always Validate Input
```typescript
const validation = await ctx.runQuery(api.userManagement.validateUserData, {
  email: userData.email,
  name: userData.name,
  phone: userData.phone,
});

if (!validation.isValid) {
  throw new Error(validation.errors.join(", "));
}
```

### 2. Handle Errors Gracefully
```typescript
try {
  const result = await ctx.runMutation(api.userManagement.updateUserProfile, data);
  return { success: true, data: result };
} catch (error) {
  return { 
    success: false, 
    error: error.message,
    code: error.code || "UNKNOWN_ERROR"
  };
}
```

### 3. Use Appropriate Deletion Type
- **Soft Delete**: For user-requested deletions (preserves data for potential restoration)
- **Hard Delete**: For GDPR compliance or security requirements

### 4. Monitor Upload Limits
```typescript
const uploadStats = await ctx.runQuery(api.userProfileImages.getUserUploadStats);
if (!uploadStats.canUpload) {
  throw new Error("Upload limit exceeded. Try again later.");
}
```

## Integration with Better Auth

The user management system integrates seamlessly with Better Auth:

```typescript
// Called by Better Auth on user creation
export const onCreateUser = async (ctx, user) => {
  return await ctx.runMutation(api.userManagement.createUserManually, {
    email: user.email,
    name: user.name,
    betterAuthUserId: user.id,
    emailVerified: user.emailVerified,
  });
};
```

## Analytics and Monitoring

All user operations generate analytics events:
- `user_created`
- `profile_updated`
- `subscription_upgraded`
- `subscription_cancelled`
- `user_verified`
- `user_deleted`
- `profile_image_updated`

These events can be used for:
- Business intelligence
- User behavior analysis
- Compliance reporting
- Performance monitoring

## File Structure

```
packages/backend/convex/
├── userManagement.ts          # Core user management mutations
├── userProfileImages.ts       # Profile image handling
├── userManagement.test.ts     # Usage examples and tests
└── USER_MANAGEMENT.md         # This documentation
```

## Support and Maintenance

For questions or issues with the user management system:
1. Check the test examples in `userManagement.test.ts`
2. Review error messages for specific guidance
3. Consult the analytics logs for debugging
4. Follow the security best practices outlined above
