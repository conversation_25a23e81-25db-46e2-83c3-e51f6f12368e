import { v } from "convex/values";
import { action, mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import type { Id } from "./_generated/dataModel";

/**
 * Process incoming Stripe webhook events
 */
export const processWebhookEvent = action({
  args: {
    eventId: v.string(),
    eventType: v.string(),
    eventData: v.any(),
    created: v.number(),
  },
  handler: async (ctx, args) => {
    // Check if we've already processed this event (idempotency)
    const existingEvent = await ctx.runQuery(internal.stripeWebhooks.getEventById, {
      eventId: args.eventId,
    });

    if (existingEvent) {
      console.log(`Event ${args.eventId} already processed`);
      return { success: true, message: "Event already processed" };
    }

    // Store the event for tracking
    await ctx.runMutation(internal.stripeWebhooks.storeEvent, {
      eventId: args.eventId,
      eventType: args.eventType,
      eventData: args.eventData,
      receivedAt: Date.now(),
    });

    try {
      // Process different event types
      switch (args.eventType) {
        case 'payment_intent.succeeded':
          await handlePaymentSucceeded(ctx, args.eventData);
          break;
          
        case 'payment_intent.payment_failed':
          await handlePaymentFailed(ctx, args.eventData);
          break;
          
        case 'account.updated':
          await handleAccountUpdated(ctx, args.eventData);
          break;
          
        case 'transfer.created':
          await handleTransferCreated(ctx, args.eventData);
          break;
          
        case 'transfer.paid':
          await handleTransferPaid(ctx, args.eventData);
          break;
          
        case 'transfer.failed':
          await handleTransferFailed(ctx, args.eventData);
          break;
          
        default:
          console.log(`Unhandled event type: ${args.eventType}`);
      }

      // Mark event as processed
      await ctx.runMutation(internal.stripeWebhooks.markEventProcessed, {
        eventId: args.eventId,
      });

      return { success: true, message: "Event processed successfully" };
    } catch (error) {
      console.error(`Error processing event ${args.eventId}:`, error);
      
      // Mark event as failed
      await ctx.runMutation(internal.stripeWebhooks.markEventFailed, {
        eventId: args.eventId,
        error: error instanceof Error ? error.message : "Unknown error",
      });

      throw new ConvexError("Failed to process webhook event");
    }
  },
});

/**
 * Handle successful payment
 */
async function handlePaymentSucceeded(ctx: any, eventData: any) {
  const paymentIntent = eventData.object;
  const orderId = paymentIntent.metadata?.orderId;

  if (!orderId) {
    console.log("No order ID found in payment intent metadata");
    return;
  }

  // Update order status
  await ctx.runMutation(internal.stripeWebhooks.updateOrderFromPayment, {
    orderId,
    paymentIntentId: paymentIntent.id,
    status: "paid",
    paidAmount: paymentIntent.amount / 100,
  });

  console.log(`Payment succeeded for order ${orderId}`);
}

/**
 * Handle failed payment
 */
async function handlePaymentFailed(ctx: any, eventData: any) {
  const paymentIntent = eventData.object;
  const orderId = paymentIntent.metadata?.orderId;

  if (!orderId) {
    console.log("No order ID found in payment intent metadata");
    return;
  }

  // Update order status
  await ctx.runMutation(internal.stripeWebhooks.updateOrderFromPayment, {
    orderId,
    paymentIntentId: paymentIntent.id,
    status: "payment_failed",
    failureReason: paymentIntent.last_payment_error?.message,
  });

  console.log(`Payment failed for order ${orderId}`);
}

/**
 * Handle Stripe Connect account updates
 */
async function handleAccountUpdated(ctx: any, eventData: any) {
  const account = eventData.object;
  
  // Find seller by Stripe account ID
  const sellerProfile = await ctx.runQuery(internal.stripeWebhooks.getSellerByStripeAccount, {
    stripeAccountId: account.id,
  });

  if (!sellerProfile) {
    console.log(`No seller found for Stripe account ${account.id}`);
    return;
  }

  // Update seller's Stripe status
  await ctx.runMutation(internal.stripeWebhooks.updateSellerStripeStatus, {
    userId: sellerProfile.userId,
    chargesEnabled: account.charges_enabled,
    payoutsEnabled: account.payouts_enabled,
    detailsSubmitted: account.details_submitted,
    requirements: account.requirements?.currently_due || [],
  });

  console.log(`Updated Stripe status for seller ${sellerProfile.userId}`);
}

/**
 * Handle transfer creation
 */
async function handleTransferCreated(ctx: any, eventData: any) {
  const transfer = eventData.object;
  const orderId = transfer.metadata?.orderId;

  if (orderId) {
    await ctx.runMutation(internal.stripeWebhooks.updateOrderTransfer, {
      orderId,
      transferId: transfer.id,
      transferAmount: transfer.amount / 100,
      status: "transfer_created",
    });
  }

  console.log(`Transfer created: ${transfer.id}`);
}

/**
 * Handle successful transfer
 */
async function handleTransferPaid(ctx: any, eventData: any) {
  const transfer = eventData.object;
  const orderId = transfer.metadata?.orderId;

  if (orderId) {
    await ctx.runMutation(internal.stripeWebhooks.updateOrderTransfer, {
      orderId,
      transferId: transfer.id,
      transferAmount: transfer.amount / 100,
      status: "transfer_paid",
    });
  }

  console.log(`Transfer paid: ${transfer.id}`);
}

/**
 * Handle failed transfer
 */
async function handleTransferFailed(ctx: any, eventData: any) {
  const transfer = eventData.object;
  const orderId = transfer.metadata?.orderId;

  if (orderId) {
    await ctx.runMutation(internal.stripeWebhooks.updateOrderTransfer, {
      orderId,
      transferId: transfer.id,
      transferAmount: transfer.amount / 100,
      status: "transfer_failed",
      failureReason: transfer.failure_message,
    });
  }

  console.log(`Transfer failed: ${transfer.id}`);
}

/**
 * Internal query to get event by ID
 */
export const getEventById = query({
  args: {
    eventId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("stripeEvents")
      .withIndex("by_stripeEventId", (q) => q.eq("stripeEventId", args.eventId))
      .first();
  },
});

/**
 * Internal mutation to store webhook event
 */
export const storeEvent = mutation({
  args: {
    eventId: v.string(),
    eventType: v.string(),
    eventData: v.any(),
    receivedAt: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("stripeEvents", {
      stripeEventId: args.eventId,
      eventType: args.eventType,
      eventData: args.eventData,
      processed: false,
      receivedAt: args.receivedAt,
    });

    return { success: true };
  },
});

/**
 * Internal mutation to mark event as processed
 */
export const markEventProcessed = mutation({
  args: {
    eventId: v.string(),
  },
  handler: async (ctx, args) => {
    const event = await ctx.db
      .query("stripeEvents")
      .withIndex("by_stripeEventId", (q) => q.eq("stripeEventId", args.eventId))
      .first();

    if (event) {
      await ctx.db.patch(event._id, {
        processed: true,
        processedAt: Date.now(),
      });
    }

    return { success: true };
  },
});

/**
 * Internal mutation to mark event as failed
 */
export const markEventFailed = mutation({
  args: {
    eventId: v.string(),
    error: v.string(),
  },
  handler: async (ctx, args) => {
    const event = await ctx.db
      .query("stripeEvents")
      .withIndex("by_stripeEventId", (q) => q.eq("stripeEventId", args.eventId))
      .first();

    if (event) {
      await ctx.db.patch(event._id, {
        processed: false,
        processingError: args.error,
        processedAt: Date.now(),
      });
    }

    return { success: true };
  },
});

// Additional internal mutations for webhook handlers
export const updateOrderFromPayment = mutation({
  args: {
    orderId: v.string(),
    paymentIntentId: v.string(),
    status: v.string(),
    paidAmount: v.optional(v.number()),
    failureReason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId as Id<"orders">);
    if (!order) return { success: false };

    const updateData: any = {
      stripePaymentIntentId: args.paymentIntentId,
      updatedAt: Date.now(),
    };

    if (args.status === "paid" && args.paidAmount) {
      updateData.orderStatus = "paid";
      updateData.paidDate = Date.now();
    } else if (args.status === "payment_failed") {
      updateData.orderStatus = "cancelled";
      updateData.cancelledDate = Date.now();
      updateData.cancellationReason = args.failureReason || "Payment failed";
    }

    await ctx.db.patch(args.orderId as Id<"orders">, updateData);
    return { success: true };
  },
});

export const getSellerByStripeAccount = query({
  args: {
    stripeAccountId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("sellerProfiles")
      .filter((q) => q.eq(q.field("stripeConnectAccountId"), args.stripeAccountId))
      .first();
  },
});

export const updateSellerStripeStatus = mutation({
  args: {
    userId: v.id("users"),
    chargesEnabled: v.boolean(),
    payoutsEnabled: v.boolean(),
    detailsSubmitted: v.boolean(),
    requirements: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    if (!sellerProfile) return { success: false };

    const isEnabled = args.chargesEnabled && args.payoutsEnabled && args.detailsSubmitted;
    const status = isEnabled ? "enabled" : args.requirements.length > 0 ? "restricted" : "pending";

    await ctx.db.patch(sellerProfile._id, {
      stripeAccountStatus: status,
      stripeOnboardingComplete: isEnabled,
      stripeChargesEnabled: args.chargesEnabled,
      stripePayoutsEnabled: args.payoutsEnabled,
      stripeDetailsSubmitted: args.detailsSubmitted,
      stripeRequirements: args.requirements,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const updateOrderTransfer = mutation({
  args: {
    orderId: v.string(),
    transferId: v.string(),
    transferAmount: v.number(),
    status: v.string(),
    failureReason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId as Id<"orders">);
    if (!order) return { success: false };

    await ctx.db.patch(args.orderId as Id<"orders">, {
      stripeTransferId: args.transferId,
      sellerEarnings: args.transferAmount,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Export internal functions
export const internal = {
  stripeWebhooks: {
    getEventById,
    storeEvent,
    markEventProcessed,
    markEventFailed,
    updateOrderFromPayment,
    getSellerByStripeAccount,
    updateSellerStripeStatus,
    updateOrderTransfer,
  },
};
