import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { requireAuth, requireAdmin } from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Submit a simple seller application
 */
export const submitApplication = mutation({
  args: {
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    dateOfBirth: v.string(),
    businessName: v.string(),
    businessType: v.union(
      v.literal("individual"),
      v.literal("llc"),
      v.literal("corporation"),
      v.literal("partnership")
    ),
    taxId: v.string(),
    businessAddress: v.string(),
    businessCity: v.string(),
    businessState: v.string(),
    businessZip: v.string(),
    businessCountry: v.string(),
    yearsExperience: v.string(),
    previousPlatforms: v.array(v.string()),
    monthlyVolume: v.string(),
    specialties: v.array(v.string()),
    termsAccepted: v.boolean(),
    privacyAccepted: v.boolean(),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Validate required fields
    if (!args.termsAccepted || !args.privacyAccepted) {
      throw new ConvexError("You must accept the terms and privacy policy");
    }

    if (args.businessName.trim().length < 2) {
      throw new ConvexError("Business name must be at least 2 characters");
    }

    // Check if user already has an application
    const existingApplication = await ctx.db
      .query("sellerApplications")
      .withIndex("email", (q) => q.eq("email", args.email))
      .first();

    if (existingApplication) {
      throw new ConvexError("You already have a seller application");
    }

    // Validate phone number format
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    if (!phoneRegex.test(args.phone)) {
      throw new ConvexError("Invalid phone number format");
    }

    // Create the application
    const applicationId = await ctx.db.insert("sellerApplications", {
      firstName: args.firstName.trim(),
      lastName: args.lastName.trim(),
      email: args.email.toLowerCase().trim(),
      phone: args.phone.trim(),
      dateOfBirth: args.dateOfBirth,
      businessName: args.businessName.trim(),
      businessType: args.businessType,
      taxId: args.taxId.trim(),
      businessAddress: args.businessAddress.trim(),
      businessCity: args.businessCity.trim(),
      businessState: args.businessState.trim(),
      businessZip: args.businessZip.trim(),
      businessCountry: args.businessCountry.trim(),
      yearsExperience: args.yearsExperience,
      previousPlatforms: args.previousPlatforms,
      monthlyVolume: args.monthlyVolume,
      specialties: args.specialties,
      termsAccepted: args.termsAccepted,
      privacyAccepted: args.privacyAccepted,
      status: "pending",
      submittedAt: Date.now(),
    });

    // Log analytics event
    await ctx.db.insert("analytics", {
      eventType: "seller_application_submitted",
      userId: user._id,
      timestamp: Date.now(),
      metadata: {
        category: "seller_onboarding",
        source: "application_form",
      },
    });

    return applicationId;
  },
});

/**
 * Get all seller applications (admin only)
 */
export const getAllApplications = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("requires_info")
    )),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    if (args.status) {
      return await ctx.db
        .query("sellerApplications")
        .withIndex("status", (q) => q.eq("status", args.status as "pending" | "under_review" | "approved" | "rejected" | "requires_info"))
        .order("desc")
        .collect();
    }
    
    return await ctx.db
      .query("sellerApplications")
      .order("desc")
      .collect();
  },
});

/**
 * Update application status (admin only)
 */
export const updateApplicationStatus = mutation({
  args: {
    applicationId: v.id("sellerApplications"),
    status: v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("requires_info")
    ),
    notes: v.optional(v.string()),
    rejectionReason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const application = await ctx.db.get(args.applicationId);
    if (!application) {
      throw new ConvexError("Application not found");
    }

    // Update the application
    await ctx.db.patch(args.applicationId, {
      status: args.status,
      notes: args.notes,
      rejectionReason: args.rejectionReason,
      reviewedAt: Date.now(),
    });

    // If approved, create a seller profile and update user type
    if (args.status === "approved") {
      // Find the user by email
      const user = await ctx.db
        .query("users")
        .withIndex("by_email", (q) => q.eq("email", application.email))
        .first();

      if (user) {
        // Update user type to seller
        await ctx.db.patch(user._id, {
          userType: "seller",
          updatedAt: Date.now(),
        });

        // Create seller profile
        await ctx.db.insert("sellerProfiles", {
          userId: user._id,
          businessName: application.businessName,
          businessType: application.businessType,
          phone: application.phone,
          address: {
            street: application.businessAddress,
            city: application.businessCity,
            state: application.businessState,
            zipCode: application.businessZip,
            country: application.businessCountry,
          },
          verificationStatus: "approved",
          commissionRate: 5,  
          applicationDate: application.submittedAt,
          approvedDate: Date.now(),
          updatedAt: Date.now(),
          isActive: true,
        });
      }
    }

    // Log analytics event
    await ctx.db.insert("analytics", {
      eventType: "seller_application_reviewed",
      timestamp: Date.now(),
      metadata: {
        category: "admin_action",
        source: args.status,
      },
    });

    return { success: true };
  },
});

/**
 * Get application by ID
 */
export const getApplicationById = query({
  args: { id: v.id("sellerApplications") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

/**
 * Get user's application by email
 */
export const getUserApplication = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("sellerApplications")
      .withIndex("email", (q) => q.eq("email", args.email))
      .first();
  },
});
