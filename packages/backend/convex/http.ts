import { httpRouter } from "convex/server";
import { auth } from "../better-auth/server";
import { betterAuthComponent } from "./auth";

const http = httpRouter();

// { cors: true } is required for client side frameworks
betterAuthComponent.registerRoutes(http, auth, { cors: true });

// Custom endpoint to update Better Auth user data
(http as any).route({
  path: "/sync-user",
  method: "POST",
  handler: async (request: any, ctx: any) => {
    try {
      const { userId, name } = await request.json();
      
      if (!userId || !name) {
        return new Response("Missing required fields", { status: 400 });
      }

      // Use Better Auth's server instance to update the user
      // TODO: Implement proper Better Auth API integration
      // const result = await auth.api.updateUser({
      //   userId,
      //   update: {
      //     name,
      //   },
      // });

      console.log("Sync user endpoint called:", userId, name);
      
      return new Response(JSON.stringify({ success: true, message: "User sync endpoint called" }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Failed to update Better Auth user:", error);
      return new Response(JSON.stringify({ error: error instanceof Error ? error.message : "Unknown error" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  },
});

export default http;
