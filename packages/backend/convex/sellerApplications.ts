import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { sendEmail } from "./lib/email";
import {
  requireAuth,
  requireAdmin
} from "./lib/auth_utils";
import SellerApplicationReceived from "@repo/email/templates/seller-application-received";
import SellerApplicationApproved from "@repo/email/templates/seller-application-approved";
import SellerApplicationRejected from "@repo/email/templates/seller-application-rejected";
import type { Id } from "./_generated/dataModel";

/**
 * Submit seller application with comprehensive validation and integration
 */
export const submitSellerApplication = mutation({
  args: {
    businessName: v.string(),
    businessType: v.string(),
    taxId: v.optional(v.string()),
    address: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    phone: v.string(),
    bankDetails: v.object({
      accountHolderName: v.string(),
      bankName: v.string(),
      accountNumber: v.string(),
      routingNumber: v.string(),
      accountType: v.union(v.literal("checking"), v.literal("savings")),
    }),
    verificationDocuments: v.array(v.object({
      type: v.union(
        v.literal("business_license"),
        v.literal("tax_document"),
        v.literal("identity_document"),
        v.literal("bank_statement"),
        v.literal("other")
      ),
      storageId: v.id("_storage"),
      fileName: v.string(),
      fileSize: v.number(),
      uploadedAt: v.number(),
    })),
    businessDescription: v.optional(v.string()),
    expectedMonthlyVolume: v.optional(v.number()),
    productCategories: v.array(v.string()),
    hasBusinessLicense: v.boolean(),
    agreeToTerms: v.boolean(),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Validate user is not already a seller or has pending application
    const existingProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    if (existingProfile) {
      if (existingProfile.verificationStatus === "pending") {
        throw new ConvexError("You already have a pending seller application");
      }
      if (existingProfile.verificationStatus === "approved") {
        throw new ConvexError("You are already an approved seller");
      }
      if (existingProfile.verificationStatus === "under_review") {
        throw new ConvexError("Your application is currently under review");
      }
    }

    // Validate required fields
    if (!args.agreeToTerms) {
      throw new ConvexError("You must agree to the terms and conditions");
    }

    if (args.businessName.trim().length < 2) {
      throw new ConvexError("Business name must be at least 2 characters");
    }

    if (args.verificationDocuments.length === 0) {
      throw new ConvexError("At least one verification document is required");
    }

    // Validate phone number format
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    if (!phoneRegex.test(args.phone)) {
      throw new ConvexError("Invalid phone number format");
    }

    // Validate bank account details
    if (args.bankDetails.accountNumber.length < 8) {
      throw new ConvexError("Invalid account number");
    }

    if (args.bankDetails.routingNumber.length !== 9) {
      throw new ConvexError("Routing number must be 9 digits");
    }

    // Validate documents
    for (const doc of args.verificationDocuments) {
      if (doc.fileSize > 10 * 1024 * 1024) { // 10MB limit
        throw new ConvexError(`Document ${doc.fileName} exceeds 10MB limit`);
      }

      // Verify storage ID exists
      const file = await ctx.storage.getUrl(doc.storageId);
      if (!file) {
        throw new ConvexError(`Document ${doc.fileName} not found in storage`);
      }
    }

    const now = Date.now();

    // Encrypt sensitive bank details (in production, use proper encryption)
    const encryptedBankDetails = {
      ...args.bankDetails,
      accountNumber: `***${args.bankDetails.accountNumber.slice(-4)}`, // Mask for storage
    };

    // Convert storage documents to URL format
    const documentUrls = await Promise.all(
      args.verificationDocuments.map(async (doc) => {
        const url = await ctx.storage.getUrl(doc.storageId);
        return {
          type: doc.type,
          url: url || "",
          fileName: doc.fileName,
          fileSize: doc.fileSize,
          uploadedAt: doc.uploadedAt,
        };
      })
    );

    // Create or update seller profile
    let profileId;
    if (existingProfile) {
      // Update existing rejected profile
      profileId = existingProfile._id;
      await ctx.db.patch(profileId, {
        businessName: args.businessName,
        businessType: args.businessType,
        taxId: args.taxId,
        address: args.address,
        phone: args.phone,
        bankDetails: encryptedBankDetails,
        verificationDocuments: documentUrls,
        verificationStatus: "pending",
        applicationDate: now,
        rejectedDate: undefined,
        rejectionReason: undefined,
        notes: undefined,
        isActive: true,
        updatedAt: now,
      });
    } else {
      // Create new seller profile
      profileId = await ctx.db.insert("sellerProfiles", {
        userId: user._id,
        businessName: args.businessName,
        businessType: args.businessType,
        taxId: args.taxId,
        address: args.address,
        phone: args.phone,
        bankDetails: encryptedBankDetails,
        verificationDocuments: documentUrls,
        verificationStatus: "pending",
        applicationDate: now,
        isActive: true,
        updatedAt: now,
      });
    }

    // Update user type to seller
    await ctx.db.patch(user._id, {
      userType: "seller",
      updatedAt: now,
    });

    // Log application submission
    await ctx.db.insert("analytics", {
      eventType: "seller_application_submitted",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: profileId,
        category: "seller_management",
        revenue: 0,
      },
    });

    // Send confirmation email
    await sendEmail(ctx, {
      to: user.email,
      subject: "Seller Application Received - HauteVault",
      react: SellerApplicationReceived({
        name: user.name,
        applicationId: `HV-${profileId.slice(-8)}`,
        businessName: args.businessName,
      }),
    });

    return {
      success: true,
      profileId,
      message: "Seller application submitted successfully. You will be notified once reviewed.",
      estimatedReviewTime: "3-5 business days",
    };
  },
});

// Keep the legacy function for backward compatibility
export const submitApplication = submitSellerApplication;

export const getApplicationByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("sellerApplications")
      .withIndex("email", (q) => q.eq("email", args.email))
      .first();
  },
});

export const getApplicationById = query({
  args: { id: v.id("sellerApplications") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Admin functions
export const getAllApplications = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("requires_info")
    )),
  },
  handler: async (ctx, args) => {
    if (args.status) {
      return await ctx.db
        .query("sellerApplications")
        .withIndex("status", (q) => q.eq("status", args.status as "pending" | "approved" | "rejected" | "under_review" | "requires_info"))
        .order("desc")
        .collect();
    }
    
    return await ctx.db
      .query("sellerApplications")
      .order("desc")
      .collect();
  },
});

/**
 * Update seller application status (admin only)
 */
export const updateApplicationStatus = mutation({
  args: {
    profileId: v.id("sellerProfiles"),
    status: v.union(
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("under_review"),
      v.literal("pending")
    ),
    adminNotes: v.optional(v.string()),
    rejectionReason: v.optional(v.string()),
    commissionRate: v.optional(v.number()),
    sendNotification: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Only admins can update application status
    const admin = await requireAdmin(ctx);

    // Get seller profile
    const profile = await ctx.db.get(args.profileId);
    if (!profile) {
      throw new ConvexError("Seller profile not found");
    }

    // Get seller user
    const seller = await ctx.db.get(profile.userId);
    if (!seller) {
      throw new ConvexError("Seller user not found");
    }

    // Validate status change
    if (args.status === "rejected" && !args.rejectionReason) {
      throw new ConvexError("Rejection reason is required when rejecting application");
    }

    if (args.status === "approved" && profile.verificationStatus === "approved") {
      throw new ConvexError("Application is already approved");
    }

    const now = Date.now();
    const previousStatus = profile.verificationStatus;

    // Prepare update data
    const updateData: any = {
      verificationStatus: args.status,
      notes: args.adminNotes,
      updatedAt: now,
    };

    // Handle status-specific updates
    switch (args.status) {
      case "approved":
        updateData.approvedDate = now;
        updateData.rejectedDate = undefined;
        updateData.rejectionReason = undefined;
        updateData.commissionRate = args.commissionRate || 0.1; // Default 10%
        updateData.isActive = true;
        break;

      case "rejected":
        updateData.rejectedDate = now;
        updateData.rejectionReason = args.rejectionReason;
        updateData.approvedDate = undefined;
        updateData.isActive = false;
        break;

      case "under_review":
        updateData.isActive = true;
        break;

      case "pending":
        updateData.approvedDate = undefined;
        updateData.rejectedDate = undefined;
        updateData.rejectionReason = undefined;
        updateData.isActive = true;
        break;
    }

    // Update seller profile
    await ctx.db.patch(args.profileId, updateData);

    // Handle seller account status
    if (args.status === "approved") {
      // Ensure user has active subscription for selling
      if (seller.subscriptionStatus !== "active") {
        // Could auto-upgrade to basic plan or require manual subscription
        console.log(`Approved seller ${seller._id} needs active subscription`);
      }
    } else if (args.status === "rejected") {
      // Optionally revert user type back to consumer
      await ctx.db.patch(profile.userId, {
        userType: "consumer",
        updatedAt: now,
      });
    }

    // Log status change
    await ctx.db.insert("analytics", {
      eventType: `seller_application_${args.status}`,
      userId: profile.userId,
      timestamp: now,
      metadata: {
        source: args.profileId,
        category: "admin_action",
        revenue: args.commissionRate,
      },
    });

    // Send notification email to seller
    if (args.sendNotification !== false) {
      if (args.status === "approved") {
        await sendEmail(ctx, {
          to: seller.email,
          subject: "Congratulations! Your Seller Application Has Been Approved",
          react: SellerApplicationApproved({
            name: seller.name,
            businessName: profile.businessName || "Your Business",
          }),
        });
      } else if (args.status === "rejected") {
        await sendEmail(ctx, {
          to: seller.email,
          subject: "Update on Your Seller Application",
          react: SellerApplicationRejected({
            name: seller.name,
            reason: args.rejectionReason || "Application did not meet our requirements",
          }),
        });
      }
    }

    return {
      success: true,
      message: `Application ${args.status} successfully`,
      newStatus: args.status,
      notificationSent: args.sendNotification !== false,
    };
  },
});

/**
 * Update seller profile information
 */
export const updateSellerProfile = mutation({
  args: {
    profileId: v.optional(v.id("sellerProfiles")), // Optional for self-update
    businessName: v.optional(v.string()),
    businessType: v.optional(v.string()),
    taxId: v.optional(v.string()),
    address: v.optional(v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    })),
    phone: v.optional(v.string()),
    businessDescription: v.optional(v.string()),
    expectedMonthlyVolume: v.optional(v.number()),
    productCategories: v.optional(v.array(v.string())),
    bankDetails: v.optional(v.object({
      accountHolderName: v.string(),
      bankName: v.string(),
      accountNumber: v.string(),
      routingNumber: v.string(),
      accountType: v.union(v.literal("checking"), v.literal("savings")),
    })),
    requiresReApproval: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Determine target profile
    let profile;
    if (args.profileId) {
      // Admin or specific profile update
      if (user.userType !== "admin") {
        throw new ConvexError("Only admins can update other seller profiles");
      }
      profile = await ctx.db.get(args.profileId);
    } else {
      // Self-update
      profile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", user._id))
        .first();
    }

    if (!profile) {
      throw new ConvexError("Seller profile not found");
    }

    // Check if seller can make updates
    if (profile.verificationStatus === "under_review") {
      throw new ConvexError("Cannot update profile while under review");
    }

    // Validate updates
    if (args.businessName && args.businessName.trim().length < 2) {
      throw new ConvexError("Business name must be at least 2 characters");
    }

    if (args.phone) {
      const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
      if (!phoneRegex.test(args.phone)) {
        throw new ConvexError("Invalid phone number format");
      }
    }

    const now = Date.now();
    let requiresReApproval = args.requiresReApproval || false;

    // Check if major changes require re-approval
    const majorChangeFields = ['businessName', 'businessType', 'taxId', 'bankDetails'];
    const hasMajorChanges = majorChangeFields.some(field => args[field as keyof typeof args] !== undefined);

    if (hasMajorChanges && profile.verificationStatus === "approved") {
      requiresReApproval = true;
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: now,
    };

    // Add provided fields
    if (args.businessName) updateData.businessName = args.businessName;
    if (args.businessType) updateData.businessType = args.businessType;
    if (args.taxId !== undefined) updateData.taxId = args.taxId;
    if (args.address) updateData.address = args.address;
    if (args.phone) updateData.phone = args.phone;
    if (args.businessDescription !== undefined) updateData.businessDescription = args.businessDescription;
    if (args.expectedMonthlyVolume !== undefined) updateData.expectedMonthlyVolume = args.expectedMonthlyVolume;
    if (args.productCategories) updateData.productCategories = args.productCategories;

    // Handle bank details with encryption
    if (args.bankDetails) {
      // Validate bank details
      if (args.bankDetails.accountNumber.length < 8) {
        throw new ConvexError("Invalid account number");
      }
      if (args.bankDetails.routingNumber.length !== 9) {
        throw new ConvexError("Routing number must be 9 digits");
      }

      // Encrypt sensitive data
      updateData.bankDetails = {
        ...args.bankDetails,
        accountNumber: `***${args.bankDetails.accountNumber.slice(-4)}`,
      };
    }

    // Handle re-approval requirement
    if (requiresReApproval) {
      updateData.verificationStatus = "pending";
      updateData.approvedDate = undefined;
    }

    // Update profile
    await ctx.db.patch(profile._id, updateData);

    // Log profile update
    await ctx.db.insert("analytics", {
      eventType: "seller_profile_updated",
      userId: profile.userId,
      timestamp: now,
      metadata: {
        source: profile._id,
        category: "seller_management",
      },
    });

    return {
      success: true,
      message: "Profile updated successfully",
      requiresReApproval,
      newStatus: requiresReApproval ? "pending" : profile.verificationStatus,
    };
  },
});

/**
 * Suspend seller account (admin only)
 */
export const suspendSeller = mutation({
  args: {
    profileId: v.id("sellerProfiles"),
    reason: v.string(),
    suspensionType: v.union(
      v.literal("temporary"),
      v.literal("permanent")
    ),
    suspensionDuration: v.optional(v.number()), // Days for temporary suspension
    sendNotification: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Only admins can suspend sellers
    const admin = await requireAdmin(ctx);

    // Get seller profile
    const profile = await ctx.db.get(args.profileId);
    if (!profile) {
      throw new ConvexError("Seller profile not found");
    }

    // Get seller user
    const seller = await ctx.db.get(profile.userId);
    if (!seller) {
      throw new ConvexError("Seller user not found");
    }

    if (profile.verificationStatus !== "approved") {
      throw new ConvexError("Can only suspend approved sellers");
    }

    const now = Date.now();
    const suspensionEnds = args.suspensionType === "temporary" && args.suspensionDuration
      ? now + (args.suspensionDuration * 24 * 60 * 60 * 1000)
      : undefined;

    // Update seller profile
    await ctx.db.patch(args.profileId, {
      verificationStatus: "rejected", // Use rejected to disable selling
      isActive: false,
      notes: `SUSPENDED: ${args.reason}`,
      rejectedDate: now,
      rejectionReason: `Suspended - ${args.reason}`,
      updatedAt: now,
    });

    // Hide all seller's active products
    const products = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", profile.userId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    for (const product of products) {
      await ctx.db.patch(product._id, {
        status: "archived", // Use archived instead of removed
        updatedAt: now,
      });
    }

    // Log suspension
    await ctx.db.insert("analytics", {
      eventType: "seller_suspended",
      userId: profile.userId,
      timestamp: now,
      metadata: {
        source: args.profileId,
        category: "admin_action",
      },
    });

    // Send notification email
    if (args.sendNotification !== false) {
      await sendEmail(ctx, {
        to: seller.email,
        subject: "Important: Your Seller Account Has Been Suspended",
        react: SellerApplicationRejected({
          name: seller.name,
          reason: `Your account has been suspended. Reason: ${args.reason}`,
        }),
      });
    }

    return {
      success: true,
      message: `Seller suspended successfully`,
      suspensionType: args.suspensionType,
      suspensionEnds,
      productsHidden: products.length,
      notificationSent: args.sendNotification !== false,
    };
  },
});

/**
 * Generate upload URL for seller documents
 */
export const generateDocumentUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    // Require authentication
    await requireAuth(ctx);

    // Generate upload URL for documents
    return await ctx.storage.generateUploadUrl();
  },
});

/**
 * Add verification document to seller profile
 */
export const addVerificationDocument = mutation({
  args: {
    profileId: v.optional(v.id("sellerProfiles")),
    document: v.object({
      type: v.union(
        v.literal("business_license"),
        v.literal("tax_document"),
        v.literal("identity_document"),
        v.literal("bank_statement"),
        v.literal("other")
      ),
      storageId: v.id("_storage"),
      fileName: v.string(),
      fileSize: v.number(),
    }),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Determine target profile
    let profile;
    if (args.profileId) {
      if (user.userType !== "admin") {
        throw new ConvexError("Only admins can update other seller profiles");
      }
      profile = await ctx.db.get(args.profileId);
    } else {
      profile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", user._id))
        .first();
    }

    if (!profile) {
      throw new ConvexError("Seller profile not found");
    }

    // Validate file size
    if (args.document.fileSize > 10 * 1024 * 1024) {
      throw new ConvexError("File size too large. Maximum size is 10MB.");
    }

    const now = Date.now();

    // Get document URL
    const documentUrl = await ctx.storage.getUrl(args.document.storageId);

    // Add document to profile
    const newDocument = {
      type: args.document.type,
      url: documentUrl || "",
      fileName: args.document.fileName,
      fileSize: args.document.fileSize,
      uploadedAt: now,
    };

    const existingDocs = profile.verificationDocuments || [];
    const updatedDocs = [...existingDocs, newDocument];

    await ctx.db.patch(profile._id, {
      verificationDocuments: updatedDocs,
      updatedAt: now,
    });

    // Log document upload
    await ctx.db.insert("analytics", {
      eventType: "verification_document_uploaded",
      userId: profile.userId,
      timestamp: now,
      metadata: {
        source: profile._id,
        category: "seller_management",
      },
    });

    return {
      success: true,
      message: "Document added successfully",
      documentCount: updatedDocs.length,
    };
  },
});

/**
 * Remove verification document from seller profile
 */
export const removeVerificationDocument = mutation({
  args: {
    profileId: v.optional(v.id("sellerProfiles")),
    documentIndex: v.number(),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Determine target profile
    let profile;
    if (args.profileId) {
      if (user.userType !== "admin") {
        throw new ConvexError("Only admins can update other seller profiles");
      }
      profile = await ctx.db.get(args.profileId);
    } else {
      profile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", user._id))
        .first();
    }

    if (!profile) {
      throw new ConvexError("Seller profile not found");
    }

    const existingDocs = profile.verificationDocuments || [];

    if (args.documentIndex < 0 || args.documentIndex >= existingDocs.length) {
      throw new ConvexError("Invalid document index");
    }

    // Remove document
    const updatedDocs = existingDocs.filter((_, index) => index !== args.documentIndex);

    await ctx.db.patch(profile._id, {
      verificationDocuments: updatedDocs,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      message: "Document removed successfully",
      documentCount: updatedDocs.length,
    };
  },
});

/**
 * Get seller application statistics (admin only)
 */
export const getApplicationStatistics = query({
  args: {
    timeRange: v.optional(v.union(
      v.literal("7d"),
      v.literal("30d"),
      v.literal("90d"),
      v.literal("1y")
    )),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const timeRange = args.timeRange || "30d";
    const now = Date.now();
    const timeRanges = {
      "7d": 7 * 24 * 60 * 60 * 1000,
      "30d": 30 * 24 * 60 * 60 * 1000,
      "90d": 90 * 24 * 60 * 60 * 1000,
      "1y": 365 * 24 * 60 * 60 * 1000,
    };

    const startTime = now - timeRanges[timeRange];

    // Get all seller profiles
    const allProfiles = await ctx.db.query("sellerProfiles").collect();

    // Filter by time range
    const profilesInRange = allProfiles.filter(p => p.applicationDate >= startTime);

    // Calculate statistics
    const totalApplications = profilesInRange.length;
    const pendingApplications = profilesInRange.filter(p => p.verificationStatus === "pending").length;
    const underReviewApplications = profilesInRange.filter(p => p.verificationStatus === "under_review").length;
    const approvedApplications = profilesInRange.filter(p => p.verificationStatus === "approved").length;
    const rejectedApplications = profilesInRange.filter(p => p.verificationStatus === "rejected").length;

    // Calculate approval rate
    const processedApplications = approvedApplications + rejectedApplications;
    const approvalRate = processedApplications > 0 ? (approvedApplications / processedApplications) * 100 : 0;

    // Average processing time
    const processedProfiles = allProfiles.filter(p =>
      p.approvedDate || p.rejectedDate
    );

    const avgProcessingTime = processedProfiles.length > 0
      ? processedProfiles.reduce((sum, p) => {
          const processedDate = p.approvedDate || p.rejectedDate || 0;
          return sum + (processedDate - p.applicationDate);
        }, 0) / processedProfiles.length
      : 0;

    return {
      timeRange,
      totalApplications,
      statusBreakdown: {
        pending: pendingApplications,
        underReview: underReviewApplications,
        approved: approvedApplications,
        rejected: rejectedApplications,
      },
      approvalRate: Math.round(approvalRate * 100) / 100,
      averageProcessingTime: Math.round(avgProcessingTime / (24 * 60 * 60 * 1000) * 100) / 100, // Days
      generatedAt: now,
    };
  },
});

// Legacy function for backward compatibility
export const uploadDocument = generateDocumentUploadUrl;