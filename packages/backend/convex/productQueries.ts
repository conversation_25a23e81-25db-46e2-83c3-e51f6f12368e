import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAuth, 
  requireAdmin 
} from "./lib/auth_utils";
import { calculateSellerRevenue } from "./lib/utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get marketplace products for public browsing (subscription required)
 */
export const getMarketplaceProducts = query({
  args: {
    category: v.optional(v.union(
      v.literal("clothing"),
      v.literal("sneakers"),
      v.literal("collectibles"),
      v.literal("accessories"),
      v.literal("handbags"),
      v.literal("jewelry"),
      v.literal("watches"),
      v.literal("sunglasses")
    )),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    brands: v.optional(v.array(v.string())),
    conditions: v.optional(v.array(v.string())),
    searchQuery: v.optional(v.string()),
    sortBy: v.optional(v.union(
      v.literal("newest"),
      v.literal("price_low"),
      v.literal("price_high"),
      v.literal("popular")
    )),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Marketplace is public - no authentication required
    // Optional: Get user if they're logged in for personalization
    const user = await getAuthUser(ctx);

    const limit = Math.min(args.limit || 24, 100); // Max 100 products per query
    const offset = args.offset || 0;
    const sortBy = args.sortBy || "newest";

    // Build the base query with proper indexing
    let baseQuery = ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"));

    // Apply category filter if provided (use index for better performance)
    if (args.category) {
      baseQuery = ctx.db
        .query("products")
        .withIndex("by_category_status", (q) => q.eq("category", args.category!).eq("status", "active"));
    }

    // Apply price filters using index if available
    if (args.minPrice !== undefined || args.maxPrice !== undefined) {
      // Use price index for better performance on price ranges
      baseQuery = ctx.db
        .query("products")
        .withIndex("by_status", (q) => q.eq("status", "active"));
    }

    // Get total count for pagination info
    const allProducts = await baseQuery.collect();
    
    // Apply filters in memory (for now - we'll optimize this later)
    let filteredProducts = allProducts;

    if (args.minPrice !== undefined) {
      filteredProducts = filteredProducts.filter(p => p.price >= args.minPrice!);
    }

    if (args.maxPrice !== undefined) {
      filteredProducts = filteredProducts.filter(p => p.price <= args.maxPrice!);
    }

    if (args.brands && args.brands.length > 0) {
      filteredProducts = filteredProducts.filter(p => 
        p.brand && args.brands!.some(filterBrand => 
          p.brand.toLowerCase() === filterBrand.toLowerCase()
        )
      );
    }

    if (args.conditions && args.conditions.length > 0) {
      filteredProducts = filteredProducts.filter(p => args.conditions!.includes(p.condition));
    }

    // Apply search filter
    if (args.searchQuery) {
      const searchTerm = args.searchQuery.toLowerCase().trim();
      filteredProducts = filteredProducts.filter(p => {
        const searchableText = [
          p.title,
          p.description,
          p.brand || "",
          ...(p.tags || [])
        ].join(" ").toLowerCase();
        
        return searchableText.includes(searchTerm);
      });
    }

    // Sort products
    filteredProducts.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return (b.publishedAt || 0) - (a.publishedAt || 0);
        case "price_low":
          return a.price - b.price;
        case "price_high":
          return b.price - a.price;
        case "popular":
          return (b.views || 0) - (a.views || 0);
        default:
          return (b.publishedAt || 0) - (a.publishedAt || 0);
      }
    });

    // Apply pagination
    const paginatedProducts = filteredProducts.slice(offset, offset + limit);

    // Enrich with seller data
    const enrichedProducts = await Promise.all(
      paginatedProducts.map(async (product) => {
        const seller = await ctx.db.get(product.sellerId);
        
        // Get seller profile for rating
        const sellerProfile = await ctx.db
          .query("sellerProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", product.sellerId))
          .first();

        // Convert storage IDs to URLs
        const imageUrls = await Promise.all(
          product.images
            .filter(imageId => imageId !== "")
            .map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url;
            })
        );
        const validImageUrls = imageUrls.filter(url => url !== null) as string[];

        return {
          _id: product._id,
          title: product.title,
          description: product.description,
          price: product.price,
          condition: product.condition,
          brand: product.brand,
          size: product.size,
          color: product.color,
          material: product.material,
          tags: product.tags,
          images: validImageUrls,
          views: product.views || 0,
          likes: product.favorites || 0,
          publishedAt: product.publishedAt,
          category: {
            name: product.category,
            slug: product.category,
          },
          seller: seller ? {
            _id: seller._id,
            name: seller.name,
            businessName: sellerProfile?.businessName,
            rating: sellerProfile?.rating || 0,
            reviewCount: sellerProfile?.reviewCount || 0,
            verificationStatus: sellerProfile?.verificationStatus,
          } : null,
        };
      })
    );

    return {
      products: enrichedProducts,
      pagination: {
        total: filteredProducts.length,
        page: Math.floor(offset / limit),
        totalPages: Math.ceil(filteredProducts.length / limit),
        hasMore: offset + limit < filteredProducts.length,
        nextCursor: offset + limit < filteredProducts.length ? (offset + limit).toString() : null,
      },
    };
  },
});

/**
 * Get seller's inventory with all product statuses
 */
export const getSellerInventory = query({
  args: {
    sellerId: v.optional(v.id("users")),
    status: v.optional(v.union(
      v.literal("draft"),
      v.literal("active"),
      v.literal("sold"),
      v.literal("archived"),
      v.literal("all")
    )),
    searchQuery: v.optional(v.string()),
    sortBy: v.optional(v.union(
      v.literal("newest"),
      v.literal("oldest"),
      v.literal("price_high"),
      v.literal("price_low"),
      v.literal("views"),
      v.literal("status")
    )),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    includeMetrics: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Determine target seller
    const targetSellerId = args.sellerId || user._id;
    
    // Authorization check
    if (args.sellerId && args.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Unauthorized: Can only view own inventory or admin access required");
    }

    // Verify target is a seller
    const seller = await ctx.db.get(targetSellerId);
    if (!seller || seller.userType !== "seller") {
      throw new ConvexError("User is not a seller");
    }

    const status = args.status || "all";
    const limit = Math.min(args.limit || 50, 200); // Max 200 for inventory management
    const offset = args.offset || 0;
    const sortBy = args.sortBy || "newest";
    const includeMetrics = args.includeMetrics ?? false;

    // Get products based on status filter
    let products;
    if (status === "all") {
      products = await ctx.db
        .query("products")
        .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
        .collect();
    } else {
      products = await ctx.db
        .query("products")
        .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
        .filter((q) => q.eq(q.field("status"), status))
        .collect();
    }

    // Apply search filter
    if (args.searchQuery) {
      const searchTerm = args.searchQuery.toLowerCase().trim();
      products = products.filter(p => {
        const searchableText = [
          p.title,
          p.description,
          p.brand || "",
          ...(p.tags || [])
        ].join(" ").toLowerCase();
        
        return searchableText.includes(searchTerm);
      });
    }

    // Sort products
    products.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return b._creationTime - a._creationTime;
        case "oldest":
          return a._creationTime - b._creationTime;
        case "price_high":
          return b.price - a.price;
        case "price_low":
          return a.price - b.price;
        case "views":
          return (b.views || 0) - (a.views || 0);
        case "status":
          return a.status.localeCompare(b.status);
        default:
          return b._creationTime - a._creationTime;
      }
    });

    // Apply pagination
    const paginatedProducts = products.slice(offset, offset + limit);

    // Enrich with additional data
    const enrichedProducts = await Promise.all(
      paginatedProducts.map(async (product) => {
        // Convert storage IDs to URLs
        const imageUrls = await Promise.all(
          product.images
            .filter(imageId => imageId !== "")
            .map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url;
            })
        );
        const validImageUrls = imageUrls.filter(url => url !== null) as string[];

        // Get product metrics if requested
        let metrics = null;
        if (includeMetrics) {
          // Get orders for this product
          const orders = await ctx.db
            .query("orders")
            .withIndex("by_productId", (q) => q.eq("productId", product._id))
            .collect();

          const completedOrders = orders.filter(o => o.orderStatus === "delivered");
          const pendingOrders = orders.filter(o => 
            ["pending", "confirmed", "shipped"].includes(o.orderStatus)
          );

          metrics = {
            totalOrders: orders.length,
            completedOrders: completedOrders.length,
            pendingOrders: pendingOrders.length,
            totalRevenue: calculateSellerRevenue(completedOrders),
            averageOrderValue: completedOrders.length > 0 
              ? completedOrders.reduce((sum, o) => sum + o.totalAmount, 0) / completedOrders.length 
              : 0,
          };
        }

        return {
          _id: product._id,
          title: product.title,
          description: product.description,
          price: product.price,
          category: product.category,
          condition: product.condition,
          brand: product.brand,
          size: product.size,
          color: product.color,
          material: product.material,
          tags: product.tags,
          images: validImageUrls,
          status: product.status,
          views: product.views || 0,
          saves: product.favorites || 0,
          featured: false,
          originalPrice: product.originalPrice,
          ownershipType: product.ownershipType || "owned", // Default to owned if not set
          consignmentInfo: product.consignmentInfo,
          _creationTime: product._creationTime,
          updatedAt: product.updatedAt,
          publishedAt: product.publishedAt,
          metrics,
        };
      })
    );

    // Calculate summary statistics
    const summary = {
      total: products.length,
      byStatus: {
        draft: products.filter(p => p.status === "draft").length,
        active: products.filter(p => p.status === "active").length,
        sold: products.filter(p => p.status === "sold").length,
        archived: products.filter(p => p.status === "archived").length,
      },
      totalValue: products
        .filter(p => p.status === "active")
        .reduce((sum, p) => sum + p.price, 0),
      averagePrice: products.length > 0 
        ? products.reduce((sum, p) => sum + p.price, 0) / products.length 
        : 0,
    };

    return {
      products: enrichedProducts,
      summary: {
        ...summary,
        totalValue: Math.round(summary.totalValue * 100) / 100,
        averagePrice: Math.round(summary.averagePrice * 100) / 100,
      },
      pagination: {
        total: products.length,
        limit,
        offset,
        hasMore: offset + limit < products.length,
      },
      filters: {
        sellerId: targetSellerId,
        status,
        searchQuery: args.searchQuery,
        sortBy,
        includeMetrics,
      },
    };
  },
});

/**
 * Get detailed product information with view tracking
 */
export const getProductDetails = query({
  args: {
    productId: v.id("products"),
    trackView: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);

    // Get product
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new ConvexError("Product not found");
    }

    // Check if product is accessible
    if (product.status === "archived" && (!user || user.userType !== "admin")) {
      throw new ConvexError("Product not available");
    }

    if (product.status === "draft") {
      // Only seller or admin can view draft products
      if (!user || (product.sellerId !== user._id && user.userType !== "admin")) {
        throw new ConvexError("Product not available");
      }
    }

    // For active/sold products, check subscription for non-owners
    if (product.status === "active" || product.status === "sold") {
      if (user && product.sellerId !== user._id && user.userType !== "admin") {
        const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") &&
          (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

        if (!hasValidSubscription) {
          throw new ConvexError("Active or trial subscription required to view products");
        }
      }
    }

    // Note: View tracking would need to be done in a separate mutation
    // since queries are read-only. This is just for demonstration.
    // In practice, you'd call a separate trackProductView mutation.

    // Get seller information
    const seller = await ctx.db.get(product.sellerId);
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", product.sellerId))
      .first();

    // Convert storage IDs to URLs
    const imageUrls = await Promise.all(
      product.images
        .filter(imageId => imageId !== "")
        .map(async (imageId) => {
          const url = await ctx.storage.getUrl(imageId);
          return url;
        })
    );
    const validImageUrls = imageUrls.filter(url => url !== null) as string[];
    
    // Also return the original storage IDs for form operations
    const validStorageIds = product.images.filter(imageId => imageId !== "");

    // Determine what data to include based on authorization
    const isOwner = user && product.sellerId === user._id;
    const isAdmin = user && user.userType === "admin";
    const canViewPrivateData = isOwner || isAdmin;

    // Get related products (same category, different seller)
    const relatedProducts = await ctx.db
      .query("products")
      .withIndex("by_category", (q) => q.eq("category", product.category))
      .filter((q) => q.and(
        q.eq(q.field("status"), "active"),
        q.neq(q.field("_id"), args.productId),
        q.neq(q.field("sellerId"), product.sellerId)
      ))
      .take(6);

    const relatedProductsWithImages = relatedProducts.map(relatedProduct => ({
      _id: relatedProduct._id,
      title: relatedProduct.title,
      price: relatedProduct.price,
      condition: relatedProduct.condition,
      brand: relatedProduct.brand,
      image: relatedProduct.images[0] || "",
    }));

    // Build response
    const response = {
      _id: product._id,
      title: product.title,
      description: product.description,
      price: product.price,
      condition: product.condition,
      brand: product.brand,
      size: product.size,
      color: product.color,
      material: product.material,
      year: product.year,
      originalPrice: product.originalPrice,
      tags: product.tags || [],
      images: validImageUrls,
      imageStorageIds: validStorageIds, // Add storage IDs for form operations
      status: product.status,
      views: product.views || 0,
      likes: product.favorites || 0,
      weight: product.weight,
      dimensions: product.dimensions,
      shippingCost: product.shippingCost,
      sourceInfo: canViewPrivateData ? product.sourceInfo : undefined,
      updatedAt: product.updatedAt,
      publishedAt: product.publishedAt,

      category: {
        name: product.category,
        slug: product.category,
      },

      seller: seller && sellerProfile ? {
        _id: seller._id,
        name: seller.name,
        businessName: sellerProfile.businessName,
        rating: sellerProfile.rating || 0,
        reviewCount: sellerProfile.reviewCount || 0,
        verificationStatus: sellerProfile.verificationStatus,
        isActive: sellerProfile.isActive,
        joinedDate: seller._creationTime,
        // Contact info for active products
        email: (product.status === "active" && user) ? seller.email : undefined,
      } : null,

      relatedProducts: relatedProductsWithImages,

      // User interaction data
      userInteraction: user ? {
        isOwner,
        canEdit: isOwner || isAdmin,
        canPurchase: !isOwner && product.status === "active" && user.subscriptionStatus === "active",
      } : null,
    };

    return response;
  },
});

/**
 * Full-text search across products with analytics
 */
export const searchProducts = query({
  args: {
    query: v.string(),
    category: v.optional(v.union(
      v.literal("clothing"),
      v.literal("sneakers"),
      v.literal("collectibles"),
      v.literal("accessories"),
      v.literal("handbags"),
      v.literal("jewelry"),
      v.literal("watches"),
      v.literal("sunglasses")
    )),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    brands: v.optional(v.array(v.string())),
    conditions: v.optional(v.array(v.string())),
    sortBy: v.optional(v.union(
      v.literal("relevance"),
      v.literal("newest"),
      v.literal("price_low"),
      v.literal("price_high"),
      v.literal("popular")
    )),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Check subscription for search access - allow trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") &&
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new ConvexError("Active or trial subscription required to search products");
    }

    const searchQuery = args.query.toLowerCase().trim();
    if (searchQuery.length < 2) {
      throw new ConvexError("Search query must be at least 2 characters long");
    }

    const limit = Math.min(args.limit || 24, 100);
    const offset = args.offset || 0;
    const sortBy = args.sortBy || "relevance";

    // Get active products
    let products = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect();

    // Apply category filter first
    if (args.category) {
      products = products.filter(p => p.category === args.category);
    }

    // Apply search filter with relevance scoring
    const searchResults = products
      .map(product => {
        const searchableText = [
          product.title,
          product.description,
          product.brand || "",
          ...(product.tags || [])
        ].join(" ").toLowerCase();

        // Calculate relevance score
        let relevanceScore = 0;
        const searchTerms = searchQuery.split(" ").filter(term => term.length > 1);

        searchTerms.forEach(term => {
          // Title matches get highest score
          if (product.title.toLowerCase().includes(term)) {
            relevanceScore += 10;
          }
          // Brand matches get high score
          if (product.brand && product.brand.toLowerCase().includes(term)) {
            relevanceScore += 8;
          }
          // Tag matches get medium score
          if (product.tags && product.tags.some(tag => tag.includes(term))) {
            relevanceScore += 5;
          }
          // Description matches get lower score
          if (product.description.toLowerCase().includes(term)) {
            relevanceScore += 2;
          }
        });

        // Boost score for exact matches
        if (searchableText.includes(searchQuery)) {
          relevanceScore += 15;
        }

        return { product, relevanceScore };
      })
      .filter(result => result.relevanceScore > 0);

    // Apply additional filters
    let filteredResults = searchResults;

    if (args.minPrice !== undefined) {
      filteredResults = filteredResults.filter(r => r.product.price >= args.minPrice!);
    }

    if (args.maxPrice !== undefined) {
      filteredResults = filteredResults.filter(r => r.product.price <= args.maxPrice!);
    }

    if (args.brands && args.brands.length > 0) {
      filteredResults = filteredResults.filter(r =>
        r.product.brand && args.brands!.includes(r.product.brand.toLowerCase())
      );
    }

    if (args.conditions && args.conditions.length > 0) {
      filteredResults = filteredResults.filter(r =>
        args.conditions!.includes(r.product.condition)
      );
    }

    // Sort results
    filteredResults.sort((a, b) => {
      switch (sortBy) {
        case "relevance":
          return b.relevanceScore - a.relevanceScore;
        case "newest":
          return b.product.publishedAt! - a.product.publishedAt!;
        case "price_low":
          return a.product.price - b.product.price;
        case "price_high":
          return b.product.price - a.product.price;
        case "popular":
          return (b.product.views || 0) - (a.product.views || 0);
        default:
          return b.relevanceScore - a.relevanceScore;
      }
    });

    // Apply pagination
    const paginatedResults = filteredResults.slice(offset, offset + limit);

    // Enrich with seller data
    const enrichedProducts = await Promise.all(
      paginatedResults.map(async (result) => {
        const product = result.product;
        const seller = await ctx.db.get(product.sellerId);

        const sellerProfile = await ctx.db
          .query("sellerProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", product.sellerId))
          .first();

        const imageUrls = product.images.filter(url => url !== "");

        return {
          _id: product._id,
          title: product.title,
          description: product.description,
          price: product.price,
          condition: product.condition,
          brand: product.brand,
          size: product.size,
          color: product.color,
          material: product.material,
          tags: product.tags,
          images: imageUrls,
          views: product.views || 0,
          likes: product.favorites || 0,
          publishedAt: product.publishedAt,
          relevanceScore: result.relevanceScore,
          category: {
            name: product.category,
            slug: product.category,
          },
          seller: seller ? {
            _id: seller._id,
            name: seller.name,
            businessName: sellerProfile?.businessName,
            rating: sellerProfile?.rating || 0,
            reviewCount: sellerProfile?.reviewCount || 0,
            verificationStatus: sellerProfile?.verificationStatus,
          } : null,
        };
      })
    );

    // Generate search suggestions for autocomplete
    const suggestions = generateSearchSuggestions(searchQuery, filteredResults.map(r => r.product));

    return {
      products: enrichedProducts,
      searchQuery,
      suggestions,
      pagination: {
        total: filteredResults.length,
        limit,
        offset,
        hasMore: offset + limit < filteredResults.length,
      },
      filters: {
        category: args.category,
        minPrice: args.minPrice,
        maxPrice: args.maxPrice,
        brands: args.brands,
        conditions: args.conditions,
        sortBy,
      },
    };
  },
});

/**
 * Get products by category with filtering and trending
 */
export const getProductsByCategory = query({
  args: {
    category: v.union(
      v.literal("clothing"),
      v.literal("sneakers"),
      v.literal("collectibles"),
      v.literal("accessories"),
      v.literal("handbags"),
      v.literal("jewelry"),
      v.literal("watches"),
      v.literal("sunglasses")
    ),
    brands: v.optional(v.array(v.string())),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    conditions: v.optional(v.array(v.string())),
    sortBy: v.optional(v.union(
      v.literal("newest"),
      v.literal("trending"),
      v.literal("price_low"),
      v.literal("price_high"),
      v.literal("popular")
    )),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    includeTrending: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Check subscription for category browsing - allow trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") &&
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new ConvexError("Active or trial subscription required to browse categories");
    }

    const limit = Math.min(args.limit || 24, 100);
    const offset = args.offset || 0;
    const sortBy = args.sortBy || "newest";

    // Get products in category
    let products = await ctx.db
      .query("products")
      .withIndex("by_category_status", (q) =>
        q.eq("category", args.category).eq("status", "active")
      )
      .collect();

    // Apply filters
    if (args.brands && args.brands.length > 0) {
      products = products.filter(p =>
        p.brand && args.brands!.includes(p.brand.toLowerCase())
      );
    }

    if (args.minPrice !== undefined) {
      products = products.filter(p => p.price >= args.minPrice!);
    }

    if (args.maxPrice !== undefined) {
      products = products.filter(p => p.price <= args.maxPrice!);
    }

    if (args.conditions && args.conditions.length > 0) {
      products = products.filter(p => args.conditions!.includes(p.condition));
    }

    // Calculate trending score for trending sort
    if (sortBy === "trending") {
      const now = Date.now();
      const sevenDaysAgo = now - (7 * 24 * 60 * 60 * 1000);

      products = products.map(product => ({
        ...product,
        trendingScore: calculateTrendingScore(product, sevenDaysAgo, now),
      }));
    }

    // Sort products
    products.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return b.publishedAt! - a.publishedAt!;
        case "trending":
          return (b as any).trendingScore - (a as any).trendingScore;
        case "price_low":
          return a.price - b.price;
        case "price_high":
          return b.price - a.price;
        case "popular":
          return (b.views || 0) - (a.views || 0);
        default:
          return b.publishedAt! - a.publishedAt!;
      }
    });

    // Apply pagination
    const paginatedProducts = products.slice(offset, offset + limit);

    // Enrich with seller data
    const enrichedProducts = await Promise.all(
      paginatedProducts.map(async (product) => {
        const seller = await ctx.db.get(product.sellerId);
        const sellerProfile = await ctx.db
          .query("sellerProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", product.sellerId))
          .first();

        const imageUrls = product.images.filter(url => url !== "");

        return {
          _id: product._id,
          title: product.title,
          description: product.description,
          price: product.price,
          condition: product.condition,
          brand: product.brand,
          size: product.size,
          color: product.color,
          material: product.material,
          tags: product.tags,
          images: imageUrls,
          views: product.views || 0,
          likes: product.favorites || 0,
          publishedAt: product.publishedAt,
          trendingScore: (product as any).trendingScore,
          seller: seller ? {
            _id: seller._id,
            name: seller.name,
            businessName: sellerProfile?.businessName,
            rating: sellerProfile?.rating || 0,
            reviewCount: sellerProfile?.reviewCount || 0,
            verificationStatus: sellerProfile?.verificationStatus,
          } : null,
        };
      })
    );

    // Get trending products if requested
    let trendingProducts = null;
    if (args.includeTrending) {
      const allCategoryProducts = await ctx.db
        .query("products")
        .withIndex("by_category_status", (q) =>
          q.eq("category", args.category).eq("status", "active")
        )
        .collect();

      const now = Date.now();
      const sevenDaysAgo = now - (7 * 24 * 60 * 60 * 1000);

      trendingProducts = allCategoryProducts
        .map(product => ({
          ...product,
          trendingScore: calculateTrendingScore(product, sevenDaysAgo, now),
        }))
        .sort((a, b) => b.trendingScore - a.trendingScore)
        .slice(0, 6);
    }

    // Get category statistics
    const categoryStats = {
      totalProducts: products.length,
      averagePrice: products.length > 0
        ? products.reduce((sum, p) => sum + p.price, 0) / products.length
        : 0,
      priceRange: products.length > 0 ? {
        min: Math.min(...products.map(p => p.price)),
        max: Math.max(...products.map(p => p.price)),
      } : null,
      topBrands: getTopBrands(products),
      conditionBreakdown: getConditionBreakdown(products),
    };

    return {
      products: enrichedProducts,
      category: {
        name: args.category,
        slug: args.category,
      },
      trendingProducts,
      categoryStats: {
        ...categoryStats,
        averagePrice: Math.round(categoryStats.averagePrice * 100) / 100,
      },
      pagination: {
        total: products.length,
        limit,
        offset,
        hasMore: offset + limit < products.length,
      },
      filters: {
        category: args.category,
        brands: args.brands,
        minPrice: args.minPrice,
        maxPrice: args.maxPrice,
        conditions: args.conditions,
        sortBy,
      },
    };
  },
});

/**
 * Helper function to generate search suggestions
 */
function generateSearchSuggestions(query: string, products: any[]): string[] {
  const suggestions = new Set<string>();
  const queryLower = query.toLowerCase();

  // Add brand suggestions
  products.forEach(product => {
    if (product.brand && product.brand.toLowerCase().includes(queryLower)) {
      suggestions.add(product.brand);
    }

    // Add tag suggestions
    if (product.tags) {
      product.tags.forEach((tag: string) => {
        if (tag.includes(queryLower)) {
          suggestions.add(tag);
        }
      });
    }
  });

  return Array.from(suggestions).slice(0, 8);
}

/**
 * Helper function to calculate trending score
 */
function calculateTrendingScore(product: any, startTime: number, endTime: number): number {
  let score = 0;

  // Recent views boost
  const views = product.views || 0;
  score += views * 2;

  // Recent likes boost
  const likes = product.favorites || 0;
  score += likes * 5;

  // Recency boost (newer products get higher score)
  const publishedAt = product.publishedAt || product._creationTime;
  if (publishedAt >= startTime) {
    const recencyFactor = (publishedAt - startTime) / (endTime - startTime);
    score += recencyFactor * 10;
  }

  // Price factor (higher priced items get slight boost for luxury market)
  if (product.price > 1000) {
    score += 2;
  }

  return score;
}

/**
 * Helper function to get top brands in products
 */
function getTopBrands(products: any[]): Array<{ brand: string; count: number }> {
  const brandCount = new Map<string, number>();

  products.forEach(product => {
    if (product.brand) {
      const brand = product.brand;
      brandCount.set(brand, (brandCount.get(brand) || 0) + 1);
    }
  });

  return Array.from(brandCount.entries())
    .map(([brand, count]) => ({ brand, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

/**
 * Helper function to get condition breakdown
 */
function getConditionBreakdown(products: any[]): Array<{ condition: string; count: number }> {
  const conditionCount = new Map<string, number>();

  products.forEach(product => {
    const condition = product.condition;
    conditionCount.set(condition, (conditionCount.get(condition) || 0) + 1);
  });

  return Array.from(conditionCount.entries())
    .map(([condition, count]) => ({ condition, count }))
    .sort((a, b) => b.count - a.count);
}

/**
 * Get filter options for marketplace filtering
 */
export const getFilterOptions = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    // Check subscription status for marketplace access - allow trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new ConvexError("Active or trial subscription required to access filter options");
    }

    const products = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect(); 

    // Get category counts
    const categoryCountMap = new Map<string, number>();
    products.forEach(p => {
      if (p.category) {
        categoryCountMap.set(p.category, (categoryCountMap.get(p.category) || 0) + 1);
      }
    });
    
    // Get brand counts
    const brandCountMap = new Map<string, number>();
    products.forEach(p => {
      if (p.brand) {
        brandCountMap.set(p.brand, (brandCountMap.get(p.brand) || 0) + 1);
      }
    });

    // Get condition counts
    const conditionCountMap = new Map<string, number>();
    products.forEach(p => {
      if (p.condition) {
        conditionCountMap.set(p.condition, (conditionCountMap.get(p.condition) || 0) + 1);
      }
    });

    // Transform to arrays with counts
    const categories = Array.from(categoryCountMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => a.name.localeCompare(b.name));
    
    const brands = Array.from(brandCountMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => a.name.localeCompare(b.name));

    const conditions = Array.from(conditionCountMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => a.name.localeCompare(b.name));
    
    const prices = products.map(p => p.price).filter(p => p > 0);
    const priceRange: [number, number] = prices.length > 0 ? [
      Math.min(...prices),
      Math.max(...prices)
    ] : [0, 10000];

    return {
      categories,
      brands,
      conditions,
      priceRange,
    };
  },
});

/**
 * Get aggregated brand counts for marketplace filtering using Convex aggregation
 */
export const getBrandCounts = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    // Check subscription status for marketplace access - allow trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new ConvexError("Active or trial subscription required to access brand counts");
    }

    // Use aggregation to get brand counts efficiently
    const brandCounts = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect()
      .then(products => {
        const brandMap = new Map<string, number>();
        
        products.forEach(product => {
          if (product.brand) {
            const currentCount = brandMap.get(product.brand) || 0;
            brandMap.set(product.brand, currentCount + 1);
          }
        });
        
        return Array.from(brandMap.entries())
          .map(([name, count]) => ({ name, count }))
          .sort((a, b) => b.count - a.count); // Sort by count descending
      });

    return brandCounts;
  },
});

/**
 * Get aggregated filter options for marketplace filtering using efficient Convex queries
 */
export const getAggregatedFilterOptions = query({
  args: {},
  handler: async (ctx) => {
    // Filter options are public - no authentication required
    const user = await getAuthUser(ctx);

    // Use efficient queries with proper indexing instead of fetching all products
    // Get all active products in a single query using the status index
    const activeProducts = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect();

    // Aggregate brands efficiently
    const brandCounts = new Map<string, number>();
    activeProducts.forEach(product => {
      if (product.brand) {
        brandCounts.set(product.brand, (brandCounts.get(product.brand) || 0) + 1);
      }
    });

    // Aggregate categories efficiently
    const categoryCounts = new Map<string, number>();
    activeProducts.forEach(product => {
      if (product.category) {
        categoryCounts.set(product.category, (categoryCounts.get(product.category) || 0) + 1);
      }
    });

    // Get conditions from the same query to avoid multiple database calls
    const conditionCounts = new Map<string, number>();
    activeProducts.forEach(product => {
      if (product.condition) {
        conditionCounts.set(product.condition, (conditionCounts.get(product.condition) || 0) + 1);
      }
    });

    // Calculate price range efficiently
    const prices = activeProducts.map(p => p.price).filter(p => p > 0);
    const priceRange: [number, number] = prices.length > 0 ? [
      Math.min(...prices),
      Math.max(...prices)
    ] : [0, 10000];

    return {
      categories: Array.from(categoryCounts.entries())
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => a.name.localeCompare(b.name)),
      brands: Array.from(brandCounts.entries())
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count), // Sort brands by count descending
      conditions: Array.from(conditionCounts.entries())
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => a.name.localeCompare(b.name)),
      priceRange,
    };
  },
});

/**
 * Test query to verify aggregation is working (for development)
 */
export const testAggregation = query({
  args: {},
  handler: async (ctx) => {
    // Simple test to verify the aggregation logic works
    const activeProducts = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect();

    const brandCounts = new Map<string, number>();
    activeProducts.forEach(product => {
      if (product.brand) {
        brandCounts.set(product.brand, (brandCounts.get(product.brand) || 0) + 1);
      }
    });

    return {
      totalProducts: activeProducts.length,
      uniqueBrands: brandCounts.size,
      brandBreakdown: Array.from(brandCounts.entries())
        .map(([brand, count]) => ({ brand, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5), // Top 5 brands
    };
  },
});

/**
 * Quick search for search popover - returns limited results for better UX
 */
export const getQuickSearchResults = query({
  args: {
    searchQuery: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = Math.min(args.limit || 5, 10); // Max 10 results for popover
    
    if (!args.searchQuery.trim()) {
      return { results: [] };
    }

    const searchTerm = args.searchQuery.toLowerCase().trim();
    
    // Get active products
    const products = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect();

    // Filter and search products
    const searchResults = products
      .filter(product => {
        const searchableText = [
          product.title,
          product.description,
          product.brand || "",
          product.model || "",
          product.category,
          ...(product.tags || [])
        ].join(" ").toLowerCase();
        
        return searchableText.includes(searchTerm);
      })
      .slice(0, limit);

    // Enrich with minimal data needed for popover
    const enrichedResults = await Promise.all(
      searchResults.map(async (product) => {
        // Get primary image URL
        let imageUrl = null;
        if (
          product.primaryImageId &&
          product.images.some((img) => img === product.primaryImageId)
        ) {
          // Only call getUrl if primaryImageId is defined
          imageUrl = await ctx.storage.getUrl(product.primaryImageId);
        } else if (
          product.images.length > 0 &&
          typeof product.images[0] === "string" &&
          product.images[0]
        ) {
          // Only call getUrl if images[0] is defined and a string
          imageUrl = await ctx.storage.getUrl(product.images[0]);
        }

        // Get seller info
        const seller = await ctx.db.get(product.sellerId);

        return {
          _id: product._id,
          title: product.title,
          brand: product.brand,
          model: product.model,
          price: product.price,
          condition: product.condition,
          category: product.category,
          size: product.size,
          color: product.color,
          material: product.material,
          imageUrl,
          sellerName: seller?.name || "Unknown Seller",
        };
      })
    );

    return {
      results: enrichedResults,
      totalFound: enrichedResults.length,
    };
  },
});

/**
 * Save a search query to user's search history
 */
export const saveSearchHistory = mutation({
  args: {
    searchQuery: v.string(),
    resultCount: v.optional(v.number()),
    category: v.optional(v.string()),
    brand: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Save the search
    await ctx.db.insert("userSearchHistory", {
      userId: user._id,
      searchQuery: args.searchQuery,
      timestamp: Date.now(),
      resultCount: args.resultCount,
      category: args.category,
      brand: args.brand,
    });
  },
});

/**
 * Get user's recent search history
 */
export const getUserRecentSearches = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const limit = Math.min(args.limit || 10, 20);
    
    const searches = await ctx.db
      .query("userSearchHistory")
      .withIndex("by_userId_timestamp", (q) => q.eq("userId", user._id))
      .order("desc")
      .take(limit);
    
    // Remove duplicates and return unique searches
    const uniqueSearches = searches
      .map(s => s.searchQuery)
      .filter((query, index, arr) => arr.indexOf(query) === index)
      .slice(0, limit);
    
    return uniqueSearches;
  },
});

/**
 * Track a search event for analytics
 */
export const trackSearchEvent = mutation({
  args: {
    query: v.string(),
    sessionId: v.optional(v.string()),
    resultCount: v.number(),
    source: v.union(v.literal("search_bar"), v.literal("search_popover"), v.literal("search_page")),
    category: v.optional(v.string()),
    brand: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    
    await ctx.db.insert("searchEvents", {
      query: args.query.trim().toLowerCase(),
      userId: user?._id,
      sessionId: args.sessionId,
      timestamp: Date.now(),
      resultCount: args.resultCount,
      source: args.source,
      category: args.category,
      brand: args.brand,
    });
  },
});

/**
 * Get popular searches based on weighted algorithm
 */
export const getPopularSearches = query({
  args: {
    limit: v.optional(v.number()),
    category: v.optional(v.string()),
    timeRange: v.optional(v.union(
      v.literal("7d"),
      v.literal("30d"),
      v.literal("90d")
    )),
  },
  handler: async (ctx, args) => {
    const limit = Math.min(args.limit || 10, 20);
    const timeRange = args.timeRange || "30d";
    
    // Calculate time threshold
    const now = Date.now();
    let timeThreshold: number;
    
    switch (timeRange) {
      case "7d":
        timeThreshold = now - (7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        timeThreshold = now - (30 * 24 * 60 * 60 * 1000);
        break;
      case "90d":
        timeThreshold = now - (90 * 24 * 60 * 60 * 1000);
        break;
      default:
        timeThreshold = now - (30 * 24 * 60 * 60 * 1000);
    }
    
    // Get search events within time range
    const searchEvents = await ctx.db
      .query("searchEvents")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", timeThreshold))
      .collect();
    
    // Filter by category if specified
    let filteredEvents = searchEvents;
    if (args.category) {
      filteredEvents = searchEvents.filter(event => 
        event.category === args.category || event.brand === args.category
      );
    }
    
    // Aggregate search data
    const searchStats = new Map<string, {
      query: string;
      searchCount: number;
      uniqueUsers: Set<string>;
      totalResults: number;
      recentSearches: number;
      category: string | undefined;
      brand: string | undefined;
    }>();
    
    filteredEvents.forEach(event => {
      const existing = searchStats.get(event.query);
      const userId = event.userId || event.sessionId || 'anonymous';
      
      if (existing) {
        existing.searchCount += 1;
        existing.uniqueUsers.add(userId);
        existing.totalResults += event.resultCount;
        
        // Give more weight to recent searches
        if (event.timestamp > now - (7 * 24 * 60 * 60 * 1000)) {
          existing.recentSearches += 1;
        }
      } else {
        searchStats.set(event.query, {
          query: event.query,
          searchCount: 1,
          uniqueUsers: new Set([userId]),
          totalResults: event.resultCount,
          recentSearches: event.timestamp > now - (7 * 24 * 60 * 60 * 1000) ? 1 : 0,
          category: event.category,
          brand: event.brand,
        });
      }
    });
    
    // Calculate popularity scores
    const popularSearches = Array.from(searchStats.values())
      .map(stats => {
        const uniqueUserCount = stats.uniqueUsers.size;
        const avgResults = stats.totalResults / stats.searchCount;
        
        // Weighted popularity algorithm
        const popularityScore = (
          (stats.searchCount * 0.4) +           // Raw volume
          (uniqueUserCount * 0.3) +             // Unique user engagement
          (stats.recentSearches * 0.2) +        // Recent trend factor
          (Math.min(avgResults, 10) * 0.1)     // Result quality (capped at 10)
        );
        
        return {
          query: stats.query,
          searchCount: stats.searchCount,
          uniqueUsers: uniqueUserCount,
          avgResults,
          recentSearches: stats.recentSearches,
          popularityScore,
          category: stats.category,
          brand: stats.brand,
        };
      })
      .filter(search => 
        search.query.length >= 2 && // Filter out very short queries
        search.searchCount >= 2     // Filter out single searches
      )
      .sort((a, b) => b.popularityScore - a.popularityScore)
      .slice(0, limit);
    
    return popularSearches;
  },
});

/**
 * Get popular searches by category
 */
export const getPopularSearchesByCategory = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = Math.min(args.limit || 5, 10);
    
    // Get popular searches for different categories
    const [brands, models, categories] = await Promise.all([
      ctx.db
        .query("searchEvents")
        .collect()
        .then(events => {
          const brandStats = new Map<string, number>();
          events.forEach(event => {
            if (event.brand) {
              brandStats.set(event.brand, (brandStats.get(event.brand) || 0) + 1);
            }
          });
          return Array.from(brandStats.entries())
            .sort(([,a], [,b]) => b - a)
            .slice(0, limit)
            .map(([brand]) => ({ type: 'brand', query: brand }));
        }),
      
      ctx.db
        .query("searchEvents")
        .collect()
        .then(events => {
          const categoryStats = new Map<string, number>();
          events.forEach(event => {
            if (event.category) {
              categoryStats.set(event.category, (categoryStats.get(event.category) || 0) + 1);
            }
          });
          return Array.from(categoryStats.entries())
            .sort(([,a], [,b]) => b - a)
            .slice(0, limit)
            .map(([category]) => ({ type: 'category', query: category }));
        }),
      
      // Get general popular searches
      Promise.resolve([]),
    ]);
    
    return {
      brands,
      categories,
      general: categories,
    };
  },
});
