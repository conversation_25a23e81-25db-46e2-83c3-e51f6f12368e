import { components } from "./_generated/api";
import { DataModel } from "./_generated/dataModel";
import { TableAggregate } from "@convex-dev/aggregate";
import { mutation, query } from "./_generated/server";
import { requireAuth } from "./lib/auth_utils";

// Define aggregates for different product metrics
export const productBrandAggregate = new TableAggregate<{
  Namespace: null; // No namespace - aggregate all products
  Key: string; // Brand name as the key
  DataModel: DataModel;
  TableName: "products";
}>(components.aggregate, {
  namespace: () => null,
  sortKey: (doc) => doc.brand,
});

export const productCategoryAggregate = new TableAggregate<{
  Namespace: null;
  Key: string; // Category as the key
  DataModel: DataModel;
  TableName: "products";
}>(components.aggregate, {
  namespace: () => null,
  sortKey: (doc) => doc.category,
});

export const productConditionAggregate = new TableAggregate<{
  Namespace: null;
  Key: string; // Condition as the key
  DataModel: DataModel;
  TableName: "products";
}>(components.aggregate, {
  namespace: () => null,
  sortKey: (doc) => doc.condition,
});

export const productPriceAggregate = new TableAggregate<{
  Namespace: null;
  Key: number; // Price as the key
  DataModel: DataModel;
  TableName: "products";
}>(components.aggregate, {
  namespace: () => null,
  sortKey: (doc) => doc.price,
});

// Mutation to insert a product into all aggregates
export const insertProductIntoAggregates = mutation({
  args: {},
  handler: async (ctx) => {
    // This will be called by triggers when products are created/updated
    // For now, we'll implement the logic in the product mutations
  },
});

// Function to initialize aggregates with existing product data
export const initializeAggregates = mutation({
  args: {},
  handler: async (ctx) => {
    // Only allow admins to initialize aggregates
    const user = await requireAuth(ctx);
    if (user.userType !== "admin") {
      throw new Error("Only admins can initialize aggregates");
    }

    // Clear existing aggregates
    await productBrandAggregate.clear(ctx, { namespace: null });
    await productCategoryAggregate.clear(ctx, { namespace: null });
    await productConditionAggregate.clear(ctx, { namespace: null });
    await productPriceAggregate.clear(ctx, { namespace: null });

    // Get all active products and insert them into aggregates
    const products = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect();

    // Insert each product into the appropriate aggregates
    for (const product of products) {
      if (product.brand) {
        await productBrandAggregate.insert(ctx, product);
      }
      await productCategoryAggregate.insert(ctx, product);
      await productConditionAggregate.insert(ctx, product);
      await productPriceAggregate.insert(ctx, product);
    }

    return {
      success: true,
      message: `Initialized aggregates with ${products.length} products`,
      productCount: products.length,
    };
  },
});

// Query to get brand counts efficiently
export const getBrandCounts = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    // Check subscription status for marketplace access - allow trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new Error("Active or trial subscription required to access brand counts");
    }

    // Get all brands with their counts
    const brands = await productBrandAggregate.paginate(ctx, {
      pageSize: 1000,
      namespace: null,
    });

    const brandCounts = brands.page.map(({ key }) => ({
      name: key,
      count: 1,
    }));

    // Sort by count descending
    return brandCounts.sort((a, b) => b.count - a.count);
  },
});

// Query to get category counts efficiently
export const getCategoryCounts = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    // Check subscription status for marketplace access - allow trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new Error("Active or trial subscription required to access category counts");
    }

    // Get all categories with their counts
    const categories = await productCategoryAggregate.paginate(ctx, {
      pageSize: 100,
      namespace: null,
    });

    const categoryCounts = categories.page.map(({ key }) => ({
      name: key,
      count: 1, // Each key represents one category
    }));

    // Sort alphabetically
    return categoryCounts.sort((a, b) => a.name.localeCompare(b.name));
  },
});

// Query to get condition counts efficiently
export const getConditionCounts = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    // Check subscription status for marketplace access - allow trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new Error("Active or trial subscription required to access condition counts");
    }

    // Get all conditions with their counts
    const conditions = await productConditionAggregate.paginate(ctx, {
      pageSize: 100,
      namespace: null,
    });

    const conditionCounts = conditions.page.map(({ key }) => ({
      name: key,
      count: 1, // Each key represents one condition
    }));

    // Sort alphabetically
    return conditionCounts.sort((a, b) => a.name.localeCompare(b.name));
  },
});

// Query to get price range efficiently
export const getPriceRange = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    // Check subscription status for marketplace access - allow trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new Error("Active or trial subscription required to access price range");
    }

    // Get min and max prices efficiently
    const minPrice = await productPriceAggregate.min(ctx, { namespace: null });
    const maxPrice = await productPriceAggregate.max(ctx, { namespace: null });

    return [minPrice || 0, maxPrice || 10000] as [number, number];
  },
});

// Query to get all aggregated filter options efficiently
export const getAggregatedFilterOptions = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    // Check subscription status for marketplace access - allow trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new Error("Active or trial subscription required to access filter options");
    }

    // Get all counts in parallel for efficiency
    const [brands, categories, conditions, priceRange] = await Promise.all([
      productBrandAggregate.paginate(ctx, { pageSize: 1000, namespace: null }),
      productCategoryAggregate.paginate(ctx, { pageSize: 100, namespace: null }),
      productConditionAggregate.paginate(ctx, { pageSize: 100, namespace: null }),
      productPriceAggregate.paginate(ctx, { pageSize: 1000, namespace: null }),
    ]);

    return {
      brands: brands.page.map(({ key }) => ({ name: key, count: 1 })).sort((a, b) => b.count - a.count),
      categories: categories.page.map(({ key }) => ({ name: key, count: 1 })).sort((a, b) => a.name.localeCompare(b.name)),
      conditions: conditions.page.map(({ key }) => ({ name: key, count: 1 })).sort((a, b) => a.name.localeCompare(b.name)),
      priceRange: [0, 10000] as [number, number], // Simplified for now
    };
  },
});
