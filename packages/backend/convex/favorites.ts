import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { getAuthUser, requireAuth } from "./lib/auth_utils";

/**
 * Add a product to user's favorites
 */
export const addToFavorites = mutation({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, { productId }) => {
    const user = await requireAuth(ctx);

    // Check if product exists
    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // Check if already favorited
    const existingFavorite = await ctx.db
      .query("favorites")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("productId"), productId))
      .first();

    if (existingFavorite) {
      throw new Error("Product already in favorites");
    }

    // Add to favorites
    const favoriteId = await ctx.db.insert("favorites", {
      userId: user._id,
      productId,
    });

    // Increment product favorites count
    await ctx.db.patch(productId, {
      favorites: (product.favorites || 0) + 1,
    });

    return favoriteId;
  },
});

/**
 * Remove a product from user's favorites
 */
export const removeFromFavorites = mutation({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, { productId }) => {
    const user = await requireAuth(ctx);

    // Find the favorite record
    const favorite = await ctx.db
      .query("favorites")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("productId"), productId))
      .first();

    if (!favorite) {
      throw new Error("Product not in favorites");
    }

    // Remove from favorites
    await ctx.db.delete(favorite._id);

    // Decrement product favorites count
    const product = await ctx.db.get(productId);
    if (product && product.favorites && product.favorites > 0) {
      await ctx.db.patch(productId, {
        favorites: product.favorites - 1,
      });
    }

    return true;
  },
});

/**
 * Toggle favorite status for a product
 */
export const toggleFavorite = mutation({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, { productId }) => {
    const user = await requireAuth(ctx);

    // Check if product exists
    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // Check if already favorited
    const existingFavorite = await ctx.db
      .query("favorites")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("productId"), productId))
      .first();

    if (existingFavorite) {
      // Remove from favorites
      await ctx.db.delete(existingFavorite._id);
      
      // Decrement product favorites count
      if (product.favorites && product.favorites > 0) {
        await ctx.db.patch(productId, {
          favorites: product.favorites - 1,
        });
      }

      return { isFavorited: false };
    } else {
      // Add to favorites
      await ctx.db.insert("favorites", {
        userId: user._id,
        productId,
      });

      // Increment product favorites count
      await ctx.db.patch(productId, {
        favorites: (product.favorites || 0) + 1,
      });

      return { isFavorited: true };
    }
  },
});

/**
 * Check if a product is favorited by the current user
 */
export const isFavorited = query({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, { productId }) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return false;
    }

    // Check if favorited
    const favorite = await ctx.db
      .query("favorites")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("productId"), productId))
      .first();

    return !!favorite;
  },
});

/**
 * Get all favorites for the current user
 */
export const getUserFavorites = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    includeSold: v.optional(v.boolean()), // New parameter to control whether to include sold products
  },
  handler: async (ctx, { limit = 20, offset = 0, includeSold = true }) => {
    const user = await requireAuth(ctx);

    // Get user's favorites
    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .collect();

    // Get the product details for each favorite
    const productIds = favorites.map(f => f.productId);
    const products = await Promise.all(
      productIds.map(async (productId) => {
        const product = await ctx.db.get(productId);
        if (!product) return null;

        // If includeSold is false, filter out sold products
        if (!includeSold && product.status === "sold") {
          return null;
        }

        // Get seller information
        let seller = null;
        if (product.sellerId) {
          const sellerUser = await ctx.db.get(product.sellerId);
          if (sellerUser) {
            const sellerProfile = await ctx.db
              .query("sellerProfiles")
              .withIndex("by_userId", (q) => q.eq("userId", sellerUser._id))
              .first();
            
            seller = {
              _id: sellerUser._id,
              name: sellerUser.name,
              businessName: sellerProfile?.businessName,
              rating: sellerProfile?.rating || 0,
              reviewCount: sellerProfile?.reviewCount || 0,
              verificationStatus: sellerProfile?.verificationStatus,
            };
          }
        }

        // Get category information
        let category = product.category;
        if (typeof product.category === "string") {
          const categoryDoc = await ctx.db
            .query("categories")
            .filter((q) => q.eq(q.field("name"), product.category))
            .first();
          
          if (categoryDoc) {
            category = categoryDoc.name as any;
          }
        }

        // Convert storage IDs to URLs
        const imageUrls = await Promise.all(
          product.images
            .filter(imageId => imageId !== "")
            .map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url;
            })
        );
        const validImageUrls = imageUrls.filter(url => url !== null) as string[];

        // Get order information for sold products to show when it was sold
        let orderInfo = null;
        if (product.status === "sold") {
          const order = await ctx.db
            .query("orders")
            .withIndex("by_productId", (q) => q.eq("productId", productId))
            .filter((q) => q.eq(q.field("orderStatus"), "delivered"))
            .first();
          
          if (order) {
            orderInfo = {
              soldDate: order.deliveredDate || order.updatedAt,
              orderNumber: order.orderNumber,
            };
          }
        }

        return {
          ...product,
          images: validImageUrls,
          seller,
          category,
          orderInfo, // Include order info for sold products
        };
      })
    );

    // Filter out null products and apply pagination
    const validProducts = products.filter(p => p !== null);
    const paginatedProducts = validProducts.slice(offset, offset + limit);

    return {
      products: paginatedProducts,
      total: validProducts.length,
      hasMore: offset + limit < validProducts.length,
      // Include summary of sold vs available products
      summary: {
        total: validProducts.length,
        available: validProducts.filter(p => p.status === "active").length,
        sold: validProducts.filter(p => p.status === "sold").length,
        reserved: validProducts.filter(p => p.status === "reserved").length,
      },
    };
  },
});

/**
 * Get favorites count for a product
 */
export const getFavoritesCount = query({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, { productId }) => {
    const product = await ctx.db.get(productId);
    return product?.favorites || 0;
  },
});

/**
 * Get multiple favorite statuses for products (useful for product lists)
 */
export const getFavoriteStatuses = query({
  args: {
    productIds: v.array(v.id("products")),
  },
  handler: async (ctx, { productIds }) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      // Return false for all products if not authenticated
      return productIds.reduce((acc, id) => {
        acc[id] = false;
        return acc;
      }, {} as Record<string, boolean>);
    }

    // Get all favorites for the user
    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .collect();

    const favoriteProductIds = new Set(favorites.map(f => f.productId));

    // Build the result object
    return productIds.reduce((acc, id) => {
      acc[id] = favoriteProductIds.has(id);
      return acc;
    }, {} as Record<string, boolean>);
  },
});

/**
 * Get favorites organized by status (available, sold, reserved) for better UX
 */
export const getFavoritesWithStatus = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, { limit = 20, offset = 0 }) => {
    const user = await requireAuth(ctx);

    // Get user's favorites
    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .collect();

    // Get the product details for each favorite
    const productIds = favorites.map(f => f.productId);
    const products = await Promise.all(
      productIds.map(async (productId) => {
        const product = await ctx.db.get(productId);
        if (!product) return null;

        // Get seller information
        let seller = null;
        if (product.sellerId) {
          const sellerUser = await ctx.db.get(product.sellerId);
          if (sellerUser) {
            const sellerProfile = await ctx.db
              .query("sellerProfiles")
              .withIndex("by_userId", (q) => q.eq("userId", sellerUser._id))
              .first();
            
            seller = {
              _id: sellerUser._id,
              name: sellerUser.name,
              businessName: sellerProfile?.businessName,
              rating: sellerProfile?.rating || 0,
              reviewCount: sellerProfile?.reviewCount || 0,
              verificationStatus: sellerProfile?.verificationStatus,
            };
          }
        }

        // Get category information
        let category = product.category;
        if (typeof product.category === "string") {
          const categoryDoc = await ctx.db
            .query("categories")
            .filter((q) => q.eq(q.field("name"), product.category))
            .first();
          
          if (categoryDoc) {
            category = categoryDoc.name as any;
          }
        }

        // Convert storage IDs to URLs
        const imageUrls = await Promise.all(
          product.images
            .filter(imageId => imageId !== "")
            .map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url;
            })
        );
        const validImageUrls = imageUrls.filter(url => url !== null) as string[];

        // Get order information for sold products
        let orderInfo = null;
        if (product.status === "sold") {
          const order = await ctx.db
            .query("orders")
            .withIndex("by_productId", (q) => q.eq("productId", productId))
            .filter((q) => q.eq(q.field("orderStatus"), "delivered"))
            .first();
          
          if (order) {
            orderInfo = {
              soldDate: order.deliveredDate || order.updatedAt,
              orderNumber: order.orderNumber,
            };
          }
        }

        return {
          ...product,
          images: validImageUrls,
          seller,
          category,
          orderInfo,
        };
      })
    );

    // Filter out null products
    const validProducts = products.filter(p => p !== null);

    // Organize products by status
    const organizedProducts = {
      available: validProducts.filter(p => p.status === "active"),
      sold: validProducts.filter(p => p.status === "sold"),
      reserved: validProducts.filter(p => p.status === "reserved"),
      other: validProducts.filter(p => !["active", "sold", "reserved"].includes(p.status)),
    };

    // Apply pagination to available products (most important for users)
    const availablePaginated = organizedProducts.available.slice(offset, offset + limit);

    return {
      // Main display: available products with pagination
      available: {
        products: availablePaginated,
        total: organizedProducts.available.length,
        hasMore: offset + limit < organizedProducts.available.length,
      },
      // Sold products (no pagination needed, show all)
      sold: {
        products: organizedProducts.sold,
        total: organizedProducts.sold.length,
      },
      // Reserved products
      reserved: {
        products: organizedProducts.reserved,
        total: organizedProducts.reserved.length,
      },
      // Summary statistics
      summary: {
        total: validProducts.length,
        available: organizedProducts.available.length,
        sold: organizedProducts.sold.length,
        reserved: organizedProducts.reserved.length,
        other: organizedProducts.other.length,
      },
      // Pagination info
      pagination: {
        limit,
        offset,
        totalAvailable: organizedProducts.available.length,
      },
    };
  },
});

/**
 * Get status information for multiple products (useful for product lists with favorites)
 */
export const getFavoritesStatuses = query({
  args: {
    productIds: v.array(v.id("products")),
  },
  handler: async (ctx, { productIds }) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      // Return empty statuses if not authenticated
      return productIds.reduce((acc, id) => {
        acc[id] = { isFavorited: false, status: null };
        return acc;
      }, {} as Record<string, { isFavorited: boolean; status: string | null }>);
    }

    // Get all favorites for the user
    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .collect();

    const favoriteProductIds = new Set(favorites.map(f => f.productId));

    // Get product statuses
    const productStatuses = await Promise.all(
      productIds.map(async (id) => {
        const product = await ctx.db.get(id);
        return {
          id,
          status: product?.status || null,
        };
      })
    );

    // Build the result object
    return productIds.reduce((acc, id) => {
      const productStatus = productStatuses.find(p => p.id === id);
      acc[id] = {
        isFavorited: favoriteProductIds.has(id),
        status: productStatus?.status || null,
      };
      return acc;
    }, {} as Record<string, { isFavorited: boolean; status: string | null }>);
  },
});
