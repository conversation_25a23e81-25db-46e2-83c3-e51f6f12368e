import { v } from "convex/values";
import { mutation, query, internalMutation, action, internalAction } from "./_generated/server";
import { ConvexError } from "convex/values";
import { betterAuthComponent } from "./auth";
import { 
  getAuthUser, 
  requireAdmin, 
  requireAuth, 
  requireRole,
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";

/**
 * Create a new user manually (called by admin or internal processes)
 */
export const createUserManually = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    userType: v.optional(v.union(v.literal("consumer"), v.literal("seller"), v.literal("admin"))),
    subscriptionPlan: v.optional(v.union(v.literal("basic"), v.literal("premium"), v.literal("enterprise"))),
    phone: v.optional(v.string()),
    profileImage: v.optional(v.string()),
    isVerified: v.optional(v.boolean()),
    // Better Auth integration fields
    betterAuthUserId: v.optional(v.string()),
    emailVerified: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(args.email)) {
      throw new ConvexError("Invalid email format");
    }

    // Validate name
    if (args.name.trim().length < 2) {
      throw new ConvexError("Name must be at least 2 characters long");
    }

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (existingUser) {
      throw new ConvexError("User with this email already exists");
    }

    const now = Date.now();
    const userType = args.userType || "consumer";
    
    // Set trial expiration (30 days for new users)
    const trialExpiry = now + (30 * 24 * 60 * 60 * 1000);

    // Create user record
    const userId = await ctx.db.insert("users", {
      email: args.email.toLowerCase().trim(),
      name: args.name.trim(),
      userType,
      subscriptionStatus: "trial",
      subscriptionPlan: args.subscriptionPlan,
      subscriptionExpiresAt: trialExpiry,
      phone: args.phone?.trim(),
      profileImage: args.profileImage,
      isVerified: args.isVerified || false,
      updatedAt: now,
    });

    // Log user creation event
    await ctx.db.insert("analytics", {
      eventType: "user_created",
      userId,
      timestamp: now,
      metadata: {
        signupMethod: args.betterAuthUserId ? "oauth" : "email",
        category: userType,
      },
    });

    // If user is a seller, create a placeholder seller profile
    if (userType === "seller") {
      await ctx.db.insert("sellerProfiles", {
        userId,
        phone: args.phone || "",
        address: {
          street: "",
          city: "",
          state: "",
          zipCode: "",
          country: "",
        },
        verificationStatus: "pending",
        applicationDate: now,
        updatedAt: now,
      });

      // Log seller application event
      await ctx.db.insert("analytics", {
        eventType: "seller_application_started",
        userId,
        timestamp: now,
        metadata: {
          category: "seller",
        },
      });
    }

    return {
      success: true,
      userId,
      message: "User created successfully",
    };
  },
});

/**
 * Get user profile information
 */
export const getUserProfile = query({
  args: {},
  handler: async (ctx) => {
    const currentUser = await requireAuth(ctx);
    
    // Get user profile using the current user's ID
    const userProfile = await ctx.db
      .query("userProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", currentUser._id))
      .first();

    return userProfile || null;
  },
});

/**
 * Get profile image URL from storage ID
 */
export const getProfileImageUrl = query({
  args: {
    storageId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    if (!args.storageId) return null;
    
    try {
      const imageUrl = await ctx.storage.getUrl(args.storageId);
      return imageUrl;
    } catch (error) {
      console.error("Failed to get profile image URL:", error);
      return null;
    }
  },
});

/**
 * Generate upload URL for profile image
 */
export const generateProfileImageUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);

    // Generate upload URL for profile images
    return await ctx.storage.generateUploadUrl();
  },
});

/**
 * Update user profile information
 */
export const updateUserProfile = mutation({
  args: {
    name: v.optional(v.string()),
    phone: v.optional(v.string()),
    profileImage: v.optional(v.string()),
    userType: v.optional(v.union(v.literal("consumer"), v.literal("seller"), v.literal("admin"))),
    isVerified: v.optional(v.boolean()),
    // Extended profile fields
    bio: v.optional(v.string()),
    location: v.optional(v.string()),
    dateOfBirth: v.optional(v.number()),
    gender: v.optional(v.union(
      v.literal("male"),
      v.literal("female"),
      v.literal("non-binary"),
      v.literal("prefer-not-to-say")
    )),
    website: v.optional(v.string()),
    instagram: v.optional(v.string()),
    twitter: v.optional(v.string()),
    linkedin: v.optional(v.string()),
    facebook: v.optional(v.string()),
    occupation: v.optional(v.string()),
    company: v.optional(v.string()),
    interests: v.optional(v.array(v.string())),
    preferredCategories: v.optional(v.array(v.string())),
    preferredBrands: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Get target user (always current user for profile updates)
    const targetUserId = currentUser._id;
    
    // Get target user
    const targetUser = await ctx.db.get(targetUserId);
    if (!targetUser) {
      throw new ConvexError("User not found");
    }

    // Validate inputs
    if (args.name && args.name.trim().length < 2) {
      throw new ConvexError("Name must be at least 2 characters long");
    }

    if (args.phone && args.phone.trim().length > 0) {
      const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
      if (!phoneRegex.test(args.phone)) {
        throw new ConvexError("Invalid phone number format");
      }
    }

    // Role change validation
    if (args.userType && args.userType !== targetUser.userType) {
      // Only admins can change user types
      if (currentUser.userType !== "admin") {
        throw new ConvexError("Unauthorized: Only admins can change user types");
      }

      // Cannot change admin role unless current user is admin
      if (args.userType === "admin" && currentUser.userType !== "admin") {
        throw new ConvexError("Unauthorized: Cannot assign admin role");
      }
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.name) updateData.name = args.name.trim();
    if (args.phone !== undefined) updateData.phone = args.phone?.trim() || undefined;
    if (args.profileImage !== undefined) updateData.profileImage = args.profileImage;
    if (args.userType) updateData.userType = args.userType;
    if (args.isVerified !== undefined) updateData.isVerified = args.isVerified;

    // Update user in Convex
    await ctx.db.patch(targetUserId, updateData);

    // Handle extended profile fields
    const hasProfileFields = args.bio !== undefined || args.location !== undefined || 
                           args.dateOfBirth !== undefined || args.gender !== undefined ||
                           args.website !== undefined || args.instagram !== undefined ||
                           args.twitter !== undefined || args.linkedin !== undefined ||
                           args.facebook !== undefined || args.occupation !== undefined ||
                           args.company !== undefined || args.interests !== undefined ||
                           args.preferredCategories !== undefined || args.preferredBrands !== undefined;

    if (hasProfileFields) {
      // Check if user profile exists
      const existingProfile = await ctx.db
        .query("userProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", targetUserId))
        .first();

      const profileData: any = {
        userId: targetUserId,
        updatedAt: Date.now(),
      };

      // Add profile fields that are provided
      if (args.bio !== undefined) profileData.bio = args.bio;
      if (args.location !== undefined) profileData.location = args.location;
      if (args.dateOfBirth !== undefined) profileData.dateOfBirth = args.dateOfBirth;
      if (args.gender !== undefined) profileData.gender = args.gender;
      if (args.website !== undefined) profileData.website = args.website;
      if (args.instagram !== undefined) profileData.instagram = args.instagram;
      if (args.twitter !== undefined) profileData.twitter = args.twitter;
      if (args.linkedin !== undefined) profileData.linkedin = args.linkedin;
      if (args.facebook !== undefined) profileData.facebook = args.facebook;
      if (args.occupation !== undefined) profileData.occupation = args.occupation;
      if (args.company !== undefined) profileData.company = args.company;
      if (args.interests !== undefined) profileData.interests = args.interests;
      if (args.preferredCategories !== undefined) profileData.preferredCategories = args.preferredCategories;
      if (args.preferredBrands !== undefined) profileData.preferredBrands = args.preferredBrands;

      if (existingProfile) {
        // Update existing profile
        await ctx.db.patch(existingProfile._id, profileData);
      } else {
        // Create new profile
        await ctx.db.insert("userProfiles", profileData);
      }
    }

    // Handle user type change to seller
    if (args.userType === "seller" && targetUser.userType !== "seller") {
      // Check if seller profile exists
      const existingProfile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", targetUserId))
        .first();

      if (!existingProfile) {
        // Create seller profile
        await ctx.db.insert("sellerProfiles", {
          userId: targetUserId,
          phone: args.phone || targetUser.phone || "",
          address: {
            street: "",
            city: "",
            state: "",
            zipCode: "",
            country: "",
          },
          verificationStatus: "pending",
          applicationDate: Date.now(),
          updatedAt: Date.now(),
        });
      }
    }

    // Log profile update event
    await ctx.db.insert("analytics", {
      eventType: "profile_updated",
      userId: targetUserId,
      timestamp: Date.now(),
      metadata: {
        source: currentUser._id,
        category: "profile",
        revenue: 0,
      },
    });

    // Sync to Better Auth if updating current user's name
    if (targetUserId === currentUser._id && args.name) {
      try {
        await ctx.scheduler.runAfter(0, internal.userManagement.syncToBetterAuth, {
          userId: String(targetUserId),
          name: args.name.trim(),
        });
      } catch (error) {
        console.error("Failed to schedule Better Auth sync:", error);
        // Don't fail the operation if sync scheduling fails
      }
    }

    return {
      success: true,
      message: "Profile updated successfully",
      needsSessionRefresh: targetUserId === currentUser._id && !!args.name, // Flag to refresh session if current user's name changed
    };
  },
});

/**
 * Get current user's complete profile information
 */
export const getCurrentUserProfile = query({
  args: {},
  handler: async (ctx) => {
    const currentUser = await requireAuth(ctx);
    
    // Get extended profile information
    const userProfile = await ctx.db
      .query("userProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", currentUser._id))
      .first();

    // Combine user data with profile data
    return {
      ...currentUser,
      profile: userProfile || null,
    };
  },
});

/**
 * Update user password through Better Auth
 */
export const updateUserPassword = mutation({
  args: {
    currentPassword: v.string(),
    newPassword: v.string(),
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Password updates are handled by Better Auth, not our custom users table
    // This function can be used to trigger password change flows
    // The actual password update happens through Better Auth's built-in mechanisms
    
    // Log password change attempt
    await ctx.db.insert("analytics", {
      eventType: "password_change_attempted",
      userId: currentUser._id,
      timestamp: Date.now(),
      metadata: {
        source: "profile_settings",
      },
    });

    // Return success - the actual password change will be handled by Better Auth
    return {
      success: true,
      message: "Password change initiated through Better Auth",
    };
  },
});

/**
 * Internal action to sync user data to Better Auth
 */
export const syncToBetterAuth = internalAction({
  args: {
    userId: v.string(),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      console.log("Attempting to sync user data to Better Auth:", args.userId, args.name);
      
      // Call our custom HTTP endpoint to update Better Auth
      const convexUrl = process.env.CONVEX_SITE_URL || "https://kindly-giraffe-916.convex.cloud";
      const response = await fetch(`${convexUrl}/sync-user`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: args.userId,
          name: args.name,
        }),
      });

      if (response.ok) {
        console.log("Successfully synced user data to Better Auth");
      } else {
        console.error("Failed to sync to Better Auth:", await response.text());
      }
    } catch (error) {
      console.error("Failed to sync to Better Auth:", error);
      // Don't throw error to avoid breaking the main operation
    }
  },
});

/**
 * Update user subscription status
 */
export const updateSubscriptionStatus = mutation({
  args: {
    userId: v.optional(v.id("users")), // Optional for admin use
    subscriptionStatus: v.union(v.literal("active"), v.literal("inactive"), v.literal("trial")),
    subscriptionPlan: v.optional(v.union(v.literal("basic"), v.literal("premium"), v.literal("enterprise"))),
    subscriptionExpiresAt: v.optional(v.number()),
    paymentIntentId: v.optional(v.string()),
    renewalType: v.optional(v.union(v.literal("upgrade"), v.literal("downgrade"), v.literal("renewal"))),
    sendNotification: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Determine target user
    const targetUserId = args.userId || currentUser._id;
    
    // Authorization check
    if (args.userId && args.userId !== currentUser._id) {
      // Only admins can update other users' subscriptions
      if (currentUser.userType !== "admin") {
        throw new ConvexError("Unauthorized: Only admins can update other users' subscriptions");
      }
    }

    // Get target user
    const targetUser = await ctx.db.get(targetUserId);
    if (!targetUser) {
      throw new ConvexError("User not found");
    }

    // Validate subscription data
    if (args.subscriptionStatus === "active" && !args.subscriptionExpiresAt) {
      throw new ConvexError("Active subscriptions must have an expiration date");
    }

    if (args.subscriptionStatus === "active" && args.subscriptionExpiresAt && args.subscriptionExpiresAt <= Date.now()) {
      throw new ConvexError("Subscription expiration date must be in the future");
    }

    const now = Date.now();
    const previousStatus = targetUser.subscriptionStatus;
    const previousPlan = targetUser.subscriptionPlan;

    // Update subscription
    await ctx.db.patch(targetUserId, {
      subscriptionStatus: args.subscriptionStatus,
      subscriptionPlan: args.subscriptionPlan || targetUser.subscriptionPlan,
      subscriptionExpiresAt: args.subscriptionExpiresAt,
      updatedAt: now,
    });

    // Determine event type
    let eventType = "subscription_updated";
    if (args.renewalType) {
      eventType = `subscription_${args.renewalType}`;
    } else if (previousStatus !== args.subscriptionStatus) {
      if (args.subscriptionStatus === "active") {
        eventType = "subscription_activated";
      } else if (args.subscriptionStatus === "inactive") {
        eventType = "subscription_cancelled";
      }
    }

    // Log subscription event
    await ctx.db.insert("analytics", {
      eventType,
      userId: targetUserId,
      timestamp: now,
      metadata: {
        source: currentUser._id,
        category: "subscription",
        revenue: 0,
      },
    });

    // Send notification if requested (placeholder for email service)
    if (args.sendNotification) {
      // TODO: Integrate with email service
      console.log(`Subscription notification sent to user ${targetUserId}`);
    }

    return {
      success: true,
      message: "Subscription updated successfully",
      subscriptionStatus: args.subscriptionStatus,
      expiresAt: args.subscriptionExpiresAt,
    };
  },
});

/**
 * Soft delete user (admin only)
 */
export const deleteUser = mutation({
  args: {
    userId: v.id("users"),
    reason: v.optional(v.string()),
    hardDelete: v.optional(v.boolean()), // For GDPR compliance
  },
  handler: async (ctx, args) => {
    // Only admins can delete users
    const currentUser = await requireAdmin(ctx);

    // Get target user
    const targetUser = await ctx.db.get(args.userId);
    if (!targetUser) {
      throw new ConvexError("User not found");
    }

    // Prevent self-deletion
    if (args.userId === currentUser._id) {
      throw new ConvexError("Cannot delete your own account");
    }

    // Prevent deletion of other admins (unless hard delete is specified)
    if (targetUser.userType === "admin" && !args.hardDelete) {
      throw new ConvexError("Cannot soft delete admin accounts. Use hard delete if necessary.");
    }

    const now = Date.now();

    if (args.hardDelete) {
      // Hard delete: Remove all user data
      await hardDeleteUserData(ctx, args.userId);

      // Log deletion event
      await ctx.db.insert("analytics", {
        eventType: "user_hard_deleted",
        userId: currentUser._id, // Log under admin who performed deletion
        timestamp: now,
        metadata: {
          source: currentUser._id,
          category: "admin_action",
          revenue: 0,
        },
      });

      return {
        success: true,
        message: "User permanently deleted",
        deletionType: "hard",
      };
    } else {
      // Soft delete: Mark as deleted but keep data
      await ctx.db.patch(args.userId, {
        email: `deleted_${now}_${targetUser.email}`, // Prevent email conflicts
        name: `[DELETED] ${targetUser.name}`,
        userType: "consumer", // Downgrade permissions
        subscriptionStatus: "inactive",
        isVerified: false,
        updatedAt: now,
      });

      // Deactivate seller profile if exists
      const sellerProfile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", args.userId))
        .first();

      if (sellerProfile) {
        await ctx.db.patch(sellerProfile._id, {
          verificationStatus: "rejected",
          isActive: false,
          notes: `Account deleted on ${new Date(now).toISOString()}. Reason: ${args.reason || "Not specified"}`,
          updatedAt: now,
        });
      }

      // Deactivate products
      const products = await ctx.db
        .query("products")
        .withIndex("by_sellerId", (q) => q.eq("sellerId", args.userId))
        .collect();

      for (const product of products) {
        await ctx.db.patch(product._id, {
          status: "archived",
          updatedAt: now,
        });
      }

      // Log deletion event
      await ctx.db.insert("analytics", {
        eventType: "user_soft_deleted",
        userId: currentUser._id,
        timestamp: now,
        metadata: {
          source: currentUser._id,
          category: "admin_action",
          revenue: 0,
        },
      });

      return {
        success: true,
        message: "User soft deleted successfully",
        deletionType: "soft",
      };
    }
  },
});

/**
 * Helper function for hard deletion
 */
async function hardDeleteUserData(ctx: any, userId: Id<"users">) {
  // Delete seller profile
  const sellerProfile = await ctx.db
    .query("sellerProfiles")
    .withIndex("by_userId", (q: any) => q.eq("userId", userId))
    .first();

  if (sellerProfile) {
    await ctx.db.delete(sellerProfile._id);
  }

  // Delete products
  const products = await ctx.db
    .query("products")
    .withIndex("by_sellerId", (q: any) => q.eq("sellerId", userId))
    .collect();

  for (const product of products) {
    await ctx.db.delete(product._id);
  }

  // Delete orders (as buyer)
  const buyerOrders = await ctx.db
    .query("orders")
    .withIndex("by_buyerId", (q: any) => q.eq("buyerId", userId))
    .collect();

  for (const order of buyerOrders) {
    await ctx.db.delete(order._id);
  }

  // Delete orders (as seller)
  const sellerOrders = await ctx.db
    .query("orders")
    .withIndex("by_sellerId", (q: any) => q.eq("sellerId", userId))
    .collect();

  for (const order of sellerOrders) {
    await ctx.db.delete(order._id);
  }

  // Delete favorites
  const favorites = await ctx.db
    .query("favorites")
    .withIndex("by_user_id", (q: any) => q.eq("userId", userId))
    .collect();

  for (const favorite of favorites) {
    await ctx.db.delete(favorite._id);
  }

  // Delete invoices
  const invoices = await ctx.db
    .query("invoices")
    .withIndex("by_sellerId", (q: any) => q.eq("sellerId", userId))
    .collect();

  for (const invoice of invoices) {
    await ctx.db.delete(invoice._id);
  }

  // Delete offline sales
  const offlineSales = await ctx.db
    .query("offlineSales")
    .withIndex("sellerId", (q: any) => q.eq("sellerId", userId))
    .collect();

  for (const sale of offlineSales) {
    await ctx.db.delete(sale._id);
  }

  // Delete analytics events
  const analyticsEvents = await ctx.db
    .query("analytics")
    .withIndex("by_userId", (q: any) => q.eq("userId", userId))
    .collect();

  for (const event of analyticsEvents) {
    await ctx.db.delete(event._id);
  }

  // Finally delete the user
  await ctx.db.delete(userId);
}

/**
 * Bulk update subscription status (admin only)
 */
export const bulkUpdateSubscriptions = mutation({
  args: {
    userIds: v.array(v.id("users")),
    subscriptionStatus: v.union(v.literal("active"), v.literal("inactive"), v.literal("trial")),
    subscriptionPlan: v.optional(v.union(v.literal("basic"), v.literal("premium"), v.literal("enterprise"))),
    subscriptionExpiresAt: v.optional(v.number()),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Only admins can perform bulk operations
    const currentUser = await requireAdmin(ctx);

    if (args.userIds.length === 0) {
      throw new ConvexError("No users specified");
    }

    if (args.userIds.length > 100) {
      throw new ConvexError("Cannot update more than 100 users at once");
    }

    const now = Date.now();
    const results = [];

    for (const userId of args.userIds) {
      try {
        const user = await ctx.db.get(userId);
        if (!user) {
          results.push({ userId, success: false, error: "User not found" });
          continue;
        }

        await ctx.db.patch(userId, {
          subscriptionStatus: args.subscriptionStatus,
          subscriptionPlan: args.subscriptionPlan || user.subscriptionPlan,
          subscriptionExpiresAt: args.subscriptionExpiresAt,
          updatedAt: now,
        });

        // Log bulk update event
        await ctx.db.insert("analytics", {
          eventType: "subscription_bulk_updated",
          userId,
          timestamp: now,
          metadata: {
            source: currentUser._id,
            category: "admin_action",
            revenue: 0,
          },
        });

        results.push({ userId, success: true });
      } catch (error) {
        results.push({
          userId,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }

    return {
      success: true,
      message: `Bulk update completed. ${results.filter(r => r.success).length}/${results.length} users updated.`,
      results,
    };
  },
});

/**
 * Restore soft-deleted user (admin only)
 */
export const restoreUser = mutation({
  args: {
    userId: v.id("users"),
    newEmail: v.optional(v.string()), // In case original email is taken
  },
  handler: async (ctx, args) => {
    // Only admins can restore users
    const currentUser = await requireAdmin(ctx);

    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    // Check if this is a soft-deleted user
    if (!user.email.startsWith("deleted_")) {
      throw new ConvexError("User is not soft-deleted");
    }

    // Extract original email
    const emailParts = user.email.split("_");
    const originalEmail = emailParts.slice(2).join("_");
    const restoreEmail = args.newEmail || originalEmail;

    // Validate new email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(restoreEmail)) {
      throw new ConvexError("Invalid email format");
    }

    // Check if email is available
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", restoreEmail))
      .first();

    if (existingUser && existingUser._id !== args.userId) {
      throw new ConvexError("Email is already in use");
    }

    const now = Date.now();

    // Restore user
    await ctx.db.patch(args.userId, {
      email: restoreEmail,
      name: user.name.replace("[DELETED] ", ""),
      subscriptionStatus: "trial", // Reset to trial
      subscriptionExpiresAt: now + (30 * 24 * 60 * 60 * 1000), // 30 days trial
      isVerified: false, // Require re-verification
      updatedAt: now,
    });

    // Log restoration event
    await ctx.db.insert("analytics", {
      eventType: "user_restored",
      userId: args.userId,
      timestamp: now,
      metadata: {
        source: currentUser._id,
        category: "admin_action",
        revenue: 0,
      },
    });

    return {
      success: true,
      message: "User restored successfully",
      newEmail: restoreEmail,
    };
  },
});

/**
 * Get user management statistics (admin only)
 */
export const getUserStats = query({
  args: {},
  handler: async (ctx) => {
    // Only admins can view user stats
    await requireAdmin(ctx);

    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = now - (7 * 24 * 60 * 60 * 1000);

    // Get all users
    const allUsers = await ctx.db.query("users").collect();

    console.log("allUsers", allUsers);

    // Calculate statistics
    const totalUsers = allUsers.length;
    const activeUsers = allUsers.filter(u => u.subscriptionStatus === "active").length;
    const trialUsers = allUsers.filter(u => u.subscriptionStatus === "trial").length;
    const inactiveUsers = allUsers.filter(u => u.subscriptionStatus === "inactive").length;

    const consumers = allUsers.filter(u => u.userType === "consumer").length;
    const sellers = allUsers.filter(u => u.userType === "seller").length;
    const admins = allUsers.filter(u => u.userType === "admin").length;

    const verifiedUsers = allUsers.filter(u => u.isVerified).length;
    const deletedUsers = allUsers.filter(u => u.email.startsWith("deleted_")).length;

    const newUsersThisMonth = allUsers.filter(u => u._creationTime >= thirtyDaysAgo).length;
    const newUsersThisWeek = allUsers.filter(u => u._creationTime >= sevenDaysAgo).length;

    // Subscription plan breakdown
    const basicPlan = allUsers.filter(u => u.subscriptionPlan === "basic").length;
    const premiumPlan = allUsers.filter(u => u.subscriptionPlan === "premium").length;
    const enterprisePlan = allUsers.filter(u => u.subscriptionPlan === "enterprise").length;

    return {
      totalUsers,
      usersByStatus: {
        active: activeUsers,
        trial: trialUsers,
        inactive: inactiveUsers,
      },
      usersByType: {
        consumers,
        sellers,
        admins,
      },
      usersByPlan: {
        basic: basicPlan,
        premium: premiumPlan,
        enterprise: enterprisePlan,
        noPlan: totalUsers - basicPlan - premiumPlan - enterprisePlan,
      },
      verificationStats: {
        verified: verifiedUsers,
        unverified: totalUsers - verifiedUsers,
      },
      growthStats: {
        newThisMonth: newUsersThisMonth,
        newThisWeek: newUsersThisWeek,
      },
      deletedUsers,
    };
  },
});

/**
 * Validate user data before creation/update
 */
export const validateUserData = query({
  args: {
    email: v.optional(v.string()),
    name: v.optional(v.string()),
    phone: v.optional(v.string()),
    excludeUserId: v.optional(v.id("users")), // For updates
  },
  handler: async (ctx, args) => {
    const errors: string[] = [];

    // Validate email
    if (args.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(args.email)) {
        errors.push("Invalid email format");
      } else {
        // Check if email is already taken
        const existingUser = await ctx.db
          .query("users")
          .withIndex("by_email", (q: any) => q.eq("email", args.email?.toLowerCase()))
          .first();

        if (existingUser && existingUser._id !== args.excludeUserId) {
          errors.push("Email is already in use");
        }
      }
    }

    // Validate name
    if (args.name) {
      if (args.name.trim().length < 2) {
        errors.push("Name must be at least 2 characters long");
      }
      if (args.name.trim().length > 100) {
        errors.push("Name must be less than 100 characters");
      }
    }

    // Validate phone
    if (args.phone) {
      const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
      if (!phoneRegex.test(args.phone)) {
        errors.push("Invalid phone number format");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
});

/**
 * Search users (admin only)
 */
export const searchUsers = query({
  args: {
    query: v.string(),
    userType: v.optional(v.union(v.literal("consumer"), v.literal("seller"), v.literal("admin"))),
    subscriptionStatus: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("trial"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Only admins can search users
    await requireAdmin(ctx);

    const limit = Math.min(args.limit || 50, 100); // Max 100 results
    const searchQuery = args.query.toLowerCase().trim();

    if (searchQuery.length < 2) {
      throw new ConvexError("Search query must be at least 2 characters");
    }

    // Get all users and filter
    let users = await ctx.db.query("users").collect();

    // Filter by search query (name or email)
    users = users.filter(user =>
      user.name.toLowerCase().includes(searchQuery) ||
      user.email.toLowerCase().includes(searchQuery)
    );

    // Filter by user type
    if (args.userType) {
      users = users.filter(user => user.userType === args.userType);
    }

    // Filter by subscription status
    if (args.subscriptionStatus) {
      users = users.filter(user => user.subscriptionStatus === args.subscriptionStatus);
    }

    // Sort by creation date (newest first)
    users.sort((a, b) => b._creationTime - a._creationTime);

    // Limit results
    users = users.slice(0, limit);

    // Return sanitized user data
    return users.map(user => ({
      _id: user._id,
      email: user.email,
      name: user.name,
      userType: user.userType,
      subscriptionStatus: user.subscriptionStatus,
      subscriptionPlan: user.subscriptionPlan,
      subscriptionExpiresAt: user.subscriptionExpiresAt,
      isVerified: user.isVerified,
      updatedAt: user.updatedAt,
      isDeleted: user.email.startsWith("deleted_"),
    }));
  },
});

/**
 * Get all users with filtering (admin only)
 */
export const getAllUsers = query({
  args: {
    userType: v.optional(v.union(v.literal("consumer"), v.literal("seller"), v.literal("admin"))),
    subscriptionStatus: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("trial"))),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Only admins can get all users
    await requireAdmin(ctx);

    const limit = Math.min(args.limit || 50, 100); // Max 100 results
    const offset = args.offset || 0;

    // Get all users
    let users = await ctx.db.query("users").collect();

    // Filter by user type
    if (args.userType) {
      users = users.filter(user => user.userType === args.userType);
    }

    // Filter by subscription status
    if (args.subscriptionStatus) {
      users = users.filter(user => user.subscriptionStatus === args.subscriptionStatus);
    }

    // Sort by creation date (newest first)
    users.sort((a, b) => b._creationTime - a._creationTime);

    // Apply pagination
    const paginatedUsers = users.slice(offset, offset + limit);

    // Return sanitized user data
    return {
      users: paginatedUsers.map(user => ({
        _id: user._id,
        _creationTime: user._creationTime,
        email: user.email,
        name: user.name,
        userType: user.userType,
        subscriptionStatus: user.subscriptionStatus,
        subscriptionPlan: user.subscriptionPlan,
        subscriptionExpiresAt: user.subscriptionExpiresAt,
        isVerified: user.isVerified,
        updatedAt: user.updatedAt,
        isDeleted: user.email.startsWith("deleted_"),
      })),
      pagination: {
        total: users.length,
        limit,
        offset,
        hasMore: offset + limit < users.length,
      },
    };
  },
});

/**
 * Get user activity log (admin only)
 */
export const getUserActivity = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Only admins can view user activity
    await requireAdmin(ctx);

    const limit = Math.min(args.limit || 50, 200);

    // Get analytics events for the user
    const events = await ctx.db
      .query("analytics")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);

    return events.map(event => ({
      _id: event._id,
      eventType: event.eventType,
      timestamp: event.timestamp,
      metadata: event.metadata,
    }));
  },
});

/**
 * Update user verification status (admin only)
 */
export const updateVerificationStatus = mutation({
  args: {
    userId: v.id("users"),
    isVerified: v.boolean(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Only admins can update verification status
    const currentUser = await requireAdmin(ctx);

    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    const now = Date.now();

    // Update verification status
    await ctx.db.patch(args.userId, {
      isVerified: args.isVerified,
      updatedAt: now,
    });

    // Log verification event
    await ctx.db.insert("analytics", {
      eventType: args.isVerified ? "user_verified" : "user_unverified",
      userId: args.userId,
      timestamp: now,
      metadata: {
        source: currentUser._id,
        category: "admin_action",
        revenue: 0,
      },
    });

    return {
      success: true,
      message: `User ${args.isVerified ? "verified" : "unverified"} successfully`,
    };
  },
});

/**
 * Get admin users for support display (public query)
 */
export const getAdminUsers = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = Math.min(args.limit || 10, 20); // Max 20 results

    // Get admin users
    const adminUsers = await ctx.db
      .query("users")
      .withIndex("by_userType", (q) => q.eq("userType", "admin"))
      .take(limit);

    // Return sanitized admin user data
    return adminUsers.map(user => ({
      _id: user._id,
      name: user.name,
      email: user.email,
      profileImage: user.profileImage,
      isVerified: user.isVerified,
    }));
  },
});

/**
 * Get a single user by ID (admin only)
 */
export const getUserById = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Only admins can get user details
    await requireAdmin(ctx);

    // Get user record
    const user = await ctx.db.get(args.userId);
    
    if (!user) {
      return null;
    }

    // Return sanitized user data
    return {
      _id: user._id,
      _creationTime: user._creationTime,
      email: user.email,
      name: user.name,
      userType: user.userType,
      subscriptionStatus: user.subscriptionStatus,
      subscriptionPlan: user.subscriptionPlan,
      subscriptionExpiresAt: user.subscriptionExpiresAt,
      profileImage: user.profileImage,
      phone: user.phone,
      isVerified: user.isVerified,
      lastLoginAt: user.lastLoginAt,
      updatedAt: user.updatedAt,
      // Add computed fields for display
      status: user.isVerified ? "active" : "suspended",
      joinDate: user._creationTime,
      lastActive: user.lastLoginAt || user.updatedAt,
      totalSpent: 0, // This would need to be calculated from orders if available
    };
  },
});
