import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import {
  getAuthUser,
  requireAuth,
  requireAdmin
} from "./lib/auth_utils";
import { api } from "./_generated/api";
import type { Id } from "./_generated/dataModel";

/**
 * Calculate commission and fees for a sale
 */
export const calculateCommission = query({
  args: {
    saleAmount: v.number(),
    sellerId: v.optional(v.id("users")),
    platformFeeOverride: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let platformFeePercentage = 5.0; // Default 5%
    
    // Get seller-specific commission rate if available
    if (args.sellerId) {
      const sellerProfile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", args.sellerId))
        .first();
      
      if (sellerProfile?.commissionRate) {
        platformFeePercentage = sellerProfile.commissionRate * 100;
      }
    }

    // Allow admin override
    if (args.platformFeeOverride !== undefined) {
      platformFeePercentage = args.platformFeeOverride;
    }

    const platformFee = args.saleAmount * (platformFeePercentage / 100);
    const stripeFee = args.saleAmount * 0.029 + 0.30; // 2.9% + $0.30
    const sellerEarnings = args.saleAmount - platformFee - stripeFee;

    return {
      saleAmount: args.saleAmount,
      platformFee,
      platformFeePercentage,
      stripeFee,
      sellerEarnings,
      breakdown: {
        gross: args.saleAmount,
        platformFeeAmount: platformFee,
        stripeFeeAmount: stripeFee,
        netToSeller: sellerEarnings,
      },
    };
  },
});

/**
 * Get seller's commission rate and earnings summary
 */
export const getSellerCommissionInfo = query({
  args: {
    sellerId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return null;
    }

    const sellerId = args.sellerId || user._id;
    
    // Get seller profile
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", sellerId))
      .first();

    if (!sellerProfile) {
      return null;
    }

    // Calculate total earnings from completed orders
    const completedOrders = await ctx.db
      .query("orders")
      .withIndex("by_seller_status", (q) => 
        q.eq("sellerId", sellerId).eq("orderStatus", "delivered")
      )
      .collect();

    const totalSales = completedOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    const totalEarnings = completedOrders.reduce((sum, order) => sum + (order.sellerEarnings || 0), 0);
    const totalPlatformFees = completedOrders.reduce((sum, order) => sum + (order.stripeApplicationFee || 0), 0);

    // Get pending earnings (paid but not yet delivered)
    const pendingOrders = await ctx.db
      .query("orders")
      .withIndex("by_seller_status", (q) => 
        q.eq("sellerId", sellerId).eq("orderStatus", "paid")
      )
      .collect();

    const pendingEarnings = pendingOrders.reduce((sum, order) => sum + (order.sellerEarnings || 0), 0);

    return {
      sellerId,
      commissionRate: sellerProfile.commissionRate || 0.05, // Default 5%
      commissionPercentage: (sellerProfile.commissionRate || 0.05) * 100,
      totalSales,
      totalEarnings,
      totalPlatformFees,
      pendingEarnings,
      completedOrdersCount: completedOrders.length,
      pendingOrdersCount: pendingOrders.length,
      averageOrderValue: completedOrders.length > 0 ? totalSales / completedOrders.length : 0,
    };
  },
});

/**
 * Update seller's commission rate (admin only)
 */
export const updateSellerCommissionRate = mutation({
  args: {
    sellerId: v.id("users"),
    newCommissionRate: v.number(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAdmin(ctx);
    
    // Validate commission rate (between 0% and 20%)
    if (args.newCommissionRate < 0 || args.newCommissionRate > 0.20) {
      throw new ConvexError("Commission rate must be between 0% and 20%");
    }

    // Get seller profile
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.sellerId))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    const oldRate = sellerProfile.commissionRate || 0.05;
    
    // Update commission rate
    await ctx.db.patch(sellerProfile._id, {
      commissionRate: args.newCommissionRate,
      updatedAt: Date.now(),
    });

    // Log the change
    await ctx.db.insert("analytics", {
      eventType: "commission_rate_updated",
      userId: user._id,
      sellerId: args.sellerId,
      timestamp: Date.now(),
      metadata: {
        oldRate: oldRate.toString(),
        newRate: args.newCommissionRate.toString(),
        reason: args.reason || "Admin update",
        updatedBy: user._id,
      },
    });

    return {
      success: true,
      oldRate,
      newRate: args.newCommissionRate,
      message: `Commission rate updated from ${(oldRate * 100).toFixed(1)}% to ${(args.newCommissionRate * 100).toFixed(1)}%`,
    };
  },
});

/**
 * Get platform commission analytics (admin only)
 */
export const getPlatformCommissionAnalytics = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAdmin(ctx);
    
    const startDate = args.startDate || Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days ago
    const endDate = args.endDate || Date.now();

    // Get all completed orders in date range
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_orderStatus", (q) => q.eq("orderStatus", "delivered"))
      .filter((q) => 
        q.and(
          q.gte(q.field("deliveredDate"), startDate),
          q.lte(q.field("deliveredDate"), endDate)
        )
      )
      .collect();

    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const totalPlatformFees = orders.reduce((sum, order) => sum + (order.stripeApplicationFee || 0), 0);
    const totalSellerEarnings = orders.reduce((sum, order) => sum + (order.sellerEarnings || 0), 0);

    // Group by seller for top earners
    const sellerEarnings = new Map<string, { earnings: number; orders: number; fees: number }>();
    
    for (const order of orders) {
      const sellerId = order.sellerId;
      const current = sellerEarnings.get(sellerId) || { earnings: 0, orders: 0, fees: 0 };
      
      sellerEarnings.set(sellerId, {
        earnings: current.earnings + (order.sellerEarnings || 0),
        orders: current.orders + 1,
        fees: current.fees + (order.stripeApplicationFee || 0),
      });
    }

    // Convert to array and sort by earnings
    const topSellers = Array.from(sellerEarnings.entries())
      .map(([sellerId, data]) => ({ sellerId, ...data }))
      .sort((a, b) => b.earnings - a.earnings)
      .slice(0, 10);

    return {
      dateRange: { startDate, endDate },
      summary: {
        totalRevenue,
        totalPlatformFees,
        totalSellerEarnings,
        averageCommissionRate: totalRevenue > 0 ? (totalPlatformFees / totalRevenue) * 100 : 0,
        orderCount: orders.length,
        averageOrderValue: orders.length > 0 ? totalRevenue / orders.length : 0,
      },
      topSellers,
    };
  },
});

/**
 * Calculate estimated earnings for a product price
 */
export const estimateEarnings = query({
  args: {
    price: v.number(),
    sellerId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    const sellerId = args.sellerId || user?._id;
    
    if (!sellerId) {
      // Use default rates for non-authenticated users
      return {
        listPrice: args.price,
        platformFee: args.price * 0.05, // 5%
        stripeFee: args.price * 0.029 + 0.30,
        estimatedEarnings: args.price * 0.921 - 0.30, // ~92.1% - $0.30
        platformFeePercentage: 5.0,
      };
    }

    const commission = await ctx.runQuery(api.commissionSystem.calculateCommission, {
      saleAmount: args.price,
      sellerId,
    });

    return {
      listPrice: args.price,
      platformFee: commission.platformFee,
      stripeFee: commission.stripeFee,
      estimatedEarnings: commission.sellerEarnings,
      platformFeePercentage: commission.platformFeePercentage,
    };
  },
});
