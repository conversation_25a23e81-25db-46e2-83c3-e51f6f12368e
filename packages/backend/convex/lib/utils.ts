import { Doc, Id } from "../_generated/dataModel";
import { v } from "convex/values";

// Generate unique invoice number
export function generateInvoiceNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `INV-${timestamp.slice(-6)}-${random}`;
}

// Calculate subscription expiry date
export function calculateSubscriptionExpiry(months: number = 1): number {
  const now = new Date();
  now.setMonth(now.getMonth() + months);
  return now.getTime();
}

// Check if subscription is active
export function isSubscriptionActive(user: Doc<"users">): boolean {
  if (user.subscriptionStatus !== "active") return false;
  if (!user.subscriptionExpiresAt) return false;
  return user.subscriptionExpiresAt > Date.now();
}

// Calculate total order amount including shipping
export function calculateOrderTotal(
  productPrice: number,
  shippingCost: number,
  taxRate: number = 0
): number {
  const subtotal = productPrice + shippingCost;
  const tax = subtotal * taxRate;
  return Math.round((subtotal + tax) * 100) / 100; // Round to 2 decimal places
}

// Generate tracking number (placeholder)
export function generateTrackingNumber(): string {
  const prefix = "HV";
  const timestamp = Date.now().toString().slice(-8);
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${prefix}${timestamp}${random}`;
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate phone number format
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}

// Format price for display
export function formatPrice(price: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(price);
}

// Calculate seller rating from reviews
export function calculateSellerRating(reviews: { rating: number }[]): {
  averageRating: number;
  totalReviews: number;
} {
  if (reviews.length === 0) {
    return { averageRating: 0, totalReviews: 0 };
  }

  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = Math.round((totalRating / reviews.length) * 10) / 10;

  return {
    averageRating,
    totalReviews: reviews.length,
  };
}

// Get product condition display text
export function getConditionDisplay(condition: Doc<"products">["condition"]): string {
  const conditionMap = {
    new: "New",
    like_new: "Like New",
    excellent: "Excellent",
    very_good: "Very Good",
    good: "Good",
    fair: "Fair",
  };
  return conditionMap[condition] || "Unknown";
}

// Get category display text
export function getCategoryDisplay(category: Doc<"products">["category"]): string {
  const categoryMap = {
    clothing: "Clothing",
    sneakers: "Sneakers",
    collectibles: "Collectibles",
    accessories: "Accessories",
    handbags: "Handbags",
    watches: "Watches",
    jewelry: "Jewelry",
    sunglasses: "Sunglasses",
  };
  return categoryMap[category] || "Unknown";
}

// Check if user can access seller features
export function canAccessSellerFeatures(user: Doc<"users">, sellerProfile?: Doc<"sellerProfiles">): boolean {
  if (user.userType !== "seller") return false;
  if (!sellerProfile) return false;
  return sellerProfile.verificationStatus === "approved";
}

// Generate search keywords for products
export function generateSearchKeywords(product: {
  title: string;
  brand: string;
  category: string;
  description: string;
}): string[] {
  const keywords = new Set<string>();
  
  // Add title words
  product.title.toLowerCase().split(/\s+/).forEach(word => {
    if (word.length > 2) keywords.add(word);
  });
  
  // Add brand
  keywords.add(product.brand.toLowerCase());
  
  // Add category
  keywords.add(product.category.toLowerCase());
  
  // Add description words (first 50 words)
  product.description.toLowerCase()
    .split(/\s+/)
    .slice(0, 50)
    .forEach(word => {
      if (word.length > 3) keywords.add(word);
    });
  
  return Array.from(keywords);
}

/**
 * Helper function to calculate seller revenue from orders
 * @param orders Array of orders to calculate revenue from
 * @returns Total revenue calculated from subtotal minus platform fees
 */
export function calculateSellerRevenue(orders: any[]): number {
  return orders.reduce((sum, order) => {
    // Calculate revenue based on subtotal (actual sale price)
    const salePrice = order.subtotal || 0;
    // Platform fee is flat $20/month, not per transaction
    // For now, we'll use the actual platformFee from the order
    // but this should be corrected to $20/month in the future
    const platformFee = order.platformFee || 0;
    const sellerEarnings = salePrice - platformFee;
    return sum + sellerEarnings;
  }, 0);
}

/**
 * Helper function to calculate seller costs and profit from orders and products
 * @param orders Array of orders to calculate from
 * @param products Array of products to get cost data from
 * @returns Object with totalCost, grossProfit, and profitMargin
 */
export function calculateSellerCostsAndProfit(orders: any[], products: any[]): {
  totalCost: number;
  grossProfit: number;
  profitMargin: number;
} {
  // Calculate total revenue first
  const totalRevenue = calculateSellerRevenue(orders);
  
  // Calculate total cost from product costPaid values
  const totalCost = orders.reduce((sum, order) => {
    // Find the product for this order
    const product = products.find(p => p._id === order.productId);
    if (!product) return sum;
    
    // Get the cost paid for this product
    const costPaid = product.sourceInfo?.costPaid || 0;
    return sum + costPaid;
  }, 0);
  
  // Calculate gross profit
  const grossProfit = totalRevenue - totalCost;
  
  // Calculate profit margin (as percentage)
  const profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;
  
  return {
    totalCost,
    grossProfit,
    profitMargin: Math.round(profitMargin * 100) / 100, // Round to 2 decimal places
  };
}