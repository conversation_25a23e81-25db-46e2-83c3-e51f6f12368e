import { RunMutationCtx } from "@convex-dev/better-auth";
import { Resend } from "@convex-dev/resend";
import { render } from "@repo/email";
import { components } from "../_generated/api";
import "../polyfill";

export const resend: Resend = new Resend(components.resend, {
  testMode: !process.env.RESEND_API_KEY, // Use test mode if no API key
});

export const sendEmail = async (
  ctx: RunMutationCtx,
  {
    from,
    to,
    subject,
    react,
    cc,
    bcc,
    replyTo,
  }: {
    from?: string;
    to: string;
    subject: string;
    react: any;
    cc?: string[];
    bcc?: string[];
    replyTo?: string[];
  }
) => {
  const defaultFrom = process.env.RESEND_FROM_EMAIL || "<EMAIL>";

  try {
    await resend.sendEmail(ctx, {
      from: from || defaultFrom,
      to: to,
      subject,
      html: await render(react),
      ...(cc && { cc }),
      ...(bcc && { bcc }),
      ...(replyTo && { replyTo }),
    });

    console.log(`✅ Email sent successfully to ${to}: ${subject}`);
  } catch (error) {
    console.error(`❌ Failed to send email to ${to}:`, error);

    // In development, log email details for debugging
    if (process.env.NODE_ENV === "development") {
      console.log("📧 Email details:", {
        from: from || defaultFrom,
        to,
        subject,
        testMode: !process.env.RESEND_API_KEY,
      });
    }

    throw new Error(`Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
