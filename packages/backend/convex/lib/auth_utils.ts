import { GenericQueryCtx, GenericMutationCtx } from "convex/server";
import { DataModel, Id } from "../_generated/dataModel";
import { betterAuthComponent } from "../auth";

type AuthContext = GenericQueryCtx<DataModel> | GenericMutationCtx<DataModel>;

export interface AuthUser {
  _id: Id<"users">;
  email: string;
  name: string;
  userType: "consumer" | "seller" | "admin";
  subscriptionStatus: "active" | "inactive" | "trial";
  subscriptionExpiresAt?: number;
  updatedAt: number;
}

export async function getAuthUser(ctx: AuthContext): Promise<AuthUser | null> {
  const userMetadata = await betterAuthComponent.getAuthUser(ctx);
  
  if (!userMetadata) return null;

  // First try to get user by Better Auth userId (this is the expected case)
  let user = await ctx.db.get(userMetadata.userId as Id<"users">);

  // If that fails, try to find user by email (fallback for existing users)
  if (!user && userMetadata.email) {
    user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", userMetadata.email))
      .first();
  }

  return user;
}

export async function requireAuth(ctx: AuthContext): Promise<AuthUser> {
  const user = await getAuthUser(ctx);
  if (!user) {
    throw new Error("Authentication required");
  }
  return user;
}

export async function requireSubscription(ctx: AuthContext): Promise<AuthUser> {
  const user = await requireAuth(ctx);
  
  if (user.subscriptionStatus !== "active" && user.subscriptionStatus !== "trial") {
    throw new Error("Active or trial subscription required");
  }
  
  if (!user.subscriptionExpiresAt || user.subscriptionExpiresAt <= Date.now()) {
    throw new Error("Subscription expired");
  }
  
  return user;
}

export async function requireRole(
  ctx: AuthContext, 
  allowedRoles: ("consumer" | "seller" | "admin")[]
): Promise<AuthUser> {
  const user = await requireAuth(ctx);
  
  if (!allowedRoles.includes(user.userType)) {
    throw new Error(`Access denied. Required roles: ${allowedRoles.join(", ")}`);
  }
  
  return user;
}

export async function requireSellerStatus(
  ctx: AuthContext,
  requireVerification: boolean = true
): Promise<{
  user: AuthUser;
  sellerProfile: any;
}> {
  const user = await requireRole(ctx, ["seller", "admin"]);
  
  if (user.userType === "admin") {
    return { user, sellerProfile: null };
  }
  
  const sellerProfile = await ctx.db
    .query("sellerProfiles")
    .withIndex("by_userId", (q) => q.eq("userId", user._id))
    .first();
  
  if (!sellerProfile) {
    throw new Error("Seller profile not found");
  }
  
  if (requireVerification && sellerProfile.verificationStatus !== "approved") {
    throw new Error("Seller verification required");
  }
  
  return { user, sellerProfile };
}

export async function requireAdmin(ctx: AuthContext): Promise<AuthUser> {
  return requireRole(ctx, ["admin"]);
}

export function isSubscriptionActive(user: AuthUser): boolean {
  return (
    user.subscriptionStatus === "active" &&
    user.subscriptionExpiresAt !== undefined &&
    user.subscriptionExpiresAt > Date.now()
  );
}

export function canAccessSellerFeatures(
  user: AuthUser,
  sellerProfile?: any
): boolean {
  if (user.userType === "admin") return true;
  if (user.userType !== "seller") return false;
  if (!sellerProfile) return false;
  return sellerProfile.verificationStatus === "approved";
}

export function getUserPermissions(user: AuthUser, sellerProfile?: any) {
  const permissions = {
    canBuy: true,
    canSell: false,
    canManageUsers: false,
    canAccessAnalytics: false,
    canManageSubscriptions: false,
    requiresSubscription: false,
  };

  // Check subscription status
  const hasActiveSubscription = isSubscriptionActive(user);
  permissions.requiresSubscription = !hasActiveSubscription;

  // Role-based permissions
  switch (user.userType) {
    case "admin":
      permissions.canSell = true;
      permissions.canManageUsers = true;
      permissions.canAccessAnalytics = true;
      permissions.canManageSubscriptions = true;
      permissions.requiresSubscription = false;
      break;
      
    case "seller":
      permissions.canSell = canAccessSellerFeatures(user, sellerProfile);
      break;
      
    case "consumer":
    default:
      // Default permissions for consumers
      break;
  }

  return permissions;
}

// Error classes for better error handling
export class AuthError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = "AuthError";
  }
}

export class SubscriptionError extends AuthError {
  constructor(message: string = "Active or trial subscription required") {
    super(message, "SUBSCRIPTION_REQUIRED");
  }
}

export class PermissionError extends AuthError {
  constructor(message: string = "Insufficient permissions") {
    super(message, "PERMISSION_DENIED");
  }
}

export class VerificationError extends AuthError {
  constructor(message: string = "Account verification required") {
    super(message, "VERIFICATION_REQUIRED");
  }
}