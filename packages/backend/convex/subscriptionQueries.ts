import { v } from "convex/values";
import { internalQuery, query, mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAdmin, 
  requireAuth, 
  requireRole 
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";
import { api, internal } from "./_generated/api";

/**
 * Get current user's subscription status with detailed information
 */
export const getUserSubscriptionStatus = query({
  args: {
    userId: v.optional(v.id("users")), // Optional for admin use
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Determine target user
    const targetUserId = args.userId || currentUser._id;
    
    // Authorization check
    if (args.userId && args.userId !== currentUser._id) {
      // Only admins can view other users' subscription status
      if (currentUser.userType !== "admin") {
        throw new ConvexError("Unauthorized: Only admins can view other users' subscriptions");
      }
    }

    // Get target user
    const user = await ctx.db.get(targetUserId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    const now = Date.now();
    
    // Calculate subscription status
    const isActive = user.subscriptionStatus === "active" && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > now);
    
    const isExpired = user.subscriptionExpiresAt && user.subscriptionExpiresAt <= now;
    const isExpiringSoon = user.subscriptionExpiresAt && 
      user.subscriptionExpiresAt <= (now + (7 * 24 * 60 * 60 * 1000)); // 7 days
    
    // Calculate days remaining
    const daysRemaining = user.subscriptionExpiresAt 
      ? Math.max(0, Math.ceil((user.subscriptionExpiresAt - now) / (1000 * 60 * 60 * 24)))
      : null;

    // Get subscription history from analytics
    const subscriptionEvents = await ctx.db
      .query("analytics")
      .withIndex("by_userId", (q) => q.eq("userId", targetUserId))
      .filter((q) => q.or(
        q.eq(q.field("eventType"), "subscription_upgrade"),
        q.eq(q.field("eventType"), "subscription_downgrade"),
        q.eq(q.field("eventType"), "subscription_cancelled"),
        q.eq(q.field("eventType"), "subscription_activated")
      ))
      .order("desc")
      .take(10);

    // Calculate subscription value
    const planValues = {
      basic: 29.99,
      premium: 59.99,
      enterprise: 149.99,
    };
    
    const monthlyValue = user.subscriptionPlan ? planValues[user.subscriptionPlan] : 0;

    return {
      userId: targetUserId,
      subscriptionStatus: user.subscriptionStatus,
      subscriptionPlan: user.subscriptionPlan,
      isActive,
      isExpired,
      isExpiringSoon,
      isTrial: user.subscriptionStatus === "trial",
      expiresAt: user.subscriptionExpiresAt,
      daysRemaining,
      monthlyValue,
      features: getSubscriptionFeatures(user.subscriptionPlan),
      limits: getSubscriptionLimits(user.subscriptionPlan),
      history: subscriptionEvents.map(event => ({
        eventType: event.eventType,
        timestamp: event.timestamp,
        metadata: event.metadata,
      })),
      canUpgrade: user.subscriptionPlan !== "enterprise",
      canDowngrade: user.subscriptionPlan && user.subscriptionPlan !== "basic",
      renewalDate: user.subscriptionExpiresAt,
      gracePeriodEnds: user.subscriptionExpiresAt 
        ? user.subscriptionExpiresAt + (3 * 24 * 60 * 60 * 1000) // 3 days grace
        : null,
    };
  },
});

/**
 * Validate subscription access for marketplace features
 */
export const validateSubscriptionAccess = query({
  args: {
    feature: v.optional(v.string()), // Specific feature to check
    userId: v.optional(v.id("users")), // Optional for admin use
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Determine target user
    const targetUserId = args.userId || currentUser._id;
    
    // Authorization check
    if (args.userId && args.userId !== currentUser._id) {
      if (currentUser.userType !== "admin") {
        throw new ConvexError("Unauthorized");
      }
    }

    // Get user
    const user = await ctx.db.get(targetUserId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    const now = Date.now();
    
    // Check subscription status
    const isActive = user.subscriptionStatus === "active" && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > now);
    
    const isInGracePeriod = user.subscriptionExpiresAt && 
      user.subscriptionExpiresAt <= now &&
      user.subscriptionExpiresAt > (now - (3 * 24 * 60 * 60 * 1000)); // 3 days grace

    const hasAccess = isActive || isInGracePeriod || user.subscriptionStatus === "trial";

    // Get subscription features and limits
    const features = getSubscriptionFeatures(user.subscriptionPlan);
    const limits = getSubscriptionLimits(user.subscriptionPlan);

    // Check specific feature access
    let featureAccess = null;
    if (args.feature) {
      featureAccess = {
        hasAccess: hasAccess && features[args.feature as keyof typeof features] === true,
        feature: args.feature,
        reason: !hasAccess ? "subscription_required" : 
                !features[args.feature as keyof typeof features] ? "plan_upgrade_required" : "granted",
      };
    }

    // Get seller-specific access if applicable
    let sellerAccess = null;
    if (user.userType === "seller") {
      const sellerProfile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", targetUserId))
        .first();

      sellerAccess = {
        canSell: hasAccess && sellerProfile?.verificationStatus === "approved",
        verificationStatus: sellerProfile?.verificationStatus || "pending",
        requiresVerification: !sellerProfile || sellerProfile.verificationStatus !== "approved",
        requiresSubscription: !hasAccess,
      };
    }

    return {
      userId: targetUserId,
      hasAccess,
      isActive,
      isInGracePeriod,
      subscriptionStatus: user.subscriptionStatus,
      subscriptionPlan: user.subscriptionPlan,
      expiresAt: user.subscriptionExpiresAt,
      features,
      limits,
      featureAccess,
      sellerAccess,
      permissions: {
        canBuy: hasAccess,
        canSell: sellerAccess?.canSell || false,
        canAccessPremiumFeatures: hasAccess && (user.subscriptionPlan === "premium" || user.subscriptionPlan === "enterprise"),
        canAccessEnterpriseFeatures: hasAccess && user.subscriptionPlan === "enterprise",
        canUploadProducts: hasAccess && user.userType === "seller",
        canAccessAnalytics: hasAccess && (user.userType === "seller" || user.userType === "admin"),
      },
      upgradeRequired: !hasAccess || (args.feature && !features[args.feature as keyof typeof features]),
      gracePeriodEnds: user.subscriptionExpiresAt 
        ? user.subscriptionExpiresAt + (3 * 24 * 60 * 60 * 1000)
        : null,
    };
  },
});

/**
 * Get comprehensive subscription metrics (admin only)
 */
export const getSubscriptionMetrics = query({
  args: {
    timeRange: v.optional(v.union(
      v.literal("7d"),
      v.literal("30d"),
      v.literal("90d"),
      v.literal("1y")
    )),
    includeChurnAnalysis: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Only admins can view subscription metrics
    await requireAdmin(ctx);

    const timeRange = args.timeRange || "30d";
    const includeChurn = args.includeChurnAnalysis ?? true;
    
    const now = Date.now();
    const timeRanges = {
      "7d": 7 * 24 * 60 * 60 * 1000,
      "30d": 30 * 24 * 60 * 60 * 1000,
      "90d": 90 * 24 * 60 * 60 * 1000,
      "1y": 365 * 24 * 60 * 60 * 1000,
    };
    
    const startTime = now - timeRanges[timeRange];

    // Get all users
    const allUsers = await ctx.db.query("users").collect();
    
    // Calculate current subscription statistics
    const totalUsers = allUsers.length;
    const activeSubscriptions = allUsers.filter(u => 
      u.subscriptionStatus === "active" && 
      (!u.subscriptionExpiresAt || u.subscriptionExpiresAt > now)
    ).length;
    
    const trialUsers = allUsers.filter(u => u.subscriptionStatus === "trial").length;
    const inactiveUsers = allUsers.filter(u => u.subscriptionStatus === "inactive").length;
    
    // Plan breakdown
    const planBreakdown = {
      basic: allUsers.filter(u => u.subscriptionPlan === "basic" && u.subscriptionStatus === "active").length,
      premium: allUsers.filter(u => u.subscriptionPlan === "premium" && u.subscriptionStatus === "active").length,
      enterprise: allUsers.filter(u => u.subscriptionPlan === "enterprise" && u.subscriptionStatus === "active").length,
    };

    // Revenue calculations
    const planValues = { basic: 29.99, premium: 59.99, enterprise: 149.99 };
    const monthlyRecurringRevenue = 
      (planBreakdown.basic * planValues.basic) +
      (planBreakdown.premium * planValues.premium) +
      (planBreakdown.enterprise * planValues.enterprise);

    const annualRecurringRevenue = monthlyRecurringRevenue * 12;

    // Get subscription events in time range
    const subscriptionEvents = await ctx.db
      .query("analytics")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", startTime))
      .filter((q) => q.or(
        q.eq(q.field("eventType"), "subscription_upgrade"),
        q.eq(q.field("eventType"), "subscription_downgrade"),
        q.eq(q.field("eventType"), "subscription_cancelled"),
        q.eq(q.field("eventType"), "subscription_activated")
      ))
      .collect();

    // Calculate growth metrics
    const newSubscriptions = subscriptionEvents.filter(e => e.eventType === "subscription_activated").length;
    const upgrades = subscriptionEvents.filter(e => e.eventType === "subscription_upgrade").length;
    const downgrades = subscriptionEvents.filter(e => e.eventType === "subscription_downgrade").length;
    const cancellations = subscriptionEvents.filter(e => e.eventType === "subscription_cancelled").length;

    // Calculate conversion rates
    const newUsersInPeriod = allUsers.filter(u => u._creationTime >= startTime).length;
    const trialToActiveConversion = newUsersInPeriod > 0 ? (newSubscriptions / newUsersInPeriod) * 100 : 0;

    // Expiring subscriptions
    const next7Days = now + (7 * 24 * 60 * 60 * 1000);
    const next30Days = now + (30 * 24 * 60 * 60 * 1000);
    
    const expiringNext7Days = allUsers.filter(u => 
      u.subscriptionExpiresAt && 
      u.subscriptionExpiresAt > now && 
      u.subscriptionExpiresAt <= next7Days
    ).length;
    
    const expiringNext30Days = allUsers.filter(u => 
      u.subscriptionExpiresAt && 
      u.subscriptionExpiresAt > now && 
      u.subscriptionExpiresAt <= next30Days
    ).length;

    let churnAnalysis = null;
    if (includeChurn) {
      // Calculate churn rate
      const activeAtStart = allUsers.filter(u => 
        u.subscriptionStatus === "active" && 
        u._creationTime < startTime
      ).length;
      
      const churnRate = activeAtStart > 0 ? (cancellations / activeAtStart) * 100 : 0;
      
      churnAnalysis = {
        churnRate,
        cancellations,
        activeAtPeriodStart: activeAtStart,
        netGrowth: newSubscriptions - cancellations,
        growthRate: activeAtStart > 0 ? ((newSubscriptions - cancellations) / activeAtStart) * 100 : 0,
      };
    }

    return {
      timeRange,
      generatedAt: now,
      overview: {
        totalUsers,
        activeSubscriptions,
        trialUsers,
        inactiveUsers,
        subscriptionRate: totalUsers > 0 ? (activeSubscriptions / totalUsers) * 100 : 0,
      },
      planBreakdown,
      revenue: {
        monthlyRecurringRevenue,
        annualRecurringRevenue,
        averageRevenuePerUser: activeSubscriptions > 0 ? monthlyRecurringRevenue / activeSubscriptions : 0,
      },
      growth: {
        newSubscriptions,
        upgrades,
        downgrades,
        cancellations,
        netGrowth: newSubscriptions - cancellations,
        trialToActiveConversion,
      },
      expiring: {
        next7Days: expiringNext7Days,
        next30Days: expiringNext30Days,
      },
      churnAnalysis,
    };
  },
});

/**
 * Get users with expiring subscriptions (admin only)
 */
export const getExpiringSubscriptions = query({
  args: {
    daysAhead: v.optional(v.number()), // Days to look ahead (default: 30)
    includeGracePeriod: v.optional(v.boolean()),
    planFilter: v.optional(v.union(v.literal("basic"), v.literal("premium"), v.literal("enterprise"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Only admins can view expiring subscriptions
    await requireAdmin(ctx);

    const daysAhead = args.daysAhead || 30;
    const includeGrace = args.includeGracePeriod ?? true;
    const limit = Math.min(args.limit || 100, 500); // Max 500 results

    const now = Date.now();
    const futureTime = now + (daysAhead * 24 * 60 * 60 * 1000);
    const gracePeriod = 3 * 24 * 60 * 60 * 1000; // 3 days

    // Get all users with expiring subscriptions
    let users = await ctx.db.query("users").collect();

    // Filter users with expiring subscriptions
    users = users.filter(user => {
      if (!user.subscriptionExpiresAt) return false;

      const expiresAt = user.subscriptionExpiresAt;
      const isExpiring = expiresAt > now && expiresAt <= futureTime;
      const isInGrace = includeGrace && expiresAt <= now && expiresAt > (now - gracePeriod);

      return isExpiring || isInGrace;
    });

    // Filter by plan if specified
    if (args.planFilter) {
      users = users.filter(user => user.subscriptionPlan === args.planFilter);
    }

    // Sort by expiration date (soonest first)
    users.sort((a, b) => (a.subscriptionExpiresAt || 0) - (b.subscriptionExpiresAt || 0));

    // Limit results
    users = users.slice(0, limit);

    // Enrich user data with additional information
    const enrichedUsers = await Promise.all(
      users.map(async (user) => {
        const daysUntilExpiry = user.subscriptionExpiresAt
          ? Math.ceil((user.subscriptionExpiresAt - now) / (1000 * 60 * 60 * 24))
          : 0;

        const isInGracePeriod = user.subscriptionExpiresAt && user.subscriptionExpiresAt <= now;
        const gracePeriodDaysLeft = isInGracePeriod
          ? Math.max(0, Math.ceil(((user.subscriptionExpiresAt || 0) + gracePeriod - now) / (1000 * 60 * 60 * 24)))
          : 0;

        // Get recent subscription events
        const recentEvents = await ctx.db
          .query("analytics")
          .withIndex("by_userId", (q) => q.eq("userId", user._id))
          .filter((q) => q.or(
            q.eq(q.field("eventType"), "subscription_upgrade"),
            q.eq(q.field("eventType"), "subscription_downgrade"),
            q.eq(q.field("eventType"), "subscription_cancelled"),
            q.eq(q.field("eventType"), "subscription_activated")
          ))
          .order("desc")
          .take(3);

        // Check if user is a seller
        let sellerInfo = null;
        if (user.userType === "seller") {
          const sellerProfile = await ctx.db
            .query("sellerProfiles")
            .withIndex("by_userId", (q) => q.eq("userId", user._id))
            .first();

          if (sellerProfile) {
            // Get seller's active products count
            const activeProducts = await ctx.db
              .query("products")
              .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id))
              .filter((q) => q.eq(q.field("status"), "active"))
              .collect();

            sellerInfo = {
              verificationStatus: sellerProfile.verificationStatus,
              activeProducts: activeProducts.length,
              totalSales: sellerProfile.totalSales || 0,
              totalEarnings: sellerProfile.totalEarnings || 0,
            };
          }
        }

        return {
          userId: user._id,
          email: user.email,
          name: user.name,
          userType: user.userType,
          subscriptionStatus: user.subscriptionStatus,
          subscriptionPlan: user.subscriptionPlan,
          expiresAt: user.subscriptionExpiresAt,
          daysUntilExpiry,
          isInGracePeriod,
          gracePeriodDaysLeft,
          urgencyLevel: daysUntilExpiry <= 0 ? "critical" :
                       daysUntilExpiry <= 3 ? "high" :
                       daysUntilExpiry <= 7 ? "medium" : "low",
          lastLoginAt: user.lastLoginAt,
          recentSubscriptionActivity: recentEvents.map(event => ({
            eventType: event.eventType,
            timestamp: event.timestamp,
            metadata: event.metadata,
          })),
          sellerInfo,
          renewalReminders: {
            shouldSend7Day: daysUntilExpiry === 7,
            shouldSend3Day: daysUntilExpiry === 3,
            shouldSend1Day: daysUntilExpiry === 1,
            shouldSendGraceReminder: isInGracePeriod && gracePeriodDaysLeft === 2,
          },
        };
      })
    );

    // Calculate summary statistics
    const summary = {
      totalExpiring: enrichedUsers.length,
      byUrgency: {
        critical: enrichedUsers.filter(u => u.urgencyLevel === "critical").length,
        high: enrichedUsers.filter(u => u.urgencyLevel === "high").length,
        medium: enrichedUsers.filter(u => u.urgencyLevel === "medium").length,
        low: enrichedUsers.filter(u => u.urgencyLevel === "low").length,
      },
      byPlan: {
        basic: enrichedUsers.filter(u => u.subscriptionPlan === "basic").length,
        premium: enrichedUsers.filter(u => u.subscriptionPlan === "premium").length,
        enterprise: enrichedUsers.filter(u => u.subscriptionPlan === "enterprise").length,
      },
      inGracePeriod: enrichedUsers.filter(u => u.isInGracePeriod).length,
      sellers: enrichedUsers.filter(u => u.userType === "seller").length,
      potentialRevenueLoss: enrichedUsers.reduce((sum, user) => {
        const planValues = { basic: 29.99, premium: 59.99, enterprise: 149.99 };
        return sum + (planValues[user.subscriptionPlan as keyof typeof planValues] || 0);
      }, 0),
    };

    return {
      summary,
      users: enrichedUsers,
      generatedAt: now,
      filters: {
        daysAhead,
        includeGracePeriod: includeGrace,
        planFilter: args.planFilter,
        limit,
      },
    };
  },
});

/**
 * Get subscription features based on plan
 */
function getSubscriptionFeatures(plan?: string) {
  const features = {
    basic: {
      canBuy: true,
      canSell: true,
      maxProducts: 50,
      premiumSupport: false,
      advancedAnalytics: false,
      bulkOperations: false,
      apiAccess: false,
      customBranding: false,
      priorityListing: false,
    },
    premium: {
      canBuy: true,
      canSell: true,
      maxProducts: 200,
      premiumSupport: true,
      advancedAnalytics: true,
      bulkOperations: true,
      apiAccess: false,
      customBranding: false,
      priorityListing: true,
    },
    enterprise: {
      canBuy: true,
      canSell: true,
      maxProducts: -1, // Unlimited
      premiumSupport: true,
      advancedAnalytics: true,
      bulkOperations: true,
      apiAccess: true,
      customBranding: true,
      priorityListing: true,
    },
  };

  return features[plan as keyof typeof features] || features.basic;
}

/**
 * Get subscription limits based on plan
 */
function getSubscriptionLimits(plan?: string) {
  const limits = {
    basic: {
      maxProducts: 50,
      maxImages: 5,
      maxFileSize: 5 * 1024 * 1024, // 5MB
      apiCallsPerMonth: 0,
      supportTicketsPerMonth: 5,
    },
    premium: {
      maxProducts: 200,
      maxImages: 10,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      apiCallsPerMonth: 0,
      supportTicketsPerMonth: 20,
    },
    enterprise: {
      maxProducts: -1, // Unlimited
      maxImages: 20,
      maxFileSize: 50 * 1024 * 1024, // 50MB
      apiCallsPerMonth: 10000,
      supportTicketsPerMonth: -1, // Unlimited
    },
  };

  return limits[plan as keyof typeof limits] || limits.basic;
}

/**
 * Get cached subscription metrics for dashboard (optimized for frequent access)
 */
export const getCachedSubscriptionMetrics = query({
  args: {},
  handler: async (ctx) => {
    // Only admins can view metrics
    await requireAdmin(ctx);

    const now = Date.now();
    const cacheKey = "subscription_metrics_cache";
    const cacheExpiry = 5 * 60 * 1000; // 5 minutes

    // Try to get cached data first
    const cachedData = await ctx.db
      .query("analytics")
      .withIndex("by_eventType", (q) => q.eq("eventType", "metrics_cache"))
      .order("desc")
      .first();

    if (cachedData && (now - cachedData.timestamp) < cacheExpiry) {
      return {
        ...cachedData.metadata,
        cached: true,
        cacheAge: now - cachedData.timestamp,
      };
    }

    const metrics = {
      timeRange: "30d" as const,
      generatedAt: now,
      overview: {
        totalSubscriptions: 0,
        activeSubscriptions: 0,
        trialSubscriptions: 0,
        cancelledSubscriptions: 0,
        revenue: { total: 0, monthly: 0, growth: 0 }
      },
      conversionRates: { trialToActive: 0, freeToTrial: 0 },
      churnAnalysis: null
    };

    return {
      ...metrics,
      cached: false,
      cacheAge: 0,
    };
  },
});

/**
 * Get subscription status for multiple users (batch query for performance)
 */
export const getBatchSubscriptionStatus = query({
  args: {
    userIds: v.array(v.id("users")),
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);

    // Only admins can batch query other users
    if (currentUser.userType !== "admin") {
      throw new ConvexError("Admin access required for batch queries");
    }

    if (args.userIds.length > 100) {
      throw new ConvexError("Maximum 100 users per batch query");
    }

    const now = Date.now();
    const results = [];

    for (const userId of args.userIds) {
      const user = await ctx.db.get(userId);
      if (!user) {
        results.push({
          userId,
          error: "User not found",
        });
        continue;
      }

      const isActive = user.subscriptionStatus === "active" &&
        (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > now);

      results.push({
        userId,
        subscriptionStatus: user.subscriptionStatus,
        subscriptionPlan: user.subscriptionPlan,
        isActive,
        expiresAt: user.subscriptionExpiresAt,
        daysRemaining: user.subscriptionExpiresAt
          ? Math.max(0, Math.ceil((user.subscriptionExpiresAt - now) / (1000 * 60 * 60 * 24)))
          : null,
      });
    }

    return results;
  },
});

/**
 * Get subscription renewal predictions (admin only)
 */
export const getSubscriptionRenewalPredictions = query({
  args: {
    daysAhead: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const daysAhead = args.daysAhead || 30;
    const now = Date.now();
    const futureTime = now + (daysAhead * 24 * 60 * 60 * 1000);

    // Get users with subscriptions expiring in the specified period
    const expiringUsers = await ctx.db.query("users")
      .filter((q) => q.and(
        q.neq(q.field("subscriptionExpiresAt"), undefined),
        q.gt(q.field("subscriptionExpiresAt"), now),
        q.lte(q.field("subscriptionExpiresAt"), futureTime)
      ))
      .collect();

    // Calculate renewal predictions based on user behavior
    const predictions = await Promise.all(
      expiringUsers.map(async (user) => {
        // Get user's subscription history
        const subscriptionHistory = await ctx.db
          .query("analytics")
          .withIndex("by_userId", (q) => q.eq("userId", user._id))
          .filter((q) => q.or(
            q.eq(q.field("eventType"), "subscription_upgrade"),
            q.eq(q.field("eventType"), "subscription_cancelled"),
            q.eq(q.field("eventType"), "subscription_activated")
          ))
          .collect();

        // Get user activity (login frequency)
        const recentLogins = user.lastLoginAt && user.lastLoginAt > (now - (30 * 24 * 60 * 60 * 1000));

        // Calculate renewal probability based on factors
        let renewalProbability = 0.5; // Base probability

        // Factor 1: Subscription history (loyal customers more likely to renew)
        const renewalCount = subscriptionHistory.filter(e =>
          e.eventType === "subscription_activated" || e.eventType === "subscription_upgrade"
        ).length;
        const cancellationCount = subscriptionHistory.filter(e =>
          e.eventType === "subscription_cancelled"
        ).length;

        if (renewalCount > cancellationCount) {
          renewalProbability += 0.2;
        }

        // Factor 2: Recent activity
        if (recentLogins) {
          renewalProbability += 0.2;
        }

        // Factor 3: Plan type (higher plans more likely to renew)
        if (user.subscriptionPlan === "premium") {
          renewalProbability += 0.1;
        } else if (user.subscriptionPlan === "enterprise") {
          renewalProbability += 0.2;
        }

        // Factor 4: Account age (older accounts more likely to renew)
        const accountAge = now - user._creationTime;
        const monthsOld = accountAge / (30 * 24 * 60 * 60 * 1000);
        if (monthsOld > 6) {
          renewalProbability += 0.1;
        }

        // Factor 5: Seller activity (sellers with products more likely to renew)
        if (user.userType === "seller") {
          const activeProducts = await ctx.db
            .query("products")
            .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id))
            .filter((q) => q.eq(q.field("status"), "active"))
            .collect();

          if (activeProducts.length > 0) {
            renewalProbability += 0.15;
          }
        }

        // Cap probability at 0.95
        renewalProbability = Math.min(renewalProbability, 0.95);

        const daysUntilExpiry = Math.ceil((user.subscriptionExpiresAt! - now) / (1000 * 60 * 60 * 24));

        return {
          userId: user._id,
          email: user.email,
          name: user.name,
          subscriptionPlan: user.subscriptionPlan,
          expiresAt: user.subscriptionExpiresAt,
          daysUntilExpiry,
          renewalProbability: Math.round(renewalProbability * 100),
          riskLevel: renewalProbability < 0.3 ? "high" :
                    renewalProbability < 0.6 ? "medium" : "low",
          factors: {
            hasRenewalHistory: renewalCount > 0,
            recentlyActive: recentLogins,
            accountAge: Math.round(monthsOld),
            isActiveSeller: user.userType === "seller",
          },
        };
      })
    );

    // Sort by renewal probability (lowest first - highest risk)
    predictions.sort((a, b) => a.renewalProbability - b.renewalProbability);

    const summary = {
      totalExpiring: predictions.length,
      highRisk: predictions.filter(p => p.riskLevel === "high").length,
      mediumRisk: predictions.filter(p => p.riskLevel === "medium").length,
      lowRisk: predictions.filter(p => p.riskLevel === "low").length,
      averageRenewalProbability: predictions.length > 0
        ? Math.round(predictions.reduce((sum, p) => sum + p.renewalProbability, 0) / predictions.length)
        : 0,
      potentialRevenueLoss: predictions
        .filter(p => p.riskLevel === "high")
        .reduce((sum, p) => {
          const planValues = { basic: 29.99, premium: 59.99, enterprise: 149.99 };
          return sum + (planValues[p.subscriptionPlan as keyof typeof planValues] || 0);
        }, 0),
    };

    return {
      summary,
      predictions,
      generatedAt: now,
      daysAhead,
    };
  },
});

/**
 * Get real-time subscription health score
 */
export const getSubscriptionHealthScore = query({
  args: {},
  handler: async (ctx) => {
    await requireAdmin(ctx);

    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);

    // Get all users
    const allUsers = await ctx.db.query("users").collect();

    // Calculate health metrics
    const totalUsers = allUsers.length;
    const activeSubscriptions = allUsers.filter(u =>
      u.subscriptionStatus === "active" &&
      (!u.subscriptionExpiresAt || u.subscriptionExpiresAt > now)
    ).length;

    // Get recent subscription events
    const recentEvents = await ctx.db
      .query("analytics")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", thirtyDaysAgo))
      .filter((q) => q.or(
        q.eq(q.field("eventType"), "subscription_activated"),
        q.eq(q.field("eventType"), "subscription_cancelled")
      ))
      .collect();

    const newSubscriptions = recentEvents.filter(e => e.eventType === "subscription_activated").length;
    const cancellations = recentEvents.filter(e => e.eventType === "subscription_cancelled").length;

    // Calculate health score (0-100)
    let healthScore = 50; // Base score

    // Factor 1: Subscription rate
    const subscriptionRate = totalUsers > 0 ? (activeSubscriptions / totalUsers) * 100 : 0;
    healthScore += (subscriptionRate - 50) * 0.5; // Adjust based on 50% target

    // Factor 2: Growth vs churn
    const netGrowth = newSubscriptions - cancellations;
    healthScore += netGrowth * 2; // Each net new subscription adds 2 points

    // Factor 3: Churn rate
    const churnRate = activeSubscriptions > 0 ? (cancellations / activeSubscriptions) * 100 : 0;
    healthScore -= churnRate * 2; // Each % of churn removes 2 points

    // Cap score between 0 and 100
    healthScore = Math.max(0, Math.min(100, healthScore));

    const healthLevel = healthScore >= 80 ? "excellent" :
                       healthScore >= 60 ? "good" :
                       healthScore >= 40 ? "fair" : "poor";

    return {
      healthScore: Math.round(healthScore),
      healthLevel,
      metrics: {
        totalUsers,
        activeSubscriptions,
        subscriptionRate: Math.round(subscriptionRate),
        newSubscriptions,
        cancellations,
        netGrowth,
        churnRate: Math.round(churnRate * 100) / 100,
      },
      recommendations: generateHealthRecommendations(healthScore, {
        subscriptionRate,
        churnRate,
        netGrowth,
      }),
      generatedAt: now,
    };
  },
});

/**
 * Generate health recommendations based on metrics
 */
function generateHealthRecommendations(
  healthScore: number,
  metrics: { subscriptionRate: number; churnRate: number; netGrowth: number }
) {
  const recommendations = [];

  if (healthScore < 40) {
    recommendations.push({
      priority: "high",
      category: "urgent",
      message: "Subscription health is critical. Immediate action required.",
      actions: ["Review pricing strategy", "Improve onboarding", "Contact at-risk customers"],
    });
  }

  if (metrics.subscriptionRate < 30) {
    recommendations.push({
      priority: "high",
      category: "conversion",
      message: "Low subscription conversion rate detected.",
      actions: ["Optimize trial experience", "Review pricing", "Improve value proposition"],
    });
  }

  if (metrics.churnRate > 10) {
    recommendations.push({
      priority: "high",
      category: "retention",
      message: "High churn rate detected.",
      actions: ["Implement retention campaigns", "Improve customer support", "Analyze churn reasons"],
    });
  }

  if (metrics.netGrowth < 0) {
    recommendations.push({
      priority: "medium",
      category: "growth",
      message: "Negative growth trend detected.",
      actions: ["Increase marketing efforts", "Improve product features", "Launch referral program"],
    });
  }

  if (recommendations.length === 0) {
    recommendations.push({
      priority: "low",
      category: "optimization",
      message: "Subscription health is good. Focus on optimization.",
      actions: ["Monitor trends", "Test new features", "Expand to new markets"],
    });
  }

  return recommendations;
}
