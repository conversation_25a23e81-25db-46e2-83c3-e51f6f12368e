import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUser } from "./lib/auth_utils";

// Helper function to sanitize contact information
function sanitizeContent(content: string): string {
  let sanitized = content;
  
  // Replace phone numbers
  sanitized = sanitized.replace(
    /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g,
    "[PHONE NUMBER REMOVED]"
  );
  
  // Replace email addresses
  sanitized = sanitized.replace(
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    "[EMAIL REMOVED]"
  );
  
  // Replace social media handles
  sanitized = sanitized.replace(
    /@[a-zA-Z0-9_]{1,15}/g,
    "[HANDLE REMOVED]"
  );
  
  return sanitized;
}

// Generate a unique ticket number
function generateTicketNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `TKT-${timestamp.slice(-6)}-${random}`;
}

// Create a new support ticket
export const createSupportTicket = mutation({
  args: {
    subject: v.string(),
    description: v.string(),
    priority: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    ),
    category: v.union(
      v.literal("general"),
      v.literal("technical"),
      v.literal("billing"),
      v.literal("order_issue"),
      v.literal("product_issue"),
      v.literal("seller_issue"),
      v.literal("refund"),
      v.literal("other")
    ),
    tags: v.optional(v.array(v.string())),
    attachments: v.optional(v.array(v.object({
      name: v.string(),
      url: v.string(),
      size: v.number(),
      type: v.string(),
    }))),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    const now = Date.now();
    const ticketNumber = generateTicketNumber();

    // Create the support ticket
    const ticketId = await ctx.db.insert("supportTickets", {
      ticketNumber,
      userId: user._id,
      subject: args.subject,
      description: sanitizeContent(args.description),
      priority: args.priority,
      status: "open",
      category: args.category,
      tags: args.tags || [],
      attachments: args.attachments,
      updatedAt: now,
    });

    // Create the initial support message
    await ctx.db.insert("supportMessages", {
      ticketId,
      senderId: user._id,
      senderType: "customer",
      content: sanitizeContent(args.description),
      messageType: "text",
      updatedAt: now,
    });

    return { ticketId, ticketNumber };
  },
});

// Get support tickets for a user
export const getUserSupportTickets = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    const tickets = await ctx.db
      .query("supportTickets")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .order("desc")
      .collect();

    // Get the latest message for each ticket
    const ticketsWithLatestMessage = await Promise.all(
      tickets.map(async (ticket) => {
        const latestMessage = await ctx.db
          .query("supportMessages")
          .withIndex("by_ticketId", (q) => q.eq("ticketId", ticket._id))
          .order("desc")
          .first();

        return {
          ...ticket,
          description: sanitizeContent(ticket.description),
          latestMessage: latestMessage ? {
            ...latestMessage,
            content: sanitizeContent(latestMessage.content),
          } : null,
        };
      })
    );

    return ticketsWithLatestMessage;
  },
});

// Get a specific support ticket with messages
export const getSupportTicket = query({
  args: { ticketId: v.id("supportTickets") },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) throw new Error("Ticket not found");

    // Check if user has access to this ticket
    if (ticket.userId !== user._id) {
      // Check if user is an admin
      const adminRole = await ctx.db
        .query("adminRoles")
        .withIndex("by_userId", (q) => q.eq("userId", user._id))
        .first();

    //   if (!adminRole || !adminRole.isActive) {
    //     throw new Error("Access denied");
    //   }
    }

    // Get all messages for this ticket
    const messages = await ctx.db
      .query("supportMessages")
      .withIndex("by_ticketId", (q) => q.eq("ticketId", args.ticketId))
      .order("asc")
      .collect();

    // Get user details for participants
    const participantIds = [...new Set(messages.map(m => m.senderId))];
    const participants = await Promise.all(
      participantIds.map(id => ctx.db.get(id))
    );

    return {
      ticket: {
        ...ticket,
        description: sanitizeContent(ticket.description),
      },
      messages: messages.map(message => ({
        ...message,
        content: sanitizeContent(message.content),
      })),
      participants: participants.filter(Boolean),
    };
  },
});

// Add a message to a support ticket
export const addSupportMessage = mutation({
  args: {
    ticketId: v.id("supportTickets"),
    content: v.string(),
    messageType: v.optional(v.union(
      v.literal("text"),
      v.literal("image"),
      v.literal("file"),
      v.literal("system")
    )),
    isInternal: v.optional(v.boolean()),
    attachments: v.optional(v.array(v.object({
      name: v.string(),
      url: v.string(),
      size: v.number(),
      type: v.string(),
    }))),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) throw new Error("Ticket not found");

    // Check if user has access to this ticket
    if (ticket.userId !== user._id) {
      // Check if user is an admin
      const adminRole = await ctx.db
        .query("adminRoles")
        .withIndex("by_userId", (q) => q.eq("userId", user._id))
        .first();

    //   if (!adminRole || !adminRole.isActive) {
    //     throw new Error("Access denied");
    //   }
    }

    const now = Date.now();
    const senderType = ticket.userId === user._id ? "customer" : "admin";

    // Create the message
    const messageId = await ctx.db.insert("supportMessages", {
      ticketId: args.ticketId,
      senderId: user._id,
      senderType,
      content: sanitizeContent(args.content),
      messageType: args.messageType || "text",
      isInternal: args.isInternal || false,
      attachments: args.attachments,
      updatedAt: now,
    });

    // Update ticket status and timestamp
    await ctx.db.patch(args.ticketId, {
      updatedAt: now,
      status: senderType === "admin" ? "in_progress" : "waiting_for_customer",
    });

    return messageId;
  },
});

// Get all support tickets (admin only)
export const getAllSupportTickets = query({
  args: {
    status: v.optional(v.union(
      v.literal("open"),
      v.literal("in_progress"),
      v.literal("waiting_for_customer"),
      v.literal("resolved"),
      v.literal("closed")
    )),
    priority: v.optional(v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    )),
    category: v.optional(v.union(
      v.literal("general"),
      v.literal("technical"),
      v.literal("billing"),
      v.literal("order_issue"),
      v.literal("product_issue"),
      v.literal("seller_issue"),
      v.literal("refund"),
      v.literal("other")
    )),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");
    let tickets;
    
    // Apply filters
    if (args.status) {
      tickets = await ctx.db
        .query("supportTickets")
        .withIndex("by_status", (q) => q.eq("status", args.status as any))
        .order("desc")
        .collect();
    } else if (args.priority) {
      tickets = await ctx.db
        .query("supportTickets")
        .withIndex("by_priority", (q) => q.eq("priority", args.priority as any))
        .order("desc")
        .collect();
    } else if (args.category) {
      tickets = await ctx.db
        .query("supportTickets")
        .withIndex("by_category", (q) => q.eq("category", args.category as any))
        .order("desc")
        .collect();
    } else {
      tickets = await ctx.db
        .query("supportTickets")
        .order("desc")
        .collect();
    }

    // Get user details and latest messages
    const ticketsWithDetails = await Promise.all(
      tickets.map(async (ticket) => {
        const [user, latestMessage] = await Promise.all([
          ctx.db.get(ticket.userId),
          ctx.db
            .query("supportMessages")
            .withIndex("by_ticketId", (q) => q.eq("ticketId", ticket._id))
            .order("desc")
            .first(),
        ]);

        return {
          ...ticket,
          description: sanitizeContent(ticket.description),
          user,
          latestMessage: latestMessage ? {
            ...latestMessage,
            content: sanitizeContent(latestMessage.content),
          } : null,
        };
      })
    );

    return ticketsWithDetails;
  },
});

// Assign a ticket to an admin
export const assignTicketToAdmin = mutation({
  args: {
    ticketId: v.id("supportTickets"),
    adminId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    // Check if target user is an admin
    const targetAdminRole = await ctx.db
      .query("adminRoles")
      .withIndex("by_userId", (q) => q.eq("userId", args.adminId))
      .first();

    if (!targetAdminRole || !targetAdminRole.isActive) {
      throw new Error("Target user is not an active admin");
    }

    const now = Date.now();

    // Update ticket assignment
    await ctx.db.patch(args.ticketId, {
      assignedAdminId: args.adminId,
      status: "in_progress",
      updatedAt: now,
    });

    // Add system message about assignment
    await ctx.db.insert("supportMessages", {
      ticketId: args.ticketId,
      senderId: user._id,
      senderType: "admin",
      content: sanitizeContent(`Ticket assigned to admin`),
      messageType: "system",
      isInternal: true,
      updatedAt: now,
    });

    return { success: true };
  },
});

// Update ticket status
export const updateTicketStatus = mutation({
  args: {
    ticketId: v.id("supportTickets"),
    status: v.union(
      v.literal("open"),
      v.literal("in_progress"),
      v.literal("waiting_for_customer"),
      v.literal("resolved"),
      v.literal("closed")
    ),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    // Check if user is an admin
    const adminRole = await ctx.db
      .query("adminRoles")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    // if (!adminRole || !adminRole.isActive) {
    //   throw new Error("Admin access required");
    // }

    const now = Date.now();
    const updateData: any = {
      status: args.status,
      updatedAt: now,
    };

    // Set resolved/closed timestamps
    if (args.status === "resolved") {
      updateData.resolvedAt = now;
    } else if (args.status === "closed") {
      updateData.closedAt = now;
    }

    await ctx.db.patch(args.ticketId, updateData);

    // Add system message about status change
    await ctx.db.insert("supportMessages", {
      ticketId: args.ticketId,
      senderId: user._id,
      senderType: "admin",
      content: sanitizeContent(`Ticket status updated to ${args.status}`),
      messageType: "system",
      isInternal: true,
      updatedAt: now,
    });

    return { success: true };
  },
});

// Get admin users
export const getAdminUsers = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    // Check if user is an admin
    const adminRole = await ctx.db
      .query("adminRoles")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    // if (!adminRole || !adminRole.isActive) {
    //   throw new Error("Admin access required");
    // }

    const adminRoles = await ctx.db
      .query("adminRoles")
      .withIndex("by_isActive", (q) => q.eq("isActive", true))
      .collect();

    // Get user details for each admin
    const adminUsers = await Promise.all(
      adminRoles.map(async (role) => {
        const user = await ctx.db.get(role.userId);
        return {
          ...role,
          user,
        };
      })
    );

    return adminUsers;
  },
});

// Create or update admin role
export const createAdminRole = mutation({
  args: {
    userId: v.id("users"),
    role: v.union(
      v.literal("super_admin"),
      v.literal("admin"),
      v.literal("support_agent"),
      v.literal("moderator")
    ),
    permissions: v.array(v.string()),
    assignedCategories: v.optional(v.array(v.string())),
    maxConcurrentTickets: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    // Check if user is a super admin
    const adminRole = await ctx.db
      .query("adminRoles")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    // if (!adminRole || adminRole.role !== "super_admin" || !adminRole.isActive) {
    //   throw new Error("Super admin access required");
    // }

    const now = Date.now();

    // Check if admin role already exists
    const existingRole = await ctx.db
      .query("adminRoles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    if (existingRole) {
      // Update existing role
      await ctx.db.patch(existingRole._id, {
        role: args.role,
        permissions: args.permissions,
        assignedCategories: args.assignedCategories,
        maxConcurrentTickets: args.maxConcurrentTickets,
        updatedAt: now,
      });
      return existingRole._id;
    } else {
      // Create new role
      return await ctx.db.insert("adminRoles", {
        userId: args.userId,
        role: args.role,
        permissions: args.permissions,
        isActive: true,
        assignedCategories: args.assignedCategories,
        maxConcurrentTickets: args.maxConcurrentTickets,
        updatedAt: now,
      });
    }
  },
});

// Get unassigned tickets count
export const getUnassignedTicketsCount = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    // Check if user is an admin
    const adminRole = await ctx.db
      .query("adminRoles")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    // if (!adminRole || !adminRole.isActive) {
    //   throw new Error("Admin access required");
    // }

    const unassignedTickets = await ctx.db
      .query("supportTickets")
      .withIndex("by_status", (q) => q.eq("status", "open"))
      .filter((q) => q.eq(q.field("assignedAdminId"), undefined))
      .collect();

    return unassignedTickets.length;
  },
});

// Get user's support tickets, sorted by creation time
export const getUserSupportItems = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    // Get all support tickets for the user
    const tickets = await ctx.db
      .query("supportTickets")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .order("desc")
      .collect();

    // Get the latest message for each ticket to show preview
    const ticketsWithLatestMessage = await Promise.all(
      tickets.map(async (ticket) => {
        const latestMessage = await ctx.db
          .query("supportMessages")
          .withIndex("by_ticketId", (q) => q.eq("ticketId", ticket._id))
          .order("desc")
          .first();

        return {
          _id: ticket._id,
          _creationTime: ticket.updatedAt,
          ticketNumber: ticket.ticketNumber,
          subject: ticket.subject,
          status: ticket.status,
          priority: ticket.priority,
          category: ticket.category,
          lastMessagePreview: latestMessage?.content || ticket.description,
          lastMessageAt: latestMessage?.updatedAt || ticket.updatedAt,
          unreadCount: 0, // You can implement unread count logic later
          ticket: ticket
        };
      })
    );

    // Sort by _creationTime (newest first) and return
    return ticketsWithLatestMessage.sort((a, b) => b._creationTime - a._creationTime);
  },
});

// Get messages for a specific support ticket
export const getTicketMessages = query({
  args: { ticketId: v.id("supportTickets") },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    // Check if user has access to this ticket
    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) throw new Error("Ticket not found");
    
    if (ticket.userId !== user._id) {
      throw new Error("Access denied");
    }

    // Get all messages for this ticket, sorted by creation time (oldest first)
    const messages = await ctx.db
      .query("supportMessages")
      .withIndex("by_ticketId", (q) => q.eq("ticketId", args.ticketId))
      .order("asc")
      .collect();

    return {
      ticket,
      messages
    };
  },
});
