import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { requireAuth } from "./lib/auth_utils";

/**
 * Generate upload URL for profile image
 */
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    // Require authentication
    await requireAuth(ctx);
    
    // Generate upload URL for profile images
    return await ctx.storage.generateUploadUrl();
  },
});

/**
 * Update user profile image
 */
export const updateProfileImage = mutation({
  args: {
    storageId: v.id("_storage"),
    userId: v.optional(v.id("users")), // Optional for admin use
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Determine target user
    const targetUserId = args.userId || currentUser._id;
    
    // Authorization check
    if (args.userId && args.userId !== currentUser._id) {
      // Only admins can update other users' profile images
      if (currentUser.userType !== "admin") {
        throw new ConvexError("Unauthorized: Only admins can update other users' profile images");
      }
    }

    // Get target user
    const targetUser = await ctx.db.get(targetUserId);
    if (!targetUser) {
      throw new ConvexError("User not found");
    }

    // Validate that the storage ID exists and get the file URL
    const imageUrl = await ctx.storage.getUrl(args.storageId);
    if (!imageUrl) {
      throw new ConvexError("Invalid file storage ID");
    }

    // Delete old profile image if it exists
    if (targetUser.profileImage) {
      try {
        // Extract storage ID from URL if it's a Convex storage URL
        const oldImageUrl = targetUser.profileImage;
        if (oldImageUrl.includes("convex.cloud")) {
          // This is a simplified approach - in production you might want to store the storage ID separately
          // For now, we'll just update the URL
        }
      } catch (error) {
        // Ignore errors when deleting old image
        console.warn("Failed to delete old profile image:", error);
      }
    }

    const now = Date.now();

    // Update user profile image
    await ctx.db.patch(targetUserId, {
      profileImage: imageUrl,
      updatedAt: now,
    });

    // Log profile image update
    await ctx.db.insert("analytics", {
      eventType: "profile_image_updated",
      userId: targetUserId,
      timestamp: now,
      metadata: {
        source: currentUser._id,
        category: "profile",
      },
    });

    return {
      success: true,
      message: "Profile image updated successfully",
      imageUrl,
    };
  },
});

/**
 * Remove user profile image
 */
export const removeProfileImage = mutation({
  args: {
    userId: v.optional(v.id("users")), // Optional for admin use
  },
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Determine target user
    const targetUserId = args.userId || currentUser._id;
    
    // Authorization check
    if (args.userId && args.userId !== currentUser._id) {
      // Only admins can remove other users' profile images
      if (currentUser.userType !== "admin") {
        throw new ConvexError("Unauthorized: Only admins can remove other users' profile images");
      }
    }

    // Get target user
    const targetUser = await ctx.db.get(targetUserId);
    if (!targetUser) {
      throw new ConvexError("User not found");
    }

    if (!targetUser.profileImage) {
      throw new ConvexError("User has no profile image to remove");
    }

    const now = Date.now();

    // Remove profile image
    await ctx.db.patch(targetUserId, {
      profileImage: undefined,
      updatedAt: now,
    });

    // Log profile image removal
    await ctx.db.insert("analytics", {
      eventType: "profile_image_removed",
      userId: targetUserId,
      timestamp: now,
      metadata: {
        source: currentUser._id,
        category: "profile",
      },
    });

    return {
      success: true,
      message: "Profile image removed successfully",
    };
  },
});

/**
 * Get profile image URL
 */
export const getProfileImageUrl = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      return null;
    }

    return user.profileImage || null;
  },
});

/**
 * Validate image file before upload
 */
export const validateImageFile = query({
  args: {
    fileSize: v.number(),
    fileType: v.string(),
  },
  handler: async (ctx, args) => {
    const errors: string[] = [];

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp", "image/gif"];
    if (!allowedTypes.includes(args.fileType)) {
      errors.push("Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed.");
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (args.fileSize > maxSize) {
      errors.push("File size too large. Maximum size is 5MB.");
    }

    // Validate minimum size (at least 1KB)
    const minSize = 1024; // 1KB
    if (args.fileSize < minSize) {
      errors.push("File size too small. Minimum size is 1KB.");
    }

    return {
      isValid: errors.length === 0,
      errors,
      maxSize,
      allowedTypes,
    };
  },
});

/**
 * Get user's upload statistics (for rate limiting)
 */
export const getUserUploadStats = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);
    
    const now = Date.now();
    const oneDayAgo = now - (24 * 60 * 60 * 1000);
    const oneHourAgo = now - (60 * 60 * 1000);

    // Get upload events from analytics
    const uploadEvents = await ctx.db
      .query("analytics")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("eventType"), "profile_image_updated"))
      .collect();

    const uploadsToday = uploadEvents.filter(event => event.timestamp >= oneDayAgo).length;
    const uploadsThisHour = uploadEvents.filter(event => event.timestamp >= oneHourAgo).length;

    // Define limits
    const dailyLimit = 10;
    const hourlyLimit = 3;

    return {
      uploadsToday,
      uploadsThisHour,
      dailyLimit,
      hourlyLimit,
      canUpload: uploadsToday < dailyLimit && uploadsThisHour < hourlyLimit,
      nextUploadAllowed: uploadsThisHour >= hourlyLimit 
        ? oneHourAgo + (60 * 60 * 1000) 
        : uploadsToday >= dailyLimit 
          ? oneDayAgo + (24 * 60 * 60 * 1000)
          : now,
    };
  },
});

/**
 * Clean up orphaned profile images (admin only)
 */
export const cleanupOrphanedImages = mutation({
  args: {
    dryRun: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Only admins can run cleanup
    const currentUser = await requireAuth(ctx);
    if (currentUser.userType !== "admin") {
      throw new ConvexError("Admin access required");
    }

    // This is a placeholder for cleanup logic
    // In a real implementation, you would:
    // 1. Get all storage files
    // 2. Check which ones are referenced by users
    // 3. Delete unreferenced files
    // 4. Return cleanup statistics

    const dryRun = args.dryRun ?? true;

    // For now, return a placeholder response
    return {
      success: true,
      message: dryRun ? "Dry run completed" : "Cleanup completed",
      dryRun,
      orphanedFiles: 0,
      deletedFiles: 0,
      spaceSaved: 0,
    };
  },
});
