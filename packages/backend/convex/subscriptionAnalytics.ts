import { v } from "convex/values";
import { query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { requireRole, requireAdmin } from "./lib/auth_utils";

/**
 * Get detailed subscription analytics with cohort analysis
 */
export const getSubscriptionCohortAnalysis = query({
  args: {
    cohortType: v.optional(v.union(v.literal("monthly"), v.literal("weekly"))),
    periodsBack: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const cohortType = args.cohortType || "monthly";
    const periodsBack = args.periodsBack || 12;
    
    const now = Date.now();
    const periodMs = cohortType === "monthly" ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000;

    // Get all users
    const allUsers = await ctx.db.query("users").collect();
    
    // Group users by cohort (signup period)
    const cohorts = new Map<string, any[]>();
    
    for (const user of allUsers) {
      const cohortPeriod = Math.floor((now - user._creationTime) / periodMs);
      if (cohortPeriod <= periodsBack) {
        const cohortKey = `period_${cohortPeriod}`;
        if (!cohorts.has(cohortKey)) {
          cohorts.set(cohortKey, []);
        }
        cohorts.get(cohortKey)!.push(user);
      }
    }

    // Calculate retention rates for each cohort
    const cohortAnalysis = [];
    
    for (const [cohortKey, cohortUsers] of cohorts) {
      const cohortSize = cohortUsers.length;
      const retentionData = [];

      // Calculate retention for each period after signup
      for (let period = 0; period <= periodsBack; period++) {
        const periodStart = now - (period * periodMs);
        const periodEnd = now - ((period - 1) * periodMs);

        const activeInPeriod = cohortUsers.filter(user => {
          return user.subscriptionStatus === "active" && 
                 user.subscriptionExpiresAt &&
                 user.subscriptionExpiresAt >= periodStart &&
                 user.subscriptionExpiresAt <= periodEnd;
        }).length;

        const retentionRate = cohortSize > 0 ? (activeInPeriod / cohortSize) * 100 : 0;
        
        retentionData.push({
          period,
          activeUsers: activeInPeriod,
          retentionRate: Math.round(retentionRate * 100) / 100,
        });
      }

      cohortAnalysis.push({
        cohort: cohortKey,
        cohortSize,
        retentionData,
        averageRetention: retentionData.length > 0 
          ? Math.round((retentionData.reduce((sum, r) => sum + r.retentionRate, 0) / retentionData.length) * 100) / 100
          : 0,
      });
    }

    return {
      cohortType,
      periodsBack,
      cohorts: cohortAnalysis,
      generatedAt: now,
      summary: {
        totalCohorts: cohortAnalysis.length,
        averageRetention: cohortAnalysis.length > 0
          ? Math.round((cohortAnalysis.reduce((sum, c) => sum + c.averageRetention, 0) / cohortAnalysis.length) * 100) / 100
          : 0,
        bestPerformingCohort: cohortAnalysis.reduce((best, current) => 
          current.averageRetention > best.averageRetention ? current : best, 
          cohortAnalysis[0] || { cohort: "none", averageRetention: 0 }
        ),
      },
    };
  },
});

/**
 * Get subscription revenue forecasting
 */
export const getSubscriptionRevenueForecast = query({
  args: {
    forecastMonths: v.optional(v.number()),
    includeScenarios: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const forecastMonths = args.forecastMonths || 12;
    const includeScenarios = args.includeScenarios ?? true;

    const now = Date.now();
    const monthMs = 30 * 24 * 60 * 60 * 1000;

    // Get current subscription data
    const allUsers = await ctx.db.query("users").collect();
    const activeSubscriptions = allUsers.filter(u => 
      u.subscriptionStatus === "active" && 
      (!u.subscriptionExpiresAt || u.subscriptionExpiresAt > now)
    );

    // Calculate current MRR
    const planValues = { basic: 29.99, premium: 59.99, enterprise: 149.99 };
    const currentMRR = activeSubscriptions.reduce((sum, user) => {
      return sum + (planValues[user.subscriptionPlan as keyof typeof planValues] || 0);
    }, 0);

    // Get historical growth data
    const threeMonthsAgo = now - (3 * monthMs);
    const recentEvents = await ctx.db
      .query("analytics")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", threeMonthsAgo))
      .filter((q) => q.or(
        q.eq(q.field("eventType"), "subscription_activated"),
        q.eq(q.field("eventType"), "subscription_cancelled"),
        q.eq(q.field("eventType"), "subscription_upgrade"),
        q.eq(q.field("eventType"), "subscription_downgrade")
      ))
      .collect();

    // Calculate monthly growth rate
    const monthlyGrowthRates = [];
    for (let i = 0; i < 3; i++) {
      const monthStart = now - ((i + 1) * monthMs);
      const monthEnd = now - (i * monthMs);
      
      const monthEvents = recentEvents.filter(e => 
        e.timestamp >= monthStart && e.timestamp < monthEnd
      );

      const newRevenue = monthEvents
        .filter(e => e.eventType === "subscription_activated" || e.eventType === "subscription_upgrade")
        .reduce((sum, e) => {
          const plan = e.metadata?.category;
          return sum + (planValues[plan as keyof typeof planValues] || 0);
        }, 0);

      const lostRevenue = monthEvents
        .filter(e => e.eventType === "subscription_cancelled" || e.eventType === "subscription_downgrade")
        .reduce((sum, e) => {
          const plan = e.metadata?.category;
          return sum + (planValues[plan as keyof typeof planValues] || 0);
        }, 0);

      const netGrowth = newRevenue - lostRevenue;
      const growthRate = currentMRR > 0 ? (netGrowth / currentMRR) * 100 : 0;
      monthlyGrowthRates.push(growthRate);
    }

    const averageGrowthRate = monthlyGrowthRates.length > 0 
      ? monthlyGrowthRates.reduce((sum, rate) => sum + rate, 0) / monthlyGrowthRates.length
      : 0;

    // Generate forecast
    const forecast = [];
    let projectedMRR = currentMRR;

    for (let month = 1; month <= forecastMonths; month++) {
      projectedMRR = projectedMRR * (1 + (averageGrowthRate / 100));
      
      forecast.push({
        month,
        date: new Date(now + (month * monthMs)).toISOString().slice(0, 7), // YYYY-MM format
        projectedMRR: Math.round(projectedMRR * 100) / 100,
        projectedARR: Math.round(projectedMRR * 12 * 100) / 100,
        cumulativeRevenue: Math.round(projectedMRR * month * 100) / 100,
      });
    }

    let scenarios = null;
    if (includeScenarios) {
      // Conservative scenario (50% of growth rate)
      const conservativeGrowthRate = averageGrowthRate * 0.5;
      let conservativeMRR = currentMRR;
      const conservativeForecast = [];

      // Optimistic scenario (150% of growth rate)
      const optimisticGrowthRate = averageGrowthRate * 1.5;
      let optimisticMRR = currentMRR;
      const optimisticForecast = [];

      for (let month = 1; month <= forecastMonths; month++) {
        conservativeMRR = conservativeMRR * (1 + (conservativeGrowthRate / 100));
        optimisticMRR = optimisticMRR * (1 + (optimisticGrowthRate / 100));

        conservativeForecast.push({
          month,
          projectedMRR: Math.round(conservativeMRR * 100) / 100,
          projectedARR: Math.round(conservativeMRR * 12 * 100) / 100,
        });

        optimisticForecast.push({
          month,
          projectedMRR: Math.round(optimisticMRR * 100) / 100,
          projectedARR: Math.round(optimisticMRR * 12 * 100) / 100,
        });
      }

      scenarios = {
        conservative: {
          growthRate: Math.round(conservativeGrowthRate * 100) / 100,
          forecast: conservativeForecast,
        },
        optimistic: {
          growthRate: Math.round(optimisticGrowthRate * 100) / 100,
          forecast: optimisticForecast,
        },
      };
    }

    return {
      currentMetrics: {
        currentMRR: Math.round(currentMRR * 100) / 100,
        currentARR: Math.round(currentMRR * 12 * 100) / 100,
        activeSubscriptions: activeSubscriptions.length,
        averageGrowthRate: Math.round(averageGrowthRate * 100) / 100,
      },
      forecast: {
        baseCase: {
          growthRate: Math.round(averageGrowthRate * 100) / 100,
          forecast,
        },
        scenarios,
      },
      assumptions: {
        basedOnMonths: monthlyGrowthRates.length,
        growthRateCalculation: "Average of last 3 months",
        excludesSeasonality: true,
        excludesMarketChanges: true,
      },
      generatedAt: now,
    };
  },
});

/**
 * Get subscription churn analysis with reasons
 */
export const getSubscriptionChurnAnalysis = query({
  args: {
    timeRange: v.optional(v.union(v.literal("30d"), v.literal("90d"), v.literal("1y"))),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const timeRange = args.timeRange || "90d";
    const now = Date.now();
    
    const timeRanges = {
      "30d": 30 * 24 * 60 * 60 * 1000,
      "90d": 90 * 24 * 60 * 60 * 1000,
      "1y": 365 * 24 * 60 * 60 * 1000,
    };
    
    const startTime = now - timeRanges[timeRange];

    // Get cancellation events
    const cancellationEvents = await ctx.db
      .query("analytics")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", startTime))
      .filter((q) => q.eq(q.field("eventType"), "subscription_cancelled"))
      .collect();

    // Get users who were active at the start of the period
    const allUsers = await ctx.db.query("users").collect();
    const activeAtStart = allUsers.filter(user => 
      user._creationTime < startTime && 
      user.subscriptionStatus === "active"
    ).length;

    // Analyze churn by plan
    const churnByPlan = {
      basic: 0,
      premium: 0,
      enterprise: 0,
    };

    // Analyze churn by user segment
    const churnBySegment = {
      newUsers: 0, // < 30 days
      establishedUsers: 0, // 30-180 days
      loyalUsers: 0, // > 180 days
    };

    // Analyze churn reasons (from metadata)
    const churnReasons = new Map<string, number>();

    for (const event of cancellationEvents) {
      const userId = event.userId;
      if (!userId) continue;

      const user = await ctx.db.get(userId);
      if (!user) continue;

      // Plan analysis
      const plan = event.metadata?.category;
      if (plan && churnByPlan.hasOwnProperty(plan)) {
        churnByPlan[plan as keyof typeof churnByPlan]++;
      }

      // Segment analysis
      const accountAge = event.timestamp - user._creationTime;
      const daysOld = accountAge / (24 * 60 * 60 * 1000);
      
      if (daysOld < 30) {
        churnBySegment.newUsers++;
      } else if (daysOld < 180) {
        churnBySegment.establishedUsers++;
      } else {
        churnBySegment.loyalUsers++;
      }

      // Reason analysis
      const reason = event.metadata?.category;
      churnReasons.set(reason || "not_specified", (churnReasons.get(reason || "not_specified") || 0) + 1);
    }

    // Calculate churn rates
    const totalChurned = cancellationEvents.length;
    const churnRate = activeAtStart > 0 ? (totalChurned / activeAtStart) * 100 : 0;

    // Convert churn reasons to array
    const churnReasonsArray = Array.from(churnReasons.entries())
      .map(([reason, count]) => ({
        reason,
        count,
        percentage: totalChurned > 0 ? Math.round((count / totalChurned) * 100 * 100) / 100 : 0,
      }))
      .sort((a, b) => b.count - a.count);

    // Calculate revenue impact
    const planValues = { basic: 29.99, premium: 59.99, enterprise: 149.99 };
    const revenueImpact = {
      monthly: Object.entries(churnByPlan).reduce((sum, [plan, count]) => {
        return sum + (count * planValues[plan as keyof typeof planValues]);
      }, 0),
      annual: Object.entries(churnByPlan).reduce((sum, [plan, count]) => {
        return sum + (count * planValues[plan as keyof typeof planValues] * 12);
      }, 0),
    };

    return {
      timeRange,
      summary: {
        totalChurned,
        activeAtStart,
        churnRate: Math.round(churnRate * 100) / 100,
        revenueImpact,
      },
      churnByPlan: Object.entries(churnByPlan).map(([plan, count]) => ({
        plan,
        count,
        percentage: totalChurned > 0 ? Math.round((count / totalChurned) * 100 * 100) / 100 : 0,
        revenueImpact: count * planValues[plan as keyof typeof planValues],
      })),
      churnBySegment: Object.entries(churnBySegment).map(([segment, count]) => ({
        segment,
        count,
        percentage: totalChurned > 0 ? Math.round((count / totalChurned) * 100 * 100) / 100 : 0,
      })),
      churnReasons: churnReasonsArray,
      recommendations: generateChurnRecommendations(churnReasonsArray, churnBySegment),
      generatedAt: now,
    };
  },
});

/**
 * Generate churn reduction recommendations
 */
function generateChurnRecommendations(
  churnReasons: Array<{ reason: string; count: number; percentage: number }>,
  churnBySegment: { newUsers: number; establishedUsers: number; loyalUsers: number }
) {
  const recommendations = [];

  // Analyze top churn reasons
  const topReason = churnReasons[0];
  if (topReason) {
    switch (topReason.reason) {
      case "price_too_high":
        recommendations.push({
          priority: "high",
          category: "pricing",
          message: "Price sensitivity is the top churn reason",
          actions: ["Consider introducing lower-tier plans", "Offer annual discounts", "Implement usage-based pricing"],
        });
        break;
      case "not_using_features":
        recommendations.push({
          priority: "high",
          category: "onboarding",
          message: "Users not utilizing features effectively",
          actions: ["Improve onboarding flow", "Add feature tutorials", "Implement progressive disclosure"],
        });
        break;
      case "found_alternative":
        recommendations.push({
          priority: "medium",
          category: "competitive",
          message: "Losing users to competitors",
          actions: ["Analyze competitor offerings", "Highlight unique value props", "Improve feature differentiation"],
        });
        break;
    }
  }

  // Analyze segment-specific issues
  const totalChurn = churnBySegment.newUsers + churnBySegment.establishedUsers + churnBySegment.loyalUsers;
  
  if (totalChurn > 0) {
    const newUserChurnRate = (churnBySegment.newUsers / totalChurn) * 100;
    
    if (newUserChurnRate > 50) {
      recommendations.push({
        priority: "high",
        category: "onboarding",
        message: "High churn rate among new users",
        actions: ["Redesign onboarding experience", "Add welcome series", "Provide early success milestones"],
      });
    }

    if (churnBySegment.loyalUsers > 0) {
      recommendations.push({
        priority: "medium",
        category: "retention",
        message: "Losing loyal customers",
        actions: ["Implement loyalty programs", "Offer exclusive features", "Provide dedicated support"],
      });
    }
  }

  return recommendations;
}
