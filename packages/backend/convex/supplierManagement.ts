import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create a new supplier
export const createSupplier = mutation({
  args: {
    supplierName: v.string(),
    supplierEmail: v.optional(v.string()),
    supplierPhone: v.optional(v.string()),
    supplierAddress: v.optional(v.string()),
    contactPerson: v.optional(v.string()),
    paymentTerms: v.optional(v.string()),
    creditLimit: v.optional(v.number()),
    notes: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user || user.userType !== "seller") {
      throw new Error("Seller access required");
    }

    const supplierId = await ctx.db.insert("suppliers", {
      sellerId: user._id,
      supplierName: args.supplierName,
      supplierEmail: args.supplierEmail,
      supplierPhone: args.supplierPhone,
      supplierAddress: args.supplierAddress,
      contactPerson: args.contactPerson,
      paymentTerms: args.paymentTerms,
      creditLimit: args.creditLimit,
      notes: args.notes,
      isActive: true,
      relationshipStartDate: Date.now(),
      tags: args.tags || [],
      updatedAt: Date.now(),
    });

    return supplierId;
  },
});

// Get all suppliers for a seller
export const getSuppliers = query({
  args: {
    sellerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Validate that the sellerId is actually a valid user ID
    const user = await ctx.db.get(args.sellerId);
    if (!user) {
      throw new Error(`User with ID ${args.sellerId} not found`);
    }
    if (user.userType !== "seller") {
      throw new Error(`User ${args.sellerId} is not a seller`);
    }

    const suppliers = await ctx.db
      .query("suppliers")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", args.sellerId))
      .order("desc")
      .collect();

    return suppliers;
  },
});

// Get supplier by ID
export const getSupplier = query({
  args: {
    supplierId: v.id("suppliers"),
  },
  handler: async (ctx, args) => {
    const supplier = await ctx.db.get(args.supplierId);
    if (!supplier) {
      throw new Error("Supplier not found");
    }
    return supplier;
  },
});

// Update supplier
export const updateSupplier = mutation({
  args: {
    supplierId: v.id("suppliers"),
    updates: v.object({
      supplierName: v.optional(v.string()),
      supplierEmail: v.optional(v.string()),
      supplierPhone: v.optional(v.string()),
      supplierAddress: v.optional(v.string()),
      contactPerson: v.optional(v.string()),
      paymentTerms: v.optional(v.string()),
      creditLimit: v.optional(v.number()),
      notes: v.optional(v.string()),
      isActive: v.optional(v.boolean()),
      tags: v.optional(v.array(v.string())),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user || user.userType !== "seller") {
      throw new Error("Seller access required");
    }

    const supplier = await ctx.db.get(args.supplierId);
    if (!supplier || supplier.sellerId !== user._id) {
      throw new Error("Supplier not found or access denied");
    }

    await ctx.db.patch(args.supplierId, {
      ...args.updates,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Delete supplier
export const deleteSupplier = mutation({
  args: {
    supplierId: v.id("suppliers"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user || user.userType !== "seller") {
      throw new Error("Seller access required");
    }

    const supplier = await ctx.db.get(args.supplierId);
    if (!supplier || supplier.sellerId !== user._id) {
      throw new Error("Supplier not found or access denied");
    }

    await ctx.db.delete(args.supplierId);
    return { success: true };
  },
});

// Create supplier transaction
export const createSupplierTransaction = mutation({
  args: {
    supplierId: v.id("suppliers"),
    transactionType: v.union(
      v.literal("purchase"),
      v.literal("payment"),
      v.literal("refund"),
      v.literal("consignment_fee"),
      v.literal("adjustment")
    ),
    amount: v.number(),
    currency: v.string(),
    transactionDate: v.number(),
    dueDate: v.optional(v.number()),
    paymentMethod: v.optional(v.string()),
    referenceNumber: v.optional(v.string()),
    description: v.string(),
    notes: v.optional(v.string()),
    productId: v.optional(v.id("products")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user || user.userType !== "seller") {
      throw new Error("Seller access required");
    }

    const supplier = await ctx.db.get(args.supplierId);
    if (!supplier || supplier.sellerId !== user._id) {
      throw new Error("Supplier not found or access denied");
    }

    const transactionId = await ctx.db.insert("supplierTransactions", {
      supplierId: args.supplierId,
      sellerId: user._id,
      transactionType: args.transactionType,
      amount: args.amount,
      currency: args.currency,
      transactionDate: args.transactionDate,
      dueDate: args.dueDate,
      status: "pending",
      paymentMethod: args.paymentMethod,
      referenceNumber: args.referenceNumber,
      description: args.description,
      notes: args.notes,
      productId: args.productId,
      updatedAt: Date.now(),
    });

    // Update supplier's last contact date
    await ctx.db.patch(args.supplierId, {
      lastContactDate: Date.now(),
      updatedAt: Date.now(),
    });

    return transactionId;
  },
});

// Get supplier transactions
export const getSupplierTransactions = query({
  args: {
    supplierId: v.id("suppliers"),
  },
  handler: async (ctx, args) => {
    const transactions = await ctx.db
      .query("supplierTransactions")
      .withIndex("by_supplierId", (q) => q.eq("supplierId", args.supplierId))
      .order("desc")
      .collect();

    return transactions;
  },
});

// Get supplier analytics
export const getSupplierAnalytics = query({
  args: {
    sellerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Validate that the sellerId is actually a valid user ID
    const user = await ctx.db.get(args.sellerId);
    if (!user) {
      throw new Error(`User with ID ${args.sellerId} not found`);
    }
    if (user.userType !== "seller") {
      throw new Error(`User ${args.sellerId} is not a seller`);
    }

    const suppliers = await ctx.db
      .query("suppliers")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", args.sellerId))
      .collect();

    const analytics = await Promise.all(
      suppliers.map(async (supplier) => {
        const transactions = await ctx.db
          .query("supplierTransactions")
          .withIndex("by_supplierId", (q) => q.eq("supplierId", supplier._id))
          .collect();

        const products = await ctx.db
          .query("products")
          .withIndex("by_sellerId", (q) => q.eq("sellerId", args.sellerId))
          .filter((q) => 
            q.and(
              q.eq(q.field("sourceInfo.source"), supplier.supplierName),
              q.neq(q.field("status"), "sold")
            )
          )
          .collect();

        const totalSpent = transactions
          .filter(t => t.transactionType === "purchase" && t.status === "completed")
          .reduce((sum, t) => sum + t.amount, 0);

        const totalPaid = transactions
          .filter(t => t.transactionType === "payment" && t.status === "completed")
          .reduce((sum, t) => sum + t.amount, 0);

        const outstandingBalance = totalSpent - totalPaid;

        const pendingTransactions = transactions.filter(t => t.status === "pending");
        const overdueTransactions = transactions.filter(t => 
          t.status === "pending" && t.dueDate && t.dueDate < Date.now()
        );

        return {
          supplier,
          totalSpent,
          totalPaid,
          outstandingBalance,
          pendingTransactions: pendingTransactions.length,
          overdueTransactions: overdueTransactions.length,
          activeProducts: products.length,
          totalTransactions: transactions.length,
        };
      })
    );

    return analytics;
  },
});

// Get supplier summary for dashboard
export const getSupplierSummary = query({
  args: {
    sellerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Validate that the sellerId is actually a valid user ID
    const user = await ctx.db.get(args.sellerId);
    if (!user) {
      throw new Error(`User with ID ${args.sellerId} not found`);
    }
    if (user.userType !== "seller") {
      throw new Error(`User ${args.sellerId} is not a seller`);
    }

    const suppliers = await ctx.db
      .query("suppliers")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", args.sellerId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const allTransactions = await Promise.all(
      suppliers.map(async (supplier) => {
        return await ctx.db
          .query("supplierTransactions")
          .withIndex("by_sellerId", (q) => q.eq("sellerId", args.sellerId))
          .filter((q) => q.eq(q.field("supplierId"), supplier._id))
          .collect();
      })
    );

    const flatTransactions = allTransactions.flat();

    const totalSpent = flatTransactions
      .filter(t => t.transactionType === "purchase" && t.status === "completed")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalOutstanding = flatTransactions
      .filter(t => t.transactionType === "purchase" && t.status === "pending")
      .reduce((sum, t) => sum + t.amount, 0);

    const overdueAmount = flatTransactions
      .filter(t => 
        t.transactionType === "purchase" && 
        t.status === "pending" && 
        t.dueDate && 
        t.dueDate < Date.now()
      )
      .reduce((sum, t) => sum + t.amount, 0);

    return {
      totalSuppliers: suppliers.length,
      totalSpent,
      totalOutstanding,
      overdueAmount,
      activeSuppliers: suppliers.filter(s => s.isActive).length,
    };
  },
});
