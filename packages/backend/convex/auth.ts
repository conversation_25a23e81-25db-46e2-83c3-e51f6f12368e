import {
  BetterAuth,
  type AuthFunctions,
  type PublicAuthFunctions,
} from "@convex-dev/better-auth";
import { api, components, internal } from "./_generated/api";
import type { DataModel, Id } from "./_generated/dataModel";
import { query, mutation, internalMutation } from "./_generated/server";
import { v } from "convex/values";

const authFunctions: AuthFunctions = internal.auth;
const publicAuthFunctions: PublicAuthFunctions = api.auth;

export const betterAuthComponent = new BetterAuth(components.betterAuth, {
  authFunctions,
  publicAuthFunctions,
});

export const {
  createUser,
  updateUser,
  deleteUser,
  createSession,
  isAuthenticated,
} = betterAuthComponent.createAuthFunctions<DataModel>({
  onCreateUser: async (ctx, user) => {
    const userId = await ctx.db.insert("users", {
      email: user.email,
      name: user.name,
      updatedAt: Date.now(),
      userType: "consumer",
      subscriptionStatus: "trial",
      subscriptionExpiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
      ...(user.image && { profileImage: user.image }),
      ...(user.phoneNumber && { phone: user.phoneNumber }),
      isVerified: user.emailVerified || false,
    });
    
    await ctx.db.insert("analytics", {
      eventType: "user_created",
      userId,
      timestamp: Date.now(),
      metadata: {
        signupMethod: "email",
        category: "consumer",
      },
    });
    
    return userId;
  },

  onUpdateUser: async (ctx, user) => {
    console.log("User updated in Better Auth, frontend will refresh session");
  },

  onDeleteUser: async (ctx, userId) => {
    const convexUserId = userId as Id<"users">;
    
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", convexUserId))
      .first();
    
    if (sellerProfile) {
      await ctx.db.delete(sellerProfile._id);

      const products = await ctx.db
        .query("products")
        .withIndex("by_sellerId", (q) => q.eq("sellerId", convexUserId))
        .collect();

      for (const product of products) {
        await ctx.db.delete(product._id);
      }
    }

    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_user_id", (q) => q.eq("userId", convexUserId))
      .collect();
    
    for (const favorite of favorites) {
      await ctx.db.delete(favorite._id);
    }

    await ctx.db.delete(convexUserId);
  },
});

// DEPRECATED: This mutation is no longer used - auth mismatches now clear auth state instead
export const createMissingUser = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    emailVerified: v.boolean(),
  },
  handler: async (ctx, args) => {
    // Get the current Better Auth user metadata to get the userId
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) {
      throw new Error("No authenticated Better Auth user found");
    }

    console.log("WARNING: createMissingUser called - this should be rare now");
    console.log("Creating user for email:", args.email, "Better Auth ID:", userMetadata.userId);

    // CRITICAL: Double-check if user already exists to prevent duplicates
    const existingUserByEmail = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (existingUserByEmail) {
      console.log("PREVENTED DUPLICATE: User already exists with email:", args.email);
      return existingUserByEmail._id;
    }

    // Also check if a user exists with the Better Auth userId
    const existingUserById = await ctx.db.get(userMetadata.userId as Id<"users">);
    if (existingUserById) {
      console.log("PREVENTED DUPLICATE: User already exists with Better Auth ID:", userMetadata.userId);
      return existingUserById._id;
    }

    // Only create if absolutely no user exists
    const userId = await ctx.db.insert("users", {
      email: args.email.toLowerCase().trim(),
      name: args.name.trim(),
      updatedAt: Date.now(),
      userType: "consumer",
      subscriptionStatus: "trial",
      subscriptionExpiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
      isVerified: args.emailVerified,
    });

    console.log("Created new user with ID:", userId, "for Better Auth user:", userMetadata.userId);

    // Log user creation event
    await ctx.db.insert("analytics", {
      eventType: "user_created",
      userId,
      timestamp: Date.now(),
      metadata: {
        signupMethod: "email",
        category: "consumer",
        source: `better_auth_sync_${userMetadata.userId.slice(-8)}`,
      },
    });

    return userId;
  },
});

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) return null;

    // First try to get user by Better Auth userId (this is the expected case)
    let user = await ctx.db.get(userMetadata.userId as Id<"users">);

    // If that fails, try to find user by email (fallback for existing users)
    if (!user && userMetadata.email) {
      user = await ctx.db
        .query("users")
        .withIndex("by_email", (q) => q.eq("email", userMetadata.email))
        .first();
      
    }

    if (!user) {
      console.log(`Better Auth user ${userMetadata.userId} (${userMetadata.email}) not found in Convex database`);
      
      return {
        needsCreation: true,
        betterAuthData: userMetadata,
        message: "Creating user account...",
      };
    }

    // User exists (either by ID or email) - return the user data
    let sellerProfile = null;
    if (user.userType === "seller") {
      sellerProfile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", user._id))
        .first();
    }

    return {
      ...user,
      ...userMetadata,
      sellerProfile: sellerProfile ? {
        ...sellerProfile,
        verificationStatus: sellerProfile.verificationStatus || "pending"
      } : null,
      isSubscriptionActive: user.subscriptionExpiresAt ? user.subscriptionExpiresAt > Date.now() : false,
    };
  },
});

export const updateUserProfile = mutation({
  args: {
    name: v.optional(v.string()),
    userType: v.optional(v.union(
      v.literal("consumer"),
      v.literal("seller"),
      v.literal("admin")
    )),
    onboardingCompleted: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user) throw new Error("User not found");

    // If user type is being updated, set appropriate subscription status
    let subscriptionUpdate = {};
    if (args.userType && args.userType !== user.userType) {
      if (args.userType === "consumer") {
        // Consumers start with inactive subscription - they must subscribe to access
        subscriptionUpdate = {
          subscriptionStatus: "inactive",
          subscriptionExpiresAt: undefined,
        };
      } else if (args.userType === "seller") {
        // Sellers get a trial period while their application is reviewed
        subscriptionUpdate = {
          subscriptionStatus: "trial",
          subscriptionExpiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
        };
      }
    }

    await ctx.db.patch(userId, {
      ...args,
      ...subscriptionUpdate,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const hasActiveSubscription = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) return false;

    const user = await ctx.db.get(userMetadata.userId as Id<"users">);
    if (!user) return false;

    // Allow trial users to have active subscription access
    if (user.subscriptionStatus === "trial") return true;
    
    // For active subscriptions, check expiration
    if (user.subscriptionStatus !== "active") return false;
    if (!user.subscriptionExpiresAt) return false;
    
    return user.subscriptionExpiresAt > Date.now();
  },
});

export const getSellerStatus = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) return null;

    const user = await ctx.db.get(userMetadata.userId as Id<"users">);
    if (!user || user.userType !== "seller") return null;

    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    const verificationStatus = sellerProfile?.verificationStatus || "pending";
    return {
      hasProfile: !!sellerProfile,
      verificationStatus,
      canSell: verificationStatus === "approved",
    };
  },
});

export const updateUserSubscription = mutation({
  args: {
    userId: v.id("users"),
    subscriptionStatus: v.union(
      v.literal("active"),
      v.literal("inactive"),
      v.literal("trial")
    ),
    subscriptionExpiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const adminUser = await ctx.db.get(userMetadata.userId as Id<"users">);
    if (!adminUser || adminUser.userType !== "admin") {
      throw new Error("Admin access required");
    }

    await ctx.db.patch(args.userId, {
      subscriptionStatus: args.subscriptionStatus,
      subscriptionExpiresAt: args.subscriptionExpiresAt,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const getUserPermissions = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) {
      return {
        canBuy: false,
        canSell: false,
        canManageUsers: false,
        canAccessAnalytics: false,
        canManageSubscriptions: false,
        requiresSubscription: true,
      };
    }

    const user = await ctx.db.get(userMetadata.userId as Id<"users">);
    if (!user) {
      return {
        canBuy: false,
        canSell: false,
        canManageUsers: false,
        canAccessAnalytics: false,
        canManageSubscriptions: false,
        requiresSubscription: true,
      };
    }

    const isSubscriptionActive = user.subscriptionStatus === "active" &&
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    const sellerProfile = user.userType === "seller"
      ? await ctx.db
          .query("sellerProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", user._id))
          .first()
      : null;

    return {
      canBuy: user.userType === "consumer" || user.userType === "seller",
      canSell: user.userType === "seller" &&
               isSubscriptionActive &&
               sellerProfile?.verificationStatus === "approved",
      canManageUsers: user.userType === "admin",
      canAccessAnalytics: user.userType === "admin" || user.userType === "seller",
      canManageSubscriptions: user.userType === "admin",
      requiresSubscription: user.userType === "seller" && !isSubscriptionActive,
    };
  },
});

export const completeOnboarding = mutation({
  args: {
    userType: v.union(v.literal("consumer"), v.literal("seller")),
    phone: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user) throw new Error("User not found");

    await ctx.db.patch(userId, {
      userType: args.userType,
      phone: args.phone,
      updatedAt: Date.now(),
    });

    // Log onboarding completion
    await ctx.db.insert("analytics", {
      eventType: "onboarding_complete",
      userId,
      timestamp: Date.now(),
      metadata: {
        category: args.userType,
      },
    });

    return { success: true };
  },
});

export const upgradeSubscription = mutation({
  args: {
    plan: v.union(v.literal("basic"), v.literal("premium"), v.literal("enterprise")),
    paymentIntentId: v.optional(v.string()),
  },
  handler: async (ctx, { plan, paymentIntentId }) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user) throw new Error("User not found");

    // Calculate expiration date (30 days from now)
    const expiresAt = Date.now() + (30 * 24 * 60 * 60 * 1000);

    await ctx.db.patch(userId, {
      subscriptionStatus: "active",
      subscriptionPlan: plan,
      subscriptionExpiresAt: expiresAt,
      updatedAt: Date.now(),
    });

    // Log the upgrade event
    await ctx.db.insert("analytics", {
      eventType: "subscription_upgrade",
      userId,
      timestamp: Date.now(),
      metadata: {
        category: plan,
        source: paymentIntentId,
      },
    });

    return { success: true, expiresAt };
  },
});

/**
 * Public mutation to fix orphaned Better Auth users
 * This can be called without authentication to resolve sync issues
 */
export const fixOrphanedUser = mutation({
  args: {
    email: v.string(),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const email = args.email.toLowerCase().trim();
    const name = args.name.trim();

    console.log(`Attempting to fix orphaned user: ${email}`);

    // Check if user already exists in Convex
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();

    if (existingUser) {
      console.log(`User ${email} already exists in Convex database`);
      return {
        success: false,
        message: "User already exists in Convex database",
        userId: existingUser._id,
      };
    }

    try {
      // Create the missing Convex user record
      const userId = await ctx.db.insert("users", {
        email: email,
        name: name,
        updatedAt: Date.now(),
        userType: "consumer",
        subscriptionStatus: "trial",
        subscriptionExpiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
        isVerified: false,
      });

      // Log the fix
      await ctx.db.insert("analytics", {
        eventType: "orphaned_user_fixed",
        userId,
        timestamp: Date.now(),
        metadata: {
          category: "auth_fix",
          source: `orphaned_user_${email.split('@')[0]}`,
        },
      });

      console.log(`Successfully created Convex user record for ${email} with ID: ${userId}`);

      return {
        success: true,
        message: `User record created for ${email}. Registration should now work.`,
        userId,
      };
    } catch (error) {
      console.error("Failed to create user record:", error);
      return {
        success: false,
        message: "Failed to create user record",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

/**
 * Handle signup conflicts by cleaning up orphaned Better Auth users
 * This runs when signup fails because user exists in Better Auth but not Convex
 */
export const handleSignupConflict = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const email = args.email.toLowerCase().trim();
    
    try {
      console.log(`Handling signup conflict for email: ${email}`);
      
      // Check if user exists in Convex database
      const convexUser = await ctx.db
        .query("users")
        .withIndex("by_email", (q) => q.eq("email", email))
        .first();
      
      if (convexUser) {
        // User exists in Convex, this is a legitimate "user already exists" error
        return {
          success: false,
          message: "User already exists in the system. Please sign in instead.",
          shouldSignIn: true,
        };
      }
      
      // User doesn't exist in Convex but exists in Better Auth (orphaned)
      // We need to find and delete the Better Auth user
      
      // Get the Better Auth user by email through the component
      try {
        // Since we can't directly query Better Auth users by email,
        // we'll use the fact that signup failed to indicate the user exists
        console.log(`Orphaned Better Auth user detected for ${email}, attempting cleanup`);
        
        // The user exists in Better Auth but not Convex, so we should clean it up
        // However, we need the Better Auth user ID to delete them
        // For now, let's return a signal that cleanup is needed
        return {
          success: true,
          message: "Orphaned Better Auth user detected. Attempting cleanup...",
          needsCleanup: true,
          email: email,
        };
      } catch (error) {
        console.error("Failed to handle Better Auth user:", error);
        return {
          success: false,
          message: "Failed to resolve signup conflict",
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
      
    } catch (error) {
      console.error("Failed to handle signup conflict:", error);
      return {
        success: false,
        message: "Failed to resolve signup conflict",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

/**
 * Clean up auth mismatch by deleting the Better Auth user
 * This handles cases where Better Auth user exists but Convex user doesn't
 */
export const cleanupAuthMismatch = mutation({
  args: {
    userId: v.string(), // Better Auth user ID
  },
  handler: async (ctx, args) => {
    try {
      console.log(`Cleaning up auth mismatch for Better Auth user: ${args.userId}`);
      
      // Instead of deleting the Better Auth user directly (which is complex),
      // we'll signal the frontend to clear all auth state and try again
      // This is a simpler and more reliable approach
      
      console.log(`Signaling auth state cleanup for Better Auth user: ${args.userId}`);
      
      return {
        success: true,
        message: `Auth state cleanup initiated for user ${args.userId}. Please try signing up again.`,
        shouldClearAuthState: true,
      };
    } catch (error) {
      console.error("Failed to cleanup auth mismatch:", error);
      return {
        success: false,
        message: "Failed to cleanup auth mismatch",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

/**
 * Clean up failed registration attempts
 * This handles cases where Better Auth created a session but Convex user creation failed
 */
export const cleanupFailedRegistration = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const email = args.email.toLowerCase().trim();
    
    console.log(`Cleaning up failed registration for: ${email}`);

    try {
      // Remove any incomplete Convex user records (unverified, recent)
      const incompleteUsers = await ctx.db
        .query("users")
        .withIndex("by_email", (q) => q.eq("email", email))
        .filter((q) => 
          q.and(
            q.eq(q.field("isVerified"), false),
            q.gt(q.field("_creationTime"), Date.now() - (5 * 60 * 1000)) // Created in last 5 minutes
          )
        )
        .collect();

      let cleanedUpCount = 0;
      for (const user of incompleteUsers) {
        await ctx.db.delete(user._id);
        cleanedUpCount++;
        console.log(`Deleted incomplete user record: ${user._id}`);
      }

      // Log the cleanup
      await ctx.db.insert("analytics", {
        eventType: "registration_cleanup",
        timestamp: Date.now(),
        metadata: {
          category: "auth_cleanup",
          source: `cleanup_${email.split('@')[0]}`,
        },
      });

      return {
        success: true,
        message: `Cleaned up ${cleanedUpCount} incomplete registration(s) for ${email}`,
        cleanedUpCount,
      };
    } catch (error) {
      console.error("Failed to cleanup registration:", error);
      return {
        success: false,
        message: "Failed to cleanup registration",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

/**
 * Mutation to create missing Convex user for existing Better Auth user
 */
export const createMissingConvexUser = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    betterAuthUserId: v.string(),
    emailVerified: v.boolean(),
  },
  handler: async (ctx, args) => {
    console.log(`Creating missing Convex user for Better Auth user: ${args.betterAuthUserId} (${args.email})`);
    
    // Double-check user doesn't exist
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
    
    if (existingUser) {
      console.log(`User ${args.email} already exists in Convex, skipping creation`);
      return existingUser._id;
    }
    
    // Create the missing Convex user
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      updatedAt: Date.now(),
      userType: "consumer",
      subscriptionStatus: "trial",
      subscriptionExpiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
      isVerified: args.emailVerified,
    });

    // Log user creation event
    await ctx.db.insert("analytics", {
      eventType: "user_created",
      userId,
      timestamp: Date.now(),
      metadata: {
        signupMethod: "sync_from_better_auth",
        category: "consumer",
        source: `better_auth_sync_${args.betterAuthUserId.slice(-8)}`,
      },
    });

    console.log(`Successfully created Convex user: ${userId} for Better Auth user: ${args.betterAuthUserId}`);
    return userId;
  },
});

