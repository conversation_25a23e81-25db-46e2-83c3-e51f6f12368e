import { v } from "convex/values";
import { query } from "./_generated/server";
import { requireAdmin } from "./lib/auth_utils";

/**
 * Get comprehensive platform metrics for admin dashboard
 */
export const getPlatformMetrics = query({
  args: {},
  handler: async (ctx) => {
    // Only admins can view platform metrics
    await requireAdmin(ctx);

    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = now - (60 * 24 * 60 * 60 * 1000);

    // Get all users
    const allUsers = await ctx.db.query("users").collect();
    
    // Get all seller profiles
    const allSellerProfiles = await ctx.db.query("sellerProfiles").collect();
    
    // Get all products
    const allProducts = await ctx.db.query("products").collect();
    
    // Get all orders
    const allOrders = await ctx.db.query("orders").collect();

    // Calculate basic user metrics
    const totalUsers = allUsers.length;
    const totalConsumers = allUsers.filter(u => u.userType === "consumer").length;
    const totalSellers = allUsers.filter(u => u.userType === "seller").length;
    
    // Calculate subscription metrics
    const activeSubscriptions = allUsers.filter(u => 
      u.subscriptionStatus === "active" && 
      (!u.subscriptionExpiresAt || u.subscriptionExpiresAt > now)
    ).length;

    // Calculate subscription revenue (monthly recurring revenue)
    const planValues = { basic: 29.99, premium: 59.99, enterprise: 149.99 };
    const planBreakdown = {
      basic: allUsers.filter(u => u.subscriptionPlan === "basic" && u.subscriptionStatus === "active").length,
      premium: allUsers.filter(u => u.subscriptionPlan === "premium" && u.subscriptionStatus === "active").length,
      enterprise: allUsers.filter(u => u.subscriptionPlan === "enterprise" && u.subscriptionStatus === "active").length,
    };

    const monthlyRecurringRevenue = 
      (planBreakdown.basic * planValues.basic) +
      (planBreakdown.premium * planValues.premium) +
      (planBreakdown.enterprise * planValues.enterprise);

    // Calculate platform revenue from completed orders (commission)
    const completedOrders = allOrders.filter(o => o.orderStatus === "delivered");
    const totalTransactionValue = completedOrders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
    const platformCommissionRevenue = completedOrders.reduce((sum, order) => sum + (order.platformFee || 0), 0);
    const totalPlatformRevenue = monthlyRecurringRevenue + platformCommissionRevenue;

    // Calculate listing metrics
    const totalListings = allProducts.length;
    const activeListings = allProducts.filter(p => p.status === "active").length;
    const soldListings = allProducts.filter(p => p.status === "sold").length;

    // Calculate pending applications
    const pendingApplications = allSellerProfiles.filter(p => p.verificationStatus === "pending").length;

    // Calculate growth metrics (comparing last 30 days vs previous 30 days)
    const currentPeriodUsers = allUsers.filter(u => u._creationTime >= thirtyDaysAgo);
    const previousPeriodUsers = allUsers.filter(u => 
      u._creationTime >= sixtyDaysAgo && u._creationTime < thirtyDaysAgo
    );

    const currentPeriodOrders = completedOrders.filter(o => o.orderDate >= thirtyDaysAgo);
    const previousPeriodOrders = completedOrders.filter(o => 
      o.orderDate >= sixtyDaysAgo && o.orderDate < thirtyDaysAgo
    );

    const currentPeriodListings = allProducts.filter(p => p._creationTime >= thirtyDaysAgo);
    const previousPeriodListings = allProducts.filter(p => 
      p._creationTime >= sixtyDaysAgo && p._creationTime < thirtyDaysAgo
    );

    const currentPeriodRevenue = currentPeriodOrders.reduce((sum, order) => sum + (order.platformFee || 0), 0);
    const previousPeriodRevenue = previousPeriodOrders.reduce((sum, order) => sum + (order.platformFee || 0), 0);

    // Calculate percentage growth
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const monthlyGrowth = {
      users: calculateGrowth(currentPeriodUsers.length, previousPeriodUsers.length),
      revenue: calculateGrowth(currentPeriodRevenue, previousPeriodRevenue),
      listings: calculateGrowth(currentPeriodListings.length, previousPeriodListings.length),
    };

    return {
      totalUsers,
      totalConsumers,
      totalSellers,
      activeSubscriptions,
      platformRevenue: Math.round(totalPlatformRevenue),
      totalListings,
      activeListings,
      soldListings,
      pendingApplications,
      monthlyGrowth: {
        users: Math.round(monthlyGrowth.users * 10) / 10, // Round to 1 decimal
        revenue: Math.round(monthlyGrowth.revenue * 10) / 10,
        listings: Math.round(monthlyGrowth.listings * 10) / 10,
      },
      subscriptionBreakdown: planBreakdown,
      monthlyRecurringRevenue: Math.round(monthlyRecurringRevenue),
      transactionMetrics: {
        totalOrders: completedOrders.length,
        totalTransactionValue: Math.round(totalTransactionValue),
        averageOrderValue: completedOrders.length > 0 ? Math.round(totalTransactionValue / completedOrders.length) : 0,
        platformCommissionRevenue: Math.round(platformCommissionRevenue),
      }
    };
  },
});

/**
 * Get recent platform activity for admin dashboard
 */
export const getRecentActivity = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Only admins can view platform activity
    await requireAdmin(ctx);

    const limit = Math.min(args.limit || 20, 50);

    // Get recent analytics events
    const recentEvents = await ctx.db
      .query("analytics")
      .order("desc")
      .take(limit * 2); // Get more to filter and format

    // Get recent user signups
    const recentUsers = await ctx.db
      .query("users")
      .order("desc")
      .take(10);

    // Get recent seller applications
    const recentApplications = await ctx.db
      .query("sellerProfiles")
      .order("desc")
      .take(10);

    // Get recent orders
    const recentOrders = await ctx.db
      .query("orders")
      .order("desc")
      .take(10);

    // Combine and format activities
    const activities: Array<{
      id: string;
      type: "user_signup" | "seller_application" | "subscription" | "listing" | "transaction";
      description: string;
      timestamp: number;
      status: "success" | "warning" | "error";
    }> = [];

    // Add user signups (last 5)
    recentUsers.slice(0, 5).forEach(user => {
      activities.push({
        id: `user_${user._id}`,
        type: "user_signup",
        description: `New ${user.userType} signed up: ${user.name || user.email}`,
        timestamp: user._creationTime,
        status: "success",
      });
    });

    // Add seller applications (last 3)
    recentApplications.slice(0, 3).forEach(app => {
      activities.push({
        id: `app_${app._id}`,
        type: "seller_application",
        description: `New seller application from ${app.businessName || "Unknown Business"}`,
        timestamp: app._creationTime,
        status: app.verificationStatus === "approved" ? "success" : 
               app.verificationStatus === "rejected" ? "error" : "warning",
      });
    });

    // Add recent high-value transactions (last 3)
    const highValueOrders = recentOrders
      .filter(order => (order.totalAmount || 0) > 1000)
      .slice(0, 3);
    
    highValueOrders.forEach(order => {
      activities.push({
        id: `order_${order._id}`,
        type: "transaction",
        description: `High-value transaction: $${Math.round(order.totalAmount || 0)} order completed`,
        timestamp: order.orderDate,
        status: order.orderStatus === "delivered" ? "success" : "warning",
      });
    });

    // Sort by timestamp and limit
    activities.sort((a, b) => b.timestamp - a.timestamp);
    
    return activities.slice(0, limit).map(activity => ({
      ...activity,
      timestamp: new Date(activity.timestamp).toISOString(),
    }));
  },
});

/**
 * Get system health metrics for admin dashboard
 */
export const getSystemHealth = query({
  args: {},
  handler: async (ctx) => {
    // Only admins can view system health
    await requireAdmin(ctx);

    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    // Get recent analytics events to check system activity
    const recentEvents = await ctx.db
      .query("analytics")
      .filter((q) => q.gte(q.field("timestamp"), oneHourAgo))
      .collect();

    // Get failed events (if any error tracking exists)
    const errorEvents = recentEvents.filter(e => 
      e.eventType.includes("error") || e.eventType.includes("failed")
    );

    // Get recent orders to check transaction health
    const recentOrders = await ctx.db
      .query("orders")
      .filter((q) => q.gte(q.field("orderDate"), oneDayAgo))
      .collect();

    const failedOrders = recentOrders.filter(o => o.orderStatus === "cancelled" || o.orderStatus === "refunded");

    // System health indicators
    const alerts = [];

    // Check for high error rate
    if (errorEvents.length > 10) {
      alerts.push({
        type: "error",
        title: "High Error Rate",
        description: `${errorEvents.length} errors detected in the last hour`,
        severity: "high",
      });
    }

    // Check for failed transactions
    if (failedOrders.length > 5) {
      alerts.push({
        type: "warning",
        title: "Transaction Issues",
        description: `${failedOrders.length} failed orders in the last 24 hours`,
        severity: "medium",
      });
    }

    // Add success indicators
    if (alerts.length === 0) {
      alerts.push({
        type: "success",
        title: "System Healthy",
        description: "All systems operating normally",
        severity: "low",
      });
    }

    return {
      systemStatus: alerts.length === 0 || alerts.every(a => a.type === "success") ? "healthy" : "warning",
      alerts,
      metrics: {
        eventsLastHour: recentEvents.length,
        errorRate: recentEvents.length > 0 ? (errorEvents.length / recentEvents.length) * 100 : 0,
        ordersLast24h: recentOrders.length,
        failedOrderRate: recentOrders.length > 0 ? (failedOrders.length / recentOrders.length) * 100 : 0,
      }
    };
  },
});
