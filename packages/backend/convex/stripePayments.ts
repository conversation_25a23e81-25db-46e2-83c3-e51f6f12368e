import { v } from "convex/values";
import { action, mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAuth 
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Create payment intent with Stripe Connect transfer
 */
export const createPaymentIntent = action({
  args: {
    amount: v.number(),
    currency: v.optional(v.string()),
    connectedAccountId: v.string(),
    metadata: v.optional(v.record(v.string(), v.string())),
  },
  handler: async (ctx, args) => {
    const { createPaymentIntentWithTransfer } = await import("./lib/stripe");
    
    try {
      const result = await createPaymentIntentWithTransfer(
        args.amount,
        args.currency || 'usd',
        args.connectedAccountId,
        args.metadata || {}
      );

      return {
        success: true,
        clientSecret: result.paymentIntent.client_secret,
        paymentIntentId: result.paymentIntent.id,
        fees: result.fees,
      };
    } catch (error) {
      console.error("Error creating payment intent:", error);
      throw new ConvexError("Failed to create payment intent");
    }
  },
});

/**
 * Confirm payment and update order status
 */
export const confirmPayment = action({
  args: {
    paymentIntentId: v.string(),
    orderId: v.id("orders"),
  },
  handler: async (ctx, args) => {
    const stripe = (await import("./lib/stripe")).default;
    
    try {
      // Retrieve payment intent from Stripe
      const paymentIntent = await stripe.paymentIntents.retrieve(args.paymentIntentId);
      
      if (paymentIntent.status !== 'succeeded') {
        throw new ConvexError("Payment not successful");
      }

      // Update order with payment confirmation
      await ctx.runMutation(internal.stripePayments.updateOrderPayment, {
        orderId: args.orderId,
        paymentIntentId: args.paymentIntentId,
        stripePaymentIntentId: paymentIntent.id,
        stripeTransferId: paymentIntent.transfer_data?.destination || null,
        stripeApplicationFee: paymentIntent.application_fee_amount ? 
          paymentIntent.application_fee_amount / 100 : null,
        paidAmount: paymentIntent.amount / 100,
      });

      // Log the payment event
      await ctx.runMutation(internal.stripePayments.logPaymentEvent, {
        eventType: "payment_confirmed",
        paymentIntentId: args.paymentIntentId,
        orderId: args.orderId,
        amount: paymentIntent.amount / 100,
      });

      return {
        success: true,
        paymentIntentId: args.paymentIntentId,
        orderId: args.orderId,
        message: "Payment confirmed successfully",
      };
    } catch (error) {
      console.error("Error confirming payment:", error);
      throw new ConvexError("Failed to confirm payment");
    }
  },
});

/**
 * Process refund for an order
 */
export const processRefund = action({
  args: {
    orderId: v.id("orders"),
    amount: v.optional(v.number()),
    reason: v.optional(v.union(
      v.literal("duplicate"),
      v.literal("fraudulent"),
      v.literal("requested_by_customer")
    )),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get order details
    const order = await ctx.runQuery(internal.stripePayments.getOrder, {
      orderId: args.orderId,
    });

    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Verify user has permission to refund
    if (order.buyerId !== user._id && order.sellerId !== user._id) {
      // Check if user is admin
      const adminRole = await ctx.runQuery(internal.stripePayments.getAdminRole, {
        userId: user._id,
      });
      
      if (!adminRole) {
        throw new ConvexError("Unauthorized to process refund");
      }
    }

    if (!order.stripePaymentIntentId) {
      throw new ConvexError("No payment intent found for this order");
    }

    const { createRefund } = await import("./lib/stripe");
    
    try {
      const refund = await createRefund(
        order.stripePaymentIntentId,
        args.amount,
        args.reason || "requested_by_customer"
      );

      // Update order status
      await ctx.runMutation(internal.stripePayments.updateOrderRefund, {
        orderId: args.orderId,
        refundId: refund.id,
        refundAmount: refund.amount / 100,
        refundReason: args.reason || "requested_by_customer",
      });

      return {
        success: true,
        refundId: refund.id,
        refundAmount: refund.amount / 100,
        message: "Refund processed successfully",
      };
    } catch (error) {
      console.error("Error processing refund:", error);
      throw new ConvexError("Failed to process refund");
    }
  },
});

/**
 * Internal mutation to update order with payment details
 */
export const updateOrderPayment = mutation({
  args: {
    orderId: v.id("orders"),
    paymentIntentId: v.string(),
    stripePaymentIntentId: v.string(),
    stripeTransferId: v.union(v.string(), v.null()),
    stripeApplicationFee: v.union(v.number(), v.null()),
    paidAmount: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.patch(args.orderId, {
      orderStatus: "paid",
      paymentIntentId: args.paymentIntentId,
      stripePaymentIntentId: args.stripePaymentIntentId,
      stripeTransferId: args.stripeTransferId,
      stripeApplicationFee: args.stripeApplicationFee,
      paidDate: now,
      updatedAt: now,
    });

    return { success: true };
  },
});

/**
 * Internal mutation to update order with refund details
 */
export const updateOrderRefund = mutation({
  args: {
    orderId: v.id("orders"),
    refundId: v.string(),
    refundAmount: v.number(),
    refundReason: v.string(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.patch(args.orderId, {
      orderStatus: "refunded",
      updatedAt: now,
      // Note: You might want to add refund-specific fields to the schema
    });

    return { success: true };
  },
});

/**
 * Internal mutation to log payment events
 */
export const logPaymentEvent = mutation({
  args: {
    eventType: v.string(),
    paymentIntentId: v.string(),
    orderId: v.id("orders"),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("analytics", {
      eventType: args.eventType,
      timestamp: Date.now(),
      metadata: {
        paymentIntentId: args.paymentIntentId,
        orderId: args.orderId,
        revenue: args.amount,
        source: "stripe_payment",
      },
    });

    return { success: true };
  },
});

/**
 * Internal query to get order details
 */
export const getOrder = mutation({
  args: {
    orderId: v.id("orders"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.orderId);
  },
});

/**
 * Internal query to get admin role
 */
export const getAdminRole = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("adminRoles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();
  },
});

// Export internal functions
export const internal = {
  stripePayments: {
    updateOrderPayment,
    updateOrderRefund,
    logPaymentEvent,
    getOrder,
    getAdminRole,
  },
};
