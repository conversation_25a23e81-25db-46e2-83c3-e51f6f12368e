import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAuth, 
  requireAdmin 
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Add a review for a seller (only after verified transaction)
 */
export const addSellerReview = mutation({
  args: {
    sellerId: v.id("users"),
    orderId: v.id("orders"),
    rating: v.number(),
    review: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Validate rating
    if (args.rating < 1 || args.rating > 5) {
      throw new ConvexError("Rating must be between 1 and 5 stars");
    }

    // Validate review text
    if (args.review.trim().length < 10) {
      throw new ConvexError("Review must be at least 10 characters long");
    }

    if (args.review.trim().length > 1000) {
      throw new ConvexError("Review must be less than 1000 characters");
    }

    // Get the order to verify it's a valid transaction
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Verify the order belongs to the current user
    if (order.buyerId !== user._id) {
      throw new ConvexError("You can only review sellers for your own purchases");
    }

    // Verify the order is for the specified seller
    if (order.sellerId !== args.sellerId) {
      throw new ConvexError("Order does not match the specified seller");
    }

    // Verify the order is completed (delivered)
    if (order.orderStatus !== "delivered") {
      throw new ConvexError("You can only review sellers after the order is delivered");
    }

    // Check if user has already reviewed this seller for this order
    const existingReview = await ctx.db
      .query("reviews")
      .withIndex("by_orderId", (q) => q.eq("orderId", args.orderId))
      .first();

    if (existingReview) {
      throw new ConvexError("You have already reviewed this seller for this order");
    }

    // Get the product from the order
    const product = await ctx.db.get(order.productId);
    if (!product) {
      throw new ConvexError("Product not found");
    }

    const now = Date.now();

    // Create the review
    const reviewId = await ctx.db.insert("reviews", {
      userId: user._id,
      productId: order.productId,
      sellerId: args.sellerId,
      orderId: args.orderId,
      rating: args.rating,
      review: args.review.trim(),
      isVerifiedPurchase: true,
      reviewDate: now,
    });

    // Update seller profile rating
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.sellerId))
      .first();

    if (sellerProfile) {
      const currentRating = sellerProfile.rating || 0;
      const currentReviewCount = sellerProfile.reviewCount || 0;
      
      // Calculate new average rating
      const totalRatingPoints = currentRating * currentReviewCount;
      const newReviewCount = currentReviewCount + 1;
      const newRating = (totalRatingPoints + args.rating) / newReviewCount;

      await ctx.db.patch(sellerProfile._id, {
        rating: Math.round(newRating * 10) / 10, // Round to 1 decimal place
        reviewCount: newReviewCount,
        updatedAt: now,
      });
    }

    // Log the review creation
    await ctx.db.insert("analytics", {
      eventType: "seller_review_created",
      userId: user._id,
      timestamp: now,
      metadata: {
        reviewId: reviewId as Id<"reviews">,
        sellerId: args.sellerId,
        orderId: args.orderId,
        rating: args.rating || 0,
        category: "seller_reviews",
      },
    });

    return {
      success: true,
      reviewId,
      message: "Review submitted successfully",
      newRating: sellerProfile ? Math.round(((sellerProfile.rating || 0) * (sellerProfile.reviewCount || 0) + args.rating) / ((sellerProfile.reviewCount || 0) + 1) * 10) / 10 : args.rating,
      totalReviews: (sellerProfile?.reviewCount || 0) + 1,
    };
  },
});

/**
 * Get seller reviews with pagination and filtering
 */
export const getSellerReviews = query({
  args: {
    sellerId: v.id("users"),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    rating: v.optional(v.number()), // Filter by specific rating
    verifiedOnly: v.optional(v.boolean()), // Default to true
  },
  handler: async (ctx, args) => {
    const limit = Math.min(args.limit || 20, 100);
    const offset = args.offset || 0;
    const verifiedOnly = args.verifiedOnly !== false; // Default to true

    // Validate that the sellerId is actually a valid user
    try {
      const seller = await ctx.db.get(args.sellerId);
      if (!seller) {
        console.warn(`Invalid sellerId: ${args.sellerId} - seller not found`);
        return {
          reviews: [],
          pagination: {
            total: 0,
            limit,
            offset,
            hasMore: false,
            totalPages: 0,
          },
          ratingStats: {
            total: 0,
            average: 0,
            distribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
          },
          filters: {
            sellerId: args.sellerId,
            rating: args.rating,
            verifiedOnly,
          },
        };
      }
    } catch (error) {
      console.warn(`Error validating sellerId ${args.sellerId}:`, error);
      return {
        reviews: [],
        pagination: {
          total: 0,
          limit,
          offset,
          hasMore: false,
          totalPages: 0,
        },
        ratingStats: {
          total: 0,
          average: 0,
          distribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
        },
        filters: {
          sellerId: args.sellerId,
          rating: args.rating,
          verifiedOnly,
        },
      };
    }

    // Build query - use a safer approach to handle potentially corrupted data
    let allReviews: any[] = [];
    
    try {
      // First try the indexed query
      let reviewsQuery = ctx.db
        .query("reviews")
        .withIndex("by_sellerId", (q) => q.eq("sellerId", args.sellerId));

      // Filter by verified purchases only
      if (verifiedOnly) {
        reviewsQuery = reviewsQuery.filter((q) => q.eq(q.field("isVerifiedPurchase"), true));
      }

      // Filter by rating if specified
      if (args.rating) {
        reviewsQuery = reviewsQuery.filter((q) => q.eq(q.field("rating"), args.rating));
      }

      allReviews = await reviewsQuery.collect();
    } catch (error) {
      console.warn("Indexed query failed, falling back to manual filtering:", error);
      
      // Fallback: get all reviews and filter manually
      const allReviewsData = await ctx.db.query("reviews").collect();
      
      // Filter manually to avoid validation errors
      allReviews = allReviewsData.filter(review => {
        try {
          // Basic validation - check if sellerId looks like a valid ID
          if (!review.sellerId || typeof review.sellerId !== 'string' || review.sellerId.length < 20) {
            return false;
          }
          
          // Check if it matches the requested sellerId
          if (review.sellerId !== args.sellerId) {
            return false;
          }
          
          // Apply other filters
          if (verifiedOnly && !review.isVerifiedPurchase) {
            return false;
          }
          
          if (args.rating && review.rating !== args.rating) {
            return false;
          }
          
          return true;
        } catch (filterError) {
          console.warn("Error filtering review:", filterError);
          return false;
        }
      });
    }
    const total = allReviews.length;

    // Apply pagination
    const paginatedReviews = allReviews
      .sort((a, b) => b.reviewDate - a.reviewDate) // Sort by newest first
      .slice(offset, offset + limit);

    // Enrich reviews with user and product information
    const enrichedReviews = await Promise.all(
      paginatedReviews.map(async (review) => {
        const [user, product, order] = await Promise.all([
          ctx.db.get(review.userId),
          ctx.db.get(review.productId),
          ctx.db.get(review.orderId),
        ]) as [any, any, any];

        return {
          _id: review._id,
          rating: review.rating,
          review: review.review,
          reviewDate: review.reviewDate,
          isVerifiedPurchase: review.isVerifiedPurchase,
          orderNumber: order?.orderNumber || null,
          product: product ? {
            _id: product._id,
            title: product.title,
            brand: product.brand,
            images: product.images,
          } : null,
          user: user ? {
            _id: user._id,
            name: user.name,
            profileImage: user.profileImage,
          } : null,
        };
      })
    );

    // Calculate rating statistics
    const ratingStats = {
      total: allReviews.length,
      average: allReviews.length > 0 
        ? Math.round((allReviews.reduce((sum, r) => sum + r.rating, 0) / allReviews.length) * 10) / 10
        : 0,
      distribution: {
        5: allReviews.filter(r => r.rating === 5).length,
        4: allReviews.filter(r => r.rating === 4).length,
        3: allReviews.filter(r => r.rating === 3).length,
        2: allReviews.filter(r => r.rating === 2).length,
        1: allReviews.filter(r => r.rating === 1).length,
      },
    };

    return {
      reviews: enrichedReviews,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
        totalPages: Math.ceil(total / limit),
      },
      ratingStats,
      filters: {
        sellerId: args.sellerId,
        rating: args.rating,
        verifiedOnly,
      },
    };
  },
});

/**
 * Add a review for a product (only after verified transaction)
 */
export const addProductReview = mutation({
  args: {
    productId: v.id("products"),
    orderId: v.id("orders"),
    rating: v.number(),
    review: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Validate rating
    if (args.rating < 1 || args.rating > 5) {
      throw new ConvexError("Rating must be between 1 and 5 stars");
    }

    // Validate review text
    if (args.review.trim().length < 10) {
      throw new ConvexError("Review must be at least 10 characters long");
    }

    if (args.review.trim().length > 1000) {
      throw new ConvexError("Review must be less than 1000 characters");
    }

    // Get the order to verify it's a valid transaction
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Verify the order belongs to the current user
    if (order.buyerId !== user._id) {
      throw new ConvexError("You can only review products for your own purchases");
    }

    // Verify the order is for the specified product
    if (order.productId !== args.productId) {
      throw new ConvexError("Order does not match the specified product");
    }

    // Verify the order is completed (delivered)
    if (order.orderStatus !== "delivered") {
      throw new ConvexError("You can only review products after the order is delivered");
    }

    // Check if user has already reviewed this product for this order
    const existingReview = await ctx.db
      .query("reviews")
      .withIndex("by_orderId", (q) => q.eq("orderId", args.orderId))
      .filter((q) => q.eq(q.field("productId"), args.productId))
      .first();

    if (existingReview) {
      throw new ConvexError("You have already reviewed this product for this order");
    }

    const now = Date.now();

    // Create the review
    const reviewId = await ctx.db.insert("reviews", {
      userId: user._id,
      productId: args.productId,
      sellerId: order.sellerId,
      orderId: args.orderId,
      rating: args.rating,
      review: args.review.trim(),
      isVerifiedPurchase: true,
      reviewDate: now,
    });

    // Update product rating
    const product = await ctx.db.get(args.productId);
    if (product) {
      // Get all reviews for this product to calculate new average
      const allProductReviews = await ctx.db
        .query("reviews")
        .withIndex("by_productId", (q) => q.eq("productId", args.productId))
        .filter((q) => q.eq(q.field("isVerifiedPurchase"), true))
        .collect();

      const totalRating = allProductReviews.reduce((sum, r) => sum + r.rating, 0);
      const averageRating = allProductReviews.length > 0 ? totalRating / allProductReviews.length : 0;

      await ctx.db.patch(args.productId, {
        rating: Math.round(averageRating * 10) / 10,
        reviewCount: allProductReviews.length,
        updatedAt: now,
      });
    }

    // Log the review creation
    await ctx.db.insert("analytics", {
      eventType: "product_review_created",
      userId: user._id,
      timestamp: now,
      metadata: {
        reviewId: reviewId as Id<"reviews">,
        productId: args.productId,
        orderId: args.orderId,
        rating: args.rating,
        category: "product_reviews",
      },
    });

    return {
      success: true,
      reviewId,
      message: "Product review submitted successfully",
    };
  },
});

/**
 * Get product reviews with pagination and filtering
 */
export const getProductReviews = query({
  args: {
    productId: v.id("products"),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    rating: v.optional(v.number()),
    verifiedOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const limit = Math.min(args.limit || 20, 100);
    const offset = args.offset || 0;
    const verifiedOnly = args.verifiedOnly !== false;

    // Build query
    let reviewsQuery = ctx.db
      .query("reviews")
      .withIndex("by_productId", (q) => q.eq("productId", args.productId));

    // Filter by verified purchases only
    if (verifiedOnly) {
      reviewsQuery = reviewsQuery.filter((q) => q.eq(q.field("isVerifiedPurchase"), true));
    }

    // Filter by rating if specified
    if (args.rating) {
      reviewsQuery = reviewsQuery.filter((q) => q.eq(q.field("rating"), args.rating));
    }

    // Get total count for pagination
    const allReviews = await reviewsQuery.collect();
    const total = allReviews.length;

    // Apply pagination
    const paginatedReviews = allReviews
      .sort((a, b) => b.reviewDate - a.reviewDate)
      .slice(offset, offset + limit);

    // Enrich reviews with user and order information
    const enrichedReviews = await Promise.all(
      paginatedReviews.map(async (review) => {
        const [user, order] = await Promise.all([
          ctx.db.get(review.userId),
          ctx.db.get(review.orderId),
        ]);

        return {
          _id: review._id,
          rating: review.rating,
          review: review.review,
          reviewDate: review.reviewDate,
          isVerifiedPurchase: review.isVerifiedPurchase,
          orderNumber: order?.orderNumber,
          user: user ? {
            _id: user._id,
            name: user.name,
            profileImage: user.profileImage,
          } : null,
        };
      })
    );

    // Calculate rating statistics
    const ratingStats = {
      total: allReviews.length,
      average: allReviews.length > 0 
        ? Math.round((allReviews.reduce((sum, r) => sum + r.rating, 0) / allReviews.length) * 10) / 10
        : 0,
      distribution: {
        5: allReviews.filter(r => r.rating === 5).length,
        4: allReviews.filter(r => r.rating === 4).length,
        3: allReviews.filter(r => r.rating === 3).length,
        2: allReviews.filter(r => r.rating === 2).length,
        1: allReviews.filter(r => r.rating === 1).length,
      },
    };

    return {
      reviews: enrichedReviews,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
        totalPages: Math.ceil(total / limit),
      },
      ratingStats,
      filters: {
        productId: args.productId,
        rating: args.rating,
        verifiedOnly,
      },
    };
  },
});

/**
 * Get user's reviewable orders for both products and sellers
 */
export const getReviewableOrders = query({
  args: {
    sellerId: v.optional(v.id("users")),
    productId: v.optional(v.id("products")),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    
    // If no user is authenticated, return empty result
    if (!user) {
      return {
        orders: [],
        total: 0,
        productReviews: 0,
        sellerReviews: 0,
      };
    }

    // Get delivered orders for the user
    let ordersQuery = ctx.db
      .query("orders")
      .withIndex("by_buyerId", (q) => q.eq("buyerId", user._id))
      .filter((q) => q.eq(q.field("orderStatus"), "delivered"));

    // Filter by specific seller if provided
    if (args.sellerId) {
      ordersQuery = ordersQuery.filter((q) => q.eq(q.field("sellerId"), args.sellerId));
    }

    // Filter by specific product if provided
    if (args.productId) {
      ordersQuery = ordersQuery.filter((q) => q.eq(q.field("productId"), args.productId));
    }

    const orders = await ordersQuery.collect();

    // Check which orders already have reviews
    const reviewableOrders = await Promise.all(
      orders.map(async (order) => {
        const [existingProductReview, existingSellerReview] = await Promise.all([
          // Check for product review
          ctx.db
            .query("reviews")
            .withIndex("by_orderId", (q) => q.eq("orderId", order._id))
            .filter((q) => q.eq(q.field("productId"), order.productId))
            .first(),
          // Check for seller review
          ctx.db
            .query("reviews")
            .withIndex("by_orderId", (q) => q.eq("orderId", order._id))
            .filter((q) => q.eq(q.field("sellerId"), order.sellerId))
            .first(),
        ]);

        const product = await ctx.db.get(order.productId);
        const seller = await ctx.db.get(order.sellerId);

        return {
          _id: order._id,
          orderNumber: order.orderNumber,
          orderDate: order.orderDate,
          deliveredDate: order.deliveredDate,
          totalAmount: order.totalAmount,
          hasProductReview: !!existingProductReview,
          hasSellerReview: !!existingSellerReview,
          product: product ? {
            _id: product._id,
            title: product.title,
            brand: product.brand,
            images: product.images,
          } : null,
          seller: seller ? {
            _id: seller._id,
            name: seller.name,
            businessName: seller.name,
          } : null,
        };
      })
    );

    return {
      orders: reviewableOrders,
      total: reviewableOrders.length,
      productReviews: reviewableOrders.filter(order => order.hasProductReview).length,
      sellerReviews: reviewableOrders.filter(order => order.hasSellerReview).length,
    };
  },
});

/**
 * Update an existing review (only by the original reviewer)
 */
export const updateSellerReview = mutation({
  args: {
    reviewId: v.id("reviews"),
    rating: v.optional(v.number()),
    review: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get the existing review
    const existingReview = await ctx.db.get(args.reviewId);
    if (!existingReview) {
      throw new ConvexError("Review not found");
    }

    // Only the original reviewer can update their review
    if (existingReview.userId !== user._id) {
      throw new ConvexError("You can only update your own reviews");
    }

    // Validate rating if provided
    if (args.rating !== undefined && (args.rating < 1 || args.rating > 5)) {
      throw new ConvexError("Rating must be between 1 and 5 stars");
    }

    // Validate review text if provided
    if (args.review !== undefined) {
      if (args.review.trim().length < 10) {
        throw new ConvexError("Review must be at least 10 characters long");
      }
      if (args.review.trim().length > 1000) {
        throw new ConvexError("Review must be less than 1000 characters");
      }
    }

    const now = Date.now();
    const updateData: any = {
      updatedAt: now,
    };

    if (args.rating !== undefined) {
      updateData.rating = args.rating;
    }

    if (args.review !== undefined) {
      updateData.review = args.review.trim();
    }

    // Update the review
    await ctx.db.patch(args.reviewId, updateData);

    // If rating changed, update seller profile rating
    if (args.rating !== undefined) {
      const sellerProfile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", existingReview.sellerId))
        .first();

      if (sellerProfile) {
        // Get all reviews for this seller to recalculate average
        const allReviews = await ctx.db
          .query("reviews")
          .withIndex("by_sellerId", (q) => q.eq("sellerId", existingReview.sellerId))
          .collect();

        const totalRating = allReviews.reduce((sum, r) => sum + r.rating, 0);
        const newRating = allReviews.length > 0 ? totalRating / allReviews.length : 0;

        await ctx.db.patch(sellerProfile._id, {
          rating: Math.round(newRating * 10) / 10,
          updatedAt: now,
        });
      }
    }

    // Log the review update
    await ctx.db.insert("analytics", {
      eventType: "seller_review_updated",
      userId: user._id,
      timestamp: now,
      metadata: {
        reviewId: args.reviewId,
        sellerId: existingReview.sellerId,
        category: "seller_reviews",
      },
    });

    return {
      success: true,
      message: "Review updated successfully",
    };
  },
});

/**
 * Delete a review (only by the original reviewer or admin)
 */
export const deleteSellerReview = mutation({
  args: {
    reviewId: v.id("reviews"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get the existing review
    const existingReview = await ctx.db.get(args.reviewId);
    if (!existingReview) {
      throw new ConvexError("Review not found");
    }

    // Only the original reviewer or admin can delete reviews
    if (existingReview.userId !== user._id && user.userType !== "admin") {
      throw new ConvexError("You can only delete your own reviews");
    }

    const now = Date.now();

    // Delete the review
    await ctx.db.delete(args.reviewId);

    // Update seller profile rating
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", existingReview.sellerId))
      .first();

    if (sellerProfile) {
      // Get remaining reviews for this seller to recalculate average
      const remainingReviews = await ctx.db
        .query("reviews")
        .withIndex("by_sellerId", (q) => q.eq("sellerId", existingReview.sellerId))
        .collect();

      const newRating = remainingReviews.length > 0 
        ? remainingReviews.reduce((sum, r) => sum + r.rating, 0) / remainingReviews.length 
        : 0;

      await ctx.db.patch(sellerProfile._id, {
        rating: Math.round(newRating * 10) / 10,
        reviewCount: Math.max(0, (sellerProfile.reviewCount || 0) - 1),
        updatedAt: now,
      });
    }

    // Log the review deletion
    await ctx.db.insert("analytics", {
      eventType: "seller_review_deleted",
      userId: user._id,
      timestamp: now,
      metadata: {
        reviewId: args.reviewId,
        sellerId: existingReview.sellerId,
        category: "seller_reviews",
      },
    });

    return {
      success: true,
      message: "Review deleted successfully",
    };
  },
});

/**
 * Get reviews written by a specific user (reviews they've left on others)
 */
export const getUserReviews = query({
  args: {
    userId: v.optional(v.id("users")),
    email: v.optional(v.string()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    const offset = args.offset || 0;

    let targetUserId: Id<"users"> | null = null;

    // If userId is provided, validate it
    if (args.userId) {
      const user = await ctx.db.get(args.userId);
      if (user) {
        targetUserId = args.userId;
      }
    }

    // If no valid userId or email provided, return empty result
    if (!targetUserId && !args.email) {
      return {
        reviews: [],
        total: 0,
        hasMore: false,
      };
    }

    // If we have email but no userId, try to find user by email
    if (!targetUserId && args.email) {
      const user = await ctx.db
        .query("users")
        .withIndex("by_email", (q) => q.eq("email", args.email!))
        .first();
      
      if (user) {
        targetUserId = user._id;
      }
    }

    // If still no valid userId, return empty result
    if (!targetUserId) {
      return {
        reviews: [],
        total: 0,
        hasMore: false,
      };
    }

    // Get reviews written by this user
    const reviews = await ctx.db
      .query("reviews")
      .withIndex("by_userId", (q) => q.eq("userId", targetUserId!))
      .order("desc")
      .collect();

    // Get total count for pagination
    const total = reviews.length;

    // Apply pagination manually
    const paginatedReviews = reviews.slice(offset, offset + limit);

    // Enrich reviews with product and seller information
    const enrichedReviews = await Promise.all(
      paginatedReviews.map(async (review) => {
        const [product, seller, order] = await Promise.all([
          review.productId ? ctx.db.get(review.productId) : null,
          ctx.db.get(review.sellerId),
          ctx.db.get(review.orderId),
        ]);

        return {
          ...review,
          product,
          seller,
          order,
        };
      })
    );

    return {
      reviews: enrichedReviews,
      total,
      hasMore: offset + limit < total,
    };
  },
});
