import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAuth, 
  requireAdmin 
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Create Stripe Connect account for approved seller
 * This is called when a seller application is approved
 */
export const createConnectAccount = action({
  args: {
    userId: v.id("users"),
    email: v.string(),
    country: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Import Stripe here to avoid issues with server-side imports
    const { createConnectAccount: createStripeAccount } = await import("./lib/stripe");
    
    try {
      // Create Stripe Connect Express account
      const account = await createStripeAccount(args.email, args.country || 'US');
      
      // Store account information in database
      await ctx.runMutation(internal.stripeConnect.storeConnectAccount, {
        userId: args.userId,
        stripeAccountId: account.id,
        email: args.email,
      });

      return {
        success: true,
        accountId: account.id,
        message: "Stripe Connect account created successfully",
      };
    } catch (error) {
      console.error("Error creating Stripe Connect account:", error);
      throw new ConvexError("Failed to create Stripe Connect account");
    }
  },
});

/**
 * Internal mutation to store Stripe Connect account info
 */
export const storeConnectAccount = mutation({
  args: {
    userId: v.id("users"),
    stripeAccountId: v.string(),
    email: v.string(),
  },
  handler: async (ctx, args) => {
    // Update seller profile with Stripe account info
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    await ctx.db.patch(sellerProfile._id, {
      stripeConnectAccountId: args.stripeAccountId,
      stripeAccountStatus: "pending",
      stripeOnboardingComplete: false,
      stripeChargesEnabled: false,
      stripePayoutsEnabled: false,
      stripeDetailsSubmitted: false,
      updatedAt: Date.now(),
    });

    // Create onboarding tracking record
    await ctx.db.insert("stripeOnboarding", {
      userId: args.userId,
      stripeAccountId: args.stripeAccountId,
      status: "pending",
      refreshCount: 0,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

/**
 * Generate onboarding link for seller
 */
export const generateOnboardingLink = action({
  args: {
    userId: v.optional(v.id("users")),
    stripeAccountId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // For actions, we need to get the user differently since we don't have direct db access
    const userId = args.userId;
    if (!userId) {
      throw new ConvexError("User ID is required");
    }
    
    // Get seller profile from a mutation that can access the database
    const sellerProfile = await ctx.runMutation(getSellerProfileForAction, {
      userId,
    });

    if (!sellerProfile?.stripeConnectAccountId) {
      throw new ConvexError("Stripe Connect account not found");
    }

    const { createAccountLink, STRIPE_CONFIG } = await import("./lib/stripe");
    
    try {
      const refreshUrl = `${STRIPE_CONFIG.appUrl}/seller/onboarding/refresh`;
      const returnUrl = `${STRIPE_CONFIG.appUrl}/seller/onboarding/complete`;
      
      const accountLink = await createAccountLink(
        sellerProfile.stripeConnectAccountId,
        refreshUrl,
        returnUrl
      );

      // Update onboarding tracking
      await ctx.runMutation(internal.stripeConnect.updateOnboardingLink, {
        userId,
        onboardingUrl: accountLink.url,
        expiresAt: accountLink.expires_at * 1000, // Convert to milliseconds
      });

      return {
        success: true,
        url: accountLink.url,
        expiresAt: accountLink.expires_at,
      };
    } catch (error) {
      console.error("Error generating onboarding link:", error);
      throw new ConvexError("Failed to generate onboarding link");
    }
  },
});

/**
 * Internal query to get seller profile
 */
export const getSellerProfile = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();
  },
});

/**
 * Helper mutation to get seller profile for actions
 */
export const getSellerProfileForAction = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();
  },
});

/**
 * Internal mutation to update onboarding link
 */
export const updateOnboardingLink = mutation({
  args: {
    userId: v.id("users"),
    onboardingUrl: v.string(),
    expiresAt: v.number(),
  },
  handler: async (ctx, args) => {
    const onboarding = await ctx.db
      .query("stripeOnboarding")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    if (onboarding) {
      await ctx.db.patch(onboarding._id, {
        onboardingUrl: args.onboardingUrl,
        onboardingExpiresAt: args.expiresAt,
        status: "in_progress",
        refreshCount: onboarding.refreshCount + 1,
        lastRefreshAt: Date.now(),
        updatedAt: Date.now(),
      });
    }

    return { success: true };
  },
});

/**
 * Check and update Stripe account status
 */
export const updateAccountStatus = action({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const sellerProfile = await ctx.runQuery(internal.stripeConnect.getSellerProfile, {
      userId: args.userId,
    });

    if (!sellerProfile?.stripeConnectAccountId) {
      throw new ConvexError("Stripe Connect account not found");
    }

    const { getAccountStatus } = await import("./lib/stripe");
    
    try {
      const accountStatus = await getAccountStatus(sellerProfile.stripeConnectAccountId);
      
      // Update seller profile with current status
      await ctx.runMutation(internal.stripeConnect.updateSellerStripeStatus, {
        userId: args.userId,
        chargesEnabled: accountStatus.charges_enabled,
        payoutsEnabled: accountStatus.payouts_enabled,
        detailsSubmitted: accountStatus.details_submitted,
        requirements: accountStatus.requirements?.currently_due || [],
      });

      return {
        success: true,
        status: accountStatus,
      };
    } catch (error) {
      console.error("Error updating account status:", error);
      throw new ConvexError("Failed to update account status");
    }
  },
});

/**
 * Internal mutation to update seller Stripe status
 */
export const updateSellerStripeStatus = mutation({
  args: {
    userId: v.id("users"),
    chargesEnabled: v.boolean(),
    payoutsEnabled: v.boolean(),
    detailsSubmitted: v.boolean(),
    requirements: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    const isEnabled = args.chargesEnabled && args.payoutsEnabled && args.detailsSubmitted;
    const status = isEnabled ? "enabled" : args.requirements.length > 0 ? "restricted" : "pending";

    await ctx.db.patch(sellerProfile._id, {
      stripeAccountStatus: status,
      stripeOnboardingComplete: isEnabled,
      stripeChargesEnabled: args.chargesEnabled,
      stripePayoutsEnabled: args.payoutsEnabled,
      stripeDetailsSubmitted: args.detailsSubmitted,
      stripeRequirements: args.requirements,
      updatedAt: Date.now(),
    });

    // Update onboarding status if complete
    if (isEnabled) {
      const onboarding = await ctx.db
        .query("stripeOnboarding")
        .withIndex("by_userId", (q) => q.eq("userId", args.userId))
        .first();

      if (onboarding) {
        await ctx.db.patch(onboarding._id, {
          status: "completed",
          completedAt: Date.now(),
          updatedAt: Date.now(),
        });
      }
    }

    return { success: true };
  },
});

/**
 * Get seller's Stripe Connect status
 */
export const getConnectStatus = query({
  args: {
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return null;
    }

    const userId = args.userId || user._id;
    
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    if (!sellerProfile) {
      return null;
    }

    const onboarding = await ctx.db
      .query("stripeOnboarding")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    return {
      hasStripeAccount: !!sellerProfile.stripeConnectAccountId,
      accountId: sellerProfile.stripeConnectAccountId,
      status: sellerProfile.stripeAccountStatus,
      onboardingComplete: sellerProfile.stripeOnboardingComplete,
      chargesEnabled: sellerProfile.stripeChargesEnabled,
      payoutsEnabled: sellerProfile.stripePayoutsEnabled,
      detailsSubmitted: sellerProfile.stripeDetailsSubmitted,
      requirements: sellerProfile.stripeRequirements || [],
      onboarding: onboarding ? {
        status: onboarding.status,
        url: onboarding.onboardingUrl,
        expiresAt: onboarding.onboardingExpiresAt,
        refreshCount: onboarding.refreshCount,
      } : null,
    };
  },
});

// Export internal functions for use in other modules
export const internal = {
  stripeConnect: {
    storeConnectAccount,
    getSellerProfile,
    updateOnboardingLink,
    updateSellerStripeStatus,
  },
};
