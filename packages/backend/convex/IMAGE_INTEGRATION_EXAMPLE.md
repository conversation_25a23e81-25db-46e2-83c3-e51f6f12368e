# Image Integration Example

This file shows how to integrate the free stock images with your seeded products.

## Step-by-Step Process

### 1. Get the Free Stock Image URLs
```typescript
const imageData = await convex.query("getFreeStockImageUrls")();
console.log("Available images:", imageData.productImages);
```

### 2. Example Image URLs for Each Product

#### Chanel Classic Flap Bag
- **Primary**: https://images.unsplash.com/photo-1591561954557-26941169b49e?w=800&h=600&fit=crop&crop=center
- **Secondary**: https://images.unsplash.com/photo-1548036328-c9fa89d128fa?w=800&h=600&fit=crop&crop=center
- **Detail**: https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=800&h=600&fit=crop&crop=center

#### Rolex Submariner Date
- **Primary**: https://images.unsplash.com/photo-1523170335258-f5ed11844a49?w=800&h=600&fit=crop&crop=center
- **Secondary**: https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=800&h=600&fit=crop&crop=center
- **Detail**: https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=800&h=600&fit=crop&crop=center

#### Nike Air Jordan 1 Chicago
- **Primary**: https://images.unsplash.com/photo-1556906781-9a412961c28c?w=800&h=600&fit=crop&crop=center
- **Secondary**: https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center
- **Detail**: https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?w=800&h=600&fit=crop&crop=center

## Integration Options

### Option A: Direct URL Usage (Frontend Only)
If you want to use the images directly in your frontend without storing them in Convex:

```typescript
// In your React component
const productImages = {
  "Chanel Classic Flap Bag - Black Caviar": [
    "https://images.unsplash.com/photo-1591561954557-26941169b49e?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1548036328-c9fa89d128fa?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=800&h=600&fit=crop&crop=center"
  ]
  // ... other products
};

// Use in your component
const images = productImages[product.title] || [];
```

### Option B: Download and Upload to Convex Storage (Recommended)
For production use, download the images and upload them to Convex storage:

```typescript
// 1. Download image from URL
const downloadImage = async (url: string) => {
  const response = await fetch(url);
  const blob = await response.blob();
  return blob;
};

// 2. Upload to Convex storage
const uploadToConvex = async (blob: Blob, filename: string) => {
  const storageId = await convex.mutation("uploadImage")({
    file: blob,
    filename: filename
  });
  return storageId;
};

// 3. Update product with storage IDs
const updateProductImages = async (productId: string, storageIds: string[]) => {
  await convex.mutation("updateProductImages")({
    productId,
    images: storageIds,
    primaryImageId: storageIds[0]
  });
};
```

### Option C: Hybrid Approach
Store both URLs and storage IDs for flexibility:

```typescript
// Add a custom field to store image URLs alongside storage IDs
const productWithBoth = {
  ...product,
  images: storageIds, // Convex storage IDs
  imageUrls: freeStockUrls, // Free stock URLs as backup
};
```

## Image Specifications

All images are optimized for web use:
- **Dimensions**: 800x600 pixels
- **Format**: JPEG
- **Quality**: High quality with center crop
- **Source**: Unsplash (free for commercial use)
- **License**: Unsplash License (very permissive)

## Best Practices

1. **Always provide alt text** for accessibility
2. **Use lazy loading** for better performance
3. **Implement image fallbacks** in case URLs are unavailable
4. **Consider image compression** for production use
5. **Cache images** on the client side when possible

## Troubleshooting

- **Images not loading**: Check if URLs are accessible
- **Slow loading**: Consider using a CDN or image optimization service
- **Storage issues**: Ensure Convex storage is properly configured
- **Memory issues**: Implement proper image cleanup in your frontend

## Next Steps

1. Test the image URLs in your browser
2. Choose your integration approach
3. Implement the image display in your frontend
4. Consider image optimization for production
5. Set up proper error handling for missing images
