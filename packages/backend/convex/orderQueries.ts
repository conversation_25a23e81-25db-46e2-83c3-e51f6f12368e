import { v } from "convex/values";
import { query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { requireAuth } from "./lib/auth_utils";
import { calculateSellerRevenue } from "./lib/utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get buyer's purchase history with filtering and search
 */
export const getBuyerOrders = query({
  args: {
    buyerId: v.optional(v.id("users")),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled"),
      v.literal("disputed"),
      v.literal("all")
    )),
    searchQuery: v.optional(v.string()),
    dateRange: v.optional(v.object({
      startDate: v.number(),
      endDate: v.number(),
    })),
    sortBy: v.optional(v.union(
      v.literal("newest"),
      v.literal("oldest"),
      v.literal("amount_high"),
      v.literal("amount_low"),
      v.literal("status")
    )),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Determine target buyer
    const targetBuyerId = args.buyerId || user._id;
    
    // Authorization check
    if (args.buyerId && args.buyerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Unauthorized: Can only view own orders or admin access required");
    }

    const status = args.status || "all";
    const limit = Math.min(args.limit || 50, 200);
    const offset = args.offset || 0;
    const sortBy = args.sortBy || "newest";

    // Get orders based on status filter
    let orders;
    if (status === "all") {
      orders = await ctx.db
        .query("orders")
        .withIndex("by_buyerId", (q) => q.eq("buyerId", targetBuyerId))
        .collect();
    } else {
      orders = await ctx.db
        .query("orders")
        .withIndex("by_buyerId", (q) => q.eq("buyerId", targetBuyerId))
        .filter((q) => q.eq(q.field("orderStatus"), status))
        .collect();
    }

    // Apply date range filter
    if (args.dateRange) {
      orders = orders.filter(order => 
        order.orderDate >= args.dateRange!.startDate && 
        order.orderDate <= args.dateRange!.endDate
      );
    }

    // Apply search filter
    if (args.searchQuery) {
      const searchTerm = args.searchQuery.toLowerCase().trim();
      
      // Get product details for search
      const ordersWithProducts = await Promise.all(
        orders.map(async (order) => {
          const product = await ctx.db.get(order.productId);
          return { order, product };
        })
      );

      const filteredOrdersWithProducts = ordersWithProducts.filter(({ order, product }) => {
        const searchableText = [
          order.orderNumber,
          product?.title || "",
          product?.brand || "",
          product?.description || "",
        ].join(" ").toLowerCase();
        
        return searchableText.includes(searchTerm);
      });

      orders = filteredOrdersWithProducts.map(({ order }) => order);
    }

    // Sort orders
    orders.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return b.orderDate - a.orderDate;
        case "oldest":
          return a.orderDate - b.orderDate;
        case "amount_high":
          return b.totalAmount - a.totalAmount;
        case "amount_low":
          return a.totalAmount - b.totalAmount;
        case "status":
          return a.orderStatus.localeCompare(b.orderStatus);
        default:
          return b.orderDate - a.orderDate;
      }
    });

    // Apply pagination
    const paginatedOrders = orders.slice(offset, offset + limit);

    // Enrich with product and seller data
    const enrichedOrders = await Promise.all(
      paginatedOrders.map(async (order) => {
        const product = await ctx.db.get(order.productId);
        const seller = await ctx.db.get(order.sellerId);
        
        const sellerProfile = await ctx.db
          .query("sellerProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", order.sellerId))
          .first();

        // Convert product images from storage IDs to URLs
        let productImages: string[] = [];
        if (product?.images) {
          const imageUrls = await Promise.all(
            product.images
              .filter(imageId => imageId !== "")
              .map(async (imageId) => {
                const url = await ctx.storage.getUrl(imageId);
                return url;
              })
          );
          productImages = imageUrls.filter(url => url !== null) as string[];
        }

        // Get dispute info if order is disputed
        let dispute = null;
        if (order.orderStatus === "disputed") {
          dispute = await ctx.db
            .query("disputes")
            .withIndex("by_orderId", (q) => q.eq("orderId", order._id))
            .first();
        }

        // Get review if exists (reviews are linked by userId and productId, not orderId)
        const review = await ctx.db
          .query("reviews")
          .withIndex("by_userId", (q) => q.eq("userId", order.buyerId))
          .filter((q) => q.eq(q.field("productId"), order.productId))
          .first();

        return {
          _id: order._id,
          orderNumber: order.orderNumber,
          orderStatus: order.orderStatus,
          paymentMethod: order.paymentMethod,
          subtotal: order.subtotal,
          shippingCost: order.shippingCost,
          tax: order.tax,
          totalAmount: order.totalAmount,
          orderDate: order.orderDate,
          confirmedDate: order.confirmedDate,
          shippedDate: order.shippedDate,
          deliveredDate: order.deliveredDate,
          cancelledDate: order.cancelledDate,
          trackingNumber: order.trackingNumber,
          carrier: order.carrier,
          estimatedDelivery: order.estimatedDelivery,
          shippingAddress: order.shippingAddress,
          notes: order.notes,

          
          product: product ? {
            _id: product._id,
            title: product.title,
            brand: product.brand,
            condition: product.condition,
            images: productImages,
            category: product.category,
          } : null,
          
          seller: seller && sellerProfile ? {
            _id: seller._id,
            name: seller.name,
            businessName: sellerProfile.businessName,
            rating: sellerProfile.rating || 0,
            reviewCount: sellerProfile.reviewCount || 0,
            verificationStatus: sellerProfile.verificationStatus,
          } : null,
          
          dispute: dispute ? {
            _id: dispute._id,
            reason: dispute.disputeReason,
            status: dispute.disputeStatus,
            disputeDate: dispute.disputeDate,
          } : null,
          
          review: review ? {
            _id: review._id,
            rating: review.rating,
            review: review.review,
            reviewDate: review._creationTime,
          } : null,
        };
      })
    );

    // Calculate summary statistics
    const summary = {
      total: orders.length,
      byStatus: {
        pending: orders.filter(o => o.orderStatus === "pending").length,
        confirmed: orders.filter(o => o.orderStatus === "confirmed").length,
        shipped: orders.filter(o => o.orderStatus === "shipped").length,
        delivered: orders.filter(o => o.orderStatus === "delivered").length,
        cancelled: orders.filter(o => o.orderStatus === "cancelled").length,
        disputed: orders.filter(o => o.orderStatus === "disputed").length,
      },
      totalSpent: orders.reduce((sum, o) => sum + o.totalAmount, 0),
      averageOrderValue: orders.length > 0 
        ? orders.reduce((sum, o) => sum + o.totalAmount, 0) / orders.length 
        : 0,
    };

    return {
      orders: enrichedOrders,
      summary: {
        ...summary,
        totalSpent: Math.round(summary.totalSpent * 100) / 100,
        averageOrderValue: Math.round(summary.averageOrderValue * 100) / 100,
      },
      pagination: {
        total: orders.length,
        limit,
        offset,
        hasMore: offset + limit < orders.length,
      },
      filters: {
        buyerId: targetBuyerId,
        status,
        searchQuery: args.searchQuery,
        dateRange: args.dateRange,
        sortBy,
      },
    };
  },
});

/**
 * Get seller's sales history with buyer information and revenue
 */
export const getSellerOrders = query({
  args: {
    sellerId: v.optional(v.id("users")),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled"),
      v.literal("disputed"),
      v.literal("all")
    )),
    searchQuery: v.optional(v.string()),
    dateRange: v.optional(v.object({
      startDate: v.number(),
      endDate: v.number(),
    })),
    sortBy: v.optional(v.union(
      v.literal("newest"),
      v.literal("oldest"),
      v.literal("amount_high"),
      v.literal("amount_low"),
      v.literal("status"),
      v.literal("earnings")
    )),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    includeRevenue: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Determine target seller
    const targetSellerId = args.sellerId || user._id;
    
    // Authorization check
    if (args.sellerId && args.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Unauthorized: Can only view own orders or admin access required");
    }

    // Verify target is a seller
    const seller = await ctx.db.get(targetSellerId);
    if (!seller || seller.userType !== "seller") {
      throw new ConvexError("User is not a seller");
    }

    const status = args.status || "all";
    const limit = Math.min(args.limit || 50, 200);
    const offset = args.offset || 0;
    const sortBy = args.sortBy || "newest";
    const includeRevenue = args.includeRevenue ?? false;

    // Get orders based on status filter
    let orders;
    if (status === "all") {
      orders = await ctx.db
        .query("orders")
        .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
        .collect();
    } else {
      orders = await ctx.db
        .query("orders")
        .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
        .filter((q) => q.eq(q.field("orderStatus"), status))
        .collect();
    }

    // Apply date range filter
    if (args.dateRange) {
      orders = orders.filter(order => 
        order.orderDate >= args.dateRange!.startDate && 
        order.orderDate <= args.dateRange!.endDate
      );
    }

    // Apply search filter
    if (args.searchQuery) {
      const searchTerm = args.searchQuery.toLowerCase().trim();
      
      const ordersWithData = await Promise.all(
        orders.map(async (order) => {
          const product = await ctx.db.get(order.productId);
          const buyer = await ctx.db.get(order.buyerId);
          return { order, product, buyer };
        })
      );

      const filteredOrdersWithData = ordersWithData.filter(({ order, product, buyer }) => {
        const searchableText = [
          order.orderNumber,
          product?.title || "",
          product?.brand || "",
          buyer?.name || "",
          buyer?.email || "",
        ].join(" ").toLowerCase();
        
        return searchableText.includes(searchTerm);
      });

      orders = filteredOrdersWithData.map(({ order }) => order);
    }

    // Sort orders
    orders.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return b.orderDate - a.orderDate;
        case "oldest":
          return a.orderDate - b.orderDate;
        case "amount_high":
          return b.totalAmount - a.totalAmount;
        case "amount_low":
          return a.totalAmount - b.totalAmount;
        case "earnings":
          return (b.sellerEarnings || 0) - (a.sellerEarnings || 0);
        case "status":
          return a.orderStatus.localeCompare(b.orderStatus);
        default:
          return b.orderDate - a.orderDate;
      }
    });

    // Apply pagination
    const paginatedOrders = orders.slice(offset, offset + limit);

    // Enrich with product and buyer data
    const enrichedOrders = await Promise.all(
      paginatedOrders.map(async (order) => {
        const product = await ctx.db.get(order.productId);
        const buyer = await ctx.db.get(order.buyerId);
        
        // Convert product images from storage IDs to URLs (for seller orders)
        let sellerProductImages: string[] = [];
        if (product?.images) {
          const imageUrls = await Promise.all(
            product.images
              .filter(imageId => imageId !== "")
              .map(async (imageId) => {
                const url = await ctx.storage.getUrl(imageId);
                return url;
              })
          );
          sellerProductImages = imageUrls.filter(url => url !== null) as string[];
        }
        
        // Get dispute info if order is disputed
        let dispute = null;
        if (order.orderStatus === "disputed") {
          dispute = await ctx.db
            .query("disputes")
            .withIndex("by_orderId", (q) => q.eq("orderId", order._id))
            .first();
        }

        // Get review if exists (reviews are linked by userId and productId, not orderId)
        const review = await ctx.db
          .query("reviews")
          .withIndex("by_userId", (q) => q.eq("userId", order.buyerId))
          .filter((q) => q.eq(q.field("productId"), order.productId))
          .first();

        return {
          _id: order._id,
          orderNumber: order.orderNumber,
          orderStatus: order.orderStatus,
          paymentMethod: order.paymentMethod,
          subtotal: order.subtotal,
          shippingCost: order.shippingCost,
          tax: order.tax,
          totalAmount: order.totalAmount,

          sellerEarnings: order.sellerEarnings,
          orderDate: order.orderDate,
          confirmedDate: order.confirmedDate,
          shippedDate: order.shippedDate,
          deliveredDate: order.deliveredDate,
          cancelledDate: order.cancelledDate,
          trackingNumber: order.trackingNumber,
          carrier: order.carrier,
          estimatedDelivery: order.estimatedDelivery,
          notes: order.notes,

          
          product: product ? {
            _id: product._id,
            title: product.title,
            brand: product.brand,
            condition: product.condition,
            images: sellerProductImages,
            category: product.category,
          } : null,
          
          buyer: buyer ? {
            _id: buyer._id,
            name: buyer.name,
            email: buyer.email, // Seller can see buyer contact info
            // Don't include sensitive buyer data
          } : null,
          
          dispute: dispute ? {
            _id: dispute._id,
            reason: dispute.disputeReason,
            status: dispute.disputeStatus,
            disputeDate: dispute.disputeDate,
          } : null,
          
          review: review ? {
            _id: review._id,
            rating: review.rating,
            review: review.review,
            reviewDate: review._creationTime,
          } : null,
        };
      })
    );

    // Calculate revenue statistics
    const deliveredOrders = orders.filter(o => o.orderStatus === "delivered");
    const revenue = {
      totalRevenue: calculateSellerRevenue(deliveredOrders),
      totalCommission: deliveredOrders.reduce((sum, o) => sum + (o.platformFee || 0), 0),
      totalGross: deliveredOrders.reduce((sum, o) => sum + o.subtotal, 0),
      averageOrderValue: deliveredOrders.length > 0 
        ? deliveredOrders.reduce((sum, o) => sum + o.subtotal, 0) / deliveredOrders.length 
        : 0,
    };

    // Calculate summary statistics
    const summary = {
      total: orders.length,
      byStatus: {
        pending: orders.filter(o => o.orderStatus === "pending").length,
        confirmed: orders.filter(o => o.orderStatus === "confirmed").length,
        shipped: orders.filter(o => o.orderStatus === "shipped").length,
        delivered: orders.filter(o => o.orderStatus === "delivered").length,
        cancelled: orders.filter(o => o.orderStatus === "cancelled").length,
        disputed: orders.filter(o => o.orderStatus === "disputed").length,
      },
      revenue: includeRevenue ? {
        ...revenue,
        totalRevenue: Math.round(revenue.totalRevenue * 100) / 100,
        totalCommission: Math.round(revenue.totalCommission * 100) / 100,
        totalGross: Math.round(revenue.totalGross * 100) / 100,
        averageOrderValue: Math.round(revenue.averageOrderValue * 100) / 100,
      } : undefined,
    };

    return {
      orders: enrichedOrders,
      summary,
      pagination: {
        total: orders.length,
        limit,
        offset,
        hasMore: offset + limit < orders.length,
      },
      filters: {
        sellerId: targetSellerId,
        status,
        searchQuery: args.searchQuery,
        dateRange: args.dateRange,
        sortBy,
        includeRevenue,
      },
    };
  },
});

/**
 * Get detailed order information with authorization checks
 */
export const getOrderDetails = query({
  args: {
    orderId: v.id("orders"),
    includePaymentInfo: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get order
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new ConvexError("Order not found");
    }

    // Authorization check - buyer, seller, or admin can view
    if (order.buyerId !== user._id && order.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Unauthorized to view this order");
    }

    // Determine what data to include based on authorization
    const isBuyer = order.buyerId === user._id;
    const isSeller = order.sellerId === user._id;
    const isAdmin = user.userType === "admin";
    const includePaymentInfo = args.includePaymentInfo && (isBuyer || isAdmin);

    // Get related data
    const product = await ctx.db.get(order.productId);
    const buyer = await ctx.db.get(order.buyerId);
    const seller = await ctx.db.get(order.sellerId);

    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", order.sellerId))
      .first();

    // Convert product images from storage IDs to URLs (for order details)
    let orderProductImages: string[] = [];
    if (product?.images) {
      const imageUrls = await Promise.all(
        product.images
          .filter(imageId => imageId !== "")
          .map(async (imageId) => {
            const url = await ctx.storage.getUrl(imageId);
            return url;
          })
      );
      orderProductImages = imageUrls.filter(url => url !== null) as string[];
    }

    // Get dispute info if order is disputed
    let dispute = null;
    if (order.orderStatus === "disputed") {
      dispute = await ctx.db
        .query("disputes")
        .withIndex("by_orderId", (q) => q.eq("orderId", order._id))
        .first();

      // Get dispute evidence if user is involved or admin
      if (dispute && (isBuyer || isSeller || isAdmin)) {
        // Convert evidence storage IDs to URLs
        const evidenceUrls = await Promise.all(
          (dispute.disputeDocuments || []).map(async (storageId) => {
            const url = await ctx.storage.getUrl(storageId);
            return url || "";
          })
        );
        dispute = { ...dispute, evidenceUrls };
      }
    }

    // Get review if exists
    const review = await ctx.db
      .query("reviews")
      .withIndex("by_userId", (q) => q.eq("userId", order.buyerId))
      .filter((q) => q.eq(q.field("productId"), order.productId))
      .first();

    // Get order analytics/tracking events
    const orderEvents = await ctx.db
      .query("analytics")
      .filter((q) => q.eq(q.field("metadata.source"), args.orderId))
      .collect();

    const timeline = orderEvents
      .filter(event => event.eventType.startsWith("order_"))
      .sort((a, b) => a.timestamp - b.timestamp)
      .map(event => ({
        eventType: event.eventType,
        timestamp: event.timestamp,
        metadata: event.metadata,
      }));

    // Build response with authorization-based data filtering
    const response = {
      _id: order._id,
      orderNumber: order.orderNumber,
      orderStatus: order.orderStatus,
      paymentMethod: order.paymentMethod,

      // Pricing information
      subtotal: order.subtotal,
      shippingCost: order.shippingCost,
      tax: order.tax,
      totalAmount: order.totalAmount,

      // Seller earnings (seller and admin only)
      platformFee: (isSeller || isAdmin) ? order.platformFee : undefined,
      sellerEarnings: (isSeller || isAdmin) ? order.sellerEarnings : undefined,

      // Dates and tracking
      orderDate: order.orderDate,
      confirmedDate: order.confirmedDate,
      shippedDate: order.shippedDate,
      deliveredDate: order.deliveredDate,
      cancelledDate: order.cancelledDate,
      trackingNumber: order.trackingNumber,
      carrier: order.carrier,
      estimatedDelivery: order.estimatedDelivery,
      // Addresses and notes
      shippingAddress: order.shippingAddress,
      notes: order.notes,
      cancellationReason: order.cancellationReason,

      // Payment information (buyer and admin only)
      paymentIntentId: includePaymentInfo ? order.paymentIntentId : undefined,

      // Product information
      product: product ? {
        _id: product._id,
        title: product.title,
        description: product.description,
        brand: product.brand,
        condition: product.condition,
        size: product.size,
        color: product.color,
        material: product.material,
        images: orderProductImages,
        category: product.category,
        tags: product.tags,
      } : null,

      // Buyer information (seller and admin can see contact info)
      buyer: buyer ? {
        _id: buyer._id,
        name: buyer.name,
        email: (isSeller || isAdmin) ? buyer.email : undefined,
        // Additional buyer info for sellers/admins
        subscriptionStatus: (isSeller || isAdmin) ? buyer.subscriptionStatus : undefined,
      } : null,

      // Seller information
      seller: seller && sellerProfile ? {
        _id: seller._id,
        name: seller.name,
        businessName: sellerProfile.businessName,
        rating: sellerProfile.rating || 0,
        reviewCount: sellerProfile.reviewCount || 0,
        verificationStatus: sellerProfile.verificationStatus,
        email: (isBuyer || isAdmin) ? seller.email : undefined,
      } : null,

      // Dispute information
      dispute: dispute ? {
        _id: dispute._id,
        reason: dispute.disputeReason,
        status: dispute.disputeStatus,
        disputeDate: dispute.disputeDate,
        // Full dispute details for involved parties  
        resolution: (isBuyer || isSeller || isAdmin) ? dispute.disputeResolution : undefined,
        notes: (isBuyer || isSeller || isAdmin) ? dispute.disputeNotes : undefined,
      } : null,

      // Review information
      review: review ? {
        _id: review._id,
        rating: review.rating,
        review: review.review,
        reviewDate: review._creationTime,
        isVerifiedPurchase: true, // All reviews from orders are verified purchases
      } : null,

      // Order timeline
      timeline,

      // User permissions
      userPermissions: {
        canCancel: isBuyer && ["pending", "confirmed"].includes(order.orderStatus),
        canConfirmDelivery: isBuyer && order.orderStatus === "shipped",
        canDispute: isBuyer && ["delivered"].includes(order.orderStatus) && !dispute,
        canShip: isSeller && order.orderStatus === "confirmed",
        canConfirm: isSeller && order.orderStatus === "pending",
        canViewPaymentInfo: includePaymentInfo,
        canViewSellerEarnings: isSeller || isAdmin,
      },
    };

    return response;
  },
});

/**
 * Get order metrics and analytics
 */
export const getOrderMetrics = query({
  args: {
    userId: v.optional(v.id("users")),
    userType: v.optional(v.union(v.literal("buyer"), v.literal("seller"))),
    timeRange: v.optional(v.union(
      v.literal("7d"),
      v.literal("30d"),
      v.literal("90d"),
      v.literal("1y"),
      v.literal("all")
    )),
    includeComparisons: v.optional(v.boolean()),
    includeBreakdowns: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Determine target user and type
    const targetUserId = args.userId || user._id;
    const userType = args.userType || (user.userType === "seller" ? "seller" : "buyer");

    // Authorization check
    if (args.userId && args.userId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Unauthorized: Can only view own metrics or admin access required");
    }

    const timeRange = args.timeRange || "30d";
    const includeComparisons = args.includeComparisons ?? false;
    const includeBreakdowns = args.includeBreakdowns ?? false;

    const now = Date.now();
    const timeRanges = {
      "7d": 7 * 24 * 60 * 60 * 1000,
      "30d": 30 * 24 * 60 * 60 * 1000,
      "90d": 90 * 24 * 60 * 60 * 1000,
      "1y": 365 * 24 * 60 * 60 * 1000,
    };

    const startTime = timeRange === "all" ? 0 : now - timeRanges[timeRange as keyof typeof timeRanges];

    // Get orders based on user type
    const indexName = userType === "seller" ? "by_sellerId" : "by_buyerId";
    const allOrders = await ctx.db
      .query("orders")
      .withIndex(indexName, (q) => q.eq(userType === "seller" ? "sellerId" : "buyerId", targetUserId))
      .collect();

    // Filter by time range
    const orders = timeRange === "all"
      ? allOrders
      : allOrders.filter(o => o.orderDate >= startTime);

    // Calculate basic metrics
    const totalOrders = orders.length;
    const deliveredOrders = orders.filter(o => o.orderStatus === "delivered");
    const cancelledOrders = orders.filter(o => o.orderStatus === "cancelled");
    const disputedOrders = orders.filter(o => o.orderStatus === "disputed");

    // Calculate financial metrics
    const totalValue = orders.reduce((sum, o) => sum + o.totalAmount, 0);
    const deliveredValue = deliveredOrders.reduce((sum, o) => sum + o.totalAmount, 0);
    const averageOrderValue = totalOrders > 0 ? totalValue / totalOrders : 0;

    // Seller-specific metrics
    let sellerMetrics = {};
    if (userType === "seller") {
      const totalEarnings = calculateSellerRevenue(deliveredOrders);
      const totalCommission = deliveredOrders.reduce((sum, o) => sum + (o.platformFee || 0), 0);

      sellerMetrics = {
        totalEarnings: Math.round(totalEarnings * 100) / 100,
        totalCommission: Math.round(totalCommission * 100) / 100,
        averageCommissionRate: deliveredOrders.length > 0
          ? deliveredOrders.reduce((sum, o) => sum + (o.platformFee || 0), 0) / deliveredOrders.length
          : 0,
        conversionRate: totalOrders > 0 ? deliveredOrders.length / totalOrders : 0,
      };
    }

    // Calculate comparison metrics if requested
    let comparisons = {};
    if (includeComparisons && timeRange !== "all") {
      const previousPeriodStart = startTime - (now - startTime);
      const previousOrders = allOrders.filter(o =>
        o.orderDate >= previousPeriodStart && o.orderDate < startTime
      );

      const previousDelivered = previousOrders.filter(o => o.orderStatus === "delivered");
      const previousValue = previousOrders.reduce((sum, o) => sum + o.totalAmount, 0);

      comparisons = {
        orderGrowth: previousOrders.length > 0
          ? ((totalOrders - previousOrders.length) / previousOrders.length) * 100
          : 0,
        valueGrowth: previousValue > 0
          ? ((totalValue - previousValue) / previousValue) * 100
          : 0,
        deliveryRateChange: previousOrders.length > 0 && totalOrders > 0
          ? ((deliveredOrders.length / totalOrders) - (previousDelivered.length / previousOrders.length)) * 100
          : 0,
      };
    }

    // Calculate breakdowns if requested
    let breakdowns = {};
    if (includeBreakdowns) {
      // Status breakdown
      const statusBreakdown = {
        pending: orders.filter(o => o.orderStatus === "pending").length,
        confirmed: orders.filter(o => o.orderStatus === "confirmed").length,
        shipped: orders.filter(o => o.orderStatus === "shipped").length,
        delivered: orders.filter(o => o.orderStatus === "delivered").length,
        cancelled: orders.filter(o => o.orderStatus === "cancelled").length,
        disputed: orders.filter(o => o.orderStatus === "disputed").length,
      };

      // Monthly breakdown for trend analysis
      const monthlyBreakdown = getMonthlyBreakdown(orders, startTime, now);

      // Category breakdown (get product categories)
      const categoryBreakdown = await getCategoryBreakdown(ctx, orders);

      breakdowns = {
        byStatus: statusBreakdown,
        byMonth: monthlyBreakdown,
        byCategory: categoryBreakdown,
      };
    }

    // Calculate customer insights for sellers
    let customerInsights = {};
    if (userType === "seller" && includeBreakdowns) {
      const uniqueBuyers = new Set(orders.map(o => o.buyerId)).size;
      const repeatCustomers = await getRepeatCustomers(ctx, targetUserId, orders);

      customerInsights = {
        uniqueCustomers: uniqueBuyers,
        repeatCustomers: repeatCustomers.length,
        repeatCustomerRate: uniqueBuyers > 0 ? repeatCustomers.length / uniqueBuyers : 0,
        averageOrdersPerCustomer: uniqueBuyers > 0 ? totalOrders / uniqueBuyers : 0,
      };
    }

    return {
      userId: targetUserId,
      userType,
      timeRange,
      generatedAt: now,

      metrics: {
        totalOrders,
        deliveredOrders: deliveredOrders.length,
        cancelledOrders: cancelledOrders.length,
        disputedOrders: disputedOrders.length,
        totalValue: Math.round(totalValue * 100) / 100,
        deliveredValue: Math.round(deliveredValue * 100) / 100,
        averageOrderValue: Math.round(averageOrderValue * 100) / 100,
        deliveryRate: totalOrders > 0 ? deliveredOrders.length / totalOrders : 0,
        cancellationRate: totalOrders > 0 ? cancelledOrders.length / totalOrders : 0,
        disputeRate: totalOrders > 0 ? disputedOrders.length / totalOrders : 0,
        ...sellerMetrics,
      },

      comparisons: includeComparisons ? {
        ...comparisons,
        orderGrowth: Math.round((comparisons as any).orderGrowth * 100) / 100,
        valueGrowth: Math.round((comparisons as any).valueGrowth * 100) / 100,
        deliveryRateChange: Math.round((comparisons as any).deliveryRateChange * 100) / 100,
      } : undefined,

      breakdowns: includeBreakdowns ? breakdowns : undefined,
      customerInsights: Object.keys(customerInsights).length > 0 ? customerInsights : undefined,
    };
  },
});

/**
 * Helper function to get monthly breakdown
 */
function getMonthlyBreakdown(orders: any[], startTime: number, endTime: number) {
  const months = [];
  const monthlyData = new Map();

  // Initialize months
  const start = new Date(startTime);
  const end = new Date(endTime);

  for (let d = new Date(start); d <= end; d.setMonth(d.getMonth() + 1)) {
    const monthKey = `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;
    monthlyData.set(monthKey, { orders: 0, value: 0, delivered: 0 });
  }

  // Aggregate orders by month
  orders.forEach(order => {
    const orderDate = new Date(order.orderDate);
    const monthKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;

    if (monthlyData.has(monthKey)) {
      const data = monthlyData.get(monthKey);
      data.orders += 1;
      data.value += order.totalAmount;
      if (order.orderStatus === "delivered") {
        data.delivered += 1;
      }
    }
  });

  return Array.from(monthlyData.entries()).map(([month, data]) => ({
    month,
    ...data,
    value: Math.round(data.value * 100) / 100,
  }));
}

/**
 * Helper function to get category breakdown
 */
async function getCategoryBreakdown(ctx: any, orders: any[]) {
  const categoryData = new Map();

  for (const order of orders) {
    const product = await ctx.db.get(order.productId);
    if (product) {
      const category = product.category;
      if (!categoryData.has(category)) {
        categoryData.set(category, { orders: 0, value: 0 });
      }
      const data = categoryData.get(category);
      data.orders += 1;
      data.value += order.totalAmount;
    }
  }

  return Array.from(categoryData.entries())
    .map(([category, data]) => ({
      category,
      orders: data.orders,
      value: Math.round(data.value * 100) / 100,
    }))
    .sort((a, b) => b.orders - a.orders);
}

/**
 * Helper function to get repeat customers
 */
async function getRepeatCustomers(ctx: any, sellerId: string, orders: any[]) {
  const buyerOrderCounts = new Map();

  orders.forEach(order => {
    const buyerId = order.buyerId;
    buyerOrderCounts.set(buyerId, (buyerOrderCounts.get(buyerId) || 0) + 1);
  });

  return Array.from(buyerOrderCounts.entries())
    .filter(([_, count]) => count > 1)
    .map(([buyerId, count]) => ({ buyerId, orderCount: count }));
}
