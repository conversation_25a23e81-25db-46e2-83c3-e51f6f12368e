# HauteVault Product Management System

This document describes the comprehensive product management system for HauteVault, including product creation, updates, publishing, removal, and offline sales management for the luxury marketplace.

## Overview

The product management system provides secure, validated workflows for sellers to manage their luxury items with proper authorization, image processing, inventory tracking, and sales management. It supports both online marketplace sales and offline transaction recording.

## Core Features

### ✅ Product Lifecycle Management
- **Draft creation** with comprehensive validation
- **Image upload and processing** with size/type validation
- **Publishing workflow** with completeness checks
- **Status management** (draft, active, sold, removed)
- **Offline sales recording** with invoice generation

### ✅ Seller Authorization & Limits
- **Seller verification** required for product creation
- **Subscription-based limits** (Basic: 50, Premium: 200, Enterprise: unlimited)
- **Active subscription validation** for all operations
- **Ownership verification** for updates and management

### ✅ Image Management
- **Secure upload URLs** with authentication
- **File validation** (max 5MB per image, 10 images per product)
- **Storage integration** with Convex file storage
- **Image replacement** and removal capabilities

### ✅ Administrative Tools
- **Bulk operations** for status updates
- **Product duplication** for similar items
- **Statistics and analytics** for seller insights
- **Admin override** capabilities for special cases

## Mutations Reference

### 1. createProduct

Create a new product listing with comprehensive validation and image processing.

```typescript
await ctx.runMutation(api.productManagement.createProduct, {
  title: "Chanel Classic Flap Bag - Medium",
  description: "Authentic Chanel Classic Flap Bag in black quilted lambskin...",
  price: 4500.00,
  categoryId: "category_handbags_id",
  condition: "like_new",
  brand: "Chanel",
  size: "Medium",
  color: "Black",
  material: "Lambskin Leather",
  yearPurchased: 2022,
  originalPrice: 6800.00,
  tags: ["luxury", "designer", "chanel", "classic"],
  images: ["storage_id_1", "storage_id_2", "storage_id_3"],
  shippingWeight: 1.2,
  dimensions: { length: 25.0, width: 15.5, height: 6.5 },
  isAuthentic: true,
  authenticityProof: "Purchased from Chanel boutique, includes authenticity card",
  privateNotes: "Stored in dust bag, rarely used",
});
```

**Features:**
- Validates seller authorization and subscription status
- Checks product limits based on subscription plan
- Validates all images exist in storage and meet size requirements
- Creates product in "draft" status for review before publishing
- Comprehensive field validation and sanitization
- Audit logging for product creation

### 2. updateProduct

Update product information with image management and validation.

```typescript
await ctx.runMutation(api.productManagement.updateProduct, {
  productId: "product_id_here",
  title: "Updated Product Title",
  price: 4200.00,
  newImages: ["storage_id_10", "storage_id_11"],
  removeImageIds: ["storage_id_1"],
  description: "Updated description with new details...",
  tags: ["luxury", "designer", "authenticated"],
});
```

**Features:**
- Ownership verification (seller or admin only)
- Prevents updates to sold or removed products
- Image addition and removal with validation
- Maintains minimum 1 image, maximum 10 images
- Comprehensive field validation
- Audit logging for all changes

### 3. publishProduct

Publish draft products to the marketplace with completeness validation.

```typescript
await ctx.runMutation(api.productManagement.publishProduct, {
  productId: "product_id_here",
});
```

**Features:**
- Validates product completeness before publishing
- Checks seller approval and subscription status
- Changes status from "draft" to "active"
- Sets publishedAt timestamp
- Audit logging for publication
- Future: Search index integration and follower notifications

### 4. removeProduct

Remove products from marketplace with order handling.

```typescript
await ctx.runMutation(api.productManagement.removeProduct, {
  productId: "product_id_here",
  reason: "Item no longer available",
  handleActiveOrders: false, // Admin can force removal
});
```

**Features:**
- Ownership verification required
- Prevents removal of products with active orders (unless admin override)
- Admin can force removal and cancel active orders
- Sets removal timestamp and reason
- Comprehensive audit logging
- Order cancellation with notifications

### 5. sellProductOffline

Record offline sales and remove from marketplace.

```typescript
await ctx.runMutation(api.productManagement.sellProductOffline, {
  productId: "product_id_here",
  buyerName: "Jane Smith",
  buyerEmail: "<EMAIL>",
  buyerPhone: "******-0123",
  salePrice: 4000.00,
  paymentMethod: "cash",
  notes: "Sold at local luxury consignment event",
  generateInvoice: true,
});
```

**Features:**
- Creates offline sale record with commission calculation
- Updates product status to "sold"
- Generates invoice if requested
- Validates no active online orders exist
- Updates seller profile statistics
- Comprehensive audit logging

## Image Management

### Upload Process

```typescript
// 1. Generate secure upload URL
const uploadUrl = await ctx.runMutation(api.productManagement.generateImageUploadUrl);

// 2. Upload file to Convex storage
const response = await fetch(uploadUrl, {
  method: "POST",
  headers: { "Content-Type": file.type },
  body: file,
});
const { storageId } = await response.json();

// 3. Validate uploaded image
const validation = await ctx.runQuery(api.productManagement.validateProductImage, {
  storageId,
});

// 4. Use in product creation/update
await ctx.runMutation(api.productManagement.createProduct, {
  // ... other fields
  images: [storageId],
});
```

### Image Validation Rules

- **File Size**: Maximum 5MB per image
- **File Count**: Minimum 1, maximum 10 images per product
- **File Types**: JPEG, PNG, WebP (validated client-side)
- **Storage Verification**: All images must exist in Convex storage
- **Authorization**: Only sellers can upload product images

## Administrative Features

### Bulk Operations

```typescript
// Bulk status updates (admin only)
await ctx.runMutation(api.productManagement.bulkUpdateProductStatus, {
  productIds: ["product_1", "product_2", "product_3"],
  status: "active", // "active" | "draft" | "removed"
  reason: "Bulk approval after review",
});
```

**Features:**
- Admin-only access with role validation
- Maximum 100 products per operation
- Individual result tracking for each product
- Comprehensive error handling and reporting
- Audit logging for all changes

### Product Duplication

```typescript
// Duplicate existing product
await ctx.runMutation(api.productManagement.duplicateProduct, {
  productId: "original_product_id",
  newTitle: "Chanel Classic Flap Bag - Medium (Black Caviar)",
});
```

**Features:**
- Ownership verification required
- Creates new product in "draft" status
- Copies all fields except status and timestamps
- Reuses same images (no storage duplication)
- Respects subscription limits

### Product Statistics

```typescript
// Get seller product statistics
const stats = await ctx.runQuery(api.productManagement.getProductStats, {
  sellerId: "seller_id", // Optional for self-lookup
  timeRange: "30d", // "7d" | "30d" | "90d" | "1y"
});
```

**Returns:**
- Product counts by status (active, draft, sold, removed)
- Performance metrics (views, likes, average price)
- Top categories and recent activity
- Time-range filtered statistics

## Authorization & Security

### Seller Requirements

```typescript
// All product operations require:
// 1. User must be authenticated
// 2. User type must be "seller"
// 3. Seller profile must exist and be approved
// 4. Seller account must be active
// 5. Active subscription required for creation/publishing
```

### Ownership Validation

```typescript
// Product updates/removal require:
// - Seller must own the product OR
// - User must be admin
if (product.sellerId !== user._id && user.userType !== "admin") {
  throw new ConvexError("Only the seller or admin can update this product");
}
```

### Subscription Limits

```typescript
const productLimits = {
  basic: 50,        // Basic plan: 50 products
  premium: 200,     // Premium plan: 200 products  
  enterprise: -1,   // Enterprise: unlimited
};
```

## Error Handling

### Common Error Scenarios

```typescript
try {
  await ctx.runMutation(api.productManagement.createProduct, productData);
} catch (error) {
  switch (error.message) {
    case "Only sellers can create products":
      // Handle non-seller user
      break;
    case "Seller must be approved to create products":
      // Handle unapproved seller
      break;
    case "Product limit reached. Upgrade subscription to add more products.":
      // Handle subscription limit
      break;
    case "At least one image is required":
      // Handle missing images
      break;
  }
}
```

### Validation Errors

- **Title**: Minimum 3 characters
- **Description**: Minimum 10 characters
- **Price**: Must be greater than 0
- **Images**: 1-10 images, max 5MB each
- **Category**: Must exist and be active
- **Email**: Valid format for offline sales

## Integration Examples

### React Product Creation Form

```typescript
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";

function ProductCreationForm() {
  const createProduct = useMutation(api.productManagement.createProduct);
  const generateUploadUrl = useMutation(api.productManagement.generateImageUploadUrl);
  
  const handleImageUpload = async (files: FileList) => {
    const uploadPromises = Array.from(files).map(async (file) => {
      const uploadUrl = await generateUploadUrl();
      const response = await fetch(uploadUrl, {
        method: "POST",
        headers: { "Content-Type": file.type },
        body: file,
      });
      const { storageId } = await response.json();
      return storageId;
    });
    
    return await Promise.all(uploadPromises);
  };
  
  const handleSubmit = async (formData: ProductFormData) => {
    try {
      const imageIds = await handleImageUpload(formData.images);
      const result = await createProduct({
        ...formData,
        images: imageIds,
      });
      showSuccess("Product created successfully!");
    } catch (error) {
      showError(error.message);
    }
  };
  
  return <ProductForm onSubmit={handleSubmit} />;
}
```

### Seller Dashboard Integration

```typescript
function SellerProductDashboard() {
  const stats = useQuery(api.productManagement.getProductStats, {
    timeRange: "30d",
  });
  
  const publishProduct = useMutation(api.productManagement.publishProduct);
  const removeProduct = useMutation(api.productManagement.removeProduct);
  
  return (
    <div>
      <ProductStats stats={stats} />
      <ProductList 
        onPublish={(id) => publishProduct({ productId: id })}
        onRemove={(id) => removeProduct({ productId: id })}
      />
    </div>
  );
}
```

## Best Practices

### 1. Image Optimization

```typescript
// Optimize images before upload
const optimizeImage = async (file: File) => {
  // Resize to max 1920x1920
  // Compress to reduce file size
  // Convert to WebP for better compression
  return optimizedFile;
};
```

### 2. Progressive Enhancement

```typescript
// Create as draft first, then publish
const productId = await createProduct({ ...data, status: "draft" });
await validateProductCompleteness(productId);
await publishProduct({ productId });
```

### 3. Error Recovery

```typescript
// Handle partial failures gracefully
const uploadImages = async (files: FileList) => {
  const results = await Promise.allSettled(
    Array.from(files).map(uploadSingleImage)
  );
  
  const successful = results
    .filter(r => r.status === "fulfilled")
    .map(r => r.value);
    
  if (successful.length === 0) {
    throw new Error("No images uploaded successfully");
  }
  
  return successful;
};
```

## File Structure

```
packages/backend/convex/
├── productManagement.ts              # Core product mutations
├── productManagement.test.ts         # Usage examples and tests
└── PRODUCT_MANAGEMENT.md             # This documentation
```

The product management system provides a comprehensive, secure foundation for managing luxury items in the HauteVault marketplace with proper validation, authorization, and audit trails.
