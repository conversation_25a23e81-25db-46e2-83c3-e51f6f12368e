import { mutation, query } from "./_generated/server";

export const seedCategories = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    const categories = [
      {
        name: "Clothing",
        slug: "clothing",
        description: "Designer clothing and apparel including tops, bottoms, outerwear, and more.",
        isActive: true,
        sortOrder: 1,
      },
      {
        name: "Sneakers",
        slug: "sneakers",
        description: "Luxury and designer sneakers for men and women.",
        isActive: true,
        sortOrder: 2,
      },
      {
        name: "Shoes",
        slug: "shoes",
        description: "Designer shoes including boots, loafers, heels, and sandals.",
        isActive: true,
        sortOrder: 3,
      },
      {
        name: "Handbags",
        slug: "handbags",
        description: "Luxury handbags, purses, totes, and clutches.",
        isActive: true,
        sortOrder: 4,
      },
      {
        name: "Accessories",
        slug: "accessories",
        description: "Designer accessories including belts, hats, scarves, and gloves.",
        isActive: true,
        sortOrder: 5,
      },
      {
        name: "Jewelry",
        slug: "jewelry",
        description: "Fine jewelry, luxury accessories, and statement pieces.",
        isActive: true,
        sortOrder: 6,
      },
      {
        name: "Watches",
        slug: "watches",
        description: "Luxury watches and timepieces.",
        isActive: true,
        sortOrder: 7,
      },
      {
        name: "Sunglasses",
        slug: "sunglasses",
        description: "Designer sunglasses and eyewear.",
        isActive: true,
        sortOrder: 8,
      },
      {
        name: "Collectibles",
        slug: "collectibles",
        description: "Rare and collectible items including art, memorabilia, and limited editions.",
        isActive: true,
        sortOrder: 9,
      },
      {
        name: "Home & Decor",
        slug: "home-decor",
        description: "Luxury home goods, decor, and lifestyle accessories.",
        isActive: true,
        sortOrder: 10,
      },
      {
        name: "Beauty",
        slug: "beauty",
        description: "High-end beauty products, skincare, and fragrances.",
        isActive: true,
        sortOrder: 11,
      },
      {
        name: "Tech & Gadgets",
        slug: "tech-gadgets",
        description: "Premium electronics, gadgets, and tech accessories.",
        isActive: true,
        sortOrder: 12,
      },
      {
        name: "Luggage & Travel",
        slug: "luggage-travel",
        description: "Designer luggage, travel bags, and travel accessories.",
        isActive: true,
        sortOrder: 13,
      },
      {
        name: "Kids",
        slug: "kids",
        description: "Luxury kidswear, shoes, and accessories.",
        isActive: true,
        sortOrder: 14,
      },
      {
        name: "Sports & Outdoors",
        slug: "sports-outdoors",
        description: "Premium sports equipment, activewear, and outdoor gear.",
        isActive: true,
        sortOrder: 15,
      },
      {
        name: "Art",
        slug: "art",
        description: "Fine art, prints, and designer wall decor.",
        isActive: true,
        sortOrder: 16,
      },
      {
        name: "Vintage",
        slug: "vintage",
        description: "Curated vintage and pre-owned luxury items.",
        isActive: true,
        sortOrder: 17,
      },
    ];

    // Check which categories already exist by slug
    const existingCategories = await ctx.db.query("categories").collect();
    const existingSlugs = existingCategories.map(c => c.slug);
    
    // Filter out categories that already exist
    const newCategories = categories.filter(category => 
      !existingSlugs.includes(category.slug)
    );
    
    if (newCategories.length === 0) {
      return { message: "All categories already exist" };
    }

    // Insert only new categories
    for (const category of newCategories) {
      await ctx.db.insert("categories", category);
    }

    return { 
      message: `Successfully added ${newCategories.length} new categories`,
      totalCategories: existingCategories.length + newCategories.length,
      newCategories: newCategories.map(c => c.name)
    };
  },
});

export const addMissingCategories = mutation({
  args: {},
  handler: async (ctx) => {
    const missingCategories = [
      {
        name: "Jewelry",
        slug: "jewelry",
        description: "Fine jewelry and luxury accessories",
        isActive: true,
        sortOrder: 6,
      },
      {
        name: "Watches",
        slug: "watches",
        description: "Luxury watches and timepieces",
        isActive: true,
        sortOrder: 7,
      },
      {
        name: "Sunglasses",
        slug: "sunglasses",
        description: "Designer sunglasses and eyewear",
        isActive: true,
        sortOrder: 8,
      },
    ];

    // Check which categories are missing
    const existingCategories = await ctx.db.query("categories").collect();
    const existingSlugs = existingCategories.map(cat => cat.slug);
    
    let addedCount = 0;
    for (const category of missingCategories) {
      if (!existingSlugs.includes(category.slug)) {
        await ctx.db.insert("categories", category);
        addedCount++;
      }
    }

    return { message: `Added ${addedCount} missing categories` };
  },
});

export const seedProducts = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    // Use the provided seller ID
    const sellerId = "j57f3232xwfgzj0xhf005qpr597nq4x6" as any;

    const sampleProducts = [
      {
        sellerId,
        title: "Chanel Classic Flap Bag - Black Caviar",
        description: "Authentic Chanel Classic Flap Bag in black caviar leather with gold hardware. Features the iconic double C logo and chain strap. Perfect condition with original dust bag and authenticity card.",
        price: 8500,
        category: "handbags" as const,
        subcategory: "Shoulder Bags",
        brand: "Chanel",
        model: "Classic Flap",
        condition: "excellent" as const,
        images: [], // Will be populated with actual storage IDs
        primaryImageId: "",
        estimatedDelivery: 3,
        sourceInfo: {
          source: "Boutique Purchase",
          costPaid: 7500,
          paymentMethod: "Credit Card",
          purchaseDate: now - 365 * 24 * 60 * 60 * 1000, // 1 year ago
          receipt: "Available upon request",
        },
        originalPrice: 9500,
        sku: "CH-CF-BLK-001",
        size: "Medium",
        color: "Black",
        material: "Caviar Leather",
        year: 2022,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Chanel Classic Flap Bag - Black Caviar Leather",
        metaDescription: "Authentic Chanel Classic Flap Bag in black caviar leather. Luxury handbag with gold hardware and chain strap.",
        tags: ["chanel", "classic", "flap", "black", "caviar", "luxury", "handbag"],
        weight: 0.8,
        dimensions: { length: 25, width: 8, height: 16 },
        shippingCost: 50,
        views: 0,
        favorites: 0,
      },
      {
        sellerId,
        title: "Rolex Submariner Date - Stainless Steel",
        description: "Classic Rolex Submariner Date in stainless steel with black dial and ceramic bezel. Automatic movement, water resistant to 300m. Includes original box, papers, and warranty card.",
        price: 12500,
        category: "watches" as const,
        subcategory: "Dive Watches",
        brand: "Rolex",
        model: "Submariner Date",
        condition: "like_new" as const,
        images: [],
        primaryImageId: "",
        estimatedDelivery: 5,
        sourceInfo: {
          source: "Authorized Dealer",
          costPaid: 11000,
          paymentMethod: "Bank Transfer",
          purchaseDate: now - 180 * 24 * 60 * 60 * 1000, // 6 months ago
        },
        originalPrice: 13500,
        sku: "RLX-SUB-SS-001",
        size: "41mm",
        color: "Stainless Steel",
        material: "Stainless Steel",
        year: 2023,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Rolex Submariner Date - Stainless Steel Luxury Watch",
        metaDescription: "Authentic Rolex Submariner Date in stainless steel with black dial. Classic dive watch with automatic movement.",
        tags: ["rolex", "submariner", "stainless", "steel", "dive", "watch", "luxury"],
        weight: 0.15,
        dimensions: { length: 41, width: 41, height: 12 },
        shippingCost: 75,
        views: 0,
        favorites: 0,
      },
      {
        sellerId,
        title: "Nike Air Jordan 1 Retro High OG - Chicago",
        description: "Limited edition Nike Air Jordan 1 Retro High OG in Chicago colorway. Features premium white leather upper with red and black accents. Deadstock condition with original box and laces.",
        price: 2800,
        category: "sneakers" as const,
        subcategory: "Basketball",
        brand: "Nike",
        model: "Air Jordan 1 Retro High OG",
        condition: "new" as const,
        images: [],
        primaryImageId: "",
        estimatedDelivery: 2,
        sourceInfo: {
          source: "SNKRS App",
          costPaid: 170,
          paymentMethod: "Credit Card",
          purchaseDate: now - 30 * 24 * 60 * 60 * 1000, // 1 month ago
        },
        originalPrice: 170,
        sku: "NIKE-AJ1-CHI-001",
        size: "US 10",
        color: "White/Red/Black",
        material: "Leather",
        year: 2024,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Nike Air Jordan 1 Retro High OG - Chicago",
        metaDescription: "Limited edition Air Jordan 1 in Chicago colorway. Deadstock condition with premium leather construction.",
        tags: ["nike", "jordan", "chicago", "retro", "high", "og", "sneakers"],
        weight: 0.4,
        dimensions: { length: 30, width: 12, height: 15 },
        shippingCost: 25,
        views: 0,
        favorites: 0,
      },
      {
        sellerId,
        title: "Cartier Love Bracelet - 18K Yellow Gold",
        description: "Iconic Cartier Love Bracelet in 18K yellow gold. Features the signature screw motif and comes with the special screwdriver. Size 17, perfect for most wrists. Includes original box and certificate.",
        price: 7200,
        category: "jewelry" as const,
        subcategory: "Bracelets",
        brand: "Cartier",
        model: "Love Bracelet",
        condition: "excellent" as const,
        images: [],
        primaryImageId: "",
        estimatedDelivery: 4,
        sourceInfo: {
          source: "Cartier Boutique",
          costPaid: 6500,
          paymentMethod: "Credit Card",
          purchaseDate: now - 730 * 24 * 60 * 60 * 1000, // 2 years ago
        },
        originalPrice: 7500,
        sku: "CRT-LOVE-YG-001",
        size: "17",
        color: "Yellow Gold",
        material: "18K Yellow Gold",
        year: 2022,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Cartier Love Bracelet - 18K Yellow Gold",
        metaDescription: "Authentic Cartier Love Bracelet in 18K yellow gold. Iconic design with screw motif and included screwdriver.",
        tags: ["cartier", "love", "bracelet", "yellow", "gold", "luxury", "jewelry"],
        weight: 0.03,
        dimensions: { length: 17, width: 0.5, height: 0.5 },
        shippingCost: 35,
        views: 0,
        favorites: 0,
      },
      {
        sellerId,
        title: "Supreme Box Logo Hoodie - Black",
        description: "Rare Supreme Box Logo Hoodie in black. Features the iconic box logo on the chest. Made from premium cotton fleece. Deadstock condition with original tags and packaging.",
        price: 1200,
        category: "clothing" as const,
        subcategory: "Hoodies",
        brand: "Supreme",
        model: "Box Logo Hoodie",
        condition: "new" as const,
        images: [],
        primaryImageId: "",
        estimatedDelivery: 3,
        sourceInfo: {
          source: "Supreme Store",
          costPaid: 168,
          paymentMethod: "Credit Card",
          purchaseDate: now - 60 * 24 * 60 * 60 * 1000, // 2 months ago
        },
        originalPrice: 168,
        sku: "SUP-BOGO-BLK-001",
        size: "Large",
        color: "Black",
        material: "Cotton Fleece",
        year: 2024,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Supreme Box Logo Hoodie - Black",
        metaDescription: "Rare Supreme Box Logo Hoodie in black. Premium cotton fleece with iconic box logo design.",
        tags: ["supreme", "box", "logo", "hoodie", "black", "streetwear"],
        weight: 0.6,
        dimensions: { length: 70, width: 60, height: 5 },
        shippingCost: 20,
        views: 0,
        favorites: 0,
      },
      {
        sellerId,
        title: "Louis Vuitton Keepall 55 - Monogram Canvas",
        description: "Classic Louis Vuitton Keepall 55 travel bag in signature monogram canvas with natural cowhide leather trim. Features brass hardware and adjustable shoulder strap. Excellent condition.",
        price: 3200,
        category: "handbags" as const,
        subcategory: "Travel Bags",
        brand: "Louis Vuitton",
        model: "Keepall 55",
        condition: "very_good" as const,
        images: [],
        primaryImageId: "",
        estimatedDelivery: 4,
        sourceInfo: {
          source: "Louis Vuitton Store",
          costPaid: 2800,
          paymentMethod: "Credit Card",
          purchaseDate: now - 1095 * 24 * 60 * 60 * 1000, // 3 years ago
        },
        originalPrice: 3200,
        sku: "LV-KEEP-55-001",
        size: "55cm",
        color: "Brown Monogram",
        material: "Monogram Canvas",
        year: 2021,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Louis Vuitton Keepall 55 - Monogram Canvas",
        metaDescription: "Classic LV Keepall 55 travel bag in signature monogram canvas. Perfect for weekend getaways and travel.",
        tags: ["louis", "vuitton", "keepall", "monogram", "canvas", "travel", "bag"],
        weight: 1.2,
        dimensions: { length: 55, width: 31, height: 23 },
        shippingCost: 45,
        views: 0,
        favorites: 0,
      },
      {
        sellerId,
        title: "Ray-Ban Aviator Classic - Gold Frame",
        description: "Timeless Ray-Ban Aviator Classic sunglasses with gold frame and green lenses. Features the iconic teardrop shape and adjustable nose pads. Includes original case and cleaning cloth.",
        price: 180,
        category: "sunglasses" as const,
        subcategory: "Aviator",
        brand: "Ray-Ban",
        model: "Aviator Classic",
        condition: "excellent" as const,
        images: [],
        primaryImageId: "",
        estimatedDelivery: 2,
        sourceInfo: {
          source: "Authorized Retailer",
          costPaid: 150,
          paymentMethod: "Credit Card",
          purchaseDate: now - 365 * 24 * 60 * 60 * 1000, // 1 year ago
        },
        originalPrice: 180,
        sku: "RB-AVI-GLD-001",
        size: "58mm",
        color: "Gold/Green",
        material: "Gold-Plated Metal",
        year: 2023,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Ray-Ban Aviator Classic - Gold Frame Sunglasses",
        metaDescription: "Classic Ray-Ban Aviator sunglasses with gold frame and green lenses. Iconic teardrop shape design.",
        tags: ["ray-ban", "aviator", "classic", "gold", "green", "sunglasses"],
        weight: 0.04,
        dimensions: { length: 58, width: 15, height: 5 },
        shippingCost: 15,
        views: 0,
        favorites: 0,
      },
      {
        sellerId,
        title: "Pokemon Charizard 1st Edition Holo - PSA 10",
        description: "Extremely rare Pokemon Charizard 1st Edition Holo card graded PSA 10. Perfect condition with no visible flaws. One of the most sought-after Pokemon cards in existence.",
        price: 45000,
        category: "collectibles" as const,
        subcategory: "Trading Cards",
        brand: "Pokemon",
        model: "1st Edition Holo",
        condition: "new" as const,
        images: [],
        primaryImageId: "",
        estimatedDelivery: 7,
        sourceInfo: {
          source: "Private Collection",
          costPaid: 40000,
          paymentMethod: "Bank Transfer",
          purchaseDate: now - 90 * 24 * 60 * 60 * 1000, // 3 months ago
        },
        originalPrice: 45000,
        sku: "PKM-CHAR-1ST-001",
        size: "Standard Card",
        color: "Multi",
        material: "Cardboard",
        year: 1999,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Pokemon Charizard 1st Edition Holo - PSA 10",
        metaDescription: "Extremely rare Pokemon Charizard 1st Edition Holo card in perfect PSA 10 condition. Ultimate collector's item.",
        tags: ["pokemon", "charizard", "1st", "edition", "holo", "psa", "10", "collectible"],
        weight: 0.01,
        dimensions: { length: 6.3, width: 8.8, height: 0.1 },
        shippingCost: 100,
        views: 0,
        favorites: 0,
      },
      {
        sellerId,
        title: "Hermes Birkin 30 - Togo Leather",
        description: "Luxurious Hermes Birkin 30 handbag in Togo leather with gold hardware. Features hand-stitched construction and the iconic lock and key. Includes dust bag and box.",
        price: 18000,
        category: "handbags" as const,
        subcategory: "Tote Bags",
        brand: "Hermes",
        model: "Birkin 30",
        condition: "excellent" as const,
        images: [],
        primaryImageId: "",
        estimatedDelivery: 5,
        sourceInfo: {
          source: "Hermes Boutique",
          costPaid: 16000,
          paymentMethod: "Bank Transfer",
          purchaseDate: now - 730 * 24 * 60 * 60 * 1000, // 2 years ago
        },
        originalPrice: 20000,
        sku: "HERM-BIRK-30-001",
        size: "30cm",
        color: "Gold",
        material: "Togo Leather",
        year: 2022,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Hermes Birkin 30 - Togo Leather Handbag",
        metaDescription: "Luxurious Hermes Birkin 30 in Togo leather with gold hardware. Hand-stitched construction and iconic design.",
        tags: ["hermes", "birkin", "togo", "leather", "gold", "luxury", "handbag"],
        weight: 1.1,
        dimensions: { length: 30, width: 22, height: 16 },
        shippingCost: 75,
        views: 0,
        favorites: 0,
      },
      {
        sellerId,
        title: "Yeezy Boost 350 V2 - Zebra",
        description: "Adidas Yeezy Boost 350 V2 in Zebra colorway. Features Primeknit upper with Boost midsole and Continental outsole. Deadstock condition with original box and tags.",
        price: 450,
        category: "sneakers" as const,
        subcategory: "Lifestyle",
        brand: "Adidas",
        model: "Yeezy Boost 350 V2",
        condition: "new" as const,
        images: [],
        primaryImageId: "",
        estimatedDelivery: 3,
        sourceInfo: {
          source: "Adidas Confirmed App",
          costPaid: 220,
          paymentMethod: "Credit Card",
          purchaseDate: now - 45 * 24 * 60 * 60 * 1000, // 1.5 months ago
        },
        originalPrice: 220,
        sku: "ADI-YZY-ZEB-001",
        size: "US 9.5",
        color: "White/Black",
        material: "Primeknit",
        year: 2024,
        ownershipType: "owned" as const,
        status: "active" as const,
        quantity: 1,
        isDraft: false,
        lastSavedAt: now,
        updatedAt: now,
        publishedAt: now,
        metaTitle: "Adidas Yeezy Boost 350 V2 - Zebra",
        metaDescription: "Yeezy Boost 350 V2 in Zebra colorway. Primeknit upper with Boost midsole technology.",
        tags: ["adidas", "yeezy", "boost", "350", "v2", "zebra", "sneakers"],
        weight: 0.35,
        dimensions: { length: 28, width: 11, height: 14 },
        shippingCost: 20,
        views: 0,
        favorites: 0,
      },
    ];

    // Check if specific products already exist by title
    const existingProducts = await ctx.db.query("products").collect();
    const existingTitles = existingProducts.map(p => p.title);
    
    // Filter out products that already exist
    const newProducts = sampleProducts.filter(product => 
      !existingTitles.includes(product.title)
    );
    
    if (newProducts.length === 0) {
      return { message: "All products already exist" };
    }

    // Insert only new products
    for (const product of newProducts) {
      await ctx.db.insert("products", product);
    }

    return { 
      message: `Successfully added ${newProducts.length} new products`,
      totalProducts: existingProducts.length + newProducts.length,
      newProducts: newProducts.map(p => p.title)
    };
  },
});

export const clearAllData = mutation({
  args: {},
  handler: async (ctx) => {
    // Clear all data (use with caution!)
    const products = await ctx.db.query("products").collect();
    const categories = await ctx.db.query("categories").collect();
    const users = await ctx.db.query("users").collect();
    
    for (const product of products) {
      await ctx.db.delete(product._id);
    }
    
    for (const category of categories) {
      await ctx.db.delete(category._id);
    }
    
    for (const user of users) {
      await ctx.db.delete(user._id);
    }
    
    return { message: "All data cleared successfully" };
  },
});

// Note: In Convex, you need to call these functions separately
// First run: seedCategories
// Then run: seedProducts

export const testSeeding = query({
  args: {},
  handler: async (ctx) => {
    const categories = await ctx.db.query("categories").collect();
    const products = await ctx.db.query("products").collect();
    const users = await ctx.db.query("users").collect();
    
    return {
      categoriesCount: categories.length,
      productsCount: products.length,
      usersCount: users.length,
      categories: categories.map(c => ({ name: c.name, slug: c.slug })),
      products: products.map(p => ({ title: p.title, category: p.category, brand: p.brand, price: p.price, hasImages: p.images.length > 0 })),
    };
  },
});

export const getFreeStockImageUrls = query({
  args: {},
  handler: async (ctx) => {
    // Free stock images from Unsplash for each product
    const productImages = {
      "Chanel Classic Flap Bag - Black Caviar": [
        "https://images.unsplash.com/photo-1591561954557-26941169b49e?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1548036328-c9fa89d128fa?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=800&h=600&fit=crop&crop=center"
      ],
      "Rolex Submariner Date - Stainless Steel": [
        "https://images.unsplash.com/photo-1523170335258-f5ed11844a49?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=800&h=600&fit=crop&crop=center"
      ],
      "Nike Air Jordan 1 Retro High OG - Chicago": [
        "https://images.unsplash.com/photo-1556906781-9a412961c28c?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?w=800&h=600&fit=crop&crop=center"
      ],
      "Cartier Love Bracelet - 18K Yellow Gold": [
        "https://images.unsplash.com/photo-1573408301185-9146fe634ad0?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1605100804763-247f67b3557e?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1602751584552-8ba73aad10e1?w=800&h=600&fit=crop&crop=center"
      ],
      "Supreme Box Logo Hoodie - Black": [
        "https://images.unsplash.com/photo-1551028719-00167b16eac5?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=800&h=600&fit=crop&crop=center"
      ],
      "Louis Vuitton Keepall 55 - Monogram Canvas": [
        "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1590874103328-eac38a683ce7?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1547949003-9792a18a2601?w=800&h=600&fit=crop&crop=center"
      ],
      "Ray-Ban Aviator Classic - Gold Frame": [
        "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=800&h=600&fit=crop&crop=center"
      ],
      "Pokemon Charizard 1st Edition Holo - PSA 10": [
        "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?w=800&h=600&fit=crop&crop=center"
      ],
      "Hermes Birkin 30 - Togo Leather": [
        "https://images.unsplash.com/photo-1591561954557-26941169b49e?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1548036328-c9fa89d128fa?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=800&h=600&fit=crop&crop=center"
      ],
      "Yeezy Boost 350 V2 - Zebra": [
        "https://images.unsplash.com/photo-1556906781-9a412961c28c?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?w=800&h=600&fit=crop&crop=center"
      ]
    };
    
    return { 
      message: "Free stock image URLs for products",
      productImages,
      note: "Use these URLs to download images and upload to Convex storage, then update the products.images field with the storage IDs."
    };
  },
});

export const getProductsWithImages = query({
  args: {},
  handler: async (ctx) => {
    const products = await ctx.db.query("products").collect();
    
    return products.map(product => ({
      _id: product._id,
      title: product.title,
      brand: product.brand,
      price: product.price,
      category: product.category,
      hasImages: product.images.length > 0,
      imageCount: product.images.length
    }));
  },
});