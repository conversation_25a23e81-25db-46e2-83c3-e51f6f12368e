import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUser } from "./lib/auth_utils";

// Helper function to detect contact information
function containsContactInfo(content: string): boolean {
  const patterns = [
    // Phone numbers (various formats)
    /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g,
    // Email addresses
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    // Common variations
    /(phone|call|text|email|contact|reach|dm|direct message)/gi,
    // Social media handles
    /@[a-zA-Z0-9_]{1,15}/g,
  ];
  
  return patterns.some(pattern => pattern.test(content));
}

// Helper function to sanitize contact information
function sanitizeContent(content: string): string {
  let sanitized = content;
  
  // Replace phone numbers
  sanitized = sanitized.replace(
    /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g,
    "[PHONE NUMBER REMOVED]"
  );
  
  // Replace email addresses
  sanitized = sanitized.replace(
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    "[EMAIL REMOVED]"
  );
  
  // Replace social media handles
  sanitized = sanitized.replace(
    /@[a-zA-Z0-9_]{1,15}/g,
    "[HANDLE REMOVED]"
  );
  
  return sanitized;
}

// Generate a consistent conversation ID for two users
function generateConversationId(userId1: string, userId2: string): string {
  return [userId1, userId2].sort().join("_");
}

// Send a message
export const sendMessage = mutation({
  args: {
    recipientId: v.id("users"),
    content: v.string(),
    productId: v.optional(v.id("products")),
    messageType: v.optional(v.union(v.literal("text"), v.literal("image"), v.literal("offer"))),
    metadata: v.optional(v.object({
      offerAmount: v.optional(v.number()),
      imageUrl: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    // Sanitize content before storing
    const sanitizedContent = sanitizeContent(args.content);
    
    const conversationId = generateConversationId(user._id, args.recipientId);
    const now = Date.now();

    // Create the message
    const messageId = await ctx.db.insert("messages", {
      conversationId,
      senderId: user._id,
      recipientId: args.recipientId,
      productId: args.productId,
      content: sanitizedContent,
      messageType: args.messageType || "text",
      isRead: false,
      metadata: args.metadata,
      updatedAt: now,
    });

    // Check if conversation exists
    let conversation = await ctx.db
      .query("conversations")
      .withIndex("by_conversationId", (q) => q.eq("conversationId", conversationId))
      .first();

    if (!conversation) {
      // Create new conversation
      await ctx.db.insert("conversations", {
        conversationId,
        participantIds: [user._id, args.recipientId],
        productId: args.productId,
        lastMessageId: messageId,
        lastMessageAt: now,
        lastMessagePreview: args.content.substring(0, 100),
        participant1Id: user._id < args.recipientId ? user._id : args.recipientId,
        participant2Id: user._id < args.recipientId ? args.recipientId : user._id,
        participant1UnreadCount: user._id < args.recipientId ? 0 : 1,
        participant2UnreadCount: user._id < args.recipientId ? 1 : 0,
        isActive: true,
        updatedAt: now,
      });
    } else {
      // Update existing conversation
      const recipientUnreadCount = conversation.participant1Id === args.recipientId 
        ? conversation.participant1UnreadCount + 1 
        : conversation.participant2UnreadCount + 1;

      await ctx.db.patch(conversation._id, {
        lastMessageId: messageId,
        lastMessageAt: now,
        lastMessagePreview: args.content.substring(0, 100),
        participant1UnreadCount: conversation.participant1Id === args.recipientId 
          ? recipientUnreadCount 
          : conversation.participant1UnreadCount,
        participant2UnreadCount: conversation.participant2Id === args.recipientId 
          ? recipientUnreadCount 
          : conversation.participant2UnreadCount,
        updatedAt: now,
      });
    }

    return messageId;
  },
});

// Get conversations for a user
export const getConversations = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_participant1", (q) => q.eq("participant1Id", user._id))
      .collect();

    const conversations2 = await ctx.db
      .query("conversations")
      .withIndex("by_participant2", (q) => q.eq("participant2Id", user._id))
      .collect();

    const allConversations = [...conversations, ...conversations2]
      .sort((a, b) => b.lastMessageAt - a.lastMessageAt);

    // Enrich with participant and product data
    const enrichedConversations = await Promise.all(
      allConversations.map(async (conversation) => {
        const otherParticipantId = conversation.participantIds.find(id => id !== user._id);
        const otherParticipant = otherParticipantId 
          ? await ctx.db.get(otherParticipantId) 
          : null;

        const product = conversation.productId 
          ? await ctx.db.get(conversation.productId) 
          : null;

        const unreadCount = conversation.participant1Id === user._id 
          ? conversation.participant1UnreadCount 
          : conversation.participant2UnreadCount;

        return {
          ...conversation,
          lastMessagePreview: sanitizeContent(conversation.lastMessagePreview),
          otherParticipant,
          product,
          unreadCount,
        };
      })
    );

    return enrichedConversations;
  },
});

// Get messages for a conversation
export const getMessages = query({
  args: {
    recipientId: v.id("users"),
    productId: v.optional(v.id("products")),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    const conversationId = generateConversationId(user._id, args.recipientId);

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversationId", (q) => q.eq("conversationId", conversationId))
      .collect();

    // Enrich with sender data and sanitize content
    const enrichedMessages = await Promise.all(
      messages.map(async (message) => {
        const sender = await ctx.db.get(message.senderId);
        const recipient = await ctx.db.get(message.recipientId);
        const product = message.productId ? await ctx.db.get(message.productId) : null;

        return {
          ...message,
          content: sanitizeContent(message.content),
          sender,
          recipient,
          product,
        };
      })
    );

    return enrichedMessages.sort((a, b) => a._creationTime - b._creationTime);
  },
});

// Get messages by conversation ID (more efficient for existing conversations)
export const getMessagesByConversationId = query({
  args: {
    conversationId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversationId", (q) => q.eq("conversationId", args.conversationId))
      .collect();

    // Enrich with sender data and sanitize content
    const enrichedMessages = await Promise.all(
      messages.map(async (message) => {
        const sender = await ctx.db.get(message.senderId);
        const recipient = await ctx.db.get(message.recipientId);
        const product = message.productId ? await ctx.db.get(message.productId) : null;

        return {
          ...message,
          content: sanitizeContent(message.content),
          sender,
          recipient,
          product,
        };
      })
    );

    return enrichedMessages.sort((a, b) => a._creationTime - b._creationTime);
  },
});

// Mark messages as read
export const markMessagesAsRead = mutation({
  args: {
    conversationId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    // Mark all unread messages in this conversation as read
    const unreadMessages = await ctx.db
      .query("messages")
      .withIndex("by_conversationId", (q) => q.eq("conversationId", args.conversationId))
      .filter((q) => 
        q.and(
          q.eq(q.field("recipientId"), user._id),
          q.eq(q.field("isRead"), false)
        )
      )
      .collect();

    await Promise.all(
      unreadMessages.map(message => 
        ctx.db.patch(message._id, { isRead: true, updatedAt: Date.now() })
      )
    );

    // Update conversation unread count
    const conversation = await ctx.db
      .query("conversations")
      .withIndex("by_conversationId", (q) => q.eq("conversationId", args.conversationId))
      .first();

    if (conversation) {
      await ctx.db.patch(conversation._id, {
        participant1UnreadCount: conversation.participant1Id === user._id ? 0 : conversation.participant1UnreadCount,
        participant2UnreadCount: conversation.participant2Id === user._id ? 0 : conversation.participant2UnreadCount,
        updatedAt: Date.now(),
      });
    }

    return unreadMessages.length;
  },
});

// Get unread message count for a user
export const getUnreadCount = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    const conversations1 = await ctx.db
      .query("conversations")
      .withIndex("by_participant1", (q) => q.eq("participant1Id", user._id))
      .collect();

    const conversations2 = await ctx.db
      .query("conversations")
      .withIndex("by_participant2", (q) => q.eq("participant2Id", user._id))
      .collect();

    const allConversations = [...conversations1, ...conversations2];

    const totalUnread = allConversations.reduce((sum, conversation) => {
      const unreadCount = conversation.participant1Id === user._id 
        ? conversation.participant1UnreadCount 
        : conversation.participant2UnreadCount;
      return sum + unreadCount;
    }, 0);

    return totalUnread;
  },
});

// Check if conversation exists between two users
export const getConversation = query({
  args: {
    otherUserId: v.id("users"),
    productId: v.optional(v.id("products")),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    const conversationId = generateConversationId(user._id, args.otherUserId);

    const conversation = await ctx.db
      .query("conversations")
      .withIndex("by_conversationId", (q) => q.eq("conversationId", conversationId))
      .first();

    if (!conversation) return null;

    const otherParticipant = await ctx.db.get(args.otherUserId);
    const product = args.productId ? await ctx.db.get(args.productId) : null;

    return {
      ...conversation,
      lastMessagePreview: sanitizeContent(conversation.lastMessagePreview),
      otherParticipant,
      product,
    };
  },
});

// Search messages within conversations
export const searchMessages = query({
  args: {
    searchQuery: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) throw new Error("Authentication required");

    if (!args.searchQuery.trim()) {
      // If no search query, return all conversations
      return await ctx.db
        .query("conversations")
        .withIndex("by_participant1", (q) => q.eq("participant1Id", user._id))
        .collect()
        .then(async (conversations1) => {
          const conversations2 = await ctx.db
            .query("conversations")
            .withIndex("by_participant2", (q) => q.eq("participant2Id", user._id))
            .collect();
          
          return [...conversations1, ...conversations2]
            .sort((a, b) => b.lastMessageAt - a.lastMessageAt);
        });
    }

    const searchTerm = args.searchQuery.toLowerCase().trim();

    // Get all conversations for the user
    const conversations1 = await ctx.db
      .query("conversations")
      .withIndex("by_participant1", (q) => q.eq("participant1Id", user._id))
      .collect();

    const conversations2 = await ctx.db
      .query("conversations")
      .withIndex("by_participant2", (q) => q.eq("participant2Id", user._id))
      .collect();

    const allConversations = [...conversations1, ...conversations2];

    // Search for messages containing the search term
    const matchingConversations = await Promise.all(
      allConversations.map(async (conversation) => {
        const messages = await ctx.db
          .query("messages")
          .withIndex("by_conversationId", (q) => q.eq("conversationId", conversation.conversationId))
          .collect();

        // Check if any message contains the search term
        const matchingMessages = messages.filter(message => 
          message.content.toLowerCase().includes(searchTerm)
        );

        if (matchingMessages.length === 0) {
          return null;
        }

        // Get the most recent matching message for preview
        const mostRecentMatch = matchingMessages.sort((a, b) => b._creationTime - a._creationTime)[0];
        
        // Additional safety check - this should never happen given the filter above, but TypeScript needs it
        if (!mostRecentMatch) {
          return null;
        }

        // Enrich conversation with search results
        const otherParticipantId = conversation.participantIds.find(id => id !== user._id);
        const otherParticipant = otherParticipantId 
          ? await ctx.db.get(otherParticipantId) 
          : null;

        const product = conversation.productId 
          ? await ctx.db.get(conversation.productId) 
          : null;

        const unreadCount = conversation.participant1Id === user._id 
          ? conversation.participant1UnreadCount 
          : conversation.participant2UnreadCount;

        return {
          ...conversation,
          otherParticipant,
          product,
          unreadCount,
          searchResults: {
            matchCount: matchingMessages.length,
            mostRecentMatch: {
              content: sanitizeContent(mostRecentMatch.content),
              _creationTime: mostRecentMatch._creationTime,
              senderId: mostRecentMatch.senderId,
            },
            matchingMessages: matchingMessages.map(msg => ({
              _id: msg._id,
              content: sanitizeContent(msg.content),
              _creationTime: msg._creationTime,
              senderId: msg.senderId,
            })),
          },
        };
      })
    );

    // Filter out null results and sort by most recent matching message
    return matchingConversations
      .filter(Boolean)
      .sort((a, b) => (b?.searchResults?.mostRecentMatch?._creationTime || 0) - (a?.searchResults?.mostRecentMatch?._creationTime || 0));
  },
});
