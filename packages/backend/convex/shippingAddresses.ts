import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { getAuthUser, requireAuth } from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get user's shipping addresses
 */
export const getUserShippingAddresses = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);

    const addresses = await ctx.db
      .query("shippingAddresses")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .collect();

    return addresses.sort((a, b) => {
      // Default address first, then by creation date
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      return b._creationTime - a._creationTime;
    });
  },
});

/**
 * Add a new shipping address
 */
export const addShippingAddress = mutation({
  args: {
    type: v.union(v.literal("home"), v.literal("work"), v.literal("other")),
    firstName: v.string(),
    lastName: v.string(),
    company: v.optional(v.string()),
    street: v.string(),
    street2: v.optional(v.string()),
    city: v.string(),
    state: v.string(),
    zip: v.string(),
    country: v.string(),
    phone: v.optional(v.string()),
    isDefault: v.optional(v.boolean()),
    deliveryInstructions: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Validate required fields
    if (!args.firstName.trim() || args.firstName.trim().length < 2) {
      throw new ConvexError("First name must be at least 2 characters");
    }
    if (!args.lastName.trim() || args.lastName.trim().length < 2) {
      throw new ConvexError("Last name must be at least 2 characters");
    }
    if (!args.street.trim()) {
      throw new ConvexError("Street address is required");
    }
    if (!args.city.trim()) {
      throw new ConvexError("City is required");
    }
    if (!args.state.trim()) {
      throw new ConvexError("State is required");
    }
    if (!args.zip.trim()) {
      throw new ConvexError("ZIP code is required");
    }

    // Validate phone if provided
    if (args.phone && args.phone.trim()) {
      const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
      if (!phoneRegex.test(args.phone)) {
        throw new ConvexError("Invalid phone number format");
      }
    }

    // Check if this should be the default address
    const existingAddresses = await ctx.db
      .query("shippingAddresses")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .collect();

    const shouldBeDefault = args.isDefault || existingAddresses.length === 0;

    // If this is being set as default, remove default status from other addresses
    if (shouldBeDefault) {
      for (const address of existingAddresses) {
        if (address.isDefault) {
          await ctx.db.patch(address._id, { isDefault: false });
        }
      }
    }

    // Create the address
    const addressId = await ctx.db.insert("shippingAddresses", {
      userId: user._id,
      type: args.type,
      firstName: args.firstName.trim(),
      lastName: args.lastName.trim(),
      company: args.company?.trim() || undefined,
      street: args.street.trim(),
      street2: args.street2?.trim() || undefined,
      city: args.city.trim(),
      state: args.state.trim(),
      zip: args.zip.trim(),
      country: args.country,
      phone: args.phone?.trim() || undefined,
      isDefault: shouldBeDefault,
      deliveryInstructions: args.deliveryInstructions?.trim() || undefined,
      updatedAt: Date.now(),
    });

    // Log address creation
    await ctx.db.insert("analytics", {
      eventType: "shipping_address_added",
      userId: user._id,
      timestamp: Date.now(),
      metadata: {
        source: addressId,
        category: "shipping",
        revenue: 0,
      },
    });

    return {
      success: true,
      addressId,
      message: "Shipping address added successfully",
    };
  },
});

/**
 * Update an existing shipping address
 */
export const updateShippingAddress = mutation({
  args: {
    addressId: v.id("shippingAddresses"),
    type: v.optional(v.union(v.literal("home"), v.literal("work"), v.literal("other"))),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    company: v.optional(v.string()),
    street: v.optional(v.string()),
    street2: v.optional(v.string()),
    city: v.optional(v.string()),
    state: v.optional(v.string()),
    zip: v.optional(v.string()),
    country: v.optional(v.string()),
    phone: v.optional(v.string()),
    isDefault: v.optional(v.boolean()),
    deliveryInstructions: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get the address
    const address = await ctx.db.get(args.addressId);
    if (!address) {
      throw new ConvexError("Address not found");
    }

    // Check ownership
    if (address.userId !== user._id) {
      throw new ConvexError("Unauthorized: You can only update your own addresses");
    }

    // Validate fields if provided
    if (args.firstName !== undefined && (!args.firstName.trim() || args.firstName.trim().length < 2)) {
      throw new ConvexError("First name must be at least 2 characters");
    }
    if (args.lastName !== undefined && (!args.lastName.trim() || args.lastName.trim().length < 2)) {
      throw new ConvexError("Last name must be at least 2 characters");
    }
    if (args.street !== undefined && !args.street.trim()) {
      throw new ConvexError("Street address is required");
    }
    if (args.city !== undefined && !args.city.trim()) {
      throw new ConvexError("City is required");
    }
    if (args.state !== undefined && !args.state.trim()) {
      throw new ConvexError("State is required");
    }
    if (args.zip !== undefined && !args.zip.trim()) {
      throw new ConvexError("ZIP code is required");
    }

    // Validate phone if provided
    if (args.phone !== undefined && args.phone && args.phone.trim()) {
      const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
      if (!phoneRegex.test(args.phone)) {
        throw new ConvexError("Invalid phone number format");
      }
    }

    // If setting as default, remove default status from other addresses
    if (args.isDefault) {
      const userAddresses = await ctx.db
        .query("shippingAddresses")
        .withIndex("by_userId", (q) => q.eq("userId", user._id))
        .collect();

      for (const userAddress of userAddresses) {
        if (userAddress._id !== args.addressId && userAddress.isDefault) {
          await ctx.db.patch(userAddress._id, { isDefault: false });
        }
      }
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.type !== undefined) updateData.type = args.type;
    if (args.firstName !== undefined) updateData.firstName = args.firstName.trim();
    if (args.lastName !== undefined) updateData.lastName = args.lastName.trim();
    if (args.company !== undefined) updateData.company = args.company?.trim() || undefined;
    if (args.street !== undefined) updateData.street = args.street.trim();
    if (args.street2 !== undefined) updateData.street2 = args.street2?.trim() || undefined;
    if (args.city !== undefined) updateData.city = args.city.trim();
    if (args.state !== undefined) updateData.state = args.state.trim();
    if (args.zip !== undefined) updateData.zip = args.zip.trim();
    if (args.country !== undefined) updateData.country = args.country;
    if (args.phone !== undefined) updateData.phone = args.phone?.trim() || undefined;
    if (args.isDefault !== undefined) updateData.isDefault = args.isDefault;
    if (args.deliveryInstructions !== undefined) updateData.deliveryInstructions = args.deliveryInstructions?.trim() || undefined;

    // Update the address
    await ctx.db.patch(args.addressId, updateData);

    // Log address update
    await ctx.db.insert("analytics", {
      eventType: "shipping_address_updated",
      userId: user._id,
      timestamp: Date.now(),
      metadata: {
        source: args.addressId,
        category: "shipping",
        revenue: 0,
      },
    });

    return {
      success: true,
      message: "Shipping address updated successfully",
    };
  },
});

/**
 * Delete a shipping address
 */
export const deleteShippingAddress = mutation({
  args: {
    addressId: v.id("shippingAddresses"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get the address
    const address = await ctx.db.get(args.addressId);
    if (!address) {
      throw new ConvexError("Address not found");
    }

    // Check ownership
    if (address.userId !== user._id) {
      throw new ConvexError("Unauthorized: You can only delete your own addresses");
    }

    // Check if this is the only address
    const userAddresses = await ctx.db
      .query("shippingAddresses")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .collect();

    if (userAddresses.length === 1) {
      throw new ConvexError("Cannot delete your only shipping address");
    }

    // If deleting default address, make another address default
    if (address.isDefault) {
      const otherAddresses = userAddresses.filter(addr => addr._id !== args.addressId);
      if (otherAddresses.length > 0 && otherAddresses[0]) {
        await ctx.db.patch(otherAddresses[0]._id, { isDefault: true });
      }
    }

    // Delete the address
    await ctx.db.delete(args.addressId);

    // Log address deletion
    await ctx.db.insert("analytics", {
      eventType: "shipping_address_deleted",
      userId: user._id,
      timestamp: Date.now(),
      metadata: {
        source: args.addressId,
        category: "shipping",
        revenue: 0,
      },
    });

    return {
      success: true,
      message: "Shipping address deleted successfully",
    };
  },
});

/**
 * Set an address as default
 */
export const setDefaultShippingAddress = mutation({
  args: {
    addressId: v.id("shippingAddresses"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get the address
    const address = await ctx.db.get(args.addressId);
    if (!address) {
      throw new ConvexError("Address not found");
    }

    // Check ownership
    if (address.userId !== user._id) {
      throw new ConvexError("Unauthorized: You can only modify your own addresses");
    }

    // Remove default status from all other addresses
    const userAddresses = await ctx.db
      .query("shippingAddresses")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .collect();

    for (const userAddress of userAddresses) {
      if (userAddress._id !== args.addressId && userAddress.isDefault) {
        await ctx.db.patch(userAddress._id, { isDefault: false });
      }
    }

    // Set this address as default
    await ctx.db.patch(args.addressId, { 
      isDefault: true,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      message: "Default shipping address updated",
    };
  },
});
