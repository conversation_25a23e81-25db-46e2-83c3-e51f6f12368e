import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { getAuthUser, requireAuth } from "./lib/auth_utils";

/**
 * Add a product to user's cart
 */
export const addToCart = mutation({
  args: {
    productId: v.id("products"),
    quantity: v.optional(v.number()),
  },
  handler: async (ctx, { productId, quantity = 1 }) => {
    const user = await requireAuth(ctx);

    // Check if product exists
    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // Check if product is available
    if (product.status !== "active") {
      throw new Error("Product is not available");
    }

    // Prevent adding own products to cart
    if (product.sellerId === user._id) {
      throw new Error("Cannot add your own product to cart");
    }

    // Check if item already exists in cart
    const existingItem = await ctx.db
      .query("cart")
      .withIndex("by_user_product", (q) => 
        q.eq("userId", user._id).eq("productId", productId)
      )
      .first();

    if (existingItem) {
      // Update quantity
      const newQuantity = existingItem.quantity + quantity;
      if (newQuantity > 10) {
        throw new Error("Maximum quantity per item is 10");
      }
      
      await ctx.db.patch(existingItem._id, {
        quantity: newQuantity,
      });

      return existingItem._id;
    } else {
      // Add new item to cart
      if (quantity > 10) {
        throw new Error("Maximum quantity per item is 10");
      }

      const cartItemId = await ctx.db.insert("cart", {
        userId: user._id,
        productId,
        quantity,
      });

      return cartItemId;
    }
  },
});

/**
 * Remove a product from user's cart
 */
export const removeFromCart = mutation({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, { productId }) => {
    const user = await requireAuth(ctx);

    // Find the cart item
    const cartItem = await ctx.db
      .query("cart")
      .withIndex("by_user_product", (q) => 
        q.eq("userId", user._id).eq("productId", productId)
      )
      .first();

    if (!cartItem) {
      throw new Error("Product not in cart");
    }

    // Remove from cart
    await ctx.db.delete(cartItem._id);

    return true;
  },
});

/**
 * Update quantity of a product in cart
 */
export const updateCartQuantity = mutation({
  args: {
    productId: v.id("products"),
    quantity: v.number(),
  },
  handler: async (ctx, { productId, quantity }) => {
    const user = await requireAuth(ctx);

    if (quantity < 1) {
      throw new Error("Quantity must be at least 1");
    }

    if (quantity > 10) {
      throw new Error("Maximum quantity per item is 10");
    }

    // Find the cart item
    const cartItem = await ctx.db
      .query("cart")
      .withIndex("by_user_product", (q) => 
        q.eq("userId", user._id).eq("productId", productId)
      )
      .first();

    if (!cartItem) {
      throw new Error("Product not in cart");
    }

    // Update quantity
    await ctx.db.patch(cartItem._id, {
      quantity,
    });

    return true;
  },
});

/**
 * Clear all items from user's cart
 */
export const clearCart = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);

    // Get all cart items for user
    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .collect();

    // Delete all cart items
    for (const item of cartItems) {
      await ctx.db.delete(item._id);
    }

    return true;
  },
});

/**
 * Get user's cart items
 */
export const getCart = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return [];
    }

    // Get cart items
    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .collect();

    // Get product details for each cart item
    const cartWithProducts = await Promise.all(
      cartItems.map(async (cartItem) => {
        const product = await ctx.db.get(cartItem.productId);
        if (!product) return null;

        // Get seller information
        let seller = null;
        if (product.sellerId) {
          const sellerUser = await ctx.db.get(product.sellerId);
          if (sellerUser) {
            const sellerProfile = await ctx.db
              .query("sellerProfiles")
              .withIndex("by_userId", (q) => q.eq("userId", sellerUser._id))
              .first();
            
            seller = {
              _id: sellerUser._id,
              name: sellerUser.name,
              businessName: sellerProfile?.businessName,
              rating: sellerProfile?.rating || 0,
              reviewCount: sellerProfile?.reviewCount || 0,
              verificationStatus: sellerProfile?.verificationStatus,
            };
          }
        }

        // Convert storage IDs to URLs for images
        const imageUrls = await Promise.all(
          product.images
            .filter(imageId => imageId !== "")
            .map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url;
            })
        );

        return {
          _id: cartItem._id,
          quantity: cartItem.quantity,
          _creationTime: cartItem._creationTime,
          product: {
            ...product,
            images: imageUrls.filter(url => url !== null) as string[], // Filter out null URLs
            seller,
          },
        };
      })
    );

    // Filter out null products and sort by most recently added
    return cartWithProducts
      .filter(item => item !== null)
      .sort((a, b) => b._creationTime - a._creationTime);
  },
});

/**
 * Get cart count for user
 */
export const getCartCount = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return 0;
    }

    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .collect();

    // Return total quantity of all items
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  },
});

/**
 * Check if a product is in user's cart
 */
export const isInCart = query({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, { productId }) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return false;
    }

    const cartItem = await ctx.db
      .query("cart")
      .withIndex("by_user_product", (q) => 
        q.eq("userId", user._id).eq("productId", productId)
      )
      .first();

    return !!cartItem;
  },
});

/**
 * Get cart total (price calculation)
 */
export const getCartTotal = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return {
        subtotal: 0,
        tax: 0,
        shipping: 0,
        total: 0,
        itemCount: 0,
      };
    }

    // Get cart items
    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .collect();

    let subtotal = 0;
    let itemCount = 0;

    // Calculate subtotal
    for (const cartItem of cartItems) {
      const product = await ctx.db.get(cartItem.productId);
      if (product && product.status === "active") {
        subtotal += product.price * cartItem.quantity;
        itemCount += cartItem.quantity;
      }
    }

    // Calculate tax (8%)
    const tax = subtotal * 0.08;

    // Calculate shipping (free over $500, otherwise $25)
    const shipping = subtotal > 500 ? 0 : 25;

    // Calculate total
    const total = subtotal + tax + shipping;

    return {
      subtotal,
      tax,
      shipping,
      total,
      itemCount,
    };
  },
});
