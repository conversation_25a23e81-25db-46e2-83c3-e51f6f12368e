import { Doc } from "../convex/_generated/dataModel";

export type UserType = "consumer" | "seller" | "admin";
export type SubscriptionStatus = "active" | "inactive" | "trial";
export type VerificationStatus = "pending" | "approved" | "rejected" | "under_review";
export type SubscriptionPlan = "basic" | "premium" | "enterprise";

export interface AuthUser extends Doc<"users"> {
  sellerProfile?: Doc<"sellerProfiles">;
  isSubscriptionActive: boolean;
}

export interface AuthSession {
  user: AuthUser;
  sessionId: string;
  expiresAt: number;
}

export interface AuthPermissions {
  canBuy: boolean;
  canSell: boolean;
  canManageUsers: boolean;
  canAccessAnalytics: boolean;
  canManageSubscriptions: boolean;
  requiresSubscription: boolean;
}

export interface SellerStatus {
  hasProfile: boolean;
  verificationStatus: VerificationStatus;
  canSell: boolean;
}

export interface AuthContextValue {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  hasActiveSubscription: boolean;
  sellerStatus: SellerStatus | null;
  permissions: AuthPermissions;
  signOut: () => Promise<void>;
  updateProfile: (data: Partial<AuthUser>) => Promise<void>;
}

export interface AuthMiddlewareConfig {
  requireAuth?: boolean;
  requireSubscription?: boolean;
  allowedRoles?: UserType[];
  requireSellerVerification?: boolean;
  redirectTo?: string;
}

export interface SignUpData {
  email: string;
  password: string;
  name: string;
  userType?: UserType;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface UpdateProfileData {
  name?: string;
  userType?: UserType;
  onboardingCompleted?: boolean;
}

export interface SubscriptionData {
  status: SubscriptionStatus;
  expiresAt?: number;
  planId?: string;
  customerId?: string;
}

// Error classes
export class AuthError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 401
  ) {
    super(message);
    this.name = "AuthError";
  }
}

export class UnauthorizedError extends AuthError {
  constructor(message: string = "Unauthorized") {
    super(message, "UNAUTHORIZED", 401);
  }
}

export class ForbiddenError extends AuthError {
  constructor(message: string = "Forbidden") {
    super(message, "FORBIDDEN", 403);
  }
}

export class SubscriptionRequiredError extends AuthError {
  constructor(message: string = "Active subscription required") {
    super(message, "SUBSCRIPTION_REQUIRED", 402);
  }
}

export class SellerVerificationRequiredError extends AuthError {
  constructor(message: string = "Seller verification required") {
    super(message, "SELLER_VERIFICATION_REQUIRED", 403);
  }
}

// Error types
export interface AuthErrorResponse {
  error: string;
  code: string;
  details?: Record<string, any>;
}

// API Response types
export interface AuthResponse<T = any> {
  data?: T;
  error?: AuthErrorResponse;
  success: boolean;
}

// Route protection types
export interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireSubscription?: boolean;
  allowedRoles?: UserType[];
  requireSellerVerification?: boolean;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export interface ProtectedRouteConfig {
  requireAuth?: boolean;
  allowedRoles?: UserType[];
  redirectUnauthenticated?: string;
  redirectUnauthorized?: string;
  requireSubscription?: boolean;
  allowedPlans?: SubscriptionPlan[];
  requireSellerVerification?: boolean;
}