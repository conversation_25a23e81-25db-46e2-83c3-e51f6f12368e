import { getSessionCookie } from "better-auth/cookies";
import { NextRequest, NextResponse } from "next/server";
import { auth } from "./server";

export interface AuthMiddlewareOptions {
  requireAuth?: boolean;
  requireSubscription?: boolean;
  allowedRoles?: ("consumer" | "seller" | "admin")[];
  requireSellerVerification?: boolean;
  redirectTo?: string;
}

export async function authMiddleware(
  request: NextRequest,
  options: AuthMiddlewareOptions = {}
) {
  const {
    requireAuth = true,
    requireSubscription = false,
    allowedRoles = [],
    requireSellerVerification = false,
    redirectTo = "/",
  } = options;

  const sessionCookie = getSessionCookie(request);

  // Check if authentication is required
  if (requireAuth && !sessionCookie) {
    const loginUrl = new URL(redirectTo, request.url);
    loginUrl.searchParams.set("redirect", request.url);
    return NextResponse.redirect(loginUrl);
  }

  // If no session cookie and auth not required, continue
  if (!sessionCookie) {
    return NextResponse.next();
  }

  try {
    // Verify session and get user data
    const authInstance = auth({} as any); // Context will be provided by Convex
    const session = await authInstance.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      if (requireAuth) {
        return NextResponse.redirect(new URL(redirectTo, request.url));
      }
      return NextResponse.next();
    }

    const user = session.user as any;
    
    // Check role-based access
    if (allowedRoles.length > 0 && !allowedRoles.includes(user.userType)) {
      return NextResponse.redirect(new URL("/unauthorized", request.url));
    }

    // Check subscription requirement
    if (requireSubscription) {
      const isSubscriptionActive = 
        user.subscriptionStatus === "active" && 
        user.subscriptionExpiresAt && 
        user.subscriptionExpiresAt > Date.now();

      if (!isSubscriptionActive) {
        return NextResponse.redirect(new URL("/subscription", request.url));
      }
    }

    // Check seller verification requirement
    if (requireSellerVerification && user.userType === "seller") {
      // This would need to be checked against the seller profile
      // For now, we'll add a header to indicate verification is needed
      const response = NextResponse.next();
      response.headers.set("x-seller-verification-required", "true");
      return response;
    }

    // Add user info to headers for downstream use
    const response = NextResponse.next();
    response.headers.set("x-user-id", user.id);
    response.headers.set("x-user-type", user.userType);
    response.headers.set("x-subscription-status", user.subscriptionStatus);

    return response;
  } catch (error) {
    console.error("Auth middleware error:", error);
    
    if (requireAuth) {
      return NextResponse.redirect(new URL(redirectTo, request.url));
    }
    
    return NextResponse.next();
  }
}

// Specific middleware functions for common use cases
export const requireSubscriptionMiddleware = (request: NextRequest) =>
  authMiddleware(request, { requireSubscription: true });

export const requireSellerMiddleware = (request: NextRequest) =>
  authMiddleware(request, { 
    allowedRoles: ["seller", "admin"],
    requireSellerVerification: true 
  });

// Note: This middleware is currently not used for admin routes
// Admin authentication is handled client-side by the AdminLayout component
export const requireAdminMiddleware = async (request: NextRequest) => {  
  const sessionCookie = getSessionCookie(request);
  
  if (!sessionCookie) {
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("redirect", request.url);
    return NextResponse.redirect(loginUrl);
  }

  try {
    // Verify session exists and is valid
    const authInstance = auth({} as any);
    const session = await authInstance.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    // Allow access - role checking will be done client-side by RequireAdmin component
    return NextResponse.next();
  } catch (error) {
    return NextResponse.redirect(new URL("/login", request.url));
  }
};

export const publicMiddleware = (request: NextRequest) =>
  authMiddleware(request, { requireAuth: false });
