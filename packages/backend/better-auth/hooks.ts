// Backend auth utilities - no React hooks allowed
export interface AuthUser {
  _id: string;
  name: string;
  email: string;
  userType: "consumer" | "seller" | "admin";
  subscriptionStatus: "active" | "inactive" | "trial";
  subscriptionPlan?: "basic" | "premium" | "enterprise";
  subscriptionExpiresAt?: number;
  profileImage?: string;
  emailVerified: boolean;
  _creationTime: number;
}

export type UserType = "consumer" | "seller" | "admin";
export type SubscriptionStatus = "active" | "inactive" | "trial";
export type SubscriptionPlan = "basic" | "premium" | "enterprise";