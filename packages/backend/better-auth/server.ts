import { convexAdapter } from "@convex-dev/better-auth";
import { convex, crossDomain } from "@convex-dev/better-auth/plugins";
import { requireMutationCtx } from "@convex-dev/better-auth/utils";
import VerifyEmail from "@repo/email/templates/verify-email";
import { betterAuth, BetterAuthOptions } from "better-auth";
import { organization } from "better-auth/plugins";
import { createAuthMiddleware } from "better-auth/api";

import { GenericCtx } from "../convex/_generated/server";
import { betterAuthComponent } from "../convex/auth";
import { sendEmail } from "../convex/lib/email";


const createOptions = (ctx: GenericCtx) =>
  ({
    baseURL: process.env.APP_URL || "https://kindly-giraffe-916.convex.cloud",
    secret: process.env.BETTER_AUTH_SECRET || (() => {
      console.warn("⚠️  BETTER_AUTH_SECRET not set! Using fallback secret for development only.");
      return "fallback-secret-for-development-only-please-set-better-auth-secret-in-production";
    })(),
    database: convexAdapter(ctx, betterAuthComponent),
    
    account: {
      accountLinking: {
        enabled: true,
        allowDifferentEmails: true,
      },
    },

    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false, // Disabled for development - no email service configured
      minPasswordLength: 8,
      maxPasswordLength: 128,
    },

    emailVerification: {
      sendVerificationEmail: async ({ user, url }) => {
        try {
          await sendEmail(requireMutationCtx(ctx), {
            to: user.email,
            subject: "Verify your email address - HauteVault",
            react: VerifyEmail({
              name: user.name || "Valued Customer",
              verificationUrl: url
            }),
          });
        } catch (error) {
          console.error("Failed to send verification email:", error);
          // In development, log the verification URL for testing
          if (process.env.NODE_ENV === "development") {
            console.log("🔗 Verification URL for testing:", url);
          }
          // Don't throw error to prevent auth flow from breaking
          // In production, you might want to handle this differently
        }
      },
      autoSignInAfterVerification: true,
    },

    socialProviders: {
      ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET && {
        google: {
          clientId: process.env.GOOGLE_CLIENT_ID,
          clientSecret: process.env.GOOGLE_CLIENT_SECRET,
          accessType: "offline",
          prompt: "select_account+consent",
          scope: ["openid", "email", "profile"],
        },
      }),
      ...(process.env.APPLE_CLIENT_ID && process.env.APPLE_CLIENT_SECRET && {
        apple: {
          clientId: process.env.APPLE_CLIENT_ID,
          clientSecret: process.env.APPLE_CLIENT_SECRET,
          scope: ["name", "email"],
        },
      }),
    },

    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
      cookieCache: {
        enabled: true,
        maxAge: 60 * 5, // 5 minutes
      },
    },



    // Advanced configuration for better session handling
    advanced: {
      crossSubDomainCookies: {
        enabled: false, // Disable for localhost
      },
      cookiePrefix: "better-auth",
      generateId: () => crypto.randomUUID(),
    },

    // Ensure no custom fields are sent to the Better Auth user model.
    // Custom app fields are stored in our own `users` table instead.
    user: {
      additionalFields: {},
      deleteUser: {
        enabled: true,
        // No verification required for cleanup - we're handling orphaned accounts
        // In a production app, you might want to add verification here
      },
    },

    plugins: [
      organization(),
      crossDomain({
        siteUrl: process.env.APP_URL || "https://haulte-valut-web.vercel.app",
      }),
    ],

    trustedOrigins: [
      "https://kindly-giraffe-916.convex.cloud",
      "http://localhost:3000",
      "https://hv.devwithbobby.com",
      "https://haulte-valut-web.vercel.app"
    ],

    rateLimit: {
      enabled: true,
      window: 60, // 1 minute
      max: 100, // 100 requests per minute
    },


  }) satisfies BetterAuthOptions;

export const auth = (ctx: GenericCtx): ReturnType<typeof betterAuth> => {
  const options = createOptions(ctx);
  return betterAuth({
    ...options,
    plugins: [
      ...options.plugins,
      convex({ options }),
    ],
  });
};

export type AuthUser = {
  id: string;
  email: string;
  name: string;
  image?: string;
  userType: "consumer" | "seller" | "admin";
  subscriptionStatus: "active" | "inactive" | "trial";
  subscriptionExpiresAt?: number;
  onboardingCompleted: boolean;
  emailVerified: boolean;
  _creationTime: number;
  updatedAt: number;
};
