[16:18:06.799] Running build in Washington, D.C., USA (East) – iad1
[16:18:06.800] Build machine configuration: 4 cores, 8 GB
[16:18:06.830] Cloning github.com/robertalv/haulte-valut (Branch: main, Commit: a2423f3)
[16:18:07.450] Cloning completed: 620.000ms
[16:18:20.964] Restored build cache from previous deployment (6RfCgxwTsofwJH2f3AAyqeiCAsfy)
[16:18:30.456] Running "vercel build"
[16:18:30.983] Vercel CLI 44.7.3
[16:18:31.166] > Detected Turbo. Adjusting default settings...
[16:18:31.415] Detected `pnpm-lock.yaml` version 9 generated by pnpm@10.x with package.json#packageManager pnpm@9.0.0
[16:18:31.422] Running "install" command: `pnpm install`...
[16:18:33.718] Scope: all 7 workspace projects
[16:18:33.982] ../..                                    |  -51 -----
[16:18:34.994] 
[16:18:34.994] devDependencies:
[16:18:34.994] + @tailwindcss/postcss 4.1.11
[16:18:34.994] - tailwindcss 3.4.17
[16:18:34.994] + tailwindcss 4.1.11
[16:18:34.994] 
[16:18:35.061] Done in 1.8s
[16:18:35.085] Detected Next.js version: 15.3.0
[16:18:35.086] Running "cd ../../packages/backend && npx convex deploy --cmd 'cd ../../apps/web && pnpm build'"
[16:18:36.437] - Running 'cd ../../apps/web && pnpm build' with environment variable "NEXT_PUBLIC_CONVEX_URL" set...
[16:18:36.438] 
[16:18:37.334] 
[16:18:37.334] > web@0.1.0 build /vercel/path0/apps/web
[16:18:37.334] > next build
[16:18:37.334] 
[16:18:38.185]    ▲ Next.js 15.3.0
[16:18:38.185] 
[16:18:38.273]    Creating an optimized production build ...
[16:18:59.386]  ✓ Compiled successfully in 16.0s
[16:18:59.391]    Linting and checking validity of types ...
[16:19:08.697] 
[16:19:08.699] ./app/(auth)/forgot-password/page.tsx
[16:19:08.699] 33:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.700] 50:17  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.700] 55:19  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.700] 81:44  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.701] 
[16:19:08.701] ./app/admin/cleanup/page.tsx
[16:19:08.701] 23:10  Warning: 'selectedEmail' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.703] 23:25  Warning: 'setSelectedEmail' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.703] 34:81  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.703] 36:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.703] 61:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.704] 145:30  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.704] 145:42  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.704] 145:63  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.704] 145:90  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.705] 
[16:19:08.705] ./app/admin/layout.tsx
[16:19:08.705] 50:52  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.705] 
[16:19:08.705] ./app/apply/page.tsx
[16:19:08.706] 4:10  Warning: 'Card' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.706] 4:16  Warning: 'CardContent' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.706] 4:29  Warning: 'CardDescription' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.706] 4:46  Warning: 'CardHeader' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.706] 4:58  Warning: 'CardTitle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.707] 5:10  Warning: 'Shield' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.707] 5:18  Warning: 'CheckCircle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.707] 5:31  Warning: 'Users' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.707] 5:38  Warning: 'TrendingUp' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.707] 
[16:19:08.708] ./app/cart/page.tsx
[16:19:08.708] 20:10  Warning: 'Id' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.709] 56:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.709] 61:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.709] 
[16:19:08.709] ./app/marketplace/product/[id]/page.tsx
[16:19:08.709] 48:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.710] 61:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.711] 68:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.711] 100:60  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.711] 100:83  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.711] 
[16:19:08.711] ./app/seller/products/[id]/sell-offline/page.tsx
[16:19:08.712] 118:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.712] 274:90  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.712] 
[16:19:08.712] ./app/seller/reports/page.tsx
[16:19:08.713] 5:29  Warning: 'CardDescription' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.713] 12:3  Warning: 'DollarSign' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.713] 13:3  Warning: 'Package' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.713] 29:21  Warning: 'setDateRange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.714] 
[16:19:08.714] ./app/seller/sales/page.tsx
[16:19:08.714] 18:11  Warning: 'user' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.714] 18:28  Warning: 'authLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.714] 136:29  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.715] 
[16:19:08.715] ./app/unauthorized/page.tsx
[16:19:08.715] 24:20  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.715] 32:22  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.716] 
[16:19:08.716] ./components/admin/AdminDashboard.tsx
[16:19:08.716] 10:10  Warning: 'UserManagement' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.716] 11:10  Warning: 'SellerManagement' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.716] 201:18  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.717] 
[16:19:08.717] ./components/admin/AdminNavigation.tsx
[16:19:08.717] 3:10  Warning: 'useState' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.717] 6:10  Warning: 'Button' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.717] 20:3  Warning: 'User' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.718] 
[16:19:08.718] ./components/admin/AdminOverview.tsx
[16:19:08.718] 19:3  Warning: 'Clock' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.718] 60:11  Warning: 'RecentActivity' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.718] 
[16:19:08.718] ./components/admin/EnhancedSellerManagement.tsx
[16:19:08.718] 56:3  Warning: 'AlertTriangle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.718] 71:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.718] 105:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.718] 119:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.718] 133:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.719] 145:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.719] 157:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.719] 
[16:19:08.719] ./components/admin/EnhancedUserManagement.tsx
[16:19:08.719] 3:20  Warning: 'useEffect' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.719] 10:29  Warning: 'TabsList' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.719] 10:39  Warning: 'TabsTrigger' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.719] 45:3  Warning: 'Filter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.719] 49:3  Warning: 'Trash2' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.719] 52:3  Warning: 'XCircle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.719] 53:3  Warning: 'Clock' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.720] 55:3  Warning: 'DollarSign' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.720] 56:3  Warning: 'TrendingUp' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.720] 58:3  Warning: 'Activity' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.720] 60:3  Warning: 'RefreshCw' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.720] 73:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.720] 82:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.720] 83:72  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.720] 94:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.720] 95:72  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.720] 107:9  Warning: 'updateUserProfile' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.720] 122:11  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.720] 122:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.720] 129:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.720] 144:9  Warning: 'formatCurrency' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.721] 226:70  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.721] 243:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.721] 260:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.721] 344:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.721] 423:49  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.721] 423:62  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.721] 
[16:19:08.721] ./components/admin/PlatformSettings.tsx
[16:19:08.721] 10:10  Warning: 'Textarea' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.721] 20:3  Warning: 'Dialog' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.721] 21:3  Warning: 'DialogContent' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.721] 22:3  Warning: 'DialogDescription' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.721] 23:3  Warning: 'DialogFooter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.721] 24:3  Warning: 'DialogHeader' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.721] 25:3  Warning: 'DialogTitle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.721] 28:3  Warning: 'Settings' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 36:3  Warning: 'Tag' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 141:29  Warning: 'setSubscriptionPlans' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 142:24  Warning: 'setPlatformFees' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 143:22  Warning: 'setCategories' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 145:10  Warning: 'editDialog' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 145:22  Warning: 'setEditDialog' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 146:10  Warning: 'editItem' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 146:20  Warning: 'setEditItem' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 146:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.722] 
[16:19:08.722] ./components/admin/SellerApplicationReview.tsx
[16:19:08.722] 6:10  Warning: 'Id' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 27:3  Warning: 'Phone' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.722] 33:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.722] 
[16:19:08.722] ./components/admin/SellerApplicationsTable.tsx
[16:19:08.722] 21:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.723] 30:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.723] 40:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.723] 
[16:19:08.723] ./components/admin/SellerManagement.tsx
[16:19:08.723] 43:3  Warning: 'Package' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.723] 45:3  Warning: 'Award' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.723] 157:68  Warning: 'action' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.723] 
[16:19:08.723] ./components/admin/SubscriptionManagement.tsx
[16:19:08.723] 7:10  Warning: 'Input' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.723] 27:3  Warning: 'Dialog' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.723] 28:3  Warning: 'DialogContent' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.723] 29:3  Warning: 'DialogDescription' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.723] 30:3  Warning: 'DialogFooter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 31:3  Warning: 'DialogHeader' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 32:3  Warning: 'DialogTitle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 35:3  Warning: 'Search' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 38:3  Warning: 'TrendingDown' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 39:3  Warning: 'Users' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 41:3  Warning: 'Calendar' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 44:3  Warning: 'XCircle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 45:3  Warning: 'Clock' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 53:10  Warning: 'toast' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 60:10  Warning: 'searchTerm' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 60:22  Warning: 'setSearchTerm' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 61:10  Warning: 'planFilter' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 61:22  Warning: 'setPlanFilter' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 62:10  Warning: 'statusFilter' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 62:24  Warning: 'setStatusFilter' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.724] 94:9  Warning: 'getStatusColor' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.725] 156:60  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.725] 375:69  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.725] 
[16:19:08.725] ./components/admin/UserActivityManagement.tsx
[16:19:08.725] 40:3  Warning: 'Calendar' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.725] 41:3  Warning: 'Clock' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.725] 49:3  Warning: 'RefreshCw' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.725] 50:3  Warning: 'Filter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.725] 51:3  Warning: 'BarChart3' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.725] 54:10  Warning: 'toast' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.725] 64:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.725] 119:65  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.725] 133:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.726] 159:60  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.726] 472:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.726] 
[16:19:08.726] ./components/admin/UserManagement.tsx
[16:19:08.726] 32:3  Warning: 'Filter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.726] 34:3  Warning: 'UserX' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.726] 125:18  Warning: 'setSortBy' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.726] 126:21  Warning: 'setSortOrder' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.726] 141:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.726] 142:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.726] 
[16:19:08.726] ./components/auth/auth-modal.tsx
[16:19:08.727] 12:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.727] 302:20  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.727] 
[16:19:08.727] ./components/auth/auth-test.tsx
[16:19:08.727] 13:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.727] 
[16:19:08.727] ./components/auth/protected-route.tsx
[16:19:08.727] 3:19  Warning: 'useRequireAdmin' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.727] 
[16:19:08.727] ./components/auth/session-debug.tsx
[16:19:08.728] 12:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.728] 
[16:19:08.728] ./components/auth/subscription-guard.tsx
[16:19:08.729] 20:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.729] 32:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.729] 108:33  Warning: 'user' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.729] 108:69  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.729] 
[16:19:08.729] ./components/dashboard/marketplace-dashboard.tsx
[16:19:08.729] 3:29  Warning: 'CardDescription' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.729] 3:46  Warning: 'CardHeader' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.729] 3:58  Warning: 'CardTitle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.729] 11:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.729] 
[16:19:08.729] ./components/dashboard/seller-dashboard.tsx
[16:19:08.729] 12:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.730] 
[16:19:08.730] ./components/favorites/FavoritesPage.tsx
[16:19:08.730] 15:3  Warning: 'SortAsc' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.730] 23:3  Warning: 'Filter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.730] 98:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.730] 283:31  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.730] 378:31  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.730] 
[16:19:08.730] ./components/marketplace/CartDropdown.tsx
[16:19:08.730] 51:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.730] 56:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.730] 
[16:19:08.731] ./components/marketplace/FilterSidebar.tsx
[16:19:08.731] 4:34  Warning: 'X' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.731] 
[16:19:08.731] ./components/marketplace/MarketplaceContent.tsx
[16:19:08.731] 35:74  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.731] 
[16:19:08.731] ./components/marketplace/MarketplaceHeader.tsx
[16:19:08.731] 4:32  Warning: 'ShoppingBag' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.731] 80:21  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.731] 
[16:19:08.731] ./components/marketplace/ProductDetailContent.tsx
[16:19:08.731] 243:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.732] 
[16:19:08.732] ./components/marketplace/ProductGrid.tsx
[16:19:08.732] 3:10  Warning: 'useState' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.732] 4:10  Warning: 'Heart' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.732] 4:17  Warning: 'Eye' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.732] 99:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.732] 122:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.732] 149:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.732] 
[16:19:08.732] ./components/marketplace/PurchaseModal.tsx
[16:19:08.732] 15:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.732] 79:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.732] 
[16:19:08.732] ./components/marketplace/ShareModal.tsx
[16:19:08.732] 4:41  Warning: 'Link2' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.732] 
[16:19:08.732] ./components/orders/OrderDetailsPage.tsx
[16:19:08.732] 18:3  Warning: 'DollarSign' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 26:10  Warning: 'formatDistanceToNow' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 38:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.733] 154:28  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.733] 154:49  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.733] 154:68  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.733] 203:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.733] 
[16:19:08.733] ./components/orders/OrdersPage.tsx
[16:19:08.733] 3:20  Warning: 'useMemo' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 10:29  Warning: 'CardHeader' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 10:41  Warning: 'CardTitle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 12:10  Warning: 'Tabs' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 12:16  Warning: 'TabsContent' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 12:29  Warning: 'TabsList' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 12:39  Warning: 'TabsTrigger' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 272:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.733] 
[16:19:08.733] ./components/product/ProductDetailPage.tsx
[16:19:08.733] 9:41  Warning: 'Star' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 90:20  Warning: 'setQuantity' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 96:31  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.733] 334:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.733] 
[16:19:08.733] ./components/product/ProductInfoPanel.tsx
[16:19:08.733] 5:36  Warning: 'Star' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 
[16:19:08.733] ./components/product/PurchaseConfirmationModal.tsx
[16:19:08.733] 18:3  Warning: 'Truck' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 19:3  Warning: 'ArrowRight' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.733] 
[16:19:08.733] ./components/product/SellerInfoCard.tsx
[16:19:08.733] 207:26  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.733] 
[16:19:08.734] ./components/product/ShareProductModal.tsx
[16:19:08.734] 54:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.734] 131:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.734] 
[16:19:08.734] ./components/search/AISearchFeatures.tsx
[16:19:08.734] 27:3  Warning: 'CheckCircle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.734] 28:3  Warning: 'AlertCircle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.734] 197:51  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.734] 274:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.734] 318:33  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.734] 
[16:19:08.734] ./components/search/EnhancedSearchBar.tsx
[16:19:08.734] 9:3  Warning: 'Popover' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.734] 10:3  Warning: 'PopoverContent' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.734] 11:3  Warning: 'PopoverTrigger' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.734] 62:43  Warning: 'getSuggestions' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.734] 274:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.734] 
[16:19:08.734] ./components/search/RecommendationEngine.tsx
[16:19:08.734] 110:18  Warning: 'getRecommendations' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.734] 116:6  Warning: React Hook useEffect has a missing dependency: 'loadRecommendations'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
[16:19:08.734] 151:11  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.734] 152:11  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.734] 271:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.734] 
[16:19:08.735] ./components/search/SearchAnalytics.tsx
[16:19:08.735] 31:3  Warning: 'Filter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.735] 92:3  Warning: 'sellerId' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.735] 378:25  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.735] 378:40  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.735] 
[16:19:08.735] ./components/search/SearchFilters.tsx
[16:19:08.735] 5:10  Warning: 'Input' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.735] 106:68  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.735] 
[16:19:08.735] ./components/search/SearchPage.tsx
[16:19:08.736] 121:71  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.736] 121:92  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.736] 155:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.736] 233:78  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.736] 
[16:19:08.736] ./components/seller/ApplicationSubmissionModal.tsx
[16:19:08.736] 20:3  Warning: 'Calendar' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 119:26  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.736] 306:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.736] 331:22  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.736] 
[16:19:08.736] ./components/seller/BusinessInformationStep.tsx
[16:19:08.736] 7:10  Warning: 'Card' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 64:9  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.736] 75:9  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.736] 
[16:19:08.736] ./components/seller/ExperienceStep.tsx
[16:19:08.736] 8:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 9:10  Warning: 'Checkbox' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 11:3  Warning: 'Select' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 12:3  Warning: 'SelectContent' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 13:3  Warning: 'SelectItem' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 14:3  Warning: 'SelectTrigger' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 15:3  Warning: 'SelectValue' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 20:3  Warning: 'ShoppingBag' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 60:7  Warning: 'VOLUME_OPTIONS' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 68:7  Warning: 'VALUE_OPTIONS' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 183:28  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.736] 186:45  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.736] 278:69  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.736] 
[16:19:08.736] ./components/seller/ImageUpload.tsx
[16:19:08.736] 7:30  Warning: 'ImageIcon' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.736] 37:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.737] 
[16:19:08.737] ./components/seller/InventoryFilters.tsx
[16:19:08.737] 7:18  Warning: 'Filter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.737] 
[16:19:08.737] ./components/seller/InventoryTable.tsx
[16:19:08.737] 16:3  Warning: 'ShoppingCart' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.737] 
[16:19:08.737] ./components/seller/PersonalInformationStep.tsx
[16:19:08.737] 8:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.737] 9:29  Warning: 'Calendar' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.737] 29:9  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.737] 32:9  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.737] 32:40  Warning: Unnecessary escape character: \(.  no-useless-escape
[16:19:08.737] 32:42  Warning: Unnecessary escape character: \).  no-useless-escape
[16:19:08.737] 99:16  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.737] 254:15  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.737] 
[16:19:08.737] ./components/seller/ProductForm.tsx
[16:19:08.737] 17:10  Warning: 'Separator' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.737] 77:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 121:91  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 153:6  Warning: React Hook useEffect has a missing dependency: 'handleAutoSave'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
[16:19:08.737] 165:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 169:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 208:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 212:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 230:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 233:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 262:28  Warning: 'data' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.737] 275:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 430:82  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.737] 
[16:19:08.737] ./components/seller/RecentSales.tsx
[16:19:08.737] 6:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.738] 14:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.738] 25:9  Warning: 'formatCurrency' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.738] 32:9  Warning: 'getStatusColor' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.738] 
[16:19:08.738] ./components/seller/SellerApplicationForm.tsx
[16:19:08.738] 133:47  Warning: 'errors' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.738] 185:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.738] 
[16:19:08.738] ./components/seller/SellerLayout.tsx
[16:19:08.738] 11:3  Warning: 'Plus' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.738] 13:3  Warning: 'FileText' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.739] 
[16:19:08.739] ./components/seller/VerificationStep.tsx
[16:19:08.739] 56:37  Warning: 'type' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.740] 88:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.740] 113:81  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.740] 163:9  Warning: 'formatFileSize' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.740] 
[16:19:08.740] ./components/seller/dashboard/EnhancedSellerDashboard.tsx
[16:19:08.740] 10:10  Warning: 'Tabs' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.740] 10:16  Warning: 'TabsContent' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.740] 10:29  Warning: 'TabsList' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.740] 10:39  Warning: 'TabsTrigger' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 23:3  Warning: 'TrendingUp' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 24:3  Warning: 'TrendingDown' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 28:3  Warning: 'Eye' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 30:3  Warning: 'Calendar' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 32:10  Warning: 'Id' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 36:7  Warning: 'PAYMENT_DATA' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 43:10  Warning: 'activeTab' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 43:21  Warning: 'setActiveTab' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 44:11  Warning: 'user' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 72:9  Warning: 'getOrderStatusColor' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.741] 
[16:19:08.741] ./components/seller/dashboard/InventoryFilters.tsx
[16:19:08.742] 21:3  Warning: 'Filter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.742] 24:3  Warning: 'Calendar' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.742] 26:3  Warning: 'Package' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.742] 95:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.742] 336:29  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.742] 336:46  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
[16:19:08.742] 
[16:19:08.742] ./components/seller/dashboard/InventoryTable.tsx
[16:19:08.742] 78:18  Warning: 'setSortBy' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.742] 79:21  Warning: 'setSortOrder' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.742] 129:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.742] 223:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.742] 
[16:19:08.743] ./components/seller/dashboard/SellerDashboardOverview.tsx
[16:19:08.743] 4:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.743] 234:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.743] 
[16:19:08.743] ./components/seller/inventory/EnhancedInventoryManagement.tsx
[16:19:08.743] 7:29  Warning: 'CardDescription' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.743] 111:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.743] 141:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.743] 150:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.743] 158:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.743] 184:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.744] 191:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.744] 196:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.744] 201:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.744] 474:27  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.744] 
[16:19:08.744] ./components/seller/offline/ClientInfoForm.tsx
[16:19:08.744] 100:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.744] 105:9  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.744] 108:9  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.744] 108:40  Warning: Unnecessary escape character: \(.  no-useless-escape
[16:19:08.745] 108:42  Warning: Unnecessary escape character: \).  no-useless-escape
[16:19:08.745] 117:9  Warning: Unexpected lexical declaration in case block.  no-case-declarations
[16:19:08.745] 128:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.745] 145:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.745] 159:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.745] 184:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.745] 214:11  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.745] 
[16:19:08.745] ./components/seller/offline/InvoiceGenerator.tsx
[16:19:08.745] 83:6  Warning: React Hook useEffect has a missing dependency: 'generateInvoice'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
[16:19:08.745] 307:11  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.745] 
[16:19:08.745] ./components/seller/offline/InvoiceManagement.tsx
[16:19:08.746] 33:3  Warning: 'Filter' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.746] 39:3  Warning: 'Clock' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.746] 145:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.746] 146:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.746] 
[16:19:08.746] ./components/seller/offline/SellOfflineModal.tsx
[16:19:08.746] 142:11  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.746] 174:24  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.746] 
[16:19:08.746] ./components/seller/product/CustomerInfoSection.tsx
[16:19:08.746] 5:10  Warning: 'Textarea' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.746] 
[16:19:08.746] ./components/seller/product/ImageUploadSection.tsx
[16:19:08.746] 52:6  Warning: React Hook useCallback has a missing dependency: 'handleFileSelection'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
[16:19:08.747] 98:55  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.747] 238:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.747] 
[16:19:08.747] ./components/seller/product/InternalInfoSection.tsx
[16:19:08.747] 21:3  Warning: 'FileText' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.747] 
[16:19:08.747] ./components/seller/product/ProductForm.tsx
[16:19:08.747] 114:6  Warning: React Hook useEffect has a missing dependency: 'handleAutoSave'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
[16:19:08.747] 
[16:19:08.747] ./components/seller/product/ProductPreview.tsx
[16:19:08.747] 88:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.747] 89:73  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.747] 143:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.747] 
[16:19:08.747] ./components/seller/product/RichTextEditor.tsx
[16:19:08.747] 13:3  Warning: 'Type' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.747] 265:14  Warning: Unknown property 'jsx' found  react/no-unknown-property
[16:19:08.747] 
[16:19:08.747] ./components/seller/products/AddProductForm.tsx
[16:19:08.747] 33:3  Warning: 'AlertCircle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.747] 122:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.747] 267:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.747] 601:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
[16:19:08.747] 
[16:19:08.748] ./components/seller/reports/KeyMetrics.tsx
[16:19:08.748] 12:3  Warning: 'Eye' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 
[16:19:08.748] ./components/seller/reports/SalesChartsSection.tsx
[16:19:08.748] 6:3  Warning: 'BarChart3' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 21:3  Warning: 'dateRange' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 40:9  Warning: 'maxRevenue' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 145:19  Warning: Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free.  @typescript-eslint/ban-ts-comment
[16:19:08.748] 
[16:19:08.748] ./components/seller/reports/SalesMetricsCards.tsx
[16:19:08.748] 4:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 
[16:19:08.748] ./components/seller/reports/SalesOverview.tsx
[16:19:08.748] 18:3  Warning: 'Legend' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 75:70  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.748] 
[16:19:08.748] ./components/seller/reports/SalesReportsDashboard.tsx
[16:19:08.748] 12:3  Warning: 'DollarSign' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 13:3  Warning: 'Package' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 14:3  Warning: 'Users' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 15:3  Warning: 'Target' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 193:9  Warning: 'formatCurrency' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.748] 
[16:19:08.748] ./components/seller/reports/TimeBasedReports.tsx
[16:19:08.749] 9:16  Warning: 'TabsContent' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.749] 20:3  Warning: 'PieChart' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.749] 21:3  Warning: 'Pie' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.749] 22:3  Warning: 'Cell' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.749] 84:9  Warning: 'COLORS' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.749] 98:85  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.749] 191:21  Warning: Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free.  @typescript-eslint/ban-ts-comment
[16:19:08.749] 219:21  Warning: Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free.  @typescript-eslint/ban-ts-comment
[16:19:08.749] 253:19  Warning: Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free.  @typescript-eslint/ban-ts-comment
[16:19:08.749] 
[16:19:08.749] ./components/seller/reports/TopPerformers.tsx
[16:19:08.749] 89:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.749] 90:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.749] 
[16:19:08.749] ./components/seller/reports/TopPerformersSection.tsx
[16:19:08.749] 4:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.749] 15:3  Warning: 'DollarSign' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.749] 
[16:19:08.749] ./components/seller/steps/BusinessInfoStep.tsx
[16:19:08.749] 5:10  Warning: 'Label' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.749] 10:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.750] 
[16:19:08.750] ./components/seller/steps/ExperienceStep.tsx
[16:19:08.750] 4:10  Warning: 'Input' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 11:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.750] 25:11  Warning: 'register' is assigned a value but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 
[16:19:08.750] ./components/seller/steps/PersonalInfoStep.tsx
[16:19:08.750] 5:10  Warning: 'Label' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 9:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.750] 10:10  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.750] 
[16:19:08.750] ./components/seller/steps/ReviewStep.tsx
[16:19:08.750] 9:43  Warning: 'CreditCard' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 12:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.750] 175:78  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.750] 
[16:19:08.750] ./components/seller/steps/SuccessStep.tsx
[16:19:08.750] 94:16  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.750] 
[16:19:08.750] ./components/seller/steps/VerificationStep.tsx
[16:19:08.750] 8:10  Warning: 'FormField' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 11:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.750] 
[16:19:08.750] ./components/settings/BillingSettings.tsx
[16:19:08.750] 8:29  Warning: 'CardDescription' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 8:46  Warning: 'CardHeader' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 8:58  Warning: 'CardTitle' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 13:3  Warning: 'CreditCard' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 16:3  Warning: 'Edit' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 18:3  Warning: 'Calendar' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 19:3  Warning: 'DollarSign' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 124:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 135:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 146:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.750] 
[16:19:08.751] ./components/settings/PreferencesSettings.tsx
[16:19:08.751] 15:3  Warning: 'Bell' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.751] 18:3  Warning: 'Globe' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.751] 19:3  Warning: 'Palette' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.751] 20:3  Warning: 'Volume2' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.751] 21:3  Warning: 'Eye' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.751] 22:3  Warning: 'Star' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.751] 23:3  Warning: 'ShoppingBag' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 24:3  Warning: 'TrendingUp' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 25:3  Warning: 'Users' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 26:3  Warning: 'Gift' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 27:3  Warning: 'MessageSquare' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 260:50  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.752] 519:44  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.752] 
[16:19:08.752] ./components/settings/ProfileSettings.tsx
[16:19:08.752] 5:23  Warning: 'useQuery' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 16:38  Warning: 'User' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 
[16:19:08.752] ./components/settings/SecuritySettings.tsx
[16:19:08.752] 114:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 128:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 139:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 148:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
[16:19:08.752] 315:38  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
[16:19:08.752] 
[16:19:08.752] ./components/settings/ShippingSettings.tsx
[16:19:08.752] 249:74  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[16:19:08.752] 
[16:19:08.752] info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
[16:19:26.396]    Collecting page data ...
[16:19:29.731]    Generating static pages (0/25) ...
[16:19:31.199]    Generating static pages (6/25) 
[16:19:31.199]    Generating static pages (12/25) 
[16:19:31.199]    Generating static pages (18/25) 
[16:19:31.199]  ✓ Generating static pages (25/25)
[16:19:31.200] AdminLayout - Auth state: {
[16:19:31.201]   isLoading: true,
[16:19:31.201]   isAuthenticated: false,
[16:19:31.201]   userType: undefined,
[16:19:31.201]   email: undefined
[16:19:31.201] }
[16:19:31.201] AdminLayout - Auth state: {
[16:19:31.201]   isLoading: true,
[16:19:31.201]   isAuthenticated: false,
[16:19:31.201]   userType: undefined,
[16:19:31.201]   email: undefined
[16:19:31.201] }
[16:19:31.201] AdminLayout - Auth state: {
[16:19:31.201]   isLoading: true,
[16:19:31.201]   isAuthenticated: false,
[16:19:31.201]   userType: undefined,
[16:19:31.201]   email: undefined
[16:19:31.201] }
[16:19:31.465]    Finalizing page optimization ...
[16:19:31.470]    Collecting build traces ...
[16:19:42.555] 
[16:19:42.633] Route (app)                                 Size  First Load JS
[16:19:42.633] ┌ ○ /                                    6.33 kB         233 kB
[16:19:42.633] ├ ○ /_not-found                            984 B         103 kB
[16:19:42.633] ├ ○ /admin                               21.3 kB         201 kB
[16:19:42.633] ├ ○ /admin/cleanup                       6.65 kB         154 kB
[16:19:42.633] ├ ○ /admin/seller-applications           9.71 kB         168 kB
[16:19:42.633] ├ ○ /apply                               11.3 kB         232 kB
[16:19:42.633] ├ ○ /cart                                3.51 kB         195 kB
[16:19:42.633] ├ ○ /dashboard                           8.95 kB         156 kB
[16:19:42.633] ├ ○ /favorites                           5.89 kB         209 kB
[16:19:42.634] ├ ○ /forgot-password                     4.31 kB         126 kB
[16:19:42.634] ├ ○ /login                               5.13 kB         169 kB
[16:19:42.634] ├ ○ /marketplace                         1.11 kB         205 kB
[16:19:42.634] ├ ƒ /marketplace/product/[id]            15.7 kB         207 kB
[16:19:42.634] ├ ○ /orders                              5.31 kB         208 kB
[16:19:42.634] ├ ƒ /orders/[id]                         4.71 kB         196 kB
[16:19:42.634] ├ ○ /register                            4.39 kB         168 kB
[16:19:42.634] ├ ○ /search                              20.1 kB         162 kB
[16:19:42.634] ├ ○ /seller/dashboard                    8.21 kB         146 kB
[16:19:42.634] ├ ○ /seller/inventory                    8.81 kB         185 kB
[16:19:42.634] ├ ƒ /seller/invoices/[id]                 5.5 kB         146 kB
[16:19:42.634] ├ ƒ /seller/products/[id]/edit           30.2 kB         234 kB
[16:19:42.634] ├ ƒ /seller/products/[id]/sell-offline   4.67 kB         196 kB
[16:19:42.634] ├ ○ /seller/products/new                 11.4 kB         180 kB
[16:19:42.634] ├ ○ /seller/reports                       128 kB         287 kB
[16:19:42.634] ├ ○ /seller/sales                        7.94 kB         154 kB
[16:19:42.634] ├ ○ /settings                            38.1 kB         248 kB
[16:19:42.634] └ ○ /unauthorized                         4.3 kB         143 kB
[16:19:42.634] + First Load JS shared by all             102 kB
[16:19:42.635]   ├ chunks/1272-28cc8114edc81e1f.js        47 kB
[16:19:42.635]   ├ chunks/7037dcde-54bdb793ab000378.js  53.2 kB
[16:19:42.635]   └ other shared chunks (total)          1.94 kB
[16:19:42.635] 
[16:19:42.635] 
[16:19:42.635] ƒ Middleware                              464 kB
[16:19:42.635] 
[16:19:42.635] ○  (Static)   prerendered as static content
[16:19:42.636] ƒ  (Dynamic)  server-rendered on demand
[16:19:42.636] 
[16:19:42.681] ✔ Ran "cd ../../apps/web && pnpm build" with environment variable "NEXT_PUBLIC_CONVEX_URL" set
[16:19:42.681] - Deploying to https://kindly-giraffe-916.convex.cloud...
[16:19:42.681] 
[16:19:49.449] ✔ Deployed Convex functions to https://kindly-giraffe-916.convex.cloud
[16:19:49.665] Traced Next.js server files in: 90.887ms
[16:19:49.814] Created all serverless functions in: 148.937ms
[16:19:50.112] Collected static files (public/, static/, .next/static): 9.853ms
[16:19:50.240] Build Completed in /vercel/output [1m]
[16:19:50.433] Deploying outputs...
[16:20:03.928] Deployment completed
[16:20:04.787] Creating build cache...